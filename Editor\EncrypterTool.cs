﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class EncrypterTool
{
    [MenuItem("Nirvana/Yifan/Encrypt")]
    private static void Encrypt()
    {
        List<string> list = new List<string>();
        DirectoryUtil.GetAllFiles(LuaConst.luaCoreDir, list);

        string targetDir = string.Format("{0}/Game/Lua", Application.dataPath);
        foreach (var path in list)
        {
            Encrypter.EncryptFile(path, Path.Combine(targetDir, DirectoryUtil.RelativePath(LuaConst.luaCoreDir, path)));
        }

        AssetDatabase.Refresh();
    }
}
