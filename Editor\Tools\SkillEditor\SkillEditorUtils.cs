using System.Collections;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 技能编辑器工具类
/// 提供通用的工具方法和扩展功能
/// </summary>
public static class SkillEditorUtils
{
    #region 文件操作
    /// <summary>
    /// 确保目录存在
    /// </summary>
    public static void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
            AssetDatabase.Refresh();
        }
    }

    /// <summary>
    /// 构建配置文件路径
    /// </summary>
    public static string BuildConfigPath(int modelType, string modelResId, string suffix)
    {
        if (!SkillEditorEventDrawer.saveFolderNameMap.TryGetValue(modelType, out string actorName) ||
            string.IsNullOrEmpty(actorName))
        {
            return "";
        }

        string basePath = Application.dataPath + SkillEditorGUIConfig.SAVE_FOLDER;
        string configPath = suffix switch
        {
            "json" => $"{basePath}json/{actorName}",
            "lua" => $"{basePath}config/{actorName}",
            _ => basePath
        };

        EnsureDirectoryExists(configPath);
        return $"{configPath}/{modelResId}_config.{suffix}";
    }
    #endregion

    #region 资产操作
    /// <summary>
    /// 获取资产ID
    /// </summary>
    public static AssetID GetAssetID(UnityEngine.Object asset)
    {
        if (asset == null) return AssetID.Empty;

        var path = AssetDatabase.GetAssetPath(asset);
        var guid = AssetDatabase.AssetPathToGUID(path);
        var importer = AssetImporter.GetAtPath(path);

        return new AssetID(importer?.assetBundleName ?? "", asset.name)
        {
            AssetGUID = guid
        };
    }

    /// <summary>
    /// 从资产路径加载GameObject
    /// </summary>
    public static GameObject LoadGameObjectFromPath(string path)
    {
        return AssetDatabase.LoadAssetAtPath<GameObject>(path);
    }

    /// <summary>
    /// 从GUID加载资产
    /// </summary>
    public static T LoadAssetFromGUID<T>(string guid) where T : UnityEngine.Object
    {
        string path = AssetDatabase.GUIDToAssetPath(guid);
        return AssetDatabase.LoadAssetAtPath<T>(path);
    }
    #endregion

    #region 层级路径操作
    /// <summary>
    /// 获取Transform的层级路径
    /// </summary>
    public static string GetHierarchyPath(Transform transform, Transform root)
    {
        if (transform == root)
        {
            return string.Empty;
        }

        var pathBuilder = new StringBuilder();
        pathBuilder.Append(transform.name);
        var currentNode = transform.parent;

        while (currentNode != null && currentNode != root)
        {
            pathBuilder.Insert(0, $"{currentNode.name}/");
            currentNode = currentNode.parent;
        }

        return pathBuilder.ToString();
    }

    /// <summary>
    /// 从层级路径查找Transform
    /// </summary>
    public static Transform FindTransformFromPath(Transform root, string hierarchyPath)
    {
        return string.IsNullOrEmpty(hierarchyPath) ? null : root.Find(hierarchyPath);
    }
    #endregion

    #region 游戏对象操作
    /// <summary>
    /// 设置游戏对象层级（递归）
    /// </summary>
    public static void SetLayerRecursively(this GameObject obj, int layer)
    {
        obj.layer = layer;
        foreach (Transform child in obj.transform)
        {
            child.gameObject.SetLayerRecursively(layer);
        }
    }

    /// <summary>
    /// 获取或添加组件
    /// </summary>
    public static T GetOrAddComponent<T>(this GameObject obj) where T : Component
    {
        T component = obj.GetComponent<T>();
        if (component == null)
        {
            component = obj.AddComponent<T>();
        }
        return component;
    }
    #endregion

    #region 协程工具
    /// <summary>
    /// 延迟执行协程
    /// </summary>
    public static IEnumerator DelayedAction(float delay, System.Action action)
    {
        yield return new WaitForSeconds(delay);
        action?.Invoke();
    }

    /// <summary>
    /// 释放游戏对象协程
    /// </summary>
    public static IEnumerator FreeGameObjectCoroutine(GameObject gameObject, float delay)
    {
        if (delay <= 0f)
        {
            GameObjectPool.Instance.Free(gameObject);
        }
        else
        {
            yield return new WaitForSeconds(delay);
            GameObjectPool.Instance.Free(gameObject);
        }
    }
    #endregion

    #region 数值工具
    /// <summary>
    /// 安全解析浮点数
    /// </summary>
    public static float SafeParseFloat(string value, float defaultValue = 0f)
    {
        return float.TryParse(value, out float result) ? result : defaultValue;
    }

    /// <summary>
    /// 安全解析整数
    /// </summary>
    public static int SafeParseInt(string value, int defaultValue = 0)
    {
        return int.TryParse(value, out int result) ? result : defaultValue;
    }

    /// <summary>
    /// 限制值在指定范围内
    /// </summary>
    public static T Clamp<T>(T value, T min, T max) where T : System.IComparable<T>
    {
        if (value.CompareTo(min) < 0) return min;
        if (value.CompareTo(max) > 0) return max;
        return value;
    }
    #endregion

    #region 字符串工具
    /// <summary>
    /// 检查字符串是否为空或null
    /// </summary>
    public static bool IsNullOrEmpty(string value)
    {
        return string.IsNullOrEmpty(value);
    }

    /// <summary>
    /// 获取安全的字符串值
    /// </summary>
    public static string GetSafeString(string value, string defaultValue = "")
    {
        return IsNullOrEmpty(value) ? defaultValue : value;
    }
    #endregion

    #region 数组工具
    /// <summary>
    /// 安全获取数组索引
    /// </summary>
    public static int GetSafeArrayIndex(string[] array, string value)
    {
        if (array == null || IsNullOrEmpty(value)) return 0;
        
        for (int i = 0; i < array.Length; i++)
        {
            if (array[i] == value) return i;
        }
        return 0;
    }

    /// <summary>
    /// 生成名称数组
    /// </summary>
    public static string[] GenerateNames(string prefix, int start, int count)
    {
        string[] names = new string[count];
        for (int i = 0; i < count; i++)
        {
            names[i] = $"{prefix}{start + i}";
        }
        return names;
    }
    #endregion
}
