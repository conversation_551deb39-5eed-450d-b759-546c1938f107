﻿using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;

public class SpriteDuplicateChecker
{
    [MenuItem("自定义工具/UI类/查找Sprite图集重复引用")]
    public static void CheckSpritesInMultipleAtlases()
    {
        string checkPath = "Assets\\Game\\UIs";
        if (!Directory.Exists(checkPath))
        {
            Debug.LogError($"文件夹不存在：{checkPath}");
            return;
        }

        string[] spritePaths = Directory.GetFiles(checkPath, "*.png", SearchOption.AllDirectories);
        Dictionary<string, List<SpriteAtlas>> spriteAtlasMap = new Dictionary<string, List<SpriteAtlas>>();

        for (int i = 0; i < spritePaths.Length; i++)
        {
            spritePaths[i] = NormalizePath(spritePaths[i]);
        }

        string[] atlasGuids = AssetDatabase.FindAssets("t:SpriteAtlas", new[] { checkPath });
        foreach (string guid in atlasGuids)
        {
            string atlasPath = AssetDatabase.GUIDToAssetPath(guid);
            SpriteAtlas atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);

            if (atlas == null)
                continue;

            Object[] packedSprite = atlas.GetPackables();
            foreach (Object obj in packedSprite)
            {
                if (obj is Sprite sprite)
                {
                    string spritePath = NormalizePath(AssetDatabase.GetAssetPath(sprite));
                    if (spritePaths.Contains(spritePath))
                    {
                        if (!spriteAtlasMap.ContainsKey(spritePath))
                        {
                            spriteAtlasMap.Add(spritePath, new List<SpriteAtlas>());
                        }
                        spriteAtlasMap[spritePath].Add(atlas);
                    }
                }
            }
        }

        foreach (var entry in spriteAtlasMap)
        {
            if (entry.Value.Count > 1)
            {
                string spriteName = Path.GetFileName(entry.Key);
                string atlasNames = string.Join(",\n", entry.Value.ConvertAll(atlas => atlas.name));
                Debug.LogError($"<color=#00FF00>图片: {spriteName} 包含在了{entry.Value.Count}个图集中：</color>\n{atlasNames}");
            }
        }
        Debug.LogError("<color=#00FFFF>检查完成</color>");
    }

    private static string NormalizePath(string path)
    {
        return path.Replace("\\", "/");
    }
}
