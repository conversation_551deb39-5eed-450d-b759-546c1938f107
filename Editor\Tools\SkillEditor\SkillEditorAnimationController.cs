using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 动画控制器
/// 负责动画的播放、控制和事件处理
/// </summary>
public class SkillEditorAnimationController
{
    #region 私有字段
    private SkillEditorModelManager _modelManager;
    private SkillEditorProjectileManager _projectileManager;
    private SkillEditorHurtManager _hurtManager;
    private int _comboGroup = 0;
    private int _comboIndex = 0;
    private Vector2 _buttonScroll;
    private string _selectEventName = string.Empty;
    #endregion

    #region 属性
    public string SelectedEventName => _selectEventName;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化动画控制器
    /// </summary>
    public void Initialize(SkillEditorModelManager modelManager, SkillEditorProjectileManager projectileManager, SkillEditorHurtManager hurtManager)
    {
        _modelManager = modelManager;
        _projectileManager = projectileManager;
        _hurtManager = hurtManager;
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制动画按钮窗口
    /// </summary>
    public void DrawAnimationButtonWindow(int currentEffectArrayIndex)
    {
        if (!IsValidModelType()) return;

        var animNameList = SkillEditorEventDrawer.animDataCache[_modelManager.ModelType];
        if (animNameList == null || animNameList.Count == 0) return;

        var overrideController = _modelManager.MainAnimator?.runtimeAnimatorController as AnimatorOverrideController;

        _buttonScroll = EditorGUILayout.BeginScrollView(_buttonScroll);
        EditorGUILayout.BeginVertical();

        DrawEffectArrayIndexController(currentEffectArrayIndex);
        DrawAnimationButtons(animNameList, overrideController);

        EditorGUILayout.EndVertical();
        EditorGUILayout.EndScrollView();
    }

    /// <summary>
    /// 验证模型类型是否有效
    /// </summary>
    private bool IsValidModelType()
    {
        return _modelManager.ModelType != 0;
    }

    /// <summary>
    /// 绘制特效数组索引控制器
    /// </summary>
    private void DrawEffectArrayIndexController(int currentEffectArrayIndex)
    {
        if (_modelManager.ModelType != (int)SkillEditorEventConfig.ModelType.Role) return;

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("当前特效索引:", GUILayout.Width(100));
        
        int newIndex = EditorGUILayout.IntSlider(
            currentEffectArrayIndex,
            0,
            SkillEditorGUIConfig.EFFECT_ARRAY_SIZE - 1,
            GUILayout.Width(200));
        
        GUILayout.Label($"(特效 {currentEffectArrayIndex + 1})", GUILayout.Width(80));
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.Space(5);
    }

    /// <summary>
    /// 绘制动画按钮
    /// </summary>
    private void DrawAnimationButtons(List<string> animNameList, AnimatorOverrideController overrideController)
    {
        bool isRowOpen = false;

        for (int i = 0; i < animNameList.Count; i++)
        {
            if (i % 4 == 0)
            {
                isRowOpen = true;
                EditorGUILayout.BeginHorizontal();
            }

            DrawAnimationButton(animNameList[i], overrideController);

            if (i % 4 == 3)
            {
                isRowOpen = false;
                EditorGUILayout.EndHorizontal();
            }
        }

        if (isRowOpen)
        {
            EditorGUILayout.EndHorizontal();
        }
    }

    /// <summary>
    /// 绘制单个动画按钮
    /// </summary>
    private void DrawAnimationButton(string animName, AnimatorOverrideController overrideController)
    {
        if (GUILayout.Button(animName, GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            OnAnimationButtonClicked(animName, overrideController);
        }
    }

    /// <summary>
    /// 动画按钮点击处理
    /// </summary>
    private void OnAnimationButtonClicked(string animName, AnimatorOverrideController overrideController)
    {
        var eventName = $"{animName}/hit";
        _selectEventName = eventName;
        PlayProjectileAction(animName, overrideController);
    }
    #endregion

    #region 动画播放控制
    /// <summary>
    /// 播放弹道动作
    /// </summary>
    private void PlayProjectileAction(string name, AnimatorOverrideController overrideController)
    {
        TryDoAnimatorCrossFade(name, overrideController);
    }

    /// <summary>
    /// 尝试执行动画师交叉淡入淡出
    /// </summary>
    private void TryDoAnimatorCrossFade(string action, AnimatorOverrideController overrideController)
    {
        if (_modelManager.PartGroupAnimators.Count > 0)
        {
            ExecuteAnimationOnPartGroup(action, overrideController);
        }
        else
        {
            ExecuteAnimationOnMainAnimator(action, overrideController);
        }
    }

    /// <summary>
    /// 在部件组上执行动画
    /// </summary>
    private void ExecuteAnimationOnPartGroup(string action, AnimatorOverrideController overrideController)
    {
        foreach (Animator animator in _modelManager.PartGroupAnimators)
        {
            if (animator != null)
            {
                DoAnimatorCrossFade(animator, action, overrideController);
            }
        }
    }

    /// <summary>
    /// 在主动画师上执行动画
    /// </summary>
    private void ExecuteAnimationOnMainAnimator(string action, AnimatorOverrideController overrideController)
    {
        if (_modelManager.MainAnimator != null)
        {
            DoAnimatorCrossFade(_modelManager.MainAnimator, action, overrideController);
        }
    }

    /// <summary>
    /// 执行动画师交叉淡入淡出
    /// </summary>
    private void DoAnimatorCrossFade(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        if (animator == null) return;

        RegisterAnimationEvents(animator, action, overrideController);
        animator.CrossFade(action, 0);
    }

    /// <summary>
    /// 注册动画事件
    /// </summary>
    private void RegisterAnimationEvents(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        RegisterBeginEvent(animator, action);
        RegisterOverEvent(animator, action, overrideController);
    }

    /// <summary>
    /// 注册开始事件
    /// </summary>
    private void RegisterBeginEvent(Animator animator, string action)
    {
        animator.WaitEvent($"{action}/begin", (param, info) =>
        {
            var target = animator.transform;
            _hurtManager.PlayHurtShow(action, target, target, null);
        });
    }

    /// <summary>
    /// 注册结束事件
    /// </summary>
    private void RegisterOverEvent(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        // 第一个结束事件：立即切换到idle
        animator.WaitEvent($"{action}/over", (param, info) =>
        {
            animator.CrossFade("idle", 0);
        });

        // 第二个结束事件：处理连击和后摇
        animator.WaitEvent($"{action}/over", (param, info) =>
        {
            HandleAnimationEnd(animator, action, overrideController);
        });
    }

    /// <summary>
    /// 处理动画结束
    /// </summary>
    private void HandleAnimationEnd(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        if (overrideController != null)
        {
            HandleComboOrBackswing(animator, action, overrideController);
        }
        else
        {
            animator.CrossFade("idle", 0);
        }
    }

    /// <summary>
    /// 处理连击或后摇
    /// </summary>
    private void HandleComboOrBackswing(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        var comboName = $"combo{_comboGroup}_{_comboIndex + 1}";

        if (HasComboAnimation(overrideController, comboName))
        {
            HandleCombo(overrideController);
        }
        else
        {
            HandleBackswing(animator, action, overrideController);
        }
    }

    /// <summary>
    /// 检查是否有连击动画
    /// </summary>
    private bool HasComboAnimation(AnimatorOverrideController overrideController, string comboName)
    {
        return overrideController[comboName] != null &&
               overrideController[comboName].name != comboName;
    }

    /// <summary>
    /// 处理连击
    /// </summary>
    private void HandleCombo(AnimatorOverrideController overrideController)
    {
        _comboIndex++;
        HandleComboAction(overrideController, _comboGroup, _comboIndex);
    }

    /// <summary>
    /// 处理后摇
    /// </summary>
    private void HandleBackswing(Animator animator, string action, AnimatorOverrideController overrideController)
    {
        if (HasBackswingAnimation(overrideController, action))
        {
            PlayBackswingAnimation(animator, action);
        }
        else
        {
            animator.CrossFade("idle", 0);
        }
    }

    /// <summary>
    /// 检查是否有后摇动画
    /// </summary>
    private bool HasBackswingAnimation(AnimatorOverrideController overrideController, string action)
    {
        return overrideController[$"d_{action}_back"] != null ||
               overrideController[$"{action}_back"] != null;
    }

    /// <summary>
    /// 播放后摇动画
    /// </summary>
    private void PlayBackswingAnimation(Animator animator, string action)
    {
        string backAction = $"{action}_back";
        animator.CrossFade(backAction, 0);

        animator.WaitEvent($"{backAction}/over", (param1, info1) =>
        {
            animator.CrossFade("idle", 0);
        });
    }

    /// <summary>
    /// 连击处理器
    /// </summary>
    private void HandleComboAction(AnimatorOverrideController overrideController, int comboGroup, int comboIndex)
    {
        var comboName = $"combo{comboGroup}_{comboIndex}";

        if (HasValidComboAnimation(overrideController, comboName))
        {
            ExecuteComboAction(comboGroup, comboIndex, overrideController);
        }
        else
        {
            ResetComboState();
        }
    }

    /// <summary>
    /// 检查是否有有效的连击动画
    /// </summary>
    private bool HasValidComboAnimation(AnimatorOverrideController overrideController, string comboName)
    {
        return overrideController[comboName] != null &&
               overrideController[comboName].name != comboName;
    }

    /// <summary>
    /// 执行连击动作
    /// </summary>
    private void ExecuteComboAction(int comboGroup, int comboIndex, AnimatorOverrideController overrideController)
    {
        var eventName = $"combo{comboGroup}_{comboIndex}/hit";
        var actionName = $"combo{comboGroup}_{comboIndex}";
        _selectEventName = eventName;
        PlayProjectileAction(actionName, overrideController);
    }

    /// <summary>
    /// 重置连击状态
    /// </summary>
    private void ResetComboState()
    {
        _comboGroup = 0;
        _comboIndex = 0;
    }
    #endregion
}
