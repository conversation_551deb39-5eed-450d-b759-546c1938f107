using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 音效管理器
/// 负责音效的配置、播放和管理
/// </summary>
public class SkillEditorSoundManager
{
    #region 私有字段
    private SkillEditorPrefabDataConfig _dataConfig;
    private SkillEditorModelManager _modelManager;
    private int _selectSoundId = -1;
    private Vector2 _soundScroll;
    private int _soundCount = 0;
    #endregion

    #region 属性
    public int SelectedSoundId => _selectSoundId;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化音效管理器
    /// </summary>
    public void Initialize(SkillEditorPrefabDataConfig dataConfig, SkillEditorModelManager modelManager)
    {
        _dataConfig = dataConfig;
        _modelManager = modelManager;
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制音效管理界面
    /// </summary>
    public void DrawSoundsWindow()
    {
        GUILayout.BeginVertical();
        DrawAddSoundButton();
        DrawSoundsList();
        GUILayout.EndVertical();

        DrawDeleteSoundButton();
    }

    /// <summary>
    /// 绘制添加音效按钮
    /// </summary>
    private void DrawAddSoundButton()
    {
        if (GUILayout.Button("增加音效", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewSound();
        }
    }

    /// <summary>
    /// 绘制音效列表
    /// </summary>
    private void DrawSoundsList()
    {
        if (_dataConfig?.actorTriggers?.sounds == null) return;

        _soundScroll = SkillEditorGUI.DrawScrollList(
            _soundScroll,
            _dataConfig.actorTriggers.sounds,
            sound => SkillEditorGUIConfig.GetDefaultButtonName(sound.soundBtnName),
            OnSoundSelected);
    }

    /// <summary>
    /// 绘制删除音效按钮
    /// </summary>
    private void DrawDeleteSoundButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect1);

        if (_dataConfig?.actorTriggers?.sounds?.Count > 0)
        {
            if (GUILayout.Button("删除音效", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedSound();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制音效详细配置
    /// </summary>
    public void DrawSoundDetails()
    {
        if (!IsValidSoundSelection()) return;

        var sound = _dataConfig.actorTriggers.sounds[_selectSoundId];

        DrawSoundBasicSettings(sound);
        DrawSoundAssetSettings(sound);

        _dataConfig.actorTriggers.sounds[_selectSoundId] = sound;
    }
    #endregion

    #region 音效配置绘制
    /// <summary>
    /// 绘制音效基础设置
    /// </summary>
    private void DrawSoundBasicSettings(SkillEditorPrefabDataConfig.TriggerSound sound)
    {
        sound.soundEventName = SkillEditorGUI.DrawEventSelector(sound.soundEventName);
        sound.soundDelay = SkillEditorGUI.DrawDelayField("延迟时间", sound.soundDelay);
        sound.soundBtnName = SkillEditorGUI.DrawButtonNameField(sound.soundBtnName);
    }

    /// <summary>
    /// 绘制音效资产设置
    /// </summary>
    private void DrawSoundAssetSettings(SkillEditorPrefabDataConfig.TriggerSound sound)
    {
        EditorGUI.BeginChangeCheck();
        AudioItem audioItem = SkillEditorGUI.DrawAudioSelector(
            sound.soundAudioGoName, 
            sound.soundAudioAsset.GetAssetPath());

        if (EditorGUI.EndChangeCheck())
        {
            sound.soundAudioAsset = SkillEditorUtils.GetAssetID(audioItem);
            sound.soundAudioGoName = audioItem?.name ?? "";
        }
    }
    #endregion

    #region 音效操作
    /// <summary>
    /// 添加新音效
    /// </summary>
    private void AddNewSound()
    {
        if (_dataConfig?.actorTriggers?.sounds == null) return;

        var sound = new SkillEditorPrefabDataConfig.TriggerSound();
        _dataConfig.actorTriggers.sounds.Add(sound);
    }

    /// <summary>
    /// 删除选中的音效
    /// </summary>
    private void DeleteSelectedSound()
    {
        if (_selectSoundId == -1 || _dataConfig.actorTriggers.sounds.Count == 0)
            return;

        _dataConfig.actorTriggers.sounds.RemoveAt(_selectSoundId);
        _selectSoundId = _dataConfig.actorTriggers.sounds.Count == 0
            ? -1
            : _dataConfig.actorTriggers.sounds.Count - 1;
    }

    /// <summary>
    /// 音效选择回调
    /// </summary>
    private void OnSoundSelected(int index)
    {
        _selectSoundId = index;
    }

    /// <summary>
    /// 验证音效选择是否有效
    /// </summary>
    private bool IsValidSoundSelection()
    {
        return _selectSoundId != -1 &&
               _dataConfig?.actorTriggers?.sounds != null &&
               _selectSoundId < _dataConfig.actorTriggers.sounds.Count;
    }
    #endregion

    #region 音效播放
    /// <summary>
    /// 播放音效事件
    /// </summary>
    public void PlaySoundEvents(string eventName)
    {
        if (_dataConfig?.actorTriggers?.sounds == null) return;

        foreach (var sound in _dataConfig.actorTriggers.sounds)
        {
            if (sound.soundEventName == eventName)
            {
                PlaySoundWithDelay(sound);
            }
        }
    }

    /// <summary>
    /// 带延迟播放音效
    /// </summary>
    private void PlaySoundWithDelay(SkillEditorPrefabDataConfig.TriggerSound sound)
    {
        if (sound.soundDelay > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(sound.soundDelay, () => PlaySound(sound)));
        }
        else
        {
            PlaySound(sound);
        }
    }

    /// <summary>
    /// 播放音效
    /// </summary>
    private void PlaySound(SkillEditorPrefabDataConfig.TriggerSound sound)
    {
        if (_soundCount >= SkillEditorGUIConfig.MAX_SOUND_COUNT) return; // 限制同时播放的音效数量
        if (_modelManager?.MainAnimator == null) return;

        ScriptablePool.Instance.Load(sound.soundAudioAsset, obj =>
        {
            if (obj == null)
            {
                Debug.LogWarning("音效资产为空");
                return;
            }

            if (obj is AudioItem item)
            {
                var audioPlayer = AudioManager.Play(item, _modelManager.MainAnimator.transform);
                _soundCount++;
                Scheduler.RunCoroutine(SoundCoroutine(audioPlayer.WaitFinish()));
            }
            else
            {
                Debug.LogWarning("无法转换音效资产");
            }
        });
    }

    /// <summary>
    /// 音效协程
    /// </summary>
    private IEnumerator SoundCoroutine(IEnumerator audioEnumerator)
    {
        yield return audioEnumerator;
        _soundCount--;
    }

    /// <summary>
    /// 播放击中音效（带权重随机选择）
    /// </summary>
    public void PlayHitSound(List<SkillEditorPrefabDataConfig.HitSoundData> hitSounds)
    {
        if (hitSounds == null || hitSounds.Count == 0) return;

        var selectedSound = SelectRandomHitSound(hitSounds);
        if (selectedSound != null && !selectedSound.soundAsset.IsEmpty)
        {
            PlayHitSoundAsset(selectedSound.soundAsset);
        }
    }

    /// <summary>
    /// 根据权重随机选择击中音效
    /// </summary>
    private SkillEditorPrefabDataConfig.HitSoundData SelectRandomHitSound(List<SkillEditorPrefabDataConfig.HitSoundData> hitSounds)
    {
        int totalWeight = CalculateTotalWeight(hitSounds);
        if (totalWeight == 0) return null;

        int randomValue = UnityEngine.Random.Range(0, totalWeight);
        int currentWeight = 0;

        foreach (var sound in hitSounds)
        {
            currentWeight += sound.weight;
            if (randomValue < currentWeight)
            {
                return sound;
            }
        }

        return null;
    }

    /// <summary>
    /// 计算总权重
    /// </summary>
    private int CalculateTotalWeight(List<SkillEditorPrefabDataConfig.HitSoundData> hitSounds)
    {
        int totalWeight = 0;
        foreach (var sound in hitSounds)
        {
            totalWeight += sound.weight;
        }
        return totalWeight;
    }

    /// <summary>
    /// 播放击中音效资产
    /// </summary>
    private void PlayHitSoundAsset(AssetID soundAsset)
    {
        if (_modelManager?.MainAnimator == null) return;

        ScriptablePool.Instance.Load(soundAsset, obj =>
        {
            if (obj is AudioItem item)
            {
                AudioManager.Play(item, _modelManager.MainAnimator.transform);
            }
        });
    }

    /// <summary>
    /// 播放单个音效资产
    /// </summary>
    public void PlaySoundAsset(AssetID soundAsset, Transform targetPos = null)
    {
        if (soundAsset.IsEmpty) return;

        Transform playPos = targetPos ?? _modelManager?.MainAnimator?.transform;
        if (playPos == null) return;

        ScriptablePool.Instance.Load(soundAsset, obj =>
        {
            if (obj is AudioItem item)
            {
                AudioManager.Play(item, playPos);
            }
        });
    }
    #endregion

    #region 击中音效配置绘制
    /// <summary>
    /// 绘制击中音效列表
    /// </summary>
    public void DrawHitSoundList(List<SkillEditorPrefabDataConfig.HitSoundData> hitSounds)
    {
        for (int i = 0; i < Mathf.Min(SkillEditorGUIConfig.MAX_HIT_SOUNDS, hitSounds.Count); i++)
        {
            EditorGUILayout.BeginVertical("box");

            var hitSound = hitSounds[i];
            hitSound.weight = EditorGUILayout.IntSlider("权重", hitSound.weight, 1, 100);

            EditorGUI.BeginChangeCheck();
            AudioItem soundItem = SkillEditorGUI.DrawAudioSelector(
                hitSound.soundGoName,
                hitSound.soundAsset.GetAssetPath(),
                $"音效{i + 1}");

            if (EditorGUI.EndChangeCheck())
            {
                hitSound.soundAsset = SkillEditorUtils.GetAssetID(soundItem);
                hitSound.soundGoName = soundItem?.name ?? "";
                hitSounds[i] = hitSound;
            }

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 绘制击中音效按钮
    /// </summary>
    public void DrawHitSoundButtons(List<SkillEditorPrefabDataConfig.HitSoundData> hitSounds)
    {
        EditorGUILayout.BeginHorizontal();

        if (hitSounds.Count < SkillEditorGUIConfig.MAX_HIT_SOUNDS && GUILayout.Button("+ 添加音效"))
        {
            hitSounds.Add(new SkillEditorPrefabDataConfig.HitSoundData());
        }

        if (hitSounds.Count > 0 && GUILayout.Button("- 删除音效"))
        {
            hitSounds.RemoveAt(hitSounds.Count - 1);
        }

        EditorGUILayout.EndHorizontal();
    }
    #endregion
}
