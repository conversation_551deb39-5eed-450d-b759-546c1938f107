﻿using System;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using Object = UnityEngine.Object;

class ActorLabelFixed
{

    [MenuItem("Assets/美术专用/添加IgnoreMaxSize标签")]
    public static void AddIgnoreMaxSizeLabel()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        ExecuteInPath(checkDirs);
                    }
                    else
                    {
                        Execute(null, path);
                    }

                    EditorUtility.DisplayProgressBar("正在处理..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    ExecuteInPath(checkDirs);
                }
                else
                {
                    Execute(null, path);
                }
            }
        }

        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        ExecuteInPath(checkDirs);
                    }
                    else
                    {
                        Execute(null, path);
                    }

                    EditorUtility.DisplayProgressBar("正在处理..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                Execute(Selection.activeGameObject);
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    private static void ExecuteInPath(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:texture", checkDirs);
        int totalCount = guids.Length;
        foreach (var guid in guids)
        {
            Execute(null, AssetDatabase.GUIDToAssetPath(guid));
        }
    }

    private static void Execute(Object selectObj, string selectObjPath = "")
    {
        if (selectObj == null && string.IsNullOrEmpty(selectObjPath))
            return;
        string path = string.IsNullOrEmpty(selectObjPath) ? AssetDatabase.GetAssetPath(selectObj) : selectObjPath;
        if (AssetDatabase.IsValidFolder(path))
            return;

        if (!path.EndsWith(".jpg") && !path.EndsWith(".png") && !path.EndsWith(".tga"))
            return;

        if (selectObj == null)
        {
            selectObj = AssetDatabase.LoadAssetAtPath(selectObjPath, typeof(Object)) as Object;
        }

        string[] oldLabels = AssetDatabase.GetLabels(new GUID(AssetDatabase.AssetPathToGUID(path)));
        AssetDatabase.SetLabels(selectObj, oldLabels != null && oldLabels.Length > 0
                                        ? oldLabels.Concat(new string[] { ImporterUtils.IgnoreMaxSize }).ToArray()
                                        : new string[] { ImporterUtils.IgnoreMaxSize });
    }

    private static void ShowWarning()
    {
        Debug.LogError("错误！请选择贴图文件进行操作！");
        //EditorUtility.DisplayDialog("注意", "请选择贴图（jpg,png,tga）文件", "ok");
    }

}