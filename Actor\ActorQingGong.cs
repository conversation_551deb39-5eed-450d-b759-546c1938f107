﻿using System.Collections;
using System;
using UnityEngine;
using Nirvana;

public class ActorQingGong : MonoBehaviour {
    public static GridFindWay gridFindWay;
    private static int origin_x;
    private static int origin_y;
    private static int width;
    private static int height;

    // 滑翔前进速度
    public float forwardSpeed = 0f;
    public float m_GravityMultiplier = 1f;
    public float turnSpeed = 1000f;
    public float drag = 0f;
    public bool autoJump = false;

    private Rigidbody m_Rigidbody;
    private Action<QingGongState> stateCallBack;
    private bool m_IsGrounded = true;
    private float m_GroundCheckDistance = 0.3f;
    private float m_WaitJumpTime = 0.35f;
    private float m_WaitJumpTimer = 0f;
    private bool m_WatrJumpStatus = false;
    private Action add_force_cb = null;
    public QingGongState curState = QingGongState.None;
    private Vector3 inputDir = Vector3.zero;
    private AnimationCurve verticalCurve;
    private AnimationCurve horizonCurve;
    private float stuckTimer = 0f;
    private float timer = 0f;
    private bool isForceLand = false;
    private Vector3 lastValidPos;
    private float downTimer = 0f;
    private float lastFallingSpeed = 0f;
    private bool readyToGround = false;
    private static Vector2 logicPos = Vector3.zero;
    private bool is_not_fly = false;
    public bool is_Mitsurugi = false;

    // Update is called once per frame
    void FixedUpdate() {
        //如果是御剑保持不变
        if (null == horizonCurve && null == verticalCurve && Math.Abs(M_Rigidbody.velocity.y) < 0.5f && curState != QingGongState.Mitsurugi)
        {
            stuckTimer += Time.fixedDeltaTime;
            if (stuckTimer >= 1f)
            {
                m_IsGrounded = true;
                if (null != stateCallBack)
                {
                    if (!readyToGround)
                    {
                        readyToGround = true;
                        stateCallBack(QingGongState.ReadyToGround);
                    }
                    stateCallBack(QingGongState.OnGround);
                }
                return;
            }
        }

        if (curState == QingGongState.Down)
        {
            if (M_Rigidbody.velocity.y > lastFallingSpeed)
            {
                m_IsGrounded = true;
                if (null != stateCallBack)
                {
                    if (!readyToGround)
                    {
                        readyToGround = true;
                        stateCallBack(QingGongState.ReadyToGround);
                    }
                    stateCallBack(QingGongState.OnGround);
                }
                return;
            }
            lastFallingSpeed = M_Rigidbody.velocity.y;

            if (!Physics.Raycast(transform.position - (Vector3.up * 2f), Vector3.down, 1000f) && curState != QingGongState.Mitsurugi)
            {
                downTimer += Time.fixedDeltaTime;
            }

            if (downTimer > 3f)
            {
                transform.position = lastValidPos;
                m_IsGrounded = true;
                if (null != stateCallBack)
                {
                    if (!readyToGround)
                    {
                        readyToGround = true;
                        stateCallBack(QingGongState.ReadyToGround);
                    }
                    stateCallBack(QingGongState.OnGround);
                }
                return;
            }
        }

        CheckGroundStatus();

        if (!m_IsGrounded || null != verticalCurve || null != horizonCurve)
        {
            if (m_WatrJumpStatus)
            {
                m_WaitJumpTimer += Time.fixedDeltaTime;

                if (m_WaitJumpTimer >= m_WaitJumpTime)
                {
                    m_WatrJumpStatus = false;

                    if (this.add_force_cb != null)
                    {
                        this.add_force_cb();
                        this.add_force_cb = null;
                    }
                }
                return;
            }

            UpdateGravity();
            UpdateRotation();
            UpdateForwardSpeed();
            UpdateVerticalSpeed();
            UpdateState();
        }

    }

    private void OnEnable()
    {
        inputDir = Vector3.zero;
        curState = QingGongState.None;
        stuckTimer = 0f;
        downTimer = 0f;
        lastFallingSpeed = 0f;
        isForceLand = false;
        readyToGround = false;
        autoJump = false;
        Animator animator = GetComponent<Animator>();
        if (null != animator)
        {
            animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
        }
    }

    private void OnDisable()
    {
        if (this.m_Rigidbody)
            Destroy(m_Rigidbody);
    }

    private void RemoveRigidbody()
    {
        if (this.m_Rigidbody)
        {
            Destroy(m_Rigidbody);
            m_Rigidbody = null;
        }
    }

    private Rigidbody M_Rigidbody
    {
        get
        {
            if (null == m_Rigidbody && !this.is_not_fly)
            {
                m_Rigidbody = transform.GetOrAddComponent<Rigidbody>();
                m_Rigidbody.constraints = RigidbodyConstraints.FreezeRotation;
                m_Rigidbody.useGravity = false;
                m_Rigidbody.interpolation = RigidbodyInterpolation.Interpolate;
                m_Rigidbody.drag = drag;
                //m_Rigidbody.isKinematic = true;

                Collider collider = transform.GetComponentInChildren<Collider>();
                if (null != collider)
                {
                    PhysicMaterial pMat = new PhysicMaterial();
                    pMat.frictionCombine = PhysicMaterialCombine.Multiply;
                    pMat.bounceCombine = PhysicMaterialCombine.Multiply;
                    pMat.dynamicFriction = 0f;
                    pMat.staticFriction = 0f;
                    pMat.bounciness = 0f;
                    collider.material = pMat;
                }

            }
            return m_Rigidbody;
        }
    }

    public float Drag
    {
        set
        {
            if (drag != value)
            {
                drag = value;
                if (m_Rigidbody)
                    m_Rigidbody.drag = value;
            }
        }
        get
        {
            return drag;
        }
    }

    public void AddJumpForce(ActorQingGongObject qinggongObject, Action action, float progress = 0)
    {
        timer = Time.fixedTime - (qinggongObject.Time * progress + m_WaitJumpTime);
        stuckTimer = 0f;
        lastFallingSpeed = 0f;
        readyToGround = false;
        m_WaitJumpTimer = 0f;
        m_WatrJumpStatus = true;
        this.add_force_cb = action;

        if (qinggongObject.EnableVerticalCurve)
        {
            verticalCurve = qinggongObject.VerticalCurve;
        }
        else
        {
            verticalCurve = null;
        }
        if (qinggongObject.EnableHorizonCurve)
        {
            inputDir = Vector3.zero;
            horizonCurve = qinggongObject.HorizonCurve;
        }
        else
        {
            horizonCurve = null;
        }

        curState = QingGongState.None;
        if (IsValidArea(transform.position))
        {
            lastValidPos = transform.position;
        }
    }

    public void ForceLanding()
    {
        if (!isForceLand)
        {
            isForceLand = true;
            verticalCurve = null;
            horizonCurve = null;
            float y = M_Rigidbody.velocity.y > -10f ? -10f : M_Rigidbody.velocity.y;
            M_Rigidbody.velocity = new Vector3(0f, y, 0f);
        }

    }

    public void StopQinggong()
    {
        verticalCurve = null;
        horizonCurve = null;
        m_IsGrounded = true;
        curState = QingGongState.None;
        if (this.m_Rigidbody)
        {
            M_Rigidbody.velocity = new Vector3(0f, 0f, 0f);
        }

        Debug.LogError($"设置位置{lastValidPos}");
        transform.position = lastValidPos;
        if (null != stateCallBack)
        {
            stateCallBack(QingGongState.Stop);
        }
    }

    //停止御剑，返回轻功即可
    public void StopMitsurugi()
    {
        if (curState != QingGongState.Mitsurugi)
            return;

        curState = QingGongState.Down;
        if (null != stateCallBack)
        {
            stateCallBack(QingGongState.Down);
        }
        this.is_Mitsurugi = false;
    }

    // 调整滑翔时的运动方向
    public void AdjustMoveMent(float fx, float fy)
    {
        if (isForceLand)
            return;

        Vector3 direction = new Vector3(fx, 0f, fy);

        if (direction.sqrMagnitude > 1f)
            direction.Normalize();

        inputDir = direction;
    }

    public void SetStateChangeCallBack(Action<QingGongState> stateCallBack)
    {
        this.stateCallBack = stateCallBack;
    }

    Vector3 extraGravityForce = Vector3.zero;
    private void UpdateGravity()
    {
        if (null != verticalCurve || curState == QingGongState.Mitsurugi)
        {
            return;
        }

        extraGravityForce = Physics.gravity * m_GravityMultiplier;
        if (isForceLand)
            extraGravityForce *= 4f;

        M_Rigidbody.AddForce(extraGravityForce);
    }

    QingGongState oldState = QingGongState.None;
    private void UpdateState()
    {
        oldState = curState;

        if (M_Rigidbody.velocity.y > 0f)
        {
            curState = QingGongState.Up;
        }
        else if (M_Rigidbody.velocity.y < 0f)
        {
            if (is_Mitsurugi)
            {
                RemoveRigidbody();
                curState = QingGongState.Mitsurugi;
                if (null != stateCallBack)
                {
                    stateCallBack(curState);
                }
                stateCallBack(QingGongState.Mitsurugi);
            }
            else
                curState = QingGongState.Down;
        }

        if (oldState != curState)
        {
            if (null != stateCallBack)
            {
                stateCallBack(curState);
            }

            if (curState == QingGongState.Down)
            {
                downTimer = 0f;
            }
        }
    }

    Vector3 dirInUpdateRotation = Vector3.zero;
    float turnAmount = 0f;
    private void UpdateRotation()
    {
        if (isForceLand)
            return;

        if (inputDir.sqrMagnitude > 0.01f)
        {
            dirInUpdateRotation = transform.InverseTransformDirection(inputDir);
            turnAmount = Mathf.Atan2(dirInUpdateRotation.x, dirInUpdateRotation.z);
            transform.Rotate(0, turnAmount * turnSpeed * Time.deltaTime, 0);
        }
    }

    Vector3 velocity = Vector3.zero;
    Vector3 moveMent = Vector3.zero;
    Vector3 dirInUpdateForwardSpeed = Vector3.zero;
    float magnitude = 0f;
    float distanceInUpdateForwardSpeed = 0f;
    bool isBorder = false;
    AnimationCurve horizonCurveInUpdateForwardSpeed = null;
    float timeInUpdateForwardSpeed = 0f;
    int lengthInUpdateForwardSpeed = 0;
    Vector3 velocityInUpdateForwardSpeed = Vector3.zero;
    float forwardSpeedInUpdateForwardSpeed = 0f;
    bool curPositionIsValid = false;
    bool nextPositionIsValid = false;
    private void UpdateForwardSpeed()
    {
        if (isForceLand || curState == QingGongState.Mitsurugi)
            return;

        //if (curState == QingGongState.Down)
        //{
        //    velocity = transform.forward * forwardSpeed;
        //    M_Rigidbody.velocity = new Vector3(velocity.x, M_Rigidbody.velocity.y, velocity.z);
        //}

        // 预先计算出下一次位移是否会到达边界
        moveMent = Time.fixedDeltaTime * M_Rigidbody.velocity;
        dirInUpdateForwardSpeed = M_Rigidbody.velocity.normalized;
        magnitude = moveMent.sqrMagnitude;
        distanceInUpdateForwardSpeed = 0f;
        isBorder = false;
        while (distanceInUpdateForwardSpeed <= magnitude)
        {
            distanceInUpdateForwardSpeed = Mathf.Min(magnitude, distanceInUpdateForwardSpeed);
            isBorder = IsBorder(transform.position + distanceInUpdateForwardSpeed * dirInUpdateForwardSpeed);
            distanceInUpdateForwardSpeed += 0.5f;
            if (isBorder)
                break;
        }
        if (isBorder && !autoJump)
        {
            M_Rigidbody.velocity = new Vector3(0f, M_Rigidbody.velocity.y, 0f);
        }

        if ((autoJump ||!isBorder) && curState != QingGongState.Down && null != horizonCurve)
        {
            horizonCurveInUpdateForwardSpeed = horizonCurve;
            timeInUpdateForwardSpeed = Time.fixedTime - timer;
            lengthInUpdateForwardSpeed = horizonCurveInUpdateForwardSpeed.length;
            if (timeInUpdateForwardSpeed >= horizonCurveInUpdateForwardSpeed.keys[lengthInUpdateForwardSpeed - 1].time)
            {
                timeInUpdateForwardSpeed = horizonCurveInUpdateForwardSpeed.keys[lengthInUpdateForwardSpeed - 1].time;
                horizonCurve = null;
            }
            forwardSpeedInUpdateForwardSpeed = horizonCurveInUpdateForwardSpeed.Evaluate(timeInUpdateForwardSpeed);
            velocityInUpdateForwardSpeed = transform.forward * forwardSpeedInUpdateForwardSpeed;
            M_Rigidbody.velocity = new Vector3(velocityInUpdateForwardSpeed.x, M_Rigidbody.velocity.y, velocityInUpdateForwardSpeed.z);
        }

        if (autoJump)
        {
            curPositionIsValid = IsValidArea(transform.position);
            nextPositionIsValid = IsValidArea(transform.position + moveMent);
            if (curPositionIsValid && !nextPositionIsValid)
            {
                StopForwardMove();
                autoJump = false;
            }
        }
    }

    private AnimationCurve verticalCurveInUpdateVerticalSpeed = null;
    float timeInUpdateVerticalSpeed = 0f;
    int lengthInUpdateVerticalSpeed = 0;
    float speedInUpdateVerticalSpeed = 0f;
    private void UpdateVerticalSpeed()
    {
        if (curState == QingGongState.Mitsurugi)
            return;

        if (null != verticalCurve)
        {
            verticalCurveInUpdateVerticalSpeed = verticalCurve;
            timeInUpdateVerticalSpeed = Time.fixedTime - timer;
            lengthInUpdateVerticalSpeed = verticalCurveInUpdateVerticalSpeed.length;
            if (timeInUpdateVerticalSpeed >= verticalCurveInUpdateVerticalSpeed.keys[lengthInUpdateVerticalSpeed - 1].time)
            {
                timeInUpdateVerticalSpeed = verticalCurveInUpdateVerticalSpeed.keys[lengthInUpdateVerticalSpeed - 1].time;
                verticalCurve = null;
            }
            speedInUpdateVerticalSpeed = verticalCurveInUpdateVerticalSpeed.Evaluate(timeInUpdateVerticalSpeed);
            M_Rigidbody.velocity = new Vector3(M_Rigidbody.velocity.x, speedInUpdateVerticalSpeed, M_Rigidbody.velocity.z);
        }
    }

    float distance = 0;
    RaycastHit hitInfo;
    private void CheckGroundStatus()
    {
        distance = readyToGround ? m_GroundCheckDistance : 3f;
        if (curState != QingGongState.Up && Physics.Raycast(transform.position + (Vector3.up * 0.1f), Vector3.down, out hitInfo, distance,
            ~(1 << GameLayers.Clickable)))
        {
            if (!readyToGround && null == verticalCurve && curState != QingGongState.Mitsurugi)
            {
                readyToGround = true;
                if (null != stateCallBack)
                {
                    stateCallBack(QingGongState.ReadyToGround);
                }
            }
        }
        else
        {
            m_IsGrounded = false;
        }
    }

    public static void SetLogicMap(int _origin_x, int _origin_y, int _width, int _height)
    {
        origin_x = _origin_x;
        origin_y = _origin_y;
        width = _width;
        height = _height;
    }

    private static float logicX = 0f;
    private static float logicY = 0f;
    private static Vector2 WorldToLogic(float x, float y)
    {
        logicX = (x - origin_x) / width;
        logicY = (y - origin_y) / height;
        return new Vector2(logicX, logicY);
    }

    private static bool IsBorder(Vector3 position)
    {
        logicPos = WorldToLogic(position.x, position.z);
        if (null != gridFindWay)
        {
            return gridFindWay.IsBorder((int)logicPos.x, (int)logicPos.y);
        }
        return false;
    }

    public static bool IsValidArea(Vector3 position)
    {
        logicPos = WorldToLogic(position.x, position.z);
        if (null != gridFindWay)
        {
            return !gridFindWay.IsBlock((int)logicPos.x, (int)logicPos.y);
        }
        return false;
    }

    public static bool IsTunnelArea(Vector3 position)
    {
        logicPos = WorldToLogic(position.x, position.z);
        if (null != gridFindWay)
        {
            return gridFindWay.IsTunnelArea((int)logicPos.x, (int)logicPos.y);
        }
        return false;
    }

    public void StopForwardMove()
    {
        horizonCurve = null;
        forwardSpeed = 0f;
        M_Rigidbody.velocity = new Vector3(0f, M_Rigidbody.velocity.y, 0f);
    }
}

public enum QingGongState
{
    None, Up, Down, ReadyToGround, Mitsurugi, OnGround, Stop
}
