﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Game;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using System.IO;
using Newtonsoft.Json;
using Nirvana;

public static class ComponentCleaner
{
    public static void ClearComponent()
    {
        FixAllRoleSkillAbName();
        ClearGameObjectAttach();
        AssetDatabase.SaveAssets();
    }

    private static void ClearGameObjectAttach()
    {
        string[] guids = AssetDatabase.FindAssets("t:scene", new string[] { "Assets/Game/Scenes/Map" });
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.EndsWith("_Main.unity") || path.EndsWith("_Detail.unity"))
            {
                Scene scene = EditorSceneManager.OpenScene(path);
                GameObject[] roots = scene.GetRootGameObjects();
                for (int i = 0; i < roots.Length; i++)
                {
                    GameObjectAttach[] attchs = roots[i].GetComponentsInChildren<GameObjectAttach>(true);
                    for (int m = 0; m < attchs.Length; m++)
                    {
                        attchs[m].RefreshAssetBundleName();
                    }
                }

                EditorSceneManager.MarkSceneDirty(scene);
                EditorSceneManager.SaveScene(scene);
                EditorSceneManager.CloseScene(scene, true);
            }
        }

        guids = AssetDatabase.FindAssets("t:prefab", new string[] { "Assets/Game" });
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            GameObjectAttach[] attchs = prefab.GetComponentsInChildren<GameObjectAttach>(true);
            bool isDirty = false;
            for (int i = 0; i < attchs.Length; i++)
            {
                if (attchs[i].IsGameobjectMissing())
                {
                    isDirty = true;
                    attchs[i].RefreshAssetBundleName();
                }
            }

            if (isDirty)
            {
                PrefabUtility.ResetToPrefabState(prefab);
                PrefabUtility.SetPropertyModifications(prefab, new PropertyModification[] { });
            }
        }
    }

    private static void FixAllRoleSkillAbName()
    {
        string[] jsonList = new string[]
        {
            "Game/Lua/config/prefab_data/json/Role/1201001_config.json",
            "Game/Lua/config/prefab_data/json/Role/1203001_config.json",
        };

        for (int i = 0; i < jsonList.Length; i++)
        {
            string path = Path.Combine(Application.dataPath, jsonList[i]);
            FixAssetPathInJsonFile(path);
        }
    }

    private static void FixAssetPathInJsonFile(string jsonPath)
    {
      
        if (!File.Exists(jsonPath))
        {
            Debug.LogErrorFormat("找不到路径 {0}", jsonPath);
            return;
        }

        var jsonContents = File.ReadAllText(jsonPath);
        var _dataConfig = JsonConvert.DeserializeObject<SkillEditorPrefabDataConfig>(jsonContents);

        List<SkillEditorPrefabDataConfig.ProjectileData> projectiles = _dataConfig.actorController.projectiles;
        for (int i = 0; i < projectiles.Count; i++)
        {
            projectiles[i].Projectile = GetNewAssetID(projectiles[i].Projectile);
        }

        List<SkillEditorPrefabDataConfig.HurtData> hurts = _dataConfig.actorController.hurts;
        for (int i = 0; i < hurts.Count; i++)
        {
            hurts[i].HurtEffect = GetNewAssetID(hurts[i].HurtEffect);
            hurts[i].HitEffect = GetNewAssetID(hurts[i].HitEffect);
        }

        List<SkillEditorPrefabDataConfig.TriggerEffect> triggerEffects = _dataConfig.actorTriggers.effects;
        for (int i = 0; i < triggerEffects.Count; i++)
        {
            triggerEffects[i].effectAsset = GetNewAssetID(triggerEffects[i].effectAsset);
        }

        string jsonstr = JsonConvert.SerializeObject(_dataConfig);

        var luaConfig = JsonToLua.Convert(jsonstr).Replace("[none]", string.Empty);
        string luatPath = jsonPath.Replace("/json/", "/config/").Replace(".json", ".lua");
        File.WriteAllText(luatPath, luaConfig);
        File.WriteAllText(jsonPath, jsonstr);
    }

    private static AssetID GetNewAssetID(AssetID assetID)
    {
        AssetID newAssetID = assetID;
        string assetPath = AssetDatabase.GUIDToAssetPath(newAssetID.AssetGUID);
        var importer = AssetImporter.GetAtPath(assetPath);
        if (null != importer)
        {
            newAssetID.BundleName = importer.assetBundleName;
            newAssetID.AssetName = assetPath.Substring(assetPath.LastIndexOf("/") + 1).Replace(".prefab", "");
        }
        return newAssetID;
    }
}
