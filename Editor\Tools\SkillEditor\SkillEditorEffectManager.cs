using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 特效管理器
/// 负责特效的配置、播放和管理
/// </summary>
public class SkillEditorEffectManager
{
    #region 私有字段
    private SkillEditorPrefabDataConfig _dataConfig;
    private SkillEditorModelManager _modelManager;
    private int _selectEffectId = -1;
    private int _currentEffectArrayIndex = 0;
    private Vector2 _effectListScroll;
    private LinkedList<EffectControl> _runningEffects = new LinkedList<EffectControl>();
    #endregion

    #region 属性
    public int SelectedEffectId => _selectEffectId;
    public int CurrentEffectArrayIndex => _currentEffectArrayIndex;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化特效管理器
    /// </summary>
    public void Initialize(SkillEditorPrefabDataConfig dataConfig, SkillEditorModelManager modelManager)
    {
        _dataConfig = dataConfig;
        _modelManager = modelManager;
    }

    /// <summary>
    /// 设置当前特效数组索引
    /// </summary>
    public void SetCurrentEffectArrayIndex(int index)
    {
        _currentEffectArrayIndex = Mathf.Clamp(index, 0, SkillEditorGUIConfig.EFFECT_ARRAY_SIZE - 1);
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制特效管理界面
    /// </summary>
    public void DrawEffectsWindow()
    {
        GUILayout.BeginVertical();
        DrawAddEffectButton();
        DrawEffectList();
        GUILayout.EndVertical();

        DrawDeleteEffectButton();
    }

    /// <summary>
    /// 绘制添加特效按钮
    /// </summary>
    private void DrawAddEffectButton()
    {
        if (GUILayout.Button("增加特效", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewEffect();
        }
    }

    /// <summary>
    /// 绘制特效列表
    /// </summary>
    private void DrawEffectList()
    {
        if (_dataConfig?.actorTriggers?.effects == null) return;

        _effectListScroll = SkillEditorGUI.DrawScrollList(
            _effectListScroll,
            _dataConfig.actorTriggers.effects,
            effect => SkillEditorGUIConfig.GetDefaultButtonName(effect.effectBtnName),
            OnEffectSelected);
    }

    /// <summary>
    /// 绘制删除特效按钮
    /// </summary>
    private void DrawDeleteEffectButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect1);

        if (_dataConfig?.actorTriggers?.effects?.Count > 0)
        {
            if (GUILayout.Button("删除特效", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedEffect();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制特效详细配置
    /// </summary>
    public void DrawEffectDetails()
    {
        if (!IsValidEffectSelection()) return;

        var effect = _dataConfig.actorTriggers.effects[_selectEffectId];

        DrawEffectBasicSettings(effect);
        DrawEffectAssetSettings(effect);
        DrawEffectAttachmentSettings(effect);
        DrawEffectTransformSettings(effect);
        DrawEffectAdvancedSettings(effect);

        _dataConfig.actorTriggers.effects[_selectEffectId] = effect;
        SkillEditorGUI.DrawSeparator();
    }
    #endregion

    #region 特效配置绘制
    /// <summary>
    /// 绘制特效基础设置
    /// </summary>
    private void DrawEffectBasicSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        effect.triggerEventName = SkillEditorGUI.DrawEventSelector(effect.triggerEventName, "触发事件");
        effect.effectBtnName = SkillEditorGUI.DrawButtonNameField(effect.effectBtnName);
        effect.triggerDelay = SkillEditorGUI.DrawDelayField("延迟时间", effect.triggerDelay);
        effect.triggerFreeDelay = SkillEditorGUI.DrawDelayField("延迟删除特效", effect.triggerFreeDelay);
    }

    /// <summary>
    /// 绘制特效资产设置
    /// </summary>
    private void DrawEffectAssetSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        if (_modelManager.ModelType == (int)SkillEditorEventConfig.ModelType.Role)
        {
            DrawEffectArraySettings(effect);
        }
        else
        {
            DrawSingleEffectSettings(effect);
        }
    }

    /// <summary>
    /// 绘制特效数组设置（角色类型）
    /// </summary>
    private void DrawEffectArraySettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        SkillEditorGUI.DrawTitle("特效数组配置:");

        for (int i = 0; i < SkillEditorGUIConfig.EFFECT_ARRAY_SIZE; i++)
        {
            EditorGUILayout.BeginVertical("box");
            GUILayout.Label($"特效 {i + 1}:", EditorStyles.label);

            EditorGUI.BeginChangeCheck();
            EffectControl effectGo = SkillEditorGUI.DrawEffectSelector(
                effect.effectArray[i].effectGoName,
                effect.effectArray[i].effectAsset.GetAssetPath(),
                $"特效{i + 1}");

            if (EditorGUI.EndChangeCheck())
            {
                effect.effectArray[i].effectAsset = SkillEditorUtils.GetAssetID(effectGo);
                effect.effectArray[i].effectGoName = effectGo?.name ?? "";
            }

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 绘制单个特效设置（非角色类型）
    /// </summary>
    private void DrawSingleEffectSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        EditorGUI.BeginChangeCheck();
        EffectControl effectGo = SkillEditorGUI.DrawEffectSelector(
            effect.effectGoName, 
            effect.effectAsset.GetAssetPath());

        if (EditorGUI.EndChangeCheck())
        {
            effect.effectAsset = SkillEditorUtils.GetAssetID(effectGo);
            effect.effectGoName = effectGo?.name ?? "";
        }
    }

    /// <summary>
    /// 绘制特效附着设置
    /// </summary>
    private void DrawEffectAttachmentSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        if (!effect.isAttach)
        {
            effect.playerAtTarget = EditorGUILayout.Toggle("是否附着在目标物身上", effect.playerAtTarget);
        }

        if (!effect.playerAtTarget)
        {
            effect.isRotation = EditorGUILayout.Toggle("是否同步释放者旋转角度", effect.isRotation);
            effect.isAttach = EditorGUILayout.Toggle("是否附着在释放者身上", effect.isAttach);

            if (effect.isAttach)
            {
                DrawAttachmentNodeSelector(effect);
            }
            else
            {
                effect.referenceNodeHierarchyPath = string.Empty;
            }
        }
    }

    /// <summary>
    /// 绘制附着节点选择器
    /// </summary>
    private void DrawAttachmentNodeSelector(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        Transform node = SkillEditorGUI.DrawNodeSelector(
            "附着节点", 
            effect.referenceNodeHierarchyPath, 
            _modelManager.MainAnimator?.transform);
        
        effect.referenceNodeHierarchyPath = SkillEditorGUI.UpdateNodePath(
            node, 
            _modelManager.MainAnimator?.transform);
    }

    /// <summary>
    /// 绘制特效变换设置
    /// </summary>
    private void DrawEffectTransformSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        effect.ignoreParentScale = EditorGUILayout.Toggle("是否忽略父节点缩放", effect.ignoreParentScale);
        effect.isUseCustomTransform = EditorGUILayout.Toggle("坐标偏移", effect.isUseCustomTransform);

        if (effect.isUseCustomTransform)
        {
            effect.offsetPosX = EditorGUILayout.FloatField("X轴偏移", effect.offsetPosX);
            effect.offsetPosY = EditorGUILayout.FloatField("Y轴偏移", effect.offsetPosY);
            effect.offsetPosZ = EditorGUILayout.FloatField("Z轴偏移", effect.offsetPosZ);
        }
    }

    /// <summary>
    /// 绘制特效高级设置
    /// </summary>
    private void DrawEffectAdvancedSettings(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        effect.triggerStopEvent = SkillEditorGUI.DrawEventSelector(effect.triggerStopEvent, "停止事件");
        effect.playerAtPos = EditorGUILayout.Toggle("是否在攻击的目标点创建特效", effect.playerAtPos);
    }
    #endregion

    #region 特效操作
    /// <summary>
    /// 添加新特效
    /// </summary>
    private void AddNewEffect()
    {
        if (_dataConfig?.actorTriggers?.effects == null) return;

        var effect = new SkillEditorPrefabDataConfig.TriggerEffect();
        InitializeEffectArray(effect);
        _dataConfig.actorTriggers.effects.Add(effect);
    }

    /// <summary>
    /// 初始化特效数组
    /// </summary>
    private void InitializeEffectArray(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        effect.effectArray = new SkillEditorPrefabDataConfig.EffectArrayItem[SkillEditorGUIConfig.EFFECT_ARRAY_SIZE];
        for (int i = 0; i < SkillEditorGUIConfig.EFFECT_ARRAY_SIZE; i++)
        {
            effect.effectArray[i] = new SkillEditorPrefabDataConfig.EffectArrayItem();
        }
    }

    /// <summary>
    /// 删除选中的特效
    /// </summary>
    private void DeleteSelectedEffect()
    {
        if (_selectEffectId == -1 || _dataConfig.actorTriggers.effects.Count == 0)
            return;

        _dataConfig.actorTriggers.effects.RemoveAt(_selectEffectId);
        _selectEffectId = _dataConfig.actorTriggers.effects.Count == 0
            ? -1
            : _dataConfig.actorTriggers.effects.Count - 1;
    }

    /// <summary>
    /// 特效选择回调
    /// </summary>
    private void OnEffectSelected(int index)
    {
        _selectEffectId = index;
    }

    /// <summary>
    /// 验证特效选择是否有效
    /// </summary>
    private bool IsValidEffectSelection()
    {
        return _selectEffectId != -1 &&
               _dataConfig?.actorTriggers?.effects != null &&
               _selectEffectId < _dataConfig.actorTriggers.effects.Count;
    }
    #endregion

    #region 特效播放
    /// <summary>
    /// 播放特效事件
    /// </summary>
    public void PlayEffectEvents(string eventName)
    {
        if (_dataConfig?.actorTriggers?.effects == null) return;

        foreach (var effect in _dataConfig.actorTriggers.effects)
        {
            if (effect.triggerEventName == eventName)
            {
                PlayEffectWithDelay(effect);
            }

            if (effect.triggerStopEvent == eventName)
            {
                StopAllEffects();
            }
        }
    }

    /// <summary>
    /// 带延迟播放特效
    /// </summary>
    private void PlayEffectWithDelay(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        if (effect.triggerDelay > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(effect.triggerDelay, () => PlayEffect(effect)));
        }
        else
        {
            PlayEffect(effect);
        }
    }

    /// <summary>
    /// 播放特效
    /// </summary>
    private void PlayEffect(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        if (_modelManager?.MainAnimator == null) return;

        var effectData = GetCurrentEffectData(effect);
        if (effectData.asset.IsEmpty) return;

        Scheduler.RunCoroutine(SpawnAndPlayEffect(effectData.asset, effect));
    }

    /// <summary>
    /// 获取当前特效数据
    /// </summary>
    private (AssetID asset, string name) GetCurrentEffectData(SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        if (_modelManager.ModelType == (int)SkillEditorEventConfig.ModelType.Role)
        {
            int index = IsValidEffectArrayIndex() ? _currentEffectArrayIndex : 0;
            return (effect.effectArray[index].effectAsset, effect.effectArray[index].effectGoName);
        }
        else
        {
            return (effect.effectAsset, effect.effectGoName);
        }
    }

    /// <summary>
    /// 验证特效数组索引是否有效
    /// </summary>
    private bool IsValidEffectArrayIndex()
    {
        return _currentEffectArrayIndex >= 0 && _currentEffectArrayIndex < SkillEditorGUIConfig.EFFECT_ARRAY_SIZE;
    }

    /// <summary>
    /// 生成并播放特效
    /// </summary>
    private IEnumerator SpawnAndPlayEffect(AssetID effectAsset, SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        var wait = GameObjectPool.Instance.SpawnAsset(effectAsset);
        yield return wait;

        if (wait?.Instance == null || wait.Error != null)
        {
            if (!string.IsNullOrEmpty(wait?.Error))
                Debug.LogError($"特效生成错误: {wait.Error}");
            yield break;
        }

        var effectCtrl = wait.Instance.GetComponent<EffectControl>();
        if (effectCtrl == null)
        {
            Debug.LogError("EffectControl为空,请检查");
            GameObjectPool.Instance.Free(wait.Instance);
            yield break;
        }

        SetupAndPlayEffect(effectCtrl, effect);
    }

    /// <summary>
    /// 设置并播放特效
    /// </summary>
    private void SetupAndPlayEffect(EffectControl effectCtrl, SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        effectCtrl.gameObject.SetLayerRecursively(_modelManager.MainAnimator.gameObject.layer);
        
        // 设置特效变换
        SetupEffectTransform(effectCtrl, effect);
        
        // 注册特效事件
        RegisterEffectEvents(effectCtrl, effect);
        
        // 播放特效
        effectCtrl.Reset();
        effectCtrl.Play();
    }

    /// <summary>
    /// 设置特效变换
    /// </summary>
    private void SetupEffectTransform(EffectControl effectCtrl, SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        Transform reference = _modelManager.MainAnimator.transform;
        Transform attachNode = SkillEditorUtils.FindTransformFromPath(reference, effect.referenceNodeHierarchyPath);
        Transform targetTransform = attachNode ?? reference;

        if (effect.isAttach)
        {
            effectCtrl.transform.SetParent(targetTransform);
            effectCtrl.transform.localPosition = Vector3.zero;
            effectCtrl.transform.localRotation = Quaternion.identity;
        }
        else
        {
            effectCtrl.transform.SetPositionAndRotation(targetTransform.position, targetTransform.rotation);
        }

        // 应用自定义偏移
        if (effect.isUseCustomTransform)
        {
            Vector3 offset = new Vector3(effect.offsetPosX, effect.offsetPosY, effect.offsetPosZ);
            effectCtrl.transform.localPosition += offset;
        }
    }

    /// <summary>
    /// 注册特效事件
    /// </summary>
    private void RegisterEffectEvents(EffectControl effectCtrl, SkillEditorPrefabDataConfig.TriggerEffect effect)
    {
        var effectNode = _runningEffects.AddLast(effectCtrl);

        System.Action onFinish = () =>
        {
            _runningEffects.Remove(effectNode);
            Scheduler.RunCoroutine(SkillEditorUtils.FreeGameObjectCoroutine(effectCtrl.gameObject, effect.triggerFreeDelay));
        };

        effectCtrl.FinishEvent += onFinish;
        effectCtrl.WaitFinsh(onFinish);
    }

    /// <summary>
    /// 停止所有特效
    /// </summary>
    public void StopAllEffects()
    {
        foreach (var effect in _runningEffects)
        {
            effect?.Stop();
        }
    }
    #endregion
}
