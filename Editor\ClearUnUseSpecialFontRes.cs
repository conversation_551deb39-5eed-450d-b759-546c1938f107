﻿using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using System.Collections.Generic;
using Nirvana.Editor;
using System.Text;

public class ClearUnUseSpecialFontRes
{
    static string FontDir = AssetBundleMarkRule.BaseDir + "/UIs/Fonts";
    static string ViewDir = AssetBundleMarkRule.BaseDir + "/UIs/View";

    [MenuItem("自定义工具/UI类/清理无用艺术字体资源")]
    static void Execute()
    {
        int curIndex = 0;
        int totalIndex = 0;

        Dictionary<string, string> fontNameList = new Dictionary<string, string>();
        string[] funtGuids = AssetDatabase.FindAssets("t:fontmaker", new string[] { FontDir });
        string[] fontsPath = Directory.GetFiles(FontDir, "*.asset", SearchOption.AllDirectories);
        totalIndex = funtGuids.Length;
        foreach (string guid in funtGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            FontMaker fontMaker = AssetDatabase.LoadAssetAtPath<FontMaker>(path);
            if (fontNameList.ContainsKey(fontMaker.atlasName))
            {
                Debug.LogErrorFormat("path:{0} ,fontName:{1} 同名", path, fontMaker.atlasName);
                curIndex++;
                EditorUtility.DisplayProgressBar("find fontmakers", string.Format("{0} / {1}", curIndex, totalIndex), (float)curIndex / (float)totalIndex);
                continue;
            }
            fontNameList.Add(fontMaker.atlasName, guid);
            curIndex++;
            EditorUtility.DisplayProgressBar("find fontmakers", string.Format("{0} / {1}", curIndex, totalIndex), (float)curIndex / (float)totalIndex);
        }

        string[] assetsPath = Directory.GetFiles(ViewDir, "*.prefab", SearchOption.AllDirectories);
        curIndex = 0;
        totalIndex = assetsPath.Length;
        foreach (string asset in assetsPath)
        {
            var real_path = asset.Replace('\\', '/');
            GameObject assetObj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
            Text[] texts = assetObj.GetComponentsInChildren<Text>(true);
            if (texts.Length > 0)
            {
                for (int i = 0; i < texts.Length; i++)
                {
                    if (texts[i].font != null && texts[i].font.name != "hwkt" && texts[i].font.name != "simhei")
                    {
                        if (fontNameList.ContainsKey(texts[i].font.name))
                        {
                            fontNameList.Remove(texts[i].font.name);
                        }
                    }
                }
            }
            curIndex++;
            EditorUtility.DisplayProgressBar("find view funt component", string.Format("{0} / {1}", curIndex, totalIndex), (float)curIndex / (float)totalIndex);
        }

        StringBuilder sb = new StringBuilder();
        sb.AppendLine("---------------- 自动删除的无用字体资源 ----------------");
        foreach (KeyValuePair<string, string> keyPair in fontNameList)
        {
            sb.AppendLine(keyPair.Key.ToString());
        }
        File.WriteAllText("D:/output.txt", sb.ToString());
        Debug.LogError("删除的字体文件已输出到D:/output.txt");

        curIndex = 0;
        totalIndex = fontNameList.Count;
        foreach (KeyValuePair<string, string> keyPair in fontNameList)
        {
            string guid = keyPair.Value;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            DirectoryInfo parent = Directory.GetParent(assetPath);
            if (Directory.Exists(parent.FullName))
            {
                string path = parent.FullName.Replace("\\", "/");
                int start = path.IndexOf("Assets/");
                path = path.Substring(start, path.Length - start);
                AssetDatabase.DeleteAsset(path);
            }
            curIndex++;
            EditorUtility.DisplayProgressBar("delete unuse asset", string.Format("{0} / {1}", curIndex, totalIndex), (float)curIndex / (float)totalIndex);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }
}