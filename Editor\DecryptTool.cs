﻿using UnityEditor;
using System.IO;
using UnityEngine;

class DecryptTool
{
    [MenuItem("Nirvana/Yifan/Decrypt")]
    public static void DoLuaLibDecrypt()
    {
        string[] libFiles = Directory.GetFiles("Assets/Game/Lua/lib", "*.lua", SearchOption.AllDirectories);
        for (int i = 0; i < libFiles.Length; i++)
        {
            string path = libFiles[i];
            byte[] strs = File.ReadAllBytes(path);
            strs = Encrypter.Decrypt(strs);
            File.WriteAllBytes(path, strs);
        }
    }
}