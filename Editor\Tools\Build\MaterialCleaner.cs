﻿using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class MaterialCleaner
{
    // YifStadndard用到的所有keyword， 请决对保持与YifSandard.shader中定义的keyword数量和顺序一致
    private static string[] yifKeywords = new string[]
    {
        "ENABLE_DIFFUSE",
        "ENABLE_PBR",
        "ENABLE_NORMAL_TEX",
        "ENABLE_AMBIENCE",
        "ENABLE_RIM",
        "ENABLE_RIM_LIGHT",
        "ENABLE_SPECULAR",
        "ENABLE_FLOW_ADD",
        "ENABLE_FLOW_MUL",
        "ENABLE_FLOW_MASK",
        "ENABLE_ALPHA_TEST",
        "ENABLE_ALPHA_BLEND",
        "ENABLE_UV_NOISE",
        "ENABLE_UV_TRANSFORM",
        "ENABLE_UV_SPEED",
        "ENABLE_DECAL_ALL",
        "ENABLE_DECAL_ALPHA",
        "ENABLE_DISSOLVE_AMOUNT",
        "ENABLE_DISSOLVE_VERTEX",
        "ENABLE_PARTICLE_UV_ANIMATION",
        "ENABLE_GLOW",
        "ENABLE_FOG",
        "FOG_LINEAR",
        "ENABLE_CLIP_RECT",
        "ENABLE_MIX_TEXTURE2",
        "ENABLE_MIX_TEXTURE3",
        "ENABLE_MIX_TEXTURE4",
        "ENABLE_MIX_NOISE",
        "LIGHTMAP_ON",
        "SHADOWS_DEPTH",
        "SHADOWS_SCREEN",
        "ENABLE_VERT_ANIM_GRASS_WAVE",
        "ENABLE_VERT_COLOR",
        "ENABLE_MAIN_TEX_USE_CHANNEL_R",
        "ENABLE_MAIN_TEX_USE_CHANNEL_G",
        "ENABLE_MAIN_TEX_USE_CHANNEL_B",
        "ENABLE_MAIN_TEX_USE_CHANNEL_A",
        "ENABLE_MAIN_TEX_USE_CHANNEL_RGB",
        "ENABLE_HSV",
        "ENABLE_VIRTUAL_LIGHT",
        "ENABLE_VIRTUAL_LIGHT_BACK",
        "ENABLE_VIRTUAL_LIGHT_DIR",
        "ENABLE_CHANNEL_MASK",
        "ENABLE_HD_TEX",
        "ENABLE_POST_EFFECT",
        "ENABLE_VERTICAL_FOG",
        "INSTANCING_ON",
    };

    // YY standard用到的所有keyword， 请决对保持与YifSandard.shader中定义的keyword数量和顺序一致
    private static string[] yyKeywords = new string[]
    {
        "_NORMALMAP",
        "_METALLICGLOSSMAP",
        "_SMOOTHNESS_TEXTURE_ALBEDO_CHANNEL_A",
        "LIGHTPROBE_SH",
        "ENABLE_RIM_LIGHT",
        "_EMISSION",
        "ENABLE_CLIP_RECT",
        "ENDABLE_IN_GLOW",
        "ENABLE_HD_TEX",
        "ENABLE_VIRTUAL_LIGHT_DIR",
        "ENABLE_ALPHA_BLEND",
        "ENABLE_ALPHA_TEST",
        "UNITY_SPECCUBE_BLENDING",
        "_GLOSSYREFLECTIONS_OFF",
        "_INDIRECT_SPEC_OFF",
        "_BRDF2",
        "NOT_CHANNEL_MASK",
        "_CHANNEL_MASK",
        "_PROBE_BLEND",
        "ENABLE_POST_EFFECT",
        "SHADOWS_SCREEN",
        "_RUNING_RAIN_ON",
        "_RIPPLE_RAIN_ON",
        "_BLINNPHONG_ON",
        "ENABLE_MIX_TEXTURE2",
        "ENABLE_MIX_TEXTURE3",
        "ENABLE_MIX_TEXTURE4",
        "ENABLE_MIX_NOISE",
        "DISABLE_MIX_SPEC",
        "ENABLE_PUDDLE",
        "LIGHTMAP_ON",
        "FOG_LINEAR",
        "DIRLIGHTMAP_COMBINED",
    };

    [MenuItem("Assets/特效专用/优化材质球", priority = 13)]
    public static void OptimieSelectEffectInHierarchy()
    {
        Material material = Selection.activeObject as Material;
        ClearInvalieProperties(material);
    }

    // 清理材质球的keyword
    public static void ClearUnExistsKeyword()
    {
        HashSet<string> yifSet = new HashSet<string>();
        for (int i = 0; i < yifKeywords.Length; i++)
        {
            yifSet.Add(yifKeywords[i]);
        }

        HashSet<string> yySet = new HashSet<string>();
        for (int i = 0; i < yyKeywords.Length; i++)
        {
            yySet.Add(yyKeywords[i]);
        }

        Material[] materials = GetMaterials();
        for (int i = 0; i < materials.Length; i++)
        {
            var mat = materials[i];
            HashSet<string> set = null;
            if (mat.shader.name.StartsWith("YifStandard"))
            {
                set = yifSet;
            }
            else if (mat.shader.name.IndexOf("YY/") >= 0)
            {
                set = yySet;
            }
            else
            {
                continue;
            }

            string[] ary = mat.shaderKeywords;
            for (int m = 0; m < ary.Length; m++)
            {
                if (!set.Contains(ary[m]))
                {
                    mat.DisableKeyword(ary[m]);
                }
            }
        }
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!assetPath.EndsWith(".mat"))
            {
                continue;
            }

            Material mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
            string[] ary = mat.shaderKeywords;
            for (int i = 0; i < ary.Length; i++)
            {
                if (!yifSet.Contains(ary[i]))
                {
                    mat.DisableKeyword(ary[i]);
                }
            }
        }

        AssetDatabase.SaveAssets();
    }

    // 清理材质球的shader统一改为yifstandard
    public static void ClearUnityDefaultShader()
    {
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!assetPath.EndsWith(".mat") || assetPath.Contains("/Shaders/Materials"))
            {
                continue;
            }

            Material mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
            if (mat == null)
            {
                //Debug.LogError("mat == null" + mat.name);
                continue;
            }
            if (mat.shader.name == "Standard")
            {
                mat.shader = Shader.Find("YifStandard/YifStandardActor");
            }
        }
    }

    // 清理默认类型的材质球，需强制指定Actor, Enviroment, Particle
    public static void ClearlDaultStyleMaterial()
    {

        var assetPaths = AssetDatabase.GetAllAssetPaths();
        float nextTime = 0;
        int count = 0;
        int totalCount = assetPaths.Length;

        foreach (var assetPath in assetPaths)
        {
            ++count;
            if (!assetPath.EndsWith(".mat") || assetPath.Contains("/Shaders/Materials"))
            {
                continue;
            }

            Material mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
            if (null == mat)
            {
                Debug.LogErrorFormat("{0} 为空", assetPath);
                continue;
            }

            if (mat.HasProperty("_MaterialStyle"))
            {
                if (mat.GetFloat("_MaterialStyle") == 0)
                {
                    if (assetPath.Contains("/Environments/"))
                    {
                        mat.SetFloat("_MaterialStyle", 2);
                    }
                    else if (mat.GetFloat("_RenderingMode") == 0
                        || mat.GetFloat("_RenderingMode") == 1
                        || mat.GetFloat("_RenderingMode") == 5)
                    {
                        mat.SetFloat("_MaterialStyle", 1);
                    }
                    else
                    {
                        mat.SetFloat("_MaterialStyle", 3);
                    }
                }

                float style = mat.GetFloat("_MaterialStyle");
                if (style == 2)
                {
                    if (mat.shader.name != "YifStandard/YifStandardEnvironments")
                        mat.shader = Shader.Find("YifStandard/YifStandardEnvironments");
                }
                else if (style == 3)
                {
                    if (mat.shader.name != "YifStandard/YifStandardParticle")
                        mat.shader = Shader.Find("YifStandard/YifStandardParticle");
                }
                else if (mat.shader.name != "YifStandard/YifStandardActor")
                {
                    mat.shader = Shader.Find("YifStandard/YifStandardActor");
                }
            }

            if (nextTime <= Time.realtimeSinceStartup)
            {
                bool cancel = EditorUtility.DisplayCancelableProgressBar("清理Shader", mat.name, (float)count / totalCount);
                nextTime = Time.realtimeSinceStartup + 0.1f;
                if (cancel)
                {
                    break;
                }
            }
        }

        EditorUtility.ClearProgressBar();
    }

    // 清除无效的keyword
    public static void ClearInvalieProperties()
    {
        Material[] materials = GetMaterials();
        for (int i = 0; i < materials.Length; i++)
        {
            Material material = materials[i];
            ClearInvalieProperties(material);
        }

        AssetDatabase.SaveAssets();
    }

    public static void ClearInvalieProperties(Material material)
    {
        if (null == material || !material.HasProperty("_MaterialStyle"))
        {
            return;
        }

        ProcessRenderMode(material);
        ProcessAlphaTest(material);
        ProcessCullMode(material);
        ProessDiffuse(material);
        ProcessUVNoise(material);
        ProcessGlow(material);
        ProcessDecallAll(material);
        ProcessNormalMap(material);
        ProcessClipRect(material);
    }

    private static void ProcessRenderMode(Material material)
    {
        if (material.GetFloat("_RenderingMode") == 1)
        {
            Texture2D texture = material.GetTexture("_MainTex") as Texture2D;
            if (texture == null)
            {
                Debug.LogErrorFormat("texture is nil, {0}", AssetDatabase.GetAssetPath(material));
                return;
            }
            if (texture.format == TextureFormat.DXT1 || texture.format == TextureFormat.PVRTC_RGB4 || texture.format == TextureFormat.ETC2_RGB) // 没有Alpha通道的贴图
            {
                material.SetFloat("_RenderingMode", 0);
                material.DisableKeyword("ENABLE_ALPHA_TEST");
            }
        }
    }

    private static void ProcessAlphaTest(Material material)
    {
        if (material.GetFloat("_RenderingMode") != 1 && material.GetFloat("_RenderingMode") != 5)
        {
            material.DisableKeyword("ENABLE_ALPHA_TEST");
        }
    }

    private static void ProcessCullMode(Material material)
    {
        if (material.GetFloat("_RenderingMode") == 0)
        {
            material.SetFloat("_CullMode", 2);
        }
    }

    private static void ProessDiffuse(Material material)
    {
        if (material.GetFloat("_MaterialStyle") == 3)
        {
            material.DisableKeyword("ENABLE_DIFFUSE");
        }
    }

    private static void ProcessUVNoise(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_UV_NOISE"))
        {
            if (null == material.GetTexture("_UVNoise"))
            {
                material.DisableKeyword("ENABLE_UV_NOISE");
                if (material.GetVector("_UVNoiseSpeed").x != 0 || material.GetVector("_UVNoiseSpeed").y != 0)
                {
                    material.EnableKeyword("ENABLE_UV_SPEED");
                    material.SetVector("_UVSpeed", material.GetVector("_UVNoiseSpeed"));
                }
            }
        }
    }

    private static void ProcessGlow(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_GLOW"))
        {
            if (null == material.GetTexture("_GlowTex") && material.GetVector("_GlowSpeed").x == 0 && material.GetVector("_GlowSpeed").y == 0)
            {
                material.DisableKeyword("ENABLE_GLOW");
            }
        }
    }

    private static void ProcessDecallAll(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_DECAL_ALL") || material.IsKeywordEnabled("ENABLE_DECAL_ALPHA"))
        {
            if (null == material.GetTexture("_DecalTex"))
            {
                material.DisableKeyword("ENABLE_DECAL_ALL");
                material.DisableKeyword("ENABLE_DECAL_ALPHA");
            }
        }
    }

    private static void ProcessNormalMap(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_NORMAL_TEX"))
        {
            Texture texture = material.GetTexture("_NormalTex");
            if (null == texture)
            {
                material.DisableKeyword("ENABLE_NORMAL_TEX");
            }
            else
            {
                TextureImporter importer = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                if (null != importer && importer.textureType != TextureImporterType.NormalMap)
                {
                    importer.textureType = TextureImporterType.NormalMap;
                    importer.SaveAndReimport();
                }
            }
        }
    }

    private static void ProcessClipRect(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_CLIP_RECT"))
        {
            var rect = material.GetVector("_ClipRect");
            if (rect.x == -9999 && rect.y == -9999 && rect.z == 9999 && rect.w == 9999)
            {
                material.DisableKeyword("ENABLE_CLIP_RECT");
            }
        }
    }

    private static Material[] GetMaterials()
    {
        List<Material> materialList = new List<Material>();
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!assetPath.EndsWith(".mat") || assetPath.Contains("Assets/Game/Shaders/Materials"))
            {
                continue;
            }

            materialList.Add(AssetDatabase.LoadAssetAtPath<Material>(assetPath));
        }

        return materialList.ToArray();
    }
}
