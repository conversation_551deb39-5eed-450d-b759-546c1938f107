﻿using Nirvana;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Game.Scripts.Editor.Tools
{
    class ChangeButtonAnim : BaseEditorWindow
    {
        [MenuItem("自定义工具/UI类/增加按钮点击效果")]
        private static void ShowWindow()
        {
            EditorWindow.GetWindow<ChangeButtonAnim>(false, "增加按钮点击效果");
        }

        private void OnGUI()
        {
            if (GUILayout.Button("替换选中预制体"))
            {
                if (Selection.activeGameObject != null)
                {
                    Check(Selection.activeGameObject);
                    AssetDatabase.SaveAssets();
                }
            }

            if (GUILayout.Button("替换所有预制体"))
            {
                ChangeAll();
                AssetDatabase.SaveAssets();
            }

        }

        private void ChangeAll()
        {
            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { "Assets/Game/UIs/View" });
            int endIndex = guids.Length;
            Debug.Log(guids.Length);
            if (endIndex < 1)
            {
                return;
            }
            float nextTime = 0;
            for (int i = 0; i < endIndex; i++)
            {
                var guid = guids[i];
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                this.Check(obj);
                if (nextTime <= Time.realtimeSinceStartup)
                {
                    bool cancel = EditorUtility.DisplayCancelableProgressBar("替换中", path, (float)i / endIndex);
                    nextTime = Time.realtimeSinceStartup + 0.1f;
                    if (cancel)
                    {
                        break;
                    }
                }
            }
            EditorUtility.ClearProgressBar();
        }

        private void Check(GameObject obj)
        {
            var btns = obj.GetComponentsInChildren<Button>(true);
            if (btns.Length < 1)
            {
                return;
            }
            foreach (Button btn in btns)
            {
                if(btn.GetComponent<ButtonClickScale>() == null)
                {
                    btn.GetOrAddComponent<ButtonClickScale>();
                }
            }

            PrefabUtility.ResetToPrefabState(obj);
            PrefabUtility.SetPropertyModifications(obj, new PropertyModification[] { });
        }

    }
}
