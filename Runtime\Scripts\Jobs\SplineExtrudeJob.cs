﻿#if ENABLE_SPLINES
#if ENABLE_COLLECTIONS
#if ENABLE_MATHEMATICS
#if ENABLE_BURST
using System;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Splines;

namespace UI_Spline_Renderer
{
    /// <summary>
    /// 样条线挤出作业
    /// 使用Unity Job System并行计算样条线的顶点和三角形数据
    /// 支持Burst编译优化以获得最佳性能
    /// </summary>
    [BurstCompile]
    internal struct SplineExtrudeJob : IJob
    {
        /// <summary>原生样条线数据</summary>
        [ReadOnly] public NativeSpline spline;

        /// <summary>宽度曲线</summary>
        [ReadOnly] public NativeCurve widthCurve;
        /// <summary>基础宽度</summary>
        [ReadOnly] public float width;
        /// <summary>是否保持Z轴为0</summary>
        [ReadOnly] public bool keepZeroZ;
        /// <summary>是否保持面向屏幕</summary>
        [ReadOnly] public bool keepBillboard;
        /// <summary>起始顶点索引</summary>
        [ReadOnly] public int startIdx;
        /// <summary>裁剪范围</summary>
        [ReadOnly] public float2 clipRange;

        /// <summary>UV乘数</summary>
        [ReadOnly] public float2 uvMultiplier;
        /// <summary>UV偏移</summary>
        [ReadOnly] public float2 uvOffset;
        /// <summary>UV映射模式</summary>
        [ReadOnly] public UVMode uvMode;
        /// <summary>基础颜色</summary>
        [ReadOnly] public Color color;
        /// <summary>颜色渐变</summary>
        [ReadOnly] internal NativeColorGradient colorGradient;

        /// <summary>边缘数量</summary>
        [ReadOnly] public int edgeCount;
        /// <summary>评估的位置数组</summary>
        public NativeArray<float3> evaluatedPos;
        /// <summary>评估的切线数组</summary>
        public NativeArray<float3> evaluatedTan;
        /// <summary>评估的法线数组</summary>
        public NativeArray<float3> evaluatedNor;

        /// <summary>输出顶点列表</summary>
        [WriteOnly] public NativeList<UIVertex> vertices;
        /// <summary>输出三角形列表</summary>
        [WriteOnly] public NativeList<int3> triangles;

        /// <summary>V坐标值</summary>
        float v;
        /// <summary>样条线长度</summary>
        float length;

        /// <summary>
        /// 执行作业的主方法
        /// 先评估样条线上的点，然后挤出生成顶点和三角形
        /// </summary>
        public void Execute()
        {
            Evaluate();
            ExtrudeSpline();
        }

        /// <summary>
        /// 评估样条线上的所有点
        /// 计算每个边缘点的位置、切线和法线
        /// </summary>
        void Evaluate()
        {
            length = spline.GetLength();
            for (int i = 0; i < edgeCount; i++)
            {
                var t = (float)i / (edgeCount - 1);
                t = t.Remap(0, 1, clipRange.x, clipRange.y);
                spline.Evaluate(t, out var pos, out var tan, out var nor);
                evaluatedPos[i] = pos;
                evaluatedTan[i] = tan;
                evaluatedNor[i] = nor;
            }
        }

        /// <summary>
        /// 挤出样条线生成顶点和三角形
        /// 沿着样条线的每个点生成一对顶点，并连接成三角形网格
        /// </summary>
        void ExtrudeSpline()
        {
            if (spline.Count < 2) return;


            var prevPosition = float3.zero;
            for (int i = 0; i < edgeCount; i++)
            {
                var t = (float)i / (edgeCount - 1);
                t = t.Remap(0, 1, clipRange.x, clipRange.y);
                var pos = evaluatedPos[i];
                var tan = evaluatedTan[i];
                var nor = evaluatedNor[i];

                // 解决零切线问题
                if (tan is { x: 0, y: 0, z: 0 })
                {
                    var prev = i == 0 ? pos : prevPosition;
                    var next = i == edgeCount - 1 ? pos : evaluatedPos[i + 1];
                    tan = next - prev;
                }


                InternalUtility.ExtrudeEdge(
                    GetWidthAt(t), GetVAt(t, i),  GetColorAt(t), ref pos, tan, nor,
                    keepBillboard, keepZeroZ, uvMultiplier, uvOffset, out var v0, out var v1);

                vertices.Add(v0);
                vertices.Add(v1);

                prevPosition = pos;

                // 生成三角形：2-1-0, 3-1-2 ...
                if (i > 0)
                {
                    triangles.Add(new int3(
                        2 * i + startIdx,
                        2 * i - 1 + startIdx,
                        2 * i - 2 + startIdx));
                    triangles.Add(new int3(
                        2 * i + 1 + startIdx,
                        2 * i - 1 + startIdx,
                        2 * i + startIdx));
                }
            }
        }

        /// <summary>
        /// 获取指定位置的宽度
        /// 根据宽度曲线和基础宽度计算最终宽度
        /// </summary>
        /// <param name="t">样条线参数(0-1)</param>
        /// <returns>计算后的宽度</returns>
        float GetWidthAt(float t)
        {
            return width * widthCurve.Evaluate(t);
        }

        /// <summary>
        /// 获取指定位置的颜色
        /// 根据颜色渐变和基础颜色计算最终颜色
        /// </summary>
        /// <param name="t">样条线参数(0-1)</param>
        /// <returns>计算后的颜色</returns>
        Color GetColorAt(float t)
        {
            return color * colorGradient.Evaluate(t);
        }

        /// <summary>
        /// 获取指定位置的V纹理坐标
        /// 根据UV模式计算纹理的V坐标
        /// </summary>
        /// <param name="t">样条线参数(0-1)</param>
        /// <param name="i">当前边缘索引</param>
        /// <returns>V纹理坐标</returns>
        float GetVAt(float t, int i)
        {
            switch (uvMode)
            {
                case UVMode.Tile:
                    return length / width * t;
                case UVMode.RepeatPerSegment:
                    return i;
                case UVMode.Stretch:
                    return t;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

    }
}
#endif
#endif
#endif
#endif