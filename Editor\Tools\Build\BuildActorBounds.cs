﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Text;
using System.IO;
using UnityEditor;

public static class BuildActorBounds
{
    private static readonly string RolePath = "Assets/Game/Actors/Character/RoleMan/1101001/1101001.prefab";
    private static readonly string BossDir = "Assets/Game/Model/Boss";
    private static readonly string SavePath = Path.Combine(Application.dataPath, "Game/Lua/config/config_actor_bounds.lua");

    public static bool Build()
    {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append("return {\n");
        GameObject role = AssetDatabase.LoadAssetAtPath(RolePath, typeof(GameObject)) as GameObject;
        if (null == role)
        {
            Debug.LogError("[BuildActorBounds]RolePath is Null!");
            return false;
        }

        var roleBounds = GetBounds(role);
        stringBuilder.Append(string.Format("\t[\"base\"] = {{x={0},y={1},z={2}}},\n", roleBounds.x, roleBounds.y, roleBounds.z));

        string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { BossDir });
        for (int i = 0; i < guids.Length; i++)
        {
            var guid = guids[i];
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
            if (!IsActorGameObj(obj))
            {
                continue;
            }
            
            var bounds = GetBounds(obj);
            AssetImporter importer = AssetImporter.GetAtPath(path);
            string bundleName = importer.assetBundleName;
            float scale = Mathf.Min(new float[] { bounds.x /roleBounds.x, bounds.y / roleBounds.y, bounds.z / roleBounds.z });
            scale = Mathf.Pow(scale, 0.5f);
            scale = scale == 0 ? 1 : scale;
            string str = string.Format("\t[\"{0}/{1}\"] = {{x={2},y={3},z={4},scale={5}}},\n", bundleName, obj.name, bounds.x, bounds.y, bounds.z, scale);
            stringBuilder.Append(str);
        }

        stringBuilder.Append("}");
        File.WriteAllText(SavePath, stringBuilder.ToString());
        AssetDatabase.Refresh();
        return true;
    }

    private static bool IsActorGameObj(GameObject gameobj)
    {
        if (null == gameobj.GetComponent<ActorAttachment>()
          && null == gameobj.GetComponent<AttachObject>()
           && null == gameobj.GetComponent<Game.AttachSkinObject>()
           && null == gameobj.GetComponent<Nirvana.AnimatorOptimizer>()
              && null == gameobj.GetComponent<Animator>())
        {
            return false;
        }

        return true;
    }

    private static Vector3 GetBounds(GameObject gameObject)
    {
        Vector3 size;
        Renderer[] renderers = gameObject.GetComponentsInChildren<Renderer>();
        if (renderers.Length == 0)
        {
            size = Vector3.zero;
        }

        if (renderers.Length == 1)
        {
            size = renderers[0].bounds.extents;
        }
        else
        {
            Bounds bounds = new Bounds(gameObject.transform.position, Vector3.zero);
            foreach (Renderer mr in renderers)
            {
                bounds.Encapsulate(mr.bounds);
            }

            size = bounds.extents;
        }

        return size;
    }
}
