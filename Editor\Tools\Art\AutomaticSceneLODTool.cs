﻿using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using Nirvana;
using System.IO;
using UnityEditor.SceneManagement;
using System.Text;
using System.Text.RegularExpressions;
using static AutomaticLOD;

public static class AutomaticSceneLODTool
{
    private static float screenRelativeTransitionHeight = 0.6f;
    private static float screenRelativeTransitionHeight2 = 0.05f;
    // 大于多少面数才生成LOD
    private static int trianglesCount = 3000;
    // 面数压缩率
    private static float compression = 0.33f;

    private delegate void CallBack(bool cancel);
    private static int curIndex = 0;
    private static int totalCount = 0;

    public static void CreateLODs(GameObject root)
    {
        MeshRenderer[] meshRenderers = root.GetComponentsInChildren<MeshRenderer>(true);
        curIndex = 0;
        totalCount = meshRenderers.Length;
        Step(meshRenderers, 0);
    }

    public static void CleanLODs(GameObject root)
    {
        LODGroup[] groups = root.GetComponentsInChildren<LODGroup>(true);
        for (int i = 0; i < groups.Length; ++i)
        {
            var group = groups[i];
            var mesh = group.GetComponent<MeshRenderer>();
            RemoveLODGroup(mesh);
        }
    }

    public static void ShowOrHideLOD(GameObject root, bool isShow)
    {
        LODGroup[] groups = root.GetComponentsInChildren<LODGroup>(true);
        for (int i = 0; i < groups.Length; ++i)
        {
            var group = groups[i];
            if (null == group)
            {
                continue;
            }

            var lods = group.GetLODs();
            for (int j = 1; j < lods.Length; ++j)
            {
                var lod = lods[j];
                foreach (var renderer in lod.renderers)
                {
                    if (null == renderer)
                        continue;

                    renderer.gameObject.SetActive(isShow);
                }
            }

            group.enabled = isShow;
        }
    }

    private static void Step(MeshRenderer[] meshRenderers, int index)
    {
        curIndex = index;
        if (index >= totalCount - 1)
        {
            AssetDatabase.SaveAssets();
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            EditorUtility.ClearProgressBar();
            Debug.LogError("一键生成场景LOD完成");
            return;
        }

        var renderer = meshRenderers[index];
        Check(renderer, (cancel) => 
        {
            if (cancel)
            {
                index = totalCount;
            }
            index = index + 1;
            if (index < meshRenderers.Length)
                Step(meshRenderers, index);
        });
    }

    private static void Check(MeshRenderer meshRenderer, CallBack action)
    {
        if (null == meshRenderer || meshRenderer.name.Contains("_LOD") || !meshRenderer.enabled)
        {
            action(false);
            return;
        }

        var meshFilter = meshRenderer.GetComponent<MeshFilter>();
        if (null == meshFilter)
        {
            Debug.LogError(string.Format("{0}没有MeshFilter组件", meshRenderer.name));
            action(false);
            return;
        }

        if (null == meshFilter.sharedMesh)
        {
            Debug.LogError(string.Format("{0}上的MeshFilter组件Mesh为空", meshRenderer.name));
            action(false);
            return;
        }

        // 地形资源不处理
        var path = GetMeshSavedPath(meshRenderer);
        if (path.Contains("Terrain") || meshRenderer.name.Contains("Terrain"))
        {
            action(false);
            return;
        }

        if (ShouldComputeMesh(meshRenderer))
        {
            LODGroup lODGroup = meshRenderer.GetComponentInChildren<LODGroup>();
            LOD[] lods = lODGroup != null ? lODGroup.GetLODs() : null;
            if (lods == null || lods.Length < 2 || lods[1].renderers == null || lods[1].renderers.Length == 0 || lods[1].renderers[0] == null)
            {
                AutoGenerateLOD(meshRenderer, (cancel) =>
                {
                    AddLODGroup(meshRenderer);
                    action(cancel);
                });
            }
        }
        else
        {
            RemoveLODGroup(meshRenderer);
            action(false);
        }
    }

    private static string FixedLodNodeName(Transform root, MeshRenderer originalRenderer, int index = 1)
    {
        string nodeName = string.Format("{0}_LOD{1}", originalRenderer.name, index);
        Transform node = root.Find(nodeName);
        if (null != node)
        {
            index += 1;
            nodeName = FixedLodNodeName(root, originalRenderer, index);
        }

        return nodeName;
    }

    private static void AddLODGroup(MeshRenderer originalRenderer)
    {
        var go = originalRenderer.gameObject;
        var meshPath = GetMeshSavedPath(originalRenderer);
        Mesh meshLOD1 = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
        if (null == meshLOD1)
        {
            return;
        }

        if (null == originalRenderer.transform.parent)
        {
            return;
        }

        Transform node = new GameObject(FixedLodNodeName(originalRenderer.transform.parent, originalRenderer)).transform;
        node.SetParent(originalRenderer.transform.parent);
        node.localPosition = originalRenderer.transform.localPosition;
        node.localRotation = originalRenderer.transform.localRotation;
        node.localScale = originalRenderer.transform.localScale;

        var copiedComponents = originalRenderer.gameObject.GetComponents<Component>();
        foreach (var copiedComponent in copiedComponents)
        {
            if (null == copiedComponent || copiedComponent.GetType() == typeof(LODGroup))
                continue;

            UnityEditorInternal.ComponentUtility.CopyComponent(copiedComponent);
            UnityEditorInternal.ComponentUtility.PasteComponentAsNew(node.gameObject);
        }

        LODGroup group = go.GetOrAddComponent<LODGroup>();
        LOD[] lods = new LOD[2];

        lods[0] = new LOD();
        lods[0].screenRelativeTransitionHeight = screenRelativeTransitionHeight;
        lods[0].renderers = new Renderer[] { originalRenderer };

        MeshFilter newMeshFilter = node.GetComponent<MeshFilter>();
        newMeshFilter.sharedMesh = meshLOD1;

        MeshRenderer newRenderer = node.GetComponent<MeshRenderer>();

        lods[1] = new LOD();
        lods[1].screenRelativeTransitionHeight = screenRelativeTransitionHeight2;
        lods[1].renderers = new Renderer[] { newRenderer };

        group.SetLODs(lods);
    }

    private static void RemoveLODGroup(MeshRenderer meshRenderer)
    {
        LODGroup group = meshRenderer.GetComponent<LODGroup>();
        if (null == group)
        {
            return;
        }

        var lods = group.GetLODs();
        //删除index>0的所有mesh
        for (int i = 1; i < lods.Length; ++i)
        {
            var lod = lods[i];
            foreach (var renderer in lod.renderers)
            {
                if (null == renderer)
                    continue;

                Mesh mesh = null;
                var meshFilter = renderer.GetComponent<MeshFilter>();
                if (null != meshFilter)
                {
                    mesh = meshFilter.sharedMesh;
                }

                if (null != mesh)
                {
                    var path = AssetDatabase.GetAssetPath(mesh.GetInstanceID());
                    AssetDatabase.DeleteAsset(path);
                }

                GameObject.DestroyImmediate(renderer.gameObject);
            }
        }

        GameObject.DestroyImmediate(group);
    }

    public static void Update()
    {
        if (curObj)
        {
            bool cancel = false;
            //if (totalCount > 0)
            //{
            //    //cancel = EditorUtility.DisplayCancelableProgressBar("Build LOD", meshPath, (float)curIndex / totalCount);
            //}

            if (curObj.m_listLODLevels.Count > 0)
            {
                var obj = curObj;
                curObj = null;

                try
                {
                    GenerateLOD(obj.gameObject, meshPath);
                }
                catch (Exception ex)
                {
                    Debug.LogErrorFormat("Exception:{0}, obj name:{1}, {2}", ex.ToString(), obj.name, obj.gameObject != null ? obj.gameObject.name : "obj.gameObject null");
                }

                UltimateGameTools.MeshSimplifier.Simplifier[] simplifiers = obj.GetComponentsInChildren<UltimateGameTools.MeshSimplifier.Simplifier>();
                foreach (var sim in simplifiers)
                {
                    GameObject.DestroyImmediate(sim, true);
                }

                AutomaticLOD[] automaticLODs = obj.GetComponentsInChildren<AutomaticLOD>();
                foreach (var lod in automaticLODs)
                {
                    GameObject.DestroyImmediate(lod, true);
                }

                if (null != curAction)
                {
                    curAction(cancel);
                }
            }
        }
    }

    private static Mesh ComputeMesh(GameObject gameObject)
    {
        Renderer renderer = gameObject.GetComponent<MeshRenderer>();
        if (null == renderer)
        {
            Debug.LogError("Renderer is Null");
            return null;
        }

        AutomaticLOD automaticLOD = renderer.GetComponent<AutomaticLOD>();
        if (null == automaticLOD)
        {
            Debug.LogError("请先点击AutomaticLOD组件上的GenerateLODs按钮");
            return null;
        }

        if (automaticLOD.m_listLODLevels.Count < 1)
        {
            Debug.LogError("请先点击AutomaticLOD组件上的GenerateLODs按钮");
            return null;
        }

        Mesh newMesh = null;
        for (int i = 1; i < automaticLOD.m_listLODLevels.Count; ++i)
        {
            newMesh = new Mesh();
            LODLevelData m_listLODLevel = automaticLOD.m_listLODLevels[i];
            var lowMesh = m_listLODLevel != null ? m_listLODLevel.m_mesh : null;

            if (lowMesh != null)
            {
                newMesh.vertices = lowMesh.vertices;
                newMesh.triangles = lowMesh.triangles;
                newMesh.uv = lowMesh.uv;
                newMesh.normals = lowMesh.normals;
                newMesh.tangents = lowMesh.tangents;
            }
        }

        return newMesh;
    }

    private static bool ShouldComputeMesh(MeshRenderer meshRenderer)
    {
        bool flag = false;
        Mesh mesh = null;

        MeshFilter meshFilter = meshRenderer.GetComponent<MeshFilter>();
        if (null != meshFilter)
        {
            mesh = meshFilter.sharedMesh;
        }

        if (null != mesh)
        {
            flag = meshRenderer.gameObject.layer == GameLayers.LODMask || mesh.triangles.Length / 3 >= trianglesCount;
        }

        return flag;
    }

    private static string GetMeshSavedPath(MeshRenderer meshRenderer, int lodIndex = 0, MeshFilter meshFilter = null, Mesh mesh = null, string path = null, string savePath = null)
    {
        lodIndex = lodIndex == 0 ? 0 : lodIndex;
        meshFilter = meshFilter == null ? meshRenderer.GetComponent<MeshFilter>() : meshFilter;
        mesh = mesh == null ? meshFilter.sharedMesh : mesh;
        path = string.IsNullOrEmpty(path) ? AssetDatabase.GetAssetPath(mesh.GetInstanceID()) : path;
        savePath = string.IsNullOrEmpty(savePath) 
            ? Path.Combine(Path.GetDirectoryName(path), string.Format("LOD/{0}_LOD.asset", mesh.name)) 
            : Path.Combine(Path.GetDirectoryName(path), string.Format("LOD/{0}_LOD_{1}.asset", mesh.name, lodIndex));

        if (File.Exists(savePath))
        {
            lodIndex += 1;
            GetMeshSavedPath(meshRenderer, lodIndex, meshFilter, mesh, path, savePath);
        }

        return savePath;
    }

    static string meshPath;
    static int s_nLastProgress = -1;
    static CallBack curAction;
    static AutomaticLOD curObj;
    static string s_strLastTitle = "";
    static string s_strLastMessage = "";
    private static void AutoGenerateLOD(MeshRenderer meshRenderer, CallBack callback)
    {
        meshPath = GetMeshSavedPath(meshRenderer);
        if (File.Exists(meshPath))
        {
            callback(false);
            return;
        }

        s_nLastProgress = -1;
        curAction = callback;

        AutomaticLOD automaticLOD = meshRenderer.gameObject.AddComponent<AutomaticLOD>();
        curObj = automaticLOD;
        CreateDefaultLODS(2, automaticLOD, true);
        automaticLOD.ComputeLODData(true, Progress);
        automaticLOD.ComputeAllLODMeshes(true, Progress);
    }

    private static void CreateDefaultLODS(int nLevels, AutomaticLOD root, bool bRecurseIntoChildren)
    {
        List<AutomaticLOD.LODLevelData> listLODLevels = new List<AutomaticLOD.LODLevelData>();

        for (int i = 0; i < nLevels; i++)
        {
            AutomaticLOD.LODLevelData data = new AutomaticLOD.LODLevelData();
            float oneminust = (float)(nLevels - i) / (float)nLevels;
            oneminust = oneminust < 1 ? compression : 1;

            data.m_fScreenCoverage = Mathf.Pow(1.0f - 1.0f - oneminust, 6f);
            data.m_fMaxCameraDistance = i == 0 ? 0.0f : i * 100.0f;
            data.m_fMeshVerticesAmount = oneminust;
            data.m_mesh = null;
            data.m_bUsesOriginalMesh = false;
            data.m_nColorEditorBarIndex = i;

            listLODLevels.Add(data);
        }

        root.SetLODLevels(listLODLevels, AutomaticLOD.EvalMode.ScreenCoverage, 1000.0f, bRecurseIntoChildren);
    }

    static void Progress(string strTitle, string strMessage, float fT)
    {
        int nPercent = Mathf.RoundToInt(fT * 100.0f);

        if (nPercent != s_nLastProgress || s_strLastTitle != strTitle || s_strLastMessage != strMessage)
        {
            s_strLastTitle = strTitle;
            s_strLastMessage = strMessage;
            s_nLastProgress = nPercent;
        }
    }

    private static void GenerateLOD(GameObject obj, string meshPath)
    {
        string fileString;
        string directoryPath;
        Mesh mesh;
        try
        {
            if (obj == null)
            {
                return;
            }

            mesh = ComputeMesh(obj);
            if (null == mesh)
            {
                return;
            }

            directoryPath = Path.GetDirectoryName(meshPath);
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            AssetDatabase.CreateAsset(mesh, meshPath);
            AssetDatabase.Refresh();

            fileString = OpenFile(meshPath);
            SetReadable(ref fileString, false);
            SaveFile(meshPath, fileString);
        }
        catch (Exception ex)
        {
            Debug.LogErrorFormat("Exception:{0}", ex.ToString());
        }
        
    }

    private static void SetReadable(ref string fileString, bool readable)
    {
        if (readable)
        {
            fileString = fileString.Replace("m_IsReadable: 0", "m_IsReadable: 1");
        }
        else
        {
            fileString = fileString.Replace("m_IsReadable: 1", "m_IsReadable: 0");
        }
    }

    private static string OpenFile(string filePath)
    {
        FileStream fs = new FileStream(filePath, FileMode.Open);
        byte[] bytes = new byte[fs.Length];
        fs.Read(bytes, 0, bytes.Length);
        string fileString = Encoding.Default.GetString(bytes);
        fs.Close();
        return fileString;
    }

    private static void SaveFile(string filePath, string fileString)
    {
        FileStream fs = new FileStream(filePath, FileMode.Open);
        byte[] bytes = Encoding.Default.GetBytes(fileString);
        fs.Write(bytes, 0, bytes.Length);
        fs.Close();
    }
}

