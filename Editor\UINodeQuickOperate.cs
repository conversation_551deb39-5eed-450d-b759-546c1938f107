﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

/*
 * 编写一个可以快速操作预制的工具
 * 功能：
 *	1.快速复制选中物体并设置 n+1 的名称, n 原来名称 设置了则为新名称
 * 	2.快速在当前节点子节点下创建一个组件，可多选是否添加ui组件
 * 	3.修改当前节点下的子节点符合名称的节点替换名称
 * 	4.修改当前节点下子节点符合当前组件替换组件
 * 	5.。。。
*/
using UnityEngine.UI;

public enum ComEnum
{
	none = 0,
	ui_button,
	ui_text,
	ui_image,
	ui_rawimage,
	ui_slider,
}
	

public class  UINodeQuickOperate : EditorWindow
{
	[MenuItem("Tools/换UI工具/预制多节点操作", false, 1050)]
	static void QuickObjOperate()
	{
		EditorWindow myWindow = (UINodeQuickOperate)EditorWindow.GetWindow(typeof(UINodeQuickOperate), false, "复合节点操作");//创建窗口
		myWindow.Show();//展示
	}

	void Awake()
	{
		
	}

	void OnGUI()
	{
		GUILayout.Label("1.快速复制选中物体并设置 n+1 的名称, n 原来名称 设置了则为新名称", GUILayout.Width(800f));
		GUILayout.Label("2.快速在当前节点子节点下创建一个组件，可多选是否添加ui组件", GUILayout.Width(800f));
		GUILayout.Label("3.修改当前节点下的子节点符合名称的节点替换名称", GUILayout.Width(800f));
		GUILayout.Label("4.修改当前节点下子节点符合当前组件替换组件", GUILayout.Width(800f));
		GUILayout.Space (20);
		this.QuickInstGameObjectEnter ();
		this.QuickInstGameObjectAndAddComponentEnter ();
		this.ReplaceNodeNameCenter ();
	}

	#region 快速复制选中物体并设置 n+1 的名称

	private string new_name = "";
	private string start_index = "1";
	private string inst_num = "0";
	//快速复制选中物体并设置 n+1 的名称
	private void QuickInstGameObjectEnter()
	{
		GUILayout.Label("快速复制", GUILayout.Width(500f));

		GUILayout.BeginHorizontal ();
		GUILayout.Label ("选择使用新名称(可不选, 使用原物体名称)", GUILayout.Width (220f));
		new_name = EditorGUILayout.TextField(new_name, GUILayout.Width(100));
		GUILayout.EndHorizontal ();
		GUILayout.BeginHorizontal ();
		GUILayout.Label ("名称n+起点(可不选, 起始为1)", GUILayout.Width (160f));
		start_index = EditorGUILayout.TextField(start_index, GUILayout.Width(30f));
		GUILayout.Label ("复制个数", GUILayout.Width (50f));
		inst_num = EditorGUILayout.TextField(inst_num, GUILayout.Width(30f));
		if (GUILayout.Button("生成", GUILayout.Width(50)))
		{
			this.QuickInstGameObject();
		}
		GUILayout.EndHorizontal ();
	}

	private void QuickInstGameObject()
	{
		GameObject[] operate_objs = this.GetSelectObjs();

		if (operate_objs == null) 
		{
			this.StatusTips ("错误提示", "发生异常！请检测问题", "确认");
			return;
		}

        string real_name = "";
        int real_start_index = 1;
        int real_inst_num = 0;
        int.TryParse(start_index, out real_start_index);
        int.TryParse(inst_num, out real_inst_num);

        for (int i = real_start_index; i < real_inst_num + real_start_index; i++)
        {
            for (int n = 0; n < operate_objs.Length; n++)
            {
                GameObject operate_obj = operate_objs[n];
                real_name = new_name == "" ? operate_obj.name : new_name;
 
                if (real_inst_num == 0)
                {
                    this.StatusTips("错误提示", "复制的个数不能为0个", "确认");
                    return;
                }
                if (operate_obj.transform.parent == null)
                {
                    this.StatusTips("错误提示", "复制的目标没有父节点", "确认");
                    return;
                }

                GameObject clone = GameObject.Instantiate(operate_obj) as GameObject;
                clone.name = string.Format("{0}{1}", real_name, i);
                clone.transform.SetParent(operate_obj.transform.parent, false);
            }
        }

		this.StatusTips ("提示", "复制成功", "确认");
	}

	#endregion

	#region 快速在当前节点子节点下创建一个组件，可多选是否添加ui组件

	private string sub_path = "";
	private string new_node_name = "none";
	private ComEnum sub_addenum = ComEnum.none; 
	//快速在当前节点子节点下创建一个组件，可多选是否添加ui组件
	private void QuickInstGameObjectAndAddComponentEnter()
	{
		GUILayout.Space (20);
		GUILayout.Label("快速在当前节点子节点下创建一个组件(可添加组件)", GUILayout.Width(500f));
		GUILayout.BeginHorizontal ();
		GUILayout.Label ("查找路径(可不选, 使用一级路径)", GUILayout.Width (180f));
		sub_path = EditorGUILayout.TextField(sub_path, GUILayout.Width(160f));
		GUILayout.EndHorizontal ();

		GUILayout.BeginHorizontal ();
		GUILayout.Label ("选择使用新名称(不填为none)", GUILayout.Width (180f));
		new_node_name = EditorGUILayout.TextField(new_node_name, GUILayout.Width(160f));
		GUILayout.EndHorizontal ();

		GUILayout.BeginHorizontal ();
		GUILayout.Label ("添加组件", GUILayout.Width (50f));
		sub_addenum = (ComEnum)EditorGUILayout.EnumPopup (sub_addenum, GUILayout.Width (100f));
		if (GUILayout.Button("生成", GUILayout.Width(50)))
		{
			this.QuickInstGameObjectAndAddComponent();
		}
		GUILayout.EndHorizontal ();
	}

	private void QuickInstGameObjectAndAddComponent()
	{
		GameObject operate_obj = this.GetSelectObj();

		if (operate_obj == null) 
		{
			this.StatusTips ("错误提示", "发生异常！请检测问题", "确认");
			return;
		}
		int child_length = operate_obj.transform.childCount;
		bool is_success = false;
		for (int i = 0; i < child_length; i++) 
		{
			Transform child = operate_obj.transform.GetChild(i);
			Transform trs_parent = child;
			if (sub_path != "")
				trs_parent = child.Find (sub_path);

			if (trs_parent != null) 
			{
				GameObject aim_obj = null;
				Transform aim_Trans = trs_parent.Find (new_node_name);
				if (aim_Trans != null)
				{
					aim_obj = aim_Trans.gameObject;
					this.NewAndAddCom (sub_addenum, aim_obj);
				} 
				else 
				{
					aim_obj = new GameObject (new_node_name);
					aim_obj.transform.SetParent (trs_parent);
					aim_obj.AddComponent<RectTransform> ();
					aim_obj.transform.localPosition = Vector3.zero;
					aim_obj.transform.localScale = Vector3.one;		
					this.NewAndAddCom (sub_addenum, aim_obj);
				}
				is_success = true;
			}
		}
		if (is_success) 
			this.StatusTips ("提示", "操作成功", "确认");
		else
			this.StatusTips ("提示", "操作失败，未找到对应节点", "确认");
	}
	#endregion

	#region 修改当前节点下的子节点符合名称的节点替换名称 修改当前节点下子节点符合当前组件替换组件

	private bool is_change_sub = false;
	private bool is_remove_and_add_com = false;
	private ComEnum old_com = ComEnum.none;
	private ComEnum new_com = ComEnum.none; 
	private string change_sub_path = "";
	private string old_change_name = "";
	private string new_change_name = "";
	private bool is_change_not_com = false;
	private bool is_change_destroy = false;
	//快速在当前节点子节点下创建一个组件，可多选是否添加ui组件
	private void ReplaceNodeNameCenter()
	{
		GUILayout.Space (20);
		GUILayout.Label("修改当前节点下的子节点符合名称的节点替换名称,增删组件", GUILayout.Width(500f));
		GUILayout.BeginHorizontal ();
		GUILayout.Label ("节点旧名称", GUILayout.Width (70f));
		old_change_name = EditorGUILayout.TextField(old_change_name, GUILayout.Width(100f));
		GUILayout.Label ("节点新名称", GUILayout.Width (70f));
		new_change_name = EditorGUILayout.TextField(new_change_name, GUILayout.Width(100f));
		GUILayout.EndHorizontal ();


		GUILayout.BeginHorizontal ();
		GUILayout.Label ("是否置换的是子节点的子节点", GUILayout.Width (180f));
		is_change_sub = EditorGUILayout.Toggle (is_change_sub);
		GUILayout.EndHorizontal ();

		if (is_change_sub) {
			GUILayout.BeginHorizontal ();
			GUILayout.Label ("查找路径(可不选, 使用一级路径)", GUILayout.Width (180f));
			change_sub_path = EditorGUILayout.TextField(change_sub_path, GUILayout.Width(160f));
			GUILayout.EndHorizontal ();
		}

		GUILayout.BeginHorizontal ();
		GUILayout.Label ("是否移除或增加组件", GUILayout.Width (180f));
		is_remove_and_add_com = EditorGUILayout.Toggle (is_remove_and_add_com);
		GUILayout.EndHorizontal ();
		if (is_remove_and_add_com) {
			GUILayout.BeginHorizontal ();
			GUILayout.Label ("移除组件", GUILayout.Width (50f));
			old_com = (ComEnum)EditorGUILayout.EnumPopup (old_com, GUILayout.Width (100f));
			GUILayout.Label ("添加组件", GUILayout.Width (50f));
			new_com = (ComEnum)EditorGUILayout.EnumPopup (new_com, GUILayout.Width (100f));
			GUILayout.EndHorizontal ();
		}
			
		GUILayout.BeginHorizontal ();
		GUILayout.Label ("一键替换(子节点全部选中)", GUILayout.Width (180f));
		is_change_not_com = EditorGUILayout.Toggle (is_change_not_com);
		GUILayout.EndHorizontal ();

		GUILayout.BeginHorizontal ();
		GUILayout.Label ("删除这个节点(慎用)", GUILayout.Width (180f));
		is_change_destroy = EditorGUILayout.Toggle (is_change_destroy);
		GUILayout.EndHorizontal ();

		if (GUILayout.Button("操作一下", GUILayout.Width(200f)))
		{
			this.QuickChangeName();
		}
	}

	private void QuickChangeName()
	{
		GameObject operate_obj = this.GetSelectObj();

		if (operate_obj == null) 
		{
			this.StatusTips ("错误提示", "发生异常！请检测问题", "确认");
			return;
		}
		List<GameObject> need_remove = new List<GameObject> ();
		string replace_name = "";
		int child_length = operate_obj.transform.childCount;
		for (int i = 0; i < child_length; i++) 
		{
			Transform child = operate_obj.transform.GetChild(i);
			Transform trs_parent = child;
			string put_string = "替换 " + operate_obj.name + "/" + child.name;
			bool is_log = false;

			if (change_sub_path != "" && is_change_sub) 
			{
				trs_parent = child.Find (change_sub_path);
				put_string += "/" + change_sub_path ;
			}
			if (trs_parent == null) 
			{
				continue;
			}
			if (is_change_destroy) {
				if (is_change_not_com) 
				{
					put_string += "---->删除了";
					is_log = true;
					need_remove.Add (trs_parent.gameObject);
				} 
				else 
				{
					if (trs_parent.name == old_change_name) 
					{
						put_string += "---->删除了";
						is_log = true;
						need_remove.Add (trs_parent.gameObject);
					}
				}
			}

			if (is_change_not_com && !is_change_destroy) 
			{
				if (new_change_name != "") 
				{
					trs_parent.name = new_change_name;
					put_string += "---->新名称:" + new_change_name;
					is_log = true;
				}
			}

			if (!is_change_not_com && !is_change_destroy) 
			{
				if (new_change_name != "" && trs_parent.name == old_change_name) 
				{
					trs_parent.name = new_change_name;
					put_string += "---->新名称:" + new_change_name;
					is_log = true;
				}
			}

			if (is_remove_and_add_com && !is_change_destroy) {
				if (old_com != ComEnum.none) 
				{
					put_string += "---->删除组件:" + old_com.ToString();
					this.NewAndAddCom (old_com, trs_parent.gameObject, true);
					is_log = true;
				}
				if (new_com != ComEnum.none) 
				{
					put_string += "---->添加新组件:" + new_com.ToString();
					this.NewAndAddCom (new_com, trs_parent.gameObject);
					is_log = true;
				}
			}	

			if (is_log) 
			{
				replace_name += put_string + "\r\n";
			}
		}

		if (need_remove != null && need_remove.Count >= 0) 
		{
			for (int i = 0; i < need_remove.Count; i++)
			{
				if (need_remove[i] != null) {
					GameObject.DestroyImmediate (need_remove[i]);
				}
			}
		}
		need_remove.Clear ();

		if (replace_name == "") {
			this.StatusTips ("错误提示", "未找到任何符合名称的节点", "确认");
		} else {
			this.putlog (replace_name);
		}
	}
	#endregion



	private void StatusTips(string title, string content, string button_dec)
	{
		UnityEditor.EditorUtility.DisplayDialog(title, content, button_dec);
	}

    private GameObject[] GetSelectObjs()
    {
        if (Selection.gameObjects.Length <= 0)
        {
            return null;
        }

        return Selection.gameObjects;
    }

    private GameObject GetSelectObj()
	{
		GameObject operate_obj = null;

		if (Selection.gameObjects.Length > 0) 
		{
			operate_obj = Selection.gameObjects [0];
		}
		else if (Selection.gameObjects.Length > 1) 
		{
			this.StatusTips ("错误提示", "当前多选会触发多次，请暂时单选", "确认");
			return null;
		}
		else
		{
			this.StatusTips ("错误提示", "至少选中一个物体", "确认");
			return null;
		}
		if (operate_obj == null) 
		{
			this.StatusTips ("错误提示", "发生异常！请检测问题", "确认");
			return null;
		}
		return operate_obj;
	}

	private void NewAndAddCom(ComEnum add_type, GameObject new_obj, bool is_remove = false)
	{
		if (new_obj == null) 
		{
			return;
		}

		switch (add_type) 
		{
		case ComEnum.none:
			break;
		case ComEnum.ui_button:
			if (is_remove) 
			{
				this.TryRemoveCom<Button> (new_obj);
				this.TryRemoveCom<CanvasRenderer> (new_obj);
			}
			else 
			{
				if (new_obj.GetComponent<Button>() == null) {
					new_obj.AddComponent<Button> ();
				}
			}
			break;
		case ComEnum.ui_image:
			if (is_remove) 
			{
				this.TryRemoveCom<Image> (new_obj);
				this.TryRemoveCom<CanvasRenderer> (new_obj);
			}
			else
			{
				if (new_obj.GetComponent<Image>() == null) {
					new_obj.AddComponent<Image> ();
				}
			}
			break;
		case ComEnum.ui_rawimage:
			if (is_remove) 
			{
				this.TryRemoveCom<RawImage> (new_obj);
				this.TryRemoveCom<CanvasRenderer> (new_obj);
			}
			else
			{
				if (new_obj.GetComponent<RawImage>() == null) {
					new_obj.AddComponent<RawImage> ();
				}
			}
			break;
		case ComEnum.ui_slider:
			if (is_remove) 
			{
				this.TryRemoveCom<Slider> (new_obj);
				this.TryRemoveCom<CanvasRenderer> (new_obj);
			}
			else
			{
				if (new_obj.GetComponent<Slider>() == null) {
					new_obj.AddComponent<Slider> ();
				}
			}
			break;
		case ComEnum.ui_text:
			if (is_remove)
			{
				this.TryRemoveCom<Text> (new_obj);
				this.TryRemoveCom<CanvasRenderer> (new_obj);
			}
			else
			{
				if (new_obj.GetComponent<Text>() == null) {
					new_obj.AddComponent<Text> ();
				}
			}
			break;
		default:
			break;
		}
	}

	//尝试删除组件
	private void TryRemoveCom<T>(GameObject obj) where T : UnityEngine.Object
	{
		T com = obj.GetComponent<T> ();
		if (com != null) 
		{
			GameObject.DestroyImmediate (com, false);
		}
	}

	private void putlog(string str)
	{
		Debug.Log(str);
		TextEditor te = new TextEditor();
		te.text = str;
		te.SelectAll();
		te.Copy();
		UnityEditor.EditorUtility.DisplayDialog("提示", "已转换完成，并把操作结果复制至剪切板", "确认");
	}
}
