﻿using Nirvana.Editor;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using Build;
using System.Runtime.InteropServices;
using UnityEngine.Networking;
using System.Collections;

class DeployToolForBuildAndroid : DeployTool
{
    [MenuItem("自定义工具/发布版本/安卓/一键加密/Release")]
    public static void OneKeyEncryptAndroidReleaseABForBuildForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyEncryptAndroidForBuild(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/一键加密/Dev")]
    public static void OneKeyEncryptAndroidDevABForBuildForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyEncryptAndroidForBuild(false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/一键解密/Release")]
    public static void OneKeyDecryptAndroidReleaseABForBuildForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForAndroidBuild(buildPlatType: BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/一键解密/Dev")]
    public static void OneKeyDecryptAndroidDevABForBuildForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForAndroidBuild(buildPlatType: BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/一键超级打包(解密资源-打包资源-加密资源)/Release")]
    public static void OneKeyBuildForAndroidFlRelease()
    {
        OneKeyBuildForAndroid(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/一键超级打包(解密资源-打包资源-加密资源)/Dev")]
    public static void OneKeyBuildForAndroidFlDev()
    {
        OneKeyBuildForAndroid(false, BuildPlatType.AndroidDev);
    }

    public static void OneKeyBuildForAndroid(bool isCallByPy, BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        if (OneKeyDecryptForAndroidBuild(isCallByPy, buildPlatType))
        {
            if (BuildAssets(buildPlatType))
            {
                OneKeyEncryptAndroidForBuild(isCallByPy, buildPlatType);
            }
            else
            {
                Debug.LogError("一键超级打包失败，请查看日志!!!");
            }
        }
        else
        {
            Debug.LogError("一键超级打包失败，解密资源出错!!!");
        }
    }

    public static void OneKeyEncryptAndroidForBuild(bool isCallByPy, BuildPlatType buildPlatType)
    {
        try
        {
            //偏移加密AssetBundle
            if (!DeployToolForEncrypt.EncryptAssetBundle(isCallByPy, buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAssetBundle");
                return;
            }
            ReportBuildLog("EncryptAssets, DeployToolForEncrypt.EncryptAssetBundle");

            if (!DeployToolForEncrypt.EncryptAndZipVersionFile(buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");
                return;
            }
            ReportBuildLog("EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");

            if (!DeployToolForEncrypt.EncryptLuaFiles("", isCallByPy, buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptLuaFiles");
                return;
            }
            ReportBuildLog("[Encrypt Success]EncryptAssets, DeployToolForEncrypt.EncryptLuaFiles");
        }
        catch (Exception ex)
        {
            Debug.LogError(ex);
            EditorUtility.ClearProgressBar();
            if (!isCallByPy)
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForBuild.OneKeyEncryptAndroidForBuild, Error Msg:" + ex.ToString());
            }
        }
        EditorUtility.ClearProgressBar();
    }

    public static bool OneKeyDecryptForAndroidBuild(bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        if (!DeployToolForEncrypt.DecryptLuaFiles("", isCallByPy, buildPlatType))
        {
            ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.DecryptLuaFiles");
            return false;
        }
        if (!DeployToolForEncrypt.EncryptAndZipVersionFile(buildPlatType))
        {
            ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");
            return false;
        }
        if (!DeployToolForEncrypt.DecryptAssetBundle(isCallByPy, buildPlatType))
        {
            ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.DecryptAssetBundle");
            return false;
        }
        ReportBuildLog("EncryptAssets, OneKeyDecryptForAndroidBuild");
        return true;
    }

    public static bool CallBatFile(string path, string fileName)
    {
        System.Diagnostics.Process proc = null;
        try
        {
            string targetDir = string.Format(@"{0}", path);
            proc = new System.Diagnostics.Process();
            proc.StartInfo.WorkingDirectory = targetDir;
            proc.StartInfo.FileName = fileName;
            proc.StartInfo.Arguments = string.Format("20");
            proc.Start();
            proc.WaitForExit();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception Occurred :{0},{1}", ex.Message, ex.StackTrace.ToString());
            return false;
        }
        return true;
    }

    /// <summary>
    /// 打包工具调用检测资源接口
    /// </summary>
    public static void OneKeyExecuteCheckForAndroidCallByPy()
    {
        ReportBuildLog("[ResourceCheckSuccess]Start Check Resource");
        AssetsCheck.ModelRoundedAnalysis analysis = new AssetsCheck.ModelRoundedAnalysis();
        if (analysis.ExecuteCheck(true))
        {
            ReportBuildLog("[ResourceCheckSuccess]Resource Tested Successfully");
        }
        else
        {
            ReportBuildLog("[ResourceCheckError]Check file client/u3d_proj/AssetsCheckModelRoundedAnalysis.txt");
        }
    }

    /// <summary>
    /// 打包工具调用接口-返利版本
    /// </summary>
    public static void OneKeyBuildForAndroidCallByPy()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForAndroidBuild(true);
        BuildAssets(BuildPlatType.Android);
        OneKeyEncryptAndroidForBuild(true, BuildPlatType.Android);
    }

    /// <summary>
    /// 打包工具调用接口-返利版本
    /// </summary>
    public static void OneKeyBuildForAndroidDevCallByPy()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForAndroidBuild(true, BuildPlatType.AndroidDev);
        BuildAssets(BuildPlatType.AndroidDev);
        OneKeyEncryptAndroidForBuild(true, BuildPlatType.AndroidDev);
    }

    public static void BuildDevAndroidByPy()
    {
        OneKeyBuildForAndroid(true, BuildPlatType.AndroidDev);
    }
}