using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System;

/// <summary>
/// 模型管理器
/// 负责模型的选择、加载、创建和管理
/// </summary>
public class SkillEditorModelManager
{
    #region 事件定义
    public event System.Action<int, string> OnModelChanged;
    public event System.Action OnModelCleared;
    #endregion

    #region 私有字段
    private int _modelType = 0;
    private int _tempModelType = 0;
    private int _modelResIdIndex = 0;
    private string _modelResId = "";
    private string _tempModelResId = "";
    private List<string> _modelResIds = new List<string>();
    private string[] _modelResIdArray;

    private Animator _animator;
    private List<Animator> _partGroupAnimators = new List<Animator>();
    private GameObject _modelContainer;
    #endregion

    #region 属性
    public int ModelType => _modelType;
    public string ModelResId => _modelResId;
    public Animator MainAnimator => _animator;
    public List<Animator> PartGroupAnimators => _partGroupAnimators;
    public bool HasValidModel => _animator != null;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化模型管理器
    /// </summary>
    public void Initialize()
    {
        _modelContainer = GameObject.Find(SkillEditorGUIConfig.MODEL_CONTAINER_NAME);
        if (_modelContainer == null)
        {
            Debug.LogError($"找不到模型容器: {SkillEditorGUIConfig.MODEL_CONTAINER_NAME}");
        }
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制模型选择界面
    /// </summary>
    public void DrawModelSelection()
    {
        SkillEditorGUI.DrawTitle("选择模型");
        
        EditorGUI.BeginChangeCheck();
        DrawModelTypeSelection();

        if (_modelType == 0)
        {
            ResetModelSelection();
            return;
        }

        DrawModelResourceSelection();
        HandleModelChange();

        if (EditorGUI.EndChangeCheck())
        {
            OnModelSelectionChanged();
        }
    }

    /// <summary>
    /// 绘制模型类型选择
    /// </summary>
    private void DrawModelTypeSelection()
    {
        GUILayout.BeginHorizontal();
        _modelType = EditorGUILayout.Popup("模型类型：", _modelType, SkillEditorEventDrawer.ModelTypeNames);
        GUILayout.EndHorizontal();
    }

    /// <summary>
    /// 绘制模型资源选择
    /// </summary>
    private void DrawModelResourceSelection()
    {
        GUILayout.BeginHorizontal();

        if (_tempModelType != _modelType)
        {
            RefreshModelResourceList();
        }

        DrawModelResourcePopup();
        GUILayout.EndHorizontal();
    }

    /// <summary>
    /// 绘制模型资源下拉框
    /// </summary>
    private void DrawModelResourcePopup()
    {
        switch (_modelType)
        {
            case (int)SkillEditorEventConfig.ModelType.Role:
                _modelResIdIndex = EditorGUIKit.Popup(_modelResIdIndex, SkillEditorEventDrawer.roleResIds);
                _modelResId = SkillEditorEventDrawer.roleResIds[_modelResIdIndex];
                break;
            case (int)SkillEditorEventConfig.ModelType.Zuoqi:
                _modelResIdIndex = EditorGUIKit.Popup(_modelResIdIndex, SkillEditorEventDrawer.fightMountResIds);
                _modelResId = SkillEditorEventDrawer.fightMountResIds[_modelResIdIndex];
                break;
            default:
                if (_modelResIdArray != null && _modelResIdArray.Length > 0)
                {
                    _modelResIdIndex = EditorGUIKit.Popup(_modelResIdIndex, _modelResIdArray);
                    _modelResId = _modelResIdArray[_modelResIdIndex];
                }
                break;
        }
    }
    #endregion

    #region 模型资源管理
    /// <summary>
    /// 刷新模型资源列表
    /// </summary>
    private void RefreshModelResourceList()
    {
        _tempModelResId = "";
        _modelResIdIndex = 0;
        _modelResIds.Clear();
        ClearModel();

        LoadModelResources();
        _modelResIdArray = _modelResIds.ToArray();
        _tempModelType = _modelType;
    }

    /// <summary>
    /// 加载模型资源
    /// </summary>
    private void LoadModelResources()
    {
        string[] guids = GetModelGuids();

        foreach (string guid in guids)
        {
            ProcessModelAsset(guid);
        }
    }

    /// <summary>
    /// 获取模型GUID数组
    /// </summary>
    private string[] GetModelGuids()
    {
        if (_modelType == (int)SkillEditorEventConfig.ModelType.Role)
        {
            return new string[] { };
        }

        if (SkillEditorEventDrawer.modelResPathMap.TryGetValue(_modelType, out string modelRoot))
        {
            return AssetDatabase.FindAssets("t:prefab", new string[] { modelRoot });
        }

        return new string[] { };
    }

    /// <summary>
    /// 处理模型资产
    /// </summary>
    private void ProcessModelAsset(string guid)
    {
        string asset = AssetDatabase.GUIDToAssetPath(guid);
        GameObject gameObject = AssetDatabase.LoadAssetAtPath(asset, typeof(GameObject)) as GameObject;

        if (gameObject?.GetComponentInChildren<ActorRender>() == null)
            return;

        if (ShouldIncludeModel(gameObject.name))
        {
            _modelResIds.Add(gameObject.name);
        }
    }

    /// <summary>
    /// 判断是否应该包含该模型
    /// </summary>
    private bool ShouldIncludeModel(string modelName)
    {
        if (string.IsNullOrEmpty(modelName)) return false;

        switch (_modelType)
        {
            case (int)SkillEditorEventConfig.ModelType.Boss:
                return modelName.Length > 0 && modelName.Substring(0, 1) == "8" && !modelName.Contains("_");
            case (int)SkillEditorEventConfig.ModelType.Monster:
                return modelName.Length > 0 && modelName.Substring(0, 1) == "2" && !modelName.Contains("_");
            case (int)SkillEditorEventConfig.ModelType.Wuhun:
                return modelName.Length > 4 && modelName.Substring(4, 1) != "0";
            default:
                return true;
        }
    }
    #endregion

    #region 模型创建和管理
    /// <summary>
    /// 处理模型变化
    /// </summary>
    private void HandleModelChange()
    {
        if (!_tempModelResId.Equals(_modelResId))
        {
            ClearModel();
            _tempModelResId = _modelResId;
        }

        if (_modelType != 0 && !string.IsNullOrEmpty(_modelResId) && _animator == null)
        {
            CreateModelInstance();
        }
        else if (_modelType == 0 || string.IsNullOrEmpty(_modelResId))
        {
            ClearModel();
        }
    }

    /// <summary>
    /// 创建模型实例
    /// </summary>
    private void CreateModelInstance()
    {
        if (_modelContainer == null) return;

        switch (_modelType)
        {
            case (int)SkillEditorEventConfig.ModelType.Role:
                CreateRoleModel();
                break;
            case (int)SkillEditorEventConfig.ModelType.Gundam:
                CreateGundamModel();
                break;
            default:
                CreateGenericModel();
                break;
        }
    }

    /// <summary>
    /// 创建角色模型
    /// </summary>
    private void CreateRoleModel()
    {
        string modelRoot = (_modelResId.Length > 0 && _modelResId.Substring(0, 1) == "1")
            ? "Assets/Game/Actors/Character/RoleMan"
            : "Assets/Game/Actors/Character/RoleWoman";

        if (_modelResId.Length > 8)
        {
            modelRoot = $"{modelRoot}/Realm";
        }

        string asset = $"{modelRoot}/{_modelResId}/{_modelResId}.prefab";
        CreateModelFromAsset(asset);
    }

    /// <summary>
    /// 创建通用模型
    /// </summary>
    private void CreateGenericModel()
    {
        if (!SkillEditorEventDrawer.modelResPathMap.TryGetValue(_modelType, out string modelRoot) ||
            string.IsNullOrEmpty(modelRoot))
            return;

        string folder = GetModelFolder();
        string asset = $"{modelRoot}/{folder}/{_modelResId}.prefab";

        if (!CreateModelFromAsset(asset))
        {
            Debug.LogError($"资源路径异常: {asset}");
        }
    }

    /// <summary>
    /// 获取模型文件夹名称
    /// </summary>
    private string GetModelFolder()
    {
        switch (_modelType)
        {
            case (int)SkillEditorEventConfig.ModelType.Wuhun:
                return _modelResId.Length > 0 ? _modelResId.Substring(0, _modelResId.Length - 1) + "1" : _modelResId;
            case (int)SkillEditorEventConfig.ModelType.Zuoqi:
            case (int)SkillEditorEventConfig.ModelType.ShuangshenTianshen:
                return _modelResId;
            default:
                return _modelResId;
        }
    }

    /// <summary>
    /// 从资产创建模型
    /// </summary>
    private bool CreateModelFromAsset(string assetPath)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
        if (prefab == null) return false;

        GameObject gameObject = UnityEngine.Object.Instantiate(prefab);
        gameObject.name = gameObject.name.Replace("(Clone)", "");
        gameObject.transform.SetParent(_modelContainer.transform, false);
        _animator = gameObject.GetComponent<Animator>();
        return true;
    }
    #endregion

    #region 高达模型特殊处理
    /// <summary>
    /// 创建高达模型
    /// </summary>
    private void CreateGundamModel()
    {
        var gundamData = ParseGundamData(_modelResId);
        CreateGundamParts(gundamData);
    }

    /// <summary>
    /// 解析高达数据
    /// </summary>
    private GundamData ParseGundamData(string weaponRes)
    {
        int intWeaponRes = int.Parse(weaponRes);
        int gundamSeq = (int)Math.Floor(intWeaponRes / 100000.0);
        int colorSeq = intWeaponRes % 100;
        int parentId = gundamSeq * 100000 + colorSeq;

        return new GundamData
        {
            ParentId = parentId,
            BodyRes = parentId,
            LArmRes = parentId + 1000,
            RArmRes = parentId + 2000,
            LLegRes = parentId + 3000,
            RLegRes = parentId + 4000,
            LWingRes = parentId + 5000,
            RWingRes = parentId + 6000,
            WeaponRes = intWeaponRes
        };
    }

    /// <summary>
    /// 创建高达部件
    /// </summary>
    private void CreateGundamParts(GundamData data)
    {
        AddGundamModel("body", data.ParentId, data.BodyRes);
        AddGundamModel("arm", data.ParentId, data.LArmRes);
        AddGundamModel("arm", data.ParentId, data.RArmRes);
        AddGundamModel("leg", data.ParentId, data.LLegRes);
        AddGundamModel("leg", data.ParentId, data.RLegRes);
        AddGundamModel("wing", data.ParentId, data.LWingRes);
        AddGundamModel("wing", data.ParentId, data.RWingRes);
        AddGundamModel("weapon", data.ParentId, data.WeaponRes);
    }

    /// <summary>
    /// 添加高达模型部件
    /// </summary>
    private void AddGundamModel(string partStr, int parentId, int res)
    {
        string asset = GetGundamAssetPath(partStr, parentId, res);

        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(asset);
        if (prefab == null)
        {
            Debug.LogError($"高达部件资源不存在: {asset}");
            return;
        }

        GameObject gameObject = UnityEngine.Object.Instantiate(prefab);
        gameObject.name = gameObject.name.Replace("(Clone)", "");
        gameObject.transform.SetParent(_modelContainer.transform, false);

        var animator = gameObject.GetComponent<Animator>();
        _partGroupAnimators.Add(animator);

        if (partStr == "body")
        {
            _animator = animator;
        }
    }

    /// <summary>
    /// 获取高达资产路径
    /// </summary>
    private string GetGundamAssetPath(string partStr, int parentId, int res)
    {
        return partStr == "weapon"
            ? $"{SkillEditorGUIConfig.GUNDAM_PATH}/{partStr}/{parentId}/{res}/{res}.prefab"
            : $"{SkillEditorGUIConfig.GUNDAM_PATH}/{partStr}/{parentId}/{res}.prefab";
    }

    /// <summary>
    /// 高达数据结构
    /// </summary>
    private struct GundamData
    {
        public int ParentId;
        public int BodyRes;
        public int LArmRes;
        public int RArmRes;
        public int LLegRes;
        public int RLegRes;
        public int LWingRes;
        public int RWingRes;
        public int WeaponRes;
    }
    #endregion

    #region 清理和重置
    /// <summary>
    /// 清理模型
    /// </summary>
    public void ClearModel()
    {
        ClearPartGroupAnimators();
        ClearMainAnimator();
        ClearModelActors();
        OnModelCleared?.Invoke();
    }

    /// <summary>
    /// 清理部件组动画器
    /// </summary>
    private void ClearPartGroupAnimators()
    {
        foreach (Animator animator in _partGroupAnimators)
        {
            if (animator != null)
            {
                UnityEngine.Object.DestroyImmediate(animator.gameObject);
            }
        }
        _partGroupAnimators.Clear();
    }

    /// <summary>
    /// 清理主动画器
    /// </summary>
    private void ClearMainAnimator()
    {
        if (_animator != null && _animator.gameObject != null)
        {
            UnityEngine.Object.DestroyImmediate(_animator.gameObject);
            _animator = null;
        }
    }

    /// <summary>
    /// 清理模型中的Actor组件
    /// </summary>
    private void ClearModelActors()
    {
        if (_modelContainer == null) return;

        Component[] actors = _modelContainer.GetComponentsInChildren<ActorRender>();
        if (actors == null || actors.Length == 0) return;

        for (int i = 0; i < actors.Length; i++)
        {
            UnityEngine.Object.DestroyImmediate(actors[i].gameObject);
        }
    }

    /// <summary>
    /// 重置模型选择状态
    /// </summary>
    private void ResetModelSelection()
    {
        _modelType = 0;
        _tempModelType = 0;
        _modelResIdIndex = 0;
        _modelResId = "";
        _tempModelResId = "";
        _modelResIds.Clear();
        _modelResIdArray = null;
        ClearModel();
    }

    /// <summary>
    /// 模型选择变化时的处理
    /// </summary>
    private void OnModelSelectionChanged()
    {
        if (!string.IsNullOrEmpty(_modelResId) && int.TryParse(_modelResId, out int prefabId))
        {
            OnModelChanged?.Invoke(prefabId, _modelResId);
        }
    }
    #endregion
}
