﻿using UnityEngine;
using LuaInterface;
using DG.Tweening;

public class CameraFollow2 : MonoBehaviour
{
	[HideInInspector]
    [NoToLua]
    public Transform target;
    public Transform Target
    {
        set
        {
            target = value;
            MoveToTarget();
        }
        get
        {
            return target;
        }
    }

    [SerializeField]
    [Tooltip("Addition fov.")]
    private float fieldOfView = 45;

    [SerializeField]
    [Toolt<PERSON>("The audio listener")]
    private AudioListener audioListener;

	[HideInInspector]
    public Vector3 TargetOffset = Vector3.zero;

	[HideInInspector]
    public float SmoothOffsetSpeed = 5;

	[HideInInspector]
    public bool AllowRotation = true;

    [HideInInspector]
    public bool AllowXRotation = true;

    [HideInInspector]
    public bool AllowYRotation = true;

    [HideInInspector]
    public Vector2 OriginAngle = new Vector2(45, 10);

	[HideInInspector]
	public Vector2 RotationSensitivity = new Vector2(5, 5);

	[HideInInspector]
    public float MinPitchAngle = 15;

	[HideInInspector]
    public float MaxPitchAngle = 55;

    [HideInInspector]
    public float MinYawAngle = -10;

    [HideInInspector]
    public float MaxYawAngle = 10;

	[HideInInspector]
    public float RotationSmoothing = 20;

	[HideInInspector]
    public bool AutoSmoothing = true;

	[HideInInspector]
    public bool AllowZoom = true;

	[HideInInspector]
    public float Distance = 9;

	[HideInInspector]
    public float MaxDistance = 13;

	[HideInInspector]
    public float MinDistance = 1;

	[HideInInspector]
    public float ZoomSmoothing = 10;


    private Transform cachedTransform;
    private Transform cameraTransform;

    private float oldDistance;
    private Quaternion oldRotation;
    private Vector2 angle;
    private Vector3 currentOffset;

    private Transform focalPoint;

    private Vector3 cameraLocalPosition = Vector3.zero;

    public AudioListener AudioListener
    {
        get
        {
            return audioListener;
        }
        set
        {
            audioListener = value;
        }
    }


    /// <summary>
    /// Gets or sets the filed of view.
    /// </summary>
    public float FieldOfView
    {
        get
        {
            return this.fieldOfView;
        }

        set
        {
            if (this.fieldOfView != value)
            {
                this.fieldOfView = value;
                this.SyncFieldOfView();
            }
        }
    }

    public void SyncFieldOfView()
    {
        Camera camera = this.GetComponentInChildren<Camera>();
        if (null != camera)
        {
            camera.fieldOfView = this.fieldOfView;
        }
    }

    /// <summary>
    /// Do animation.
    /// </summary>
    public Tweener DOFieldOfView(float endValue, float duration)
    {
        return DOTween.To(
            () => this.fieldOfView,
            v =>
            {
                this.fieldOfView = v;
                this.SyncFieldOfView();
            },
            endValue,
            duration);
    }

    private void Start()
    {
        cachedTransform = transform;

        CreateFocalPoint();

        angle = OriginAngle;
        Quaternion cameraRotation = Quaternion.Euler(angle.x, angle.y, 0);

		cachedTransform.position = focalPoint.position - cameraRotation * Vector3.forward * Distance;
		cachedTransform.LookAt(focalPoint.position, Vector3.up);

		oldRotation = cameraRotation;

        var camera = GetComponentInChildren<Camera>();
        if (camera)
        {
            cameraTransform = camera.transform;
            cameraLocalPosition = cameraTransform.localPosition;
        }
    }

    private void Update()
    {
        if (target == null)
        {
            return;
        }

        
        var targetTransform = target.transform;
        var offset = targetTransform.rotation * TargetOffset;

        currentOffset = Vector3.Lerp(currentOffset, offset, 5 * Time.deltaTime);

        focalPoint.position = targetTransform.position + currentOffset;
    }

    private void LateUpdate()
    {
        if (target == null)
        {
            return;
        }

        var targetQuat = Quaternion.Euler(angle.x, angle.y, 0);
        var nowQuat = Quaternion.Slerp(oldRotation, targetQuat, Time.deltaTime * RotationSmoothing * 0.5f);
        oldRotation = nowQuat;

        var currentDistance = (Distance - oldDistance) * Time.deltaTime * ZoomSmoothing * 0.5f + oldDistance;

        var diffTrans = nowQuat * Vector3.forward * currentDistance;
        var newPos = focalPoint.position - diffTrans;

        oldDistance = currentDistance;
        cachedTransform.position = newPos;
        cachedTransform.LookAt(focalPoint.position);

        if (cameraTransform != null)
        {
            cameraTransform.localPosition = cameraLocalPosition;

            // 相机位置
            var pos = cameraTransform.position;
            var height = pos.y;
            pos.y = 1000;

            RaycastHit hit;
            if (Physics.Raycast(pos, Vector3.down, out hit, 2000, EditorSupport.LayerMask.Walkable))
            {
                var collideHeight = hit.point.y + 0.5f;
                if (collideHeight >= height)
                {
                    height = collideHeight;
                }
            }
            pos.y = height;
            cameraTransform.position = pos;
        }

        if (this.target != null && this.audioListener != null)
        {
            this.audioListener.transform.position = this.target.position;
        }
    }

    public void Swipe(float x, float y)
    {
        if (!AllowRotation)
        {
            return;
        }

        if (AllowXRotation)
        {

            angle.x += -y * RotationSensitivity.x * 0.1f;
            angle.x = Mathf.Clamp(angle.x, MinPitchAngle, MaxPitchAngle);
        }

        if (AllowYRotation)
        {
            angle.y += x * RotationSensitivity.y * 0.1f;
            // angle.y = Mathf.Clamp(angle.y, OriginAngle.y + MinYawAngle, OriginAngle.y + MaxYawAngle);
        }
    }

    public void Pinch(float delta)
    {
        if (!AllowZoom)
        {
            return;
        }

        Distance = Distance + delta * -0.03f;
        Distance = Mathf.Clamp(Distance, MinDistance, MaxDistance);
    }

    public void SyncImmediate()
    {
        if (target == null)
        {
            return;
        }

        MoveToTarget();
    }

    public void SyncRotation()
    {
        var targetQuat = Quaternion.Euler(angle.x, angle.y, 0);
        oldRotation = targetQuat;
    }

    public void ClampRotationAndDistance()
    {
        Distance = Mathf.Clamp(Distance, MinDistance, MaxDistance);
        angle.x = Mathf.Clamp(angle.x, MinPitchAngle, MaxPitchAngle);
        // angle.y = Mathf.Clamp(angle.y, OriginAngle.y + MinYawAngle, OriginAngle.y + MaxYawAngle);
    }

    private float GetHeight(Vector3 pos)
    {
        float originHeight = pos.y;
        RaycastHit hit;
        pos.y = 1000;

        if (Physics.Raycast(pos, Vector3.down, out hit, 2000, EditorSupport.LayerMask.Walkable))
        {
            return hit.point.y;
        }
        else
        {
            return originHeight;
        }
    }

    private void CreateFocalPoint()
	{
		GameObject go = new GameObject();
		go.name = "CamerafocalPoint";
		focalPoint = go.transform;

		MoveToTarget();
	}

	public void MoveToTarget()
	{
		if(target != null)
		{
			focalPoint.position = target.position + target.rotation * TargetOffset;
		}
	}

    public static CameraFollow2 Bind(GameObject go)
    {
        var CameraFollow2 = go.GetComponent<CameraFollow2>() ?? go.AddComponent<CameraFollow2>();
        return CameraFollow2;
    }
}
