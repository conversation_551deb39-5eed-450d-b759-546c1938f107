﻿using UnityEngine;
using System.Collections.Generic;
using Nirvana;

public enum CameraCullingDisType
{
    Cull30,
    Cull50,
    Cull70,
    Cull90,
    Cull120,
    Cull150,
    Cull180,
    Cull200,
    <PERSON>ull220,
    <PERSON>ull240,
    <PERSON>ull260,
    <PERSON><PERSON>280,
    <PERSON>ull300,
    <PERSON><PERSON><PERSON><PERSON>,
    MAX,
}

public class CameraCullingDistance : MonoBehaviour
{
    public float[] cullDistances = new float[32];
    private Camera _camera;

    void Start()
    {
        _camera = GetComponent<Camera>();
        this.UpdateDistances();
    }

    public void UpdateDistances()
    {
        SceneOptimizeMgr.SetCullDistances(cullDistances);
    }

    public void SetDistance(int layer, float distance)
    {
        if (distance < 0 || layer < 0 || layer >= cullDistances.Length)
        {
            return;
        }

        cullDistances[layer] = distance;
        UpdateDistances();
    }
}
