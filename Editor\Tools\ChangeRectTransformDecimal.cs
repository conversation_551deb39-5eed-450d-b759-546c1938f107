using System;
using UnityEditor;
using UnityEngine;

namespace Assets.Game.Scripts.Editor.Tools
{
	class ChangeRecttransformDecimal : BaseEditorWindow
	{
		[MenuItem("Assets/技术专用/UI/修正RectTransform小数")]
		private static void FixedAssetsSelection()
		{
			FixedSelection();
		}

		[MenuItem("GameObject/技术专用/UI/修正RectTransform小数")]
		private static void FixedGameObjectSelection()
		{
			FixedSelection(true);
		}

		public static void FixedSelection(bool is_check_prefab = false)
		{
			ChangeRecttransformDecimal filter = new ChangeRecttransformDecimal();
			GameObject[] objs = Selection.gameObjects;
			int count = objs.Length;
			int index = 0;
			string path = "";
			GameObject gameObjec;
			try
			{
				foreach (var obj in objs)
				{
					gameObjec = obj;

					if (is_check_prefab)
					{
						gameObjec = PrefabUtility.GetCorrespondingObjectFromSource(obj);
					}

					path = AssetDatabase.GetAssetPath(gameObjec);

					if (!path.StartsWith("Assets/Game/UIs/View"))
					{
						continue;
					}

					gameObjec = GameObject.Instantiate(gameObjec);
					filter.FixGameobject(gameObjec);
					PrefabUtility.SaveAsPrefabAsset(gameObjec, path);
					GameObject.DestroyImmediate(gameObjec);

					index++;
					EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
				}
			}
			catch (Exception ex)
			{
				EditorUtility.ClearProgressBar();
				Debug.LogErrorFormat("Exception:{0}", ex.ToString());
			}
			EditorUtility.ClearProgressBar();
		}


		[MenuItem("Tools/换UI工具/修正所有UI预制体RectTransform小数")]
		private static void ShowWindow()
		{
			EditorWindow.GetWindow<ChangeRecttransformDecimal>(false, "修正所有UI预制体RectTransform小数");
		}

		private void OnGUI()
		{
			GUILayout.Space(100);
			if (GUILayout.Button("Change All"))
			{
				OnFilterExecute();
			}

		}

		// 指定要检查的文件夹
		private string[] checkDirs = { "Assets/Game/UIs/View" };
		private void OnFilterExecute(string[] _checkDirs = null)
		{
			if (null == _checkDirs)
			{
				_checkDirs = this.checkDirs;
			}

			string[] guids = AssetDatabase.FindAssets("t:prefab", _checkDirs);
			int curIndex = 0;
			int assetCount = guids.Length;
			foreach (string guid in guids)
			{
				string path = AssetDatabase.GUIDToAssetPath(guid);
				GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
				this.FixGameobject(gameobj);

				curIndex++;
				EditorUtility.DisplayProgressBar("正在处理中，路径：" + path,
					   string.Format("{0} / {1}", curIndex, assetCount), (float)curIndex / (float)assetCount);
			}

			EditorUtility.ClearProgressBar();
			AssetDatabase.Refresh();
			AssetDatabase.SaveAssets();
		}

		private void FixGameobject(GameObject gameobj)
		{
			if (gameobj == null)
			{
				return;
			}

			RectTransform[] rectComponents = gameobj.GetComponentsInChildren<RectTransform>(true);
			for (int i = 0; i < rectComponents.Length; i++)
			{
				RectTransform rect = rectComponents[i];
				FixRectTransform(rect);
			}
		}

		private void FixRectTransform(RectTransform rectTransform)
		{

			rectTransform.localPosition = new Vector3(Round(rectTransform.localPosition.x),
													  Round(rectTransform.localPosition.y),
													  Round(rectTransform.localPosition.z));
			rectTransform.anchoredPosition = new Vector3(Round(rectTransform.anchoredPosition.x),
														 Round(rectTransform.anchoredPosition.y));

			rectTransform.sizeDelta = new Vector2(Round(rectTransform.sizeDelta.x),
												  Round(rectTransform.sizeDelta.y));

			rectTransform.anchorMin = new Vector2(RoundAnchor(rectTransform.anchorMin.x),
												  RoundAnchor(rectTransform.anchorMin.y));

			rectTransform.anchorMax = new Vector2(RoundAnchor(rectTransform.anchorMax.x),
												  RoundAnchor(rectTransform.anchorMax.y));

			rectTransform.pivot = new Vector2(RoundAnchor(rectTransform.pivot.x),
											  RoundAnchor(rectTransform.pivot.y));

			rectTransform.localEulerAngles = new Vector3(Round(rectTransform.localEulerAngles.x),
														   Round(rectTransform.localEulerAngles.y),
														   Round(rectTransform.localEulerAngles.z));

			rectTransform.localScale = new Vector3(Round(rectTransform.localScale.x, 2),
												   Round(rectTransform.localScale.y, 2),
												   Round(rectTransform.localScale.z, 2));

		}

		private float Round(float v, int decimal_num = 0)
		{
			bool isNegative = false;
			if (v < 0)
			{
				isNegative = true;
				v = -v;
			}
			double Int = Math.Round(v, decimal_num, MidpointRounding.AwayFromZero);
			v = isNegative ? -(float)Int : (float)Int;

			return v;
		}

		private float RoundAnchor(float v)
		{
			v = v > 1 ? 1 : v;
			v = v < -1 ? -1 : v;
			v = (0.75 <= v && v <= 1) ? 1 : v;
			v = (0.25 <= v && v < 0.75) ? 0.5f : v;
			v = (-0.25 <= v && v < 0.25) ? 0 : v;
			v = (-0.75 <= v && v < -0.25) ? -0.5f : v;
			v = (-1 <= v && v < -0.75) ? -1 : v;
			return v;
		}

	}
}