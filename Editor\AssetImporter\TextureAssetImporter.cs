﻿using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System;
using System.Text.RegularExpressions;
using System.Text;
using UnityEngine.Experimental.Rendering;

public class TextureAssetImporter : AssetPostprocessor
{
    public static readonly string GameDir = "Assets/Game/";
    public static readonly string UiDir = "Assets/Game/UIs/";
    private static readonly string ViewDir = "Assets/Game/UIs/View/";
    public static readonly string ImagesDir = "Assets/Game/UIs/Images";
    public static readonly string MainUIDir = "Assets/Game/UIs/View/main_ui/Images/";
    public static readonly string FontDir = "Assets/Game/UIs/Fonts";
    private static readonly string FontAtlasDir = "Assets/Game/UIs/Fonts/FontAtlas.png";
    public static readonly string ArtFontDir = "Assets/Game/UIs/TMP/ArtFont";
    private static readonly string RawImageDir = "Assets/Game/UIs/RawImages";
    private static readonly string EffectsDir = "Assets/Game/Effects";
    private static readonly string ActorDir = "Assets/Game/Actors/";
    private static readonly string ModelDir = "Assets/Game/Model";
    private static readonly string EffectsTexUIDir = "Assets/Game/Effects/Texture/UI";
    private static readonly string SceneEnviromentDir = "Assets/Game/Environments/";
    private static readonly string ShaderDir = "Assets/Game/Shaders";
    private static readonly string ActorSharedDir = "Assets/Game/Actors/Shared";
    public static readonly string UiSceneDir = "Assets/Game/Misc/Texture/UI_Scene/";

    // nopack
    public static readonly string Nopack = "/nopack/";
    public static readonly string IconsDir = "Assets/Game/UIs/Icons/";
    public static readonly string ItemIconDir = IconsDir + "Item/";
    public static readonly string TitleIconDir = IconsDir + "Title/";
    public static readonly string BossIconDir = IconsDir + "Boss/";
    public static readonly string IconTouXiangBig = IconsDir + "toxiang_big/";
    public static readonly string FunctionSkill = "/Icon/FunctionSkill/";
    public static readonly string MainSkill = "/Icon/MainSkill/";
    public static readonly string CultivationStage = "/Icon/CultivationStage/";

    // other
    public static readonly string EmojiInputDir = "Assets/Emoji";
    private static readonly string EmojiDir = UiDir + "Emoji";
    public static readonly string HUDTexInputDir = "Assets/ThirdParty/HUDPackage/Texture";
    public static readonly string HUDTexDir = UiDir + "HUDProgramme/Atlas";
    public static readonly string AppearanceDyeDir = "Assets/Game/UIs/Images/nopack/a3_sz_se_lun";

    private const string DeployDir = "Assets/Game/Deploy/";
    private static readonly string T4MFolder = "Assets/T4M/";

    private static HashSet<string> tempIgnoreAssets = new HashSet<string>();

    // 正则匹配
    private static readonly string suffixMatch = ".*(_{0}[\\._]).*";            // 命名后缀正则式
    private const string CONFIG_PATH = "Assets/Config/TextureConfig.json";
    
    private static bool textureEditing = false;  

    private bool texDataIsChange = false;
    [System.Serializable]
    public class RootConfig
    {
        public List<RuleGroup> Rules;
    }

    [System.Serializable]
    public class RuleGroup
    {
        public string Name;
        public List<Rule> Rules;
    }

    [System.Serializable]
    public class Rule
    {
        public string Name;
        public List<string> PathList;
        public string KeyWord;
        public int TextureType;
        public int AlphaSource;
        public bool sRGB;
        public int NonPowOf2 = 1;
        public bool ReadWriteEnable;
        public bool GenerateMipMaps;
        public int WrapMode;
        public int FilterMode;
        public int MaxSize;
        public int Format;
        public int CompressorQuality = 1; 
        public bool NotPlatformOverride;
        public int PCMaxSize;
        public int PCFormat;
    }

    private static RootConfig cachedConfig = null;
    public static bool configLoaded = false;
    private static RootConfig LoadConfig()
    {
        if (!configLoaded)
        {
            if (File.Exists(CONFIG_PATH))
            {
                string configText = File.ReadAllText(CONFIG_PATH, Encoding.UTF8);
                cachedConfig = JsonUtility.FromJson<RootConfig>(configText);
            }
            else
            {
                cachedConfig = new RootConfig();
                Debug.LogError("svn更新Asset目录");
            }
            configLoaded = true;
        }
        return cachedConfig;
    }

    public static void ReloadRootConfig()
    {
        if (File.Exists(CONFIG_PATH))
        {
            string configText = File.ReadAllText(CONFIG_PATH, Encoding.UTF8);
            cachedConfig = JsonUtility.FromJson<RootConfig>(configText);
        }
    }

    private void OnPreprocessTexture()
    {
        if (textureEditing)
        {
            return;
        }
        
        TextureImporter textureImporter = (TextureImporter)assetImporter;
        if (HideFlags.NotEditable == textureImporter.hideFlags)
        {
            return;
        }

        if (tempIgnoreAssets.Contains(assetPath))
        {
            return;
        }
        
        if (ImporterUtils.CheckLabel(textureImporter))
        {
            return;
        }
        
        if (textureImporter.assetPath.Contains(ActorSharedDir))
        {
            return;
        }
        
        texDataIsChange = false;
        if (!ProcessByTextureConfig())
        {
            // 如果没有贴图设置配置，执行旧逻辑
            ProcessTextureType(textureImporter);
            ProcessSRGBSwitch(textureImporter);
            ProcessReadable(textureImporter, assetPath);
            ProcessFilterMode(textureImporter);
            ProcessAnislLevel(textureImporter);
            ProcessAdvancedAndWrapMode(textureImporter);
            ProcessPlatformSetting(textureImporter);
            ProcessNonPowerOf2(textureImporter);
            ProcessMipmap(textureImporter);
        }

        SpriteAtlasAssetImporter.TryCreateSpriteAtlas(textureImporter);
    }

    private bool MatchesAny(string path, List<string> pathList)
    {
        return pathList.Exists(pattern => path.StartsWith(pattern));
    }

    private bool ProcessByTextureConfig()
    {
        var rules = LoadConfig();
        if (rules == null || rules.Rules == null)
        {
            return false;
        }

        string fileName = Path.GetFileName(assetPath);
        foreach (var group in rules.Rules)
        {
            foreach (var rule in group.Rules)
            {
                if (MatchesAny(assetPath, rule.PathList))
                {
                    if (Regex.IsMatch(fileName, rule.KeyWord, RegexOptions.IgnoreCase))
                    {
                        ApplyImportSettings(rule);
                        return true;
                    }
                }
            }
        }

        if (assetPath.StartsWith(ActorDir) || assetPath.StartsWith(ModelDir))
        {
            Debug.LogErrorFormat("此贴图命名有误{0}，或需要添加贴图规则", assetPath);
        }

        return false;
    }

    private void ApplyImportSettings(Rule rule)
    {
        if (rule == null) return;

        TextureImporter importer = (TextureImporter)assetImporter;
        var textureType = (TextureImporterType)rule.TextureType;
        if(importer.textureType != textureType)
        {
            texDataIsChange = true;
            importer.textureType = textureType;
        }

        var alphaSource = (TextureImporterAlphaSource)rule.AlphaSource;
        if (importer.alphaSource != alphaSource)
        {
            texDataIsChange = true;
            importer.alphaSource = alphaSource;
        }
        
        if(importer.sRGBTexture != rule.sRGB)
        {
            texDataIsChange = true;
            importer.sRGBTexture = rule.sRGB;
        }

        importer.npotScale = (TextureImporterNPOTScale)rule.NonPowOf2;

        if (importer.isReadable != rule.ReadWriteEnable)
        {
            texDataIsChange = true;
            importer.isReadable = rule.ReadWriteEnable;
        }
        
        if(importer.mipmapEnabled != rule.GenerateMipMaps)
        {
            texDataIsChange = true;
            importer.mipmapEnabled = rule.GenerateMipMaps;
        }

        if (importer.streamingMipmaps != rule.GenerateMipMaps)
        {
            texDataIsChange = true;
            importer.streamingMipmaps = rule.GenerateMipMaps;
        }

        var wrapMode = (TextureWrapMode)rule.WrapMode;
        if (importer.wrapMode != wrapMode) {
            texDataIsChange = true;
            importer.wrapMode = wrapMode;
        }
        
        var filterMode = (FilterMode)rule.FilterMode;
        if (importer.filterMode != filterMode) {
            texDataIsChange = true;
            importer.filterMode = filterMode;
        }

        SetPlatformSetting(importer, rule);
        if (texDataIsChange)
        {
            importer.SaveAndReimport();
        }
    }

    // 旧
    private void SetPlatformSetting(TextureImporter textureImporter, TextureImporterFormat mobileFormat, int maxSize, bool notOverride = false)
    {
        var defaultSettings = textureImporter.GetDefaultPlatformTextureSettings();
        
        // 诡异情况导致默认平台出现override，进而导致导入器错误
        var alpha8 = defaultSettings.format == TextureImporterFormat.Alpha8 ||
                     textureImporter.textureType == TextureImporterType.SingleChannel;
        TextureImporterFormat pcFormat;
        if (alpha8)
        {
            pcFormat = TextureImporterFormat.Alpha8;
            mobileFormat = TextureImporterFormat.Alpha8;
        }
        else
        {
            pcFormat = TextureImporterFormat.RGBA32;// IsSpineTexture(assetPath) ? TextureImporterFormat.RGBA32 : TextureImporterFormat.DXT5;
        }

        if (defaultSettings.maxTextureSize < 2048)
            defaultSettings.maxTextureSize = 2048;

        defaultSettings.overridden = notOverride;
        defaultSettings.textureCompression = TextureImporterCompression.CompressedHQ;
        defaultSettings.format = notOverride ? mobileFormat : TextureImporterFormat.Automatic;
        textureImporter.SetPlatformTextureSettings(defaultSettings);

        var standaloneSettings = textureImporter.GetPlatformTextureSettings("Standalone");
        if (SetTextureFormat(standaloneSettings, pcFormat, notOverride) | SetTextureMaxSize(standaloneSettings, maxSize))
            textureImporter.SetPlatformTextureSettings(standaloneSettings);

        var iosSettings = textureImporter.GetPlatformTextureSettings("iPhone");
        if (SetTextureFormat(iosSettings, mobileFormat,notOverride) | SetTextureMaxSize(iosSettings, maxSize))
            textureImporter.SetPlatformTextureSettings(iosSettings);

        var androidSettings = textureImporter.GetPlatformTextureSettings("Android");
        if (SetTextureFormat(androidSettings, mobileFormat, notOverride) | SetTextureMaxSize(androidSettings, maxSize))
            textureImporter.SetPlatformTextureSettings(androidSettings);
    }

    // 走配置
    private void SetPlatformSetting(TextureImporter textureImporter, Rule rule)
    {
        if (textureImporter == null || rule == null) return;
        TextureImporterFormat mobileFormat = (TextureImporterFormat)rule.Format;
        TextureImporterFormat pcFormat = (TextureImporterFormat)rule.PCFormat;
        int mobileMaxSize = rule.MaxSize;
        int pcMaxSize = rule.PCMaxSize;
        bool notOverride = rule.NotPlatformOverride;
        int compressorQuality = rule.CompressorQuality;

        var defaultSettings = textureImporter.GetDefaultPlatformTextureSettings();
        // 诡异情况导致默认平台出现override，进而导致导入器错误
        var alpha8 = defaultSettings.format == TextureImporterFormat.Alpha8 ||
                     textureImporter.textureType == TextureImporterType.SingleChannel;
        
        if (alpha8)
        {
            pcFormat = TextureImporterFormat.Alpha8;
            mobileFormat = TextureImporterFormat.Alpha8;
        }

        if (defaultSettings.maxTextureSize < 2048)
            defaultSettings.maxTextureSize = 2048;

        defaultSettings.overridden = notOverride;
        defaultSettings.textureCompression = TextureImporterCompression.CompressedHQ;
        defaultSettings.format = notOverride ? mobileFormat : TextureImporterFormat.Automatic;
        textureImporter.SetPlatformTextureSettings(defaultSettings);

        var standaloneSettings = textureImporter.GetPlatformTextureSettings("Standalone");
        if (SetTextureFormat(standaloneSettings, pcFormat, notOverride) | SetTextureMaxSize(standaloneSettings, pcMaxSize))
            textureImporter.SetPlatformTextureSettings(standaloneSettings);

        var iosSettings = textureImporter.GetPlatformTextureSettings("iPhone");
        if (SetTextureFormat(iosSettings, mobileFormat, notOverride, compressorQuality) | SetTextureMaxSize(iosSettings, mobileMaxSize))
            textureImporter.SetPlatformTextureSettings(iosSettings);

        var androidSettings = textureImporter.GetPlatformTextureSettings("Android");
        if (SetTextureFormat(androidSettings, mobileFormat, notOverride, compressorQuality) | SetTextureMaxSize(androidSettings, mobileMaxSize))
            textureImporter.SetPlatformTextureSettings(androidSettings);
    }

    private bool SetTextureFormat(TextureImporterPlatformSettings settings, TextureImporterFormat format, bool notOverride = false, int compressorQualityLevel = 1)
    {
        int compressorQuality = compressorQualityLevel switch
        {
            0 => 0,
            1 => 50,
            2 => 100,
        };
        
        var dirty = settings.format != format || settings.overridden != !notOverride || settings.compressionQuality != compressorQuality;
        if (dirty)
        {
            texDataIsChange = true;
            settings.format = format;
            settings.overridden = !notOverride;
            settings.compressionQuality = compressorQuality;
        }

        return dirty;
    }

    // 设置最大分辨率
    private bool SetTextureMaxSize(TextureImporterPlatformSettings settings, int maxSize = 0)
    {
        if (maxSize <= 0)
            return false;

        var dirty = settings.maxTextureSize != maxSize;
        if (dirty)
        {
            texDataIsChange = true;
            settings.maxTextureSize = maxSize;
        }
        return dirty;
    }




    public static bool IsMiniMapTexture(string path)
    {
        var fileName = Path.GetFileNameWithoutExtension(path);
        // 注：可能有 minimap1 之类的命名
        return fileName.StartsWith("minimap", StringComparison.Ordinal);
    }

    public static bool HasKeyWordInName(string path, string keyword)
    {
        //return path.Contains("_" + keyword + ".", StringComparison.InvariantCultureIgnoreCase)
        //    || fileName.Contains("_" + keyword + "_", StringComparison.InvariantCultureIgnoreCase);
        string sm = string.Format(suffixMatch, keyword);
        Match mPath = Regex.Match(path, sm, RegexOptions.IgnoreCase);
        if (mPath.Success)
            return true;

        var fileName = Path.GetFileNameWithoutExtension(path);
        Match mfileName = Regex.Match(fileName, sm, RegexOptions.IgnoreCase);
        return mfileName.Success;
    }

    public static bool IsNormalMapTexture(string path)
    {
        return HasKeyWordInName(path, "DF") || HasKeyWordInName(path, "N");
    }

    public static bool IsSpineTexture(string path)
    {
        return path.Contains("SpineAnimations") || path.ToLower().Contains("spine");
    }

    private void OnPostprocessTexture(Texture2D texture)
    {
        if (textureEditing)
        {
            return;
        }
        
        if (ImporterUtils.CheckLabel(assetPath))
        {
            return;
        }

        TextureImporter textureImporter = (TextureImporter)assetImporter;
        if (tempIgnoreAssets.Contains(assetPath))
        {
            return;
        }

        if (textureImporter.assetPath.Contains(ActorSharedDir))
        {
            return;
        }

        ProcessResizeTextureToMutiple4(textureImporter, texture);
    }

    // 设置贴图的类型
    private void ProcessTextureType(TextureImporter textureImporter)
    {
        if (assetPath.StartsWith(FontDir)
            || assetPath.StartsWith(ViewDir)
            || assetPath.StartsWith(IconsDir)
            || assetPath.StartsWith(ImagesDir)
            || assetPath.StartsWith(HUDTexDir)
            || assetPath.StartsWith(ArtFontDir)
            || assetPath.StartsWith(EmojiInputDir))
        {
            if (IsSpineTexture(assetPath))
            {
                textureImporter.textureType = TextureImporterType.Default;
                textureImporter.sRGBTexture = true;
            }
            else
            {
                textureImporter.textureType = TextureImporterType.Sprite;
            }
        }
        else if (assetPath.StartsWith(RawImageDir)
            || assetPath.StartsWith(HUDTexInputDir)
            || IsNormalMapTexture(assetPath))
        {
            textureImporter.textureType = TextureImporterType.Default;
        }
    }

    private void ProcessMipmap(TextureImporter textureImporter)
    {
        if (!assetPath.StartsWith(GameDir))
        {
            return;
        }

        bool isMipMap = false;
        float mipmapBias = 0;
        if (assetPath.StartsWith(EffectsDir))
        {
            if (textureImporter.textureType == TextureImporterType.Default && !assetPath.StartsWith(EffectsTexUIDir))
            {
                isMipMap = true;
            }
        }
        else if (assetPath.StartsWith(SceneEnviromentDir))
        {
            isMipMap = true;
        }
        else if (assetPath.StartsWith(ShaderDir))
        {
            isMipMap = true;
        }

        if (textureImporter.textureShape != TextureImporterShape.Texture2D)
        {
            isMipMap = true;
        }

        if (HasKeyWordInName(assetPath, "shadowmask") || HasKeyWordInName(assetPath, "comp_light"))
        {
            isMipMap = true;
        }

        //有mip关键字不强制改
        if (HasKeyWordInName(assetPath, "mip"))
            return;

        if (textureImporter.mipmapEnabled != isMipMap || (isMipMap && textureImporter.mipMapBias != mipmapBias))
        {
            textureImporter.mipmapEnabled = isMipMap;
            textureImporter.mipMapBias = mipmapBias;
            textureImporter.SaveAndReimport();
        }
    }

    private void ProcessReadable(TextureImporter textureImporter, string assetPath)
    {
        if (assetPath.StartsWith(FontDir)
            || assetPath.StartsWith(EmojiInputDir)
            || assetPath.StartsWith(HUDTexInputDir)
            || assetPath.StartsWith(AppearanceDyeDir)
            || assetPath.StartsWith(HUDTexDir)
            || assetPath.StartsWith(ArtFontDir))
        {
            textureImporter.isReadable = true;
        }
        else
        {
            textureImporter.isReadable = false;
        }
    }

    private void ProcessSRGBSwitch(TextureImporter textureImporter)
    {
        //Linear Spaces下法线纹理均不勾选sRGB
        if (IsNormalMapTexture(assetPath))
        {
            textureImporter.sRGBTexture = false;
        }
        //UI贴图是在Gamma2.2的颜色空间下制作，为了保留原生的半透效果，需要取消sRGB勾选才能保证不被做线性转换
        
        if (assetPath.StartsWith(UiDir))
        {
            textureImporter.sRGBTexture = true;
        }
    }

    private void ProcessFilterMode(TextureImporter textureImporter)
    {
        if (TextureImporterType.Sprite == textureImporter.textureType)
        {
            textureImporter.filterMode = FilterMode.Bilinear;
        }
    }

    private void ProcessAnislLevel(TextureImporter textureImporter)
    {
        if (textureImporter.anisoLevel > 4)
        {
            textureImporter.anisoLevel = 4;
        }
    }

    private void ProcessAdvancedAndWrapMode(TextureImporter textureImporter)
    {
        if (assetPath.StartsWith(RawImageDir))
        {
            textureImporter.alphaIsTransparency = false;
            textureImporter.npotScale = TextureImporterNPOTScale.None;
            textureImporter.wrapMode = TextureWrapMode.Clamp;
        }
    }

    // 特殊压缩规则
    private void SpecialCompressRule(TextureImporter textureImporter, TextureImporterFormat format)
    {
        var standaloneFormat = format switch
        {
            TextureImporterFormat.Alpha8 => TextureImporterFormat.Alpha8,
            TextureImporterFormat.ARGB32 => TextureImporterFormat.ARGB32,
            _ => TextureImporterFormat.DXT5
        };

        var defaultSettings = textureImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.overridden = false;
        defaultSettings.format = TextureImporterFormat.Automatic;

        //美术字不压缩，散图不进包，压缩后打图集效果会不对
        if(assetPath.StartsWith(ArtFontDir))
        {
            defaultSettings.textureCompression = TextureImporterCompression.Uncompressed;
        }
        //表情包不压缩，散图不进包，压缩后打图集会报错
        if (assetPath.StartsWith(EmojiInputDir))
        {
            defaultSettings.format = TextureImporterFormat.Automatic;
            defaultSettings.textureCompression = TextureImporterCompression.Uncompressed;
        }
        
        textureImporter.SetPlatformTextureSettings(defaultSettings);

        var standaloneSettings = textureImporter.GetPlatformTextureSettings("Standalone");
        if (SetTextureFormat(standaloneSettings, standaloneFormat))
            textureImporter.SetPlatformTextureSettings(standaloneSettings);

        var iosSettings = textureImporter.GetPlatformTextureSettings("iPhone");
        if (SetTextureFormat(iosSettings, format))
            textureImporter.SetPlatformTextureSettings(iosSettings);

        var androidSettings = textureImporter.GetPlatformTextureSettings("Android");
        if (SetTextureFormat(androidSettings, format))
            textureImporter.SetPlatformTextureSettings(androidSettings);
    }

    // 处理平台设置
    private void ProcessPlatformSetting(TextureImporter textureImporter)
    {
        if (// 特殊处理Editor和Deploy部分，统一不使用压缩
            assetPath.Contains("/Editor/") ||
            assetPath.Contains("/PostProcessing/") ||
            assetPath.StartsWith(DeployDir) ||
            assetPath.StartsWith(T4MFolder) ||
            textureImporter.textureType == TextureImporterType.GUI)
        {
            // 精灵使用SpriteAtlas的压缩流程，不再额外配置本身
            return;
        }

        // Emoji和FontAtlas统一压缩到ASTC6x6
        if (assetPath.EndsWith(FontAtlasDir) || assetPath.StartsWith(EmojiDir) || assetPath.StartsWith(HUDTexDir))
        {
            SpecialCompressRule(textureImporter, TextureImporterFormat.ASTC_6x6);
            return;
        }


        // 中间图不压缩
        if (assetPath.StartsWith(FontDir)|| assetPath.StartsWith(ArtFontDir) || assetPath.StartsWith(EmojiInputDir) || assetPath.StartsWith(HUDTexInputDir))
        {
            SpecialCompressRule(textureImporter, TextureImporterFormat.RGBA32);
            return;
        }
        
        TextureImporterFormat mobileFormat = GetCompressType(textureImporter, textureImporter.textureType);

        int maxSize = 0;
        if (ImporterUtils.CheckLabel(textureImporter, ImporterUtils.IgnoreMaxSize))
        {
            maxSize = 0;
        }
        else
        {
            if (HasKeyWordInName(assetPath, "shadowmask"))
            {
                maxSize = 2048;
                mobileFormat = TextureImporterFormat.ASTC_6x6;
            }
            else if (HasKeyWordInName(assetPath, "comp_light"))
            {
                textureImporter.GetSourceTextureWidthAndHeight(out int width, out int height);
                maxSize = width / 4;
                mobileFormat = TextureImporterFormat.ASTC_6x6;
            }
            // Ui直接给个默认数值，防止旧时代被压缩的部分无法恢复
            else if (assetPath.StartsWith(UiDir) ||
                assetPath.StartsWith(UiSceneDir) || 
                textureImporter.textureType == TextureImporterType.Sprite ||
                textureImporter.textureType == TextureImporterType.Lightmap ||
                IsMiniMapTexture(assetPath))
            {
                maxSize = 2048;
            }
            else
            {
                if (IsNormalMapTexture(assetPath) || HasKeyWordInName(assetPath, "pbr"))
                    maxSize = 1024;
                else
                    maxSize = 512;
            }
        }

        SetPlatformSetting(textureImporter, mobileFormat, maxSize);
    }

    public static TextureImporterFormat GetCompressType(TextureImporter importer, TextureImporterType textureType)
    {
        if (IsSpineTexture(importer.assetPath))
            return TextureImporterFormat.ASTC_5x5;

        return TextureImporterFormat.ASTC_6x6;
    }

    private static void ProcessResizeTextureToMutiple4(TextureImporter textureImporter, Texture2D texture)
    {
        if (!string.IsNullOrEmpty(textureImporter.spritePackingTag)
            || textureImporter.assetPath.StartsWith(FontDir)
            || textureImporter.assetPath.StartsWith(EmojiInputDir)
            || textureImporter.assetPath.StartsWith(HUDTexInputDir))
        {
            return;
        }

        int newWidth = texture.width;
        if (texture.width % 4 != 0)
        {
            newWidth = 4 - texture.width % 4 + texture.width;
        }

        int newHeight = texture.height;
        if (texture.height % 4 != 0)
        {
            newHeight = 4 - texture.height % 4 + texture.height;
        }

        if (newWidth != texture.width || newHeight != texture.height)
        {
            tempIgnoreAssets.Add(textureImporter.assetPath);
            textureImporter.isReadable = true;
            textureImporter.SaveAndReimport();

            var newTexture = new Texture2D(newWidth, newHeight);

            for (int x = 0; x < newTexture.width; ++x)
            {
                for (int y = 0; y < newTexture.height; ++y)
                {
                    newTexture.SetPixel(x, y, new Color(0, 0, 0, 0));
                }
            }

            newTexture.SetPixels32(newWidth != texture.width ? 1 : 0, newHeight != texture.height ? 1 : 0, texture.width, texture.height, texture.GetPixels32());
            var bytes = newTexture.EncodeToPNG();
            File.WriteAllBytes(textureImporter.assetPath, bytes);

            textureImporter.isReadable = false;
            textureImporter.SaveAndReimport();
            tempIgnoreAssets.Remove(textureImporter.assetPath);
        }
    }

    private static void ProcessNonPowerOf2(TextureImporter textureImporter)
    {
        if (textureImporter.assetPath.StartsWith(EmojiInputDir) || textureImporter.assetPath.StartsWith(HUDTexInputDir))
        {
            Texture2D texture = AssetDatabase.LoadAssetAtPath(textureImporter.assetPath, typeof(Texture2D)) as Texture2D;
            if (null != texture)
            {
                if (texture.width * texture.height > 64 * 64)
                {
                    textureImporter.npotScale = TextureImporterNPOTScale.ToSmaller;
                }
                else if (texture.width * texture.height < 64 * 64)
                {
                    textureImporter.npotScale = TextureImporterNPOTScale.ToLarger;
                }
            }
        }
    }

    public static void BeginTextureEditing()
    {
        textureEditing = true;
    }
    
    public static void EndTextureEditing()
    {
        textureEditing = false;
    }
}