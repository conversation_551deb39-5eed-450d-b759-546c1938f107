
using System;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ActorRenderEffectData))]
public class ActorRenderEffectDataEditor : Editor
{
    private SerializedProperty dissolveColor;
    private SerializedProperty dissolveWidth;
    private SerializedProperty dissolveCurve;
    private SerializedProperty dissolveTexture;
    private SerializedProperty dissolveLife;
    private SerializedProperty blinkLife;
    private SerializedProperty blinkCurve;
    private SerializedProperty blinkFresnelPower;
    private SerializedProperty blinkColor;

    private void OnEnable()
    {
        dissolveColor = this.serializedObject.FindProperty("dissolveColor");
        dissolveWidth = this.serializedObject.FindProperty("dissolveWidth");
        dissolveCurve = this.serializedObject.FindProperty("dissolveCurve");
        dissolveTexture = this.serializedObject.FindProperty("dissolveTexture");
        dissolveLife = this.serializedObject.FindProperty("dissolveLife");
        blinkLife = this.serializedObject.FindProperty("blinkLife");
        blinkCurve = this.serializedObject.FindProperty("blinkCurve");
        blinkFresnelPower = this.serializedObject.FindProperty("blinkFresnelPower");
        blinkColor = this.serializedObject.FindProperty("blinkColor");
    }

    public override void OnInspectorGUI()
    {
        EditorGUILayout.LabelField("消融", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        dissolveColor.colorValue = EditorGUILayout.ColorField(new GUIContent("颜色"), dissolveColor.colorValue, true, true, true);
        EditorGUILayout.PropertyField(dissolveWidth,new GUIContent("宽度"));
        EditorGUILayout.PropertyField(dissolveCurve,new GUIContent("曲线"));
        EditorGUILayout.PropertyField(dissolveTexture,new GUIContent("噪声图"));
        EditorGUILayout.PropertyField(dissolveLife,new GUIContent("时长"));
        EditorGUI.indentLevel--;
        EditorGUILayout.LabelField("受击", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;
        EditorGUILayout.PropertyField(blinkCurve,new GUIContent("曲线"));
        EditorGUILayout.PropertyField(blinkLife,new GUIContent("时长"));
        EditorGUILayout.PropertyField(blinkFresnelPower,new GUIContent("边缘对比度"));
        blinkColor.colorValue = EditorGUILayout.ColorField(new GUIContent("颜色"), blinkColor.colorValue, true, true, true);
        EditorGUI.indentLevel--;
        serializedObject.ApplyModifiedProperties();
    }
}