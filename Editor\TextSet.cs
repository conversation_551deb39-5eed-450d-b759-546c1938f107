﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using Nirvana;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class TextSet : SetModuleEditor
{
    public static void ShowWindow()
    {
        TextSet window = GetWindow<TextSet>("Text Setting");
        window.Show();
    }
    
    private Text textComponent;
    private UIGradient uiGradientComponent;
    private Outline outlineComponent;
    private Shadow shadowComponent;
    private UITextExpand uiTextExpandComponent;

	[SerializeField] private TextJsonData recordJsonData;   //撤销到上一步的数据
    [SerializeField] private TextJsonList jsonDataList;     //JsonUnility规则，必须创建一个这样的类

    private bool canChangeFontSize = false;                 //赋值时是否改变字体大小

    protected override void Awake()
    {
        base.Awake();
        jsonPath = SearchThisCSharpDirectory(typeof(TextSet).ToString());
        ReadJson();
    }

    void OnGUI()
    {
        selectGo = EditorGUILayout.ObjectField("要更换的字体", selectGo, typeof(GameObject), true, GUILayout.Width(500)) as GameObject;
        selectGo = Selection.activeGameObject;
        if (selectGo == null)
        {
            return;
        }

        if (CheckExist<Text>(selectGo))
        {
            EditorGUILayout.LabelField("Text", guiStyle);

            textComponent = SaveGetComponent<Text>(selectGo);
            textComponent.font = EditorGUILayout.ObjectField("Font:", textComponent.font, typeof(Font), true) as Font;
            textComponent.fontStyle = (FontStyle)EditorGUILayout.EnumPopup("Font Style:", textComponent.fontStyle);
            canChangeFontSize = GUILayout.Toggle(canChangeFontSize, "Can Change Font Size");
            if (canChangeFontSize)
            {
                textComponent.fontSize = EditorGUILayout.IntField("Font Size", textComponent.fontSize);
            }
            textComponent.color = EditorGUILayout.ColorField("Color", textComponent.color);
        }
        else
        {
            //为了使Help的窗口大小如下
            GUILayout.BeginArea(new Rect(0, 20, 480, 40));
            EditorGUILayout.HelpBox("Don't Find Text Component！！！", MessageType.Error, true);
            GUILayout.EndArea();

            GUILayout.Space(40);

            if (GUILayout.Button("添加 Text", GUILayout.Width(200)))
            {
                selectGo.AddComponent<Text>();
            }
        }

        fadeGroup.target = EditorGUILayout.Foldout(fadeGroup.target, "Other Component", true);

        if (CheckExist<UIGradient>(selectGo))
        {
            if (EditorGUILayout.BeginFadeGroup(fadeGroup.faded))
            {
                EditorGUILayout.LabelField("UIGradient", guiStyle);
                uiGradientComponent = SaveGetComponent<UIGradient>(selectGo);

                uiGradientComponent.GradientMode =
                    (UIGradient.GradientModeEnum)EditorGUILayout.EnumPopup("Gradient Mode",
                        uiGradientComponent.GradientMode);
                uiGradientComponent.GradientDirection =
                    (UIGradient.GradientDirectionEnum)EditorGUILayout.EnumPopup("Gradient Direction",
                        uiGradientComponent.GradientDirection);
                uiGradientComponent.ColorMode =
                    (UIGradient.ColorModeEnum)EditorGUILayout.EnumPopup("Color Mode",
                        uiGradientComponent.ColorMode);
                uiGradientComponent.Color1 = EditorGUILayout.ColorField("Vertex 1", uiGradientComponent.Color1);
                uiGradientComponent.Color2 = EditorGUILayout.ColorField("Vertex 2", uiGradientComponent.Color2);
                uiGradientComponent.UseGraphicAlpha = EditorGUILayout.Toggle("Use Graphic Alpha", uiGradientComponent.UseGraphicAlpha);
            }

            EditorGUILayout.EndFadeGroup();

            if (GUILayout.Button("移除UIGradient", GUILayout.Width(200)))
            {
                DestroyImmediate(selectGo.GetComponent<UIGradient>());
            }
        }
        else
        {
            if (GUILayout.Button("添加UIGradient", GUILayout.Width(200)))
            {
                selectGo.AddComponent<UIGradient>();
            }
        }

		if (CheckExist<UITextExpand>(selectGo))
		{
			if (EditorGUILayout.BeginFadeGroup(fadeGroup.faded))
			{
				EditorGUILayout.LabelField("TextExpand", guiStyle);
				uiTextExpandComponent = SaveGetComponent<UITextExpand>(selectGo);

				uiTextExpandComponent.fitWidth =
					EditorGUILayout.FloatField("Fit Width", uiTextExpandComponent.fitWidth);
			}
			EditorGUILayout.EndFadeGroup();

			if (GUILayout.Button("移除TextExpand", GUILayout.Width(200)))
			{
				DestroyImmediate(selectGo.GetComponent<UITextExpand>());
			}
		}
		else
		{
			if (GUILayout.Button("添加TextExpand", GUILayout.Width(200)))
			{
				selectGo.AddComponent<UITextExpand>();
			}
		}

		if (CheckExist<Outline>(selectGo))
        {
            outlineComponent = SaveGetComponent<Outline>(selectGo);

            if (EditorGUILayout.BeginFadeGroup(fadeGroup.faded))
            {
                EditorGUILayout.LabelField("Outline", guiStyle);

                outlineComponent.effectColor =
                    EditorGUILayout.ColorField("Effect Color", outlineComponent.effectColor);
                outlineComponent.effectDistance =
                    EditorGUILayout.Vector2Field("Effect Distance", outlineComponent.effectDistance);
                outlineComponent.useGraphicAlpha =
                    EditorGUILayout.Toggle("Use Graphic Alpha", outlineComponent.useGraphicAlpha);
            }
            EditorGUILayout.EndFadeGroup();

            if (GUILayout.Button("移除Outline", GUILayout.Width(200)))
            {
                DestroyImmediate(outlineComponent);
            }
        }
        else
        {
            if (GUILayout.Button("添加Outline", GUILayout.Width(200)))
            {
                selectGo.AddComponent<Outline>();
            }
        }

        if (CheckExist<Shadow>(selectGo))
        {
            shadowComponent = SaveGetComponent<Shadow>(selectGo);

            if (EditorGUILayout.BeginFadeGroup(fadeGroup.faded))
            {
                EditorGUILayout.LabelField("Shadow", guiStyle);

                shadowComponent.effectColor =
                    EditorGUILayout.ColorField("Effect Color", shadowComponent.effectColor);
                shadowComponent.effectDistance =
                    EditorGUILayout.Vector2Field("Effect Distance", shadowComponent.effectDistance);
                shadowComponent.useGraphicAlpha =
                    EditorGUILayout.Toggle("Use Graphic Alpha", shadowComponent.useGraphicAlpha);
            }
            EditorGUILayout.EndFadeGroup();

            if (GUILayout.Button("移除Shadow", GUILayout.Width(200)))
            {
                DestroyImmediate(shadowComponent);
            }
        }
        else
        {
            if (GUILayout.Button("添加Shadow", GUILayout.Width(200)))
            {
                selectGo.AddComponent<Shadow>();
            }
        }

		EditorGUILayout.LabelField("Select Mode", guiStyle);

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("保存模板", GUILayout.Width(200)))
        {
            if (!string.IsNullOrEmpty(newModuleName))
            {
                AddModule(newModuleName);
            }
        }
        newModuleName = EditorGUILayout.TextField(newModuleName, GUILayout.Width(270));

        EditorGUILayout.EndHorizontal();

        scroll = EditorGUILayout.BeginScrollView(scroll);

        for (int i = 0; i < jsonDataList.inforList.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();

            GUI.skin.button.alignment = TextAnchor.LowerLeft;
            if (GUILayout.Button(jsonDataList.inforList[i].name, GUILayout.Width(250)))
            {
                LoadModule(jsonDataList.inforList[i].name);
            }
            GUI.skin.button.alignment = TextAnchor.MiddleCenter;

            if (GUILayout.Button("上移", GUILayout.Width(50)))
            {
                if (i > 0)
                {
                    TextJsonData tmpdata = jsonDataList.inforList[i];
                    jsonDataList.inforList[i] = jsonDataList.inforList[i - 1];
                    jsonDataList.inforList[i - 1] = tmpdata;
                    WriteJson();
                }
            }

            if (GUILayout.Button("下移", GUILayout.Width(50)))
            {
                if (i < jsonDataList.inforList.Count - 1)
                {
                    TextJsonData tmpdata = jsonDataList.inforList[i];
                    jsonDataList.inforList[i] = jsonDataList.inforList[i + 1];
                    jsonDataList.inforList[i + 1] = tmpdata;
                    WriteJson();
                }
            }

            if (GUILayout.Button("移除", GUILayout.Width(100)))
            {
                RemoveModule(jsonDataList.inforList[i].name);
            }

            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndScrollView();

        GUILayout.Space(20);

        if (GUILayout.Button("撤销", GUILayout.Width(470)))
        {
            if (recordJsonData != null)
            {
                SetValueByJsonData(recordJsonData);
            }
        }

    }

    T SaveGetComponent<T>(GameObject go) where T : Component
    {
        T cpnt = go.GetComponent<T>();

        if (cpnt == null)
        {
            cpnt = go.AddComponent<T>();
        }
        else
        {
            if (typeof(T) == typeof(Shadow))
            {
                //因为Outline继承于Shadow，所以要确保获得的组件为Shadow
                Shadow[] shadows = selectGo.GetComponents<Shadow>();
                foreach (var shadow in shadows)
                {
                    if (shadow.GetType() == typeof(Shadow))
                    {
                        return shadow as T;
                    }
                }

                cpnt = go.AddComponent<T>();
            }
		}

        return cpnt;
    }

    bool CheckExist<T>(GameObject go) where T : Component
    {
        if (typeof(T) == typeof(Shadow))
        {
            //因为Outline继承于Shadow，所以要确保获得的组件为Shadow
            Shadow[] shadows = selectGo.GetComponents<Shadow>();
            foreach (var shadow in shadows)
            {
                if (shadow.GetType() == typeof(Shadow))
                {
                    return true;
                }
            }
            return false;
        }
		else
        {
            return go.GetComponent<T>() != null;
        }
    }

    protected override void LoadModule(string moduleName)
    {
        recordJsonData = GetJsonDataFromObject("recordJsonData");
        ReadJson();
        foreach (var infor in jsonDataList.inforList)
        {
            if (infor.name == moduleName)
			{
				SetValueByJsonData(infor);
            }
        }
    }

    protected override void AddModule(string moduleName)
    {
        ReadJson();
        for (int i = 0; i < jsonDataList.inforList.Count; i++)
        {
            if (jsonDataList.inforList[i].name == moduleName)
            {
                jsonDataList.inforList[i] = GetJsonDataFromObject(moduleName);
                WriteJson();
                return;
            }
        }
        TextJsonData jsonData = GetJsonDataFromObject(moduleName);
        jsonDataList.inforList.Add(jsonData);
        WriteJson();
    }

    protected override void RemoveModule(string moduleName)
    {
        ReadJson();
        for (int i = 0; i < jsonDataList.inforList.Count; i++)
        {
            if (jsonDataList.inforList[i].name == moduleName)
            {
                jsonDataList.inforList.RemoveAt(i);
                WriteJson();
                return;
            }
        }
    }

    private void ReadJson()
    {
        if (!File.Exists(jsonPath))
        {
            File.Create(jsonPath);
        }
        string data = File.ReadAllText(jsonPath, Encoding.UTF8);

        jsonDataList = JsonUtility.FromJson<TextJsonList>(data);
        if (jsonDataList == null)
        {
            jsonDataList = new TextJsonList();
        }
        if (jsonDataList.inforList == null)
        {
            jsonDataList.inforList = new List<TextJsonData>();
        }
    }

    private void WriteJson()
    {
        string data = JsonUtility.ToJson(jsonDataList);
        File.WriteAllText(jsonPath, data, Encoding.UTF8);
    }

    private void SetValueByJsonData(TextJsonData infor)
    {
        var isPrefab = PrefabUtility.GetPrefabType(selectGo);
        UnityEngine.Object thisPrefab = null;
        GameObject rootPrefab = null;

        if (isPrefab == PrefabType.PrefabInstance)
        {
            thisPrefab = PrefabUtility.GetPrefabParent(selectGo);
            rootPrefab = PrefabUtility.FindPrefabRoot(selectGo);
            PrefabUtility.DisconnectPrefabInstance(selectGo);
        }

		if (infor.shadowEffectColor != Vector4.zero)
        {
            shadowComponent = SaveGetComponent<Shadow>(selectGo);

            shadowComponent.effectColor = infor.shadowEffectColor;
            shadowComponent.effectDistance = infor.shadowEffectDistance;
            shadowComponent.useGraphicAlpha = infor.shadowUseGraphicAlpha;

            while (UnityEditorInternal.ComponentUtility.MoveComponentUp(shadowComponent)) { }
        }
        else
        {
            DestroyImmediate(SaveGetComponent<Shadow>(selectGo));
        }

        if (infor.outlineEffectColor != Vector4.zero)
        {
            outlineComponent = SaveGetComponent<Outline>(selectGo);

            outlineComponent.effectColor = infor.outlineEffectColor;
            outlineComponent.effectDistance = infor.outlineEffectDistance;
            outlineComponent.useGraphicAlpha = infor.outlineUseGraphicAlpha;

            while (UnityEditorInternal.ComponentUtility.MoveComponentUp(outlineComponent)) { }
        }
        else
        {
            DestroyImmediate(SaveGetComponent<Outline>(selectGo));
        }

        if (!string.IsNullOrEmpty(infor.gradientMode))
        {
            uiGradientComponent = SaveGetComponent<UIGradient>(selectGo);
            uiGradientComponent.GradientMode =
                (UIGradient.GradientModeEnum)Enum.Parse(typeof(UIGradient.GradientModeEnum),
                    infor.gradientMode);
            uiGradientComponent.GradientDirection =
                (UIGradient.GradientDirectionEnum)Enum.Parse(typeof(UIGradient.GradientDirectionEnum),
                    infor.gradientDirection);
            uiGradientComponent.ColorMode =
                (UIGradient.ColorModeEnum)Enum.Parse(typeof(UIGradient.ColorModeEnum),
                    infor.gradientColorMode);
            uiGradientComponent.Color1 = infor.gradientColor1;
            uiGradientComponent.Color2 = infor.gradientColor2;
            uiGradientComponent.UseGraphicAlpha = infor.gradientUseGraphicAlpha;

            while (UnityEditorInternal.ComponentUtility.MoveComponentUp(uiGradientComponent)) { }
        }
        else
        {
            DestroyImmediate(selectGo.GetComponent<UIGradient>());
        }

		if (infor.uiTextExpandFitWidth != 0)
		{
			uiTextExpandComponent = SaveGetComponent<UITextExpand>(selectGo);

			uiTextExpandComponent.fitWidth = infor.uiTextExpandFitWidth;

			while (UnityEditorInternal.ComponentUtility.MoveComponentUp(uiTextExpandComponent)) { }
		}
		else
		{
			DestroyImmediate(selectGo.GetComponent<UITextExpand>());
		}

		if (infor.textFont != null)
        {
            textComponent = SaveGetComponent<Text>(selectGo);
            string[] fontIDs = AssetDatabase.FindAssets("t:Font");
            foreach (var fontID in fontIDs)
            {
                string fontPath = AssetDatabase.GUIDToAssetPath(fontID);
                if (Regex.IsMatch(fontPath, infor.textFont.Trim()))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<Font>(fontPath);
                    textComponent.font = asset;
                    break;
                }
            }
            textComponent.fontStyle = (FontStyle)Enum.Parse(typeof(FontStyle), infor.textFontStyle);
            if (canChangeFontSize)
            {
                textComponent.fontSize = infor.textFontSize;
            }
            textComponent.color = infor.textColor;

            while (UnityEditorInternal.ComponentUtility.MoveComponentUp(textComponent)) { }
        }

        ////脚本里Apply预制体，但是比较耗性能
        //if (isPrefab == PrefabType.PrefabInstance)
        //{
        //    PrefabUtility.ReplacePrefab(rootPrefab, thisPrefab);
        //    PrefabUtility.ConnectGameObjectToPrefab(rootPrefab, (GameObject)thisPrefab);
        //}
    }

    private TextJsonData GetJsonDataFromObject(string moduleName)
    {
        TextJsonData infor = new TextJsonData();
        infor.name = moduleName;

        if (CheckExist<Text>(selectGo))
        {
            if (textComponent == null)
            {
                textComponent = SaveGetComponent<Text>(selectGo);
            }
            infor.textFont = textComponent.font.ToString().Replace("(UnityEngine.Font)", "").Trim();
            infor.textFontStyle = textComponent.fontStyle.ToString();
            infor.textFontSize = textComponent.fontSize;
            infor.textColor = textComponent.color;
        }

        if (CheckExist<UIGradient>(selectGo))
        {
            if (uiGradientComponent == null)
            {
                uiGradientComponent = SaveGetComponent<UIGradient>(selectGo);
            }
            infor.gradientMode = uiGradientComponent.GradientMode.ToString();
            infor.gradientDirection = uiGradientComponent.GradientDirection.ToString();
            infor.gradientColorMode = uiGradientComponent.ColorMode.ToString();
            infor.gradientColor1 = uiGradientComponent.Color1;
            infor.gradientColor2 = uiGradientComponent.Color2;
            infor.gradientUseGraphicAlpha = uiGradientComponent.UseGraphicAlpha;
        }

		if (CheckExist<UITextExpand>(selectGo))
		{
			if (uiTextExpandComponent == null)
			{
				uiTextExpandComponent = SaveGetComponent<UITextExpand>(selectGo);
			}
			infor.uiTextExpandFitWidth = uiTextExpandComponent.fitWidth;
		}

		if (CheckExist<Outline>(selectGo))
        {
            if (outlineComponent == null)
            {
                outlineComponent = SaveGetComponent<Outline>(selectGo);
            }
            infor.outlineEffectColor = outlineComponent.effectColor;
            infor.outlineEffectDistance = outlineComponent.effectDistance;
            infor.outlineUseGraphicAlpha = outlineComponent.useGraphicAlpha;
        }

        if (CheckExist<Shadow>(selectGo))
        {
            Shadow[] shadows = selectGo.GetComponents<Shadow>();
            foreach (var shadow in shadows)
            {
                if (shadow.GetType() == typeof(Shadow))
                {
                    infor.shadowEffectColor = shadow.effectColor;
                    infor.shadowEffectDistance = shadow.effectDistance;
                    infor.shadowUseGraphicAlpha = shadow.useGraphicAlpha;
                }
            }
        }

		return infor;
    }
    
}

#region//定义Json的类
[Serializable]
public class TextJsonData
{
    public string name;

    public string textFont;
    public string textFontStyle;
    public int textFontSize;
    public Vector4 textColor;

    public Vector4 shadowEffectColor;
    public Vector2 shadowEffectDistance;
    public bool shadowUseGraphicAlpha;

    public Vector4 outlineEffectColor;
    public Vector2 outlineEffectDistance;
    public bool outlineUseGraphicAlpha;

    public string gradientMode;
    public string gradientDirection;
    public string gradientColorMode;
    public Vector4 gradientColor1;
    public Vector4 gradientColor2;
    public bool gradientUseGraphicAlpha;

	public float uiTextExpandFitWidth;
}

[Serializable]
public class TextJsonList
{
    public List<TextJsonData> inforList;
}
#endregion
