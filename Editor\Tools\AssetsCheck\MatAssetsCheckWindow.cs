﻿using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using System.Collections.Generic;

public class MatAssetsCheckWindow : OdinEditorWindow
{
    [MenuItem("自定义工具/资源检查/材质球Shader丢失查找", false, 110)]
    static void OpenWindow()
    {
        GetWindow<MatAssetsCheckWindow>().Show();
    }

    [PropertyOrder(-1)]
    [ReadOnly]
    public List<string> miscDirs = new List<string>(){
        "Assets/Game",
    };


    [PropertyOrder(-1)]
    [Button("查找缺少Shader的材质", ButtonSizes.Large), GUIColor(0f, 0.8f, 1f, 1f)]
    void FindMatShader()
    {
        matList.Clear();
        int count = 0;
        var materialGuids = AssetDatabase.FindAssets("t:Material", miscDirs.ToArray());

        int total = materialGuids.Length;
        foreach (var materialGuid in materialGuids)
        {
            count++;
            var materialPath = AssetDatabase.GUIDToAssetPath(materialGuid);
            var materical = AssetDatabase.LoadAssetAtPath(materialPath, typeof(Material)) as Material;

            if(materical.shader == null || materical.shader.name.Contains("InternalErrorShader"))
            {
                matList.Add(materical);
            }
            EditorUtility.DisplayProgressBar("查找中", string.Format("{0}/{1}", count, total), (float)(count * 1.0f / total));
        }
        EditorUtility.ClearProgressBar();
    }

    [LabelText("缺少Shader材质列表")]
    public List<Material> matList = new List<Material>();
}