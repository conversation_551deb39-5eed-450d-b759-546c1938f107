﻿using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine;
using System.Collections.Generic;

class PreloadShaderVariantBuilder : EditorWindow
{
    private static Dictionary<string, List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>>> dictShaderName2ShaderVariant = new Dictionary<string, List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>>>();

    public void StartBuild()
    {
        dictShaderName2ShaderVariant.Clear();
        FindKeyword("t:material", new string[] { "Assets/Game/Shaders/Materials" }, dictShaderName2ShaderVariant);
        if (dictShaderName2ShaderVariant.Count > 0)
        {
            TransDict2ShaderVariantCollection();
        }
    }

    static void FindKeyword(string filter, string[] paths, Dictionary<string, List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>>> dict)
    {
        var assets = AssetDatabase.FindAssets(filter, paths);
        int totalCount = assets.Length;
        int curIndex = 0;
        for (int i = 0; i < assets.Length; i++)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(assets[i]);
            var curMat = AssetDatabase.LoadMainAssetAtPath(assetPath);
            if (curMat is Material)
            {
                AddToDict(curMat as Material, dict);
            }

            curIndex += 1;
            EditorUtility.DisplayProgressBar(assetPath, string.Format("{0} / {1}", curIndex, totalCount), (float)curIndex / (float)totalCount);
        }

        EditorUtility.ClearProgressBar();
    }

    static void AddToDict(Material curMat, Dictionary<string, List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>>> dict)
    {
        if (!curMat || !curMat.shader) return;
        string matName = curMat.name;
        string shaderName = curMat.shader.name;

        List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>> list;
        if (!dict.TryGetValue(shaderName, out list))
        {
            list = new List<KeyValuePair<string, ShaderVariantCollection.ShaderVariant>>();
            dict.Add(shaderName, list);
        }

        PassType passType = PassType.ForwardBase;

        ShaderVariantCollection.ShaderVariant sv = new ShaderVariantCollection.ShaderVariant();
        sv.shader = curMat.shader;
        sv.keywords = curMat.shaderKeywords;
        sv.passType = passType;

        KeyValuePair<string, ShaderVariantCollection.ShaderVariant> kv = new KeyValuePair<string, ShaderVariantCollection.ShaderVariant>(matName, sv);

        bool isContain = false;
        foreach (var val in list)
        {
            if (val.Value.shader == kv.Value.shader
                && val.Value.passType == kv.Value.passType
                && System.Linq.Enumerable.SequenceEqual(val.Value.keywords, kv.Value.keywords))
            {
                isContain = true;
                break;
            }
        }
        if (!isContain)
        {
            list.Add(kv);
        }
    }

    public static void TransDict2ShaderVariantCollection()
    {
        ShaderVariantCollection svc = new ShaderVariantCollection();
        string exportPath = "Assets/Game/Shaders";
        if (!string.IsNullOrEmpty(exportPath))
        {
            string dummyMaterialFolderPath = exportPath;
            if (dictShaderName2ShaderVariant != null && dictShaderName2ShaderVariant.Count > 0)
            {
                foreach (var val in dictShaderName2ShaderVariant)
                {
                    foreach (var cur in val.Value)
                    {
                        svc.Add(cur.Value);
                    }
                }
            }
            AssetDatabase.CreateAsset(svc, exportPath + "/ShaderVariantCollection.shadervariants");
            AssetDatabase.Refresh();
            Debug.Log("Export ShaderVariantCollection Success");
        }
        else
        {
            Debug.Log("Export ShaderVariantCollection Fail");
        }
    }
}
