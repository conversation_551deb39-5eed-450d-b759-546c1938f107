﻿using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class CleanerUtil
{
    // 引用集合，如A资源被B,C资源引用
    private static Dictionary<string, HashSet<string>> refDic = new Dictionary<string, HashSet<string>>();

    public static bool FixMissingRefrence(GameObject gameobj)
    {
        bool isPrefabDirty = false;
        var isMissing = false;
        var transforms = gameobj.GetComponentsInChildren<Transform>();
        for (int i = 0; i < transforms.Length; i++)
        {
            var components = transforms[i].GetComponents<Component>();
            var serializedObject = new SerializedObject(transforms[i].gameObject);
            var prop = serializedObject.FindProperty("m_Component");

            int index = 0;
            bool isDirty = false;
            for (var j = 0; j < components.Length; j++)
            {
                if (components[j] == null)
                {
                    prop.DeleteArrayElementAtIndex(index);
                    isDirty = true;
                }
                else
                {
                    index++;
                }
            }

            if (isDirty)
            {
                isMissing = true;
                serializedObject.ApplyModifiedProperties();
            }
        }

        if (isMissing && AssetDatabase.IsMainAsset(gameobj))
        {
            isPrefabDirty = true;
            PrefabUtility.ResetToPrefabState(gameobj);
            PrefabUtility.SetPropertyModifications(gameobj, new PropertyModification[] { });

            var newGameObj = GameObject.Instantiate<GameObject>(gameobj);
            PrefabUtility.ReplacePrefab(newGameObj, gameobj);
            GameObject.DestroyImmediate(newGameObj);
        }

        return isPrefabDirty;
    }

    // 构建资源的引用关系
    public static void BuildRefrence(string filter, string[] searchDirs)
    {
        string[] guids = AssetDatabase.FindAssets(filter, searchDirs);
        foreach (var guid in guids)
        {
            var asset_path = AssetDatabase.GUIDToAssetPath(guid);
            string[] depends = AssetDatabase.GetDependencies(asset_path);
            foreach (var depend in depends)
            {
                // 自己引用自己
                if (depend == asset_path)
                {
                    continue;
                }

                HashSet<string> hashset;
                if (!refDic.TryGetValue(depend, out hashset))
                {
                    hashset = new HashSet<string>();
                    refDic.Add(depend, hashset);
                }
                if (!hashset.Contains(asset_path))
                {
                    hashset.Add(asset_path);
                }
            }
        }
    }

    // 清除资源的引用关系
    public static void ClearRefrence()
    {
        refDic.Clear();
    }

    public static bool IsBeReference(string assetPath)
    {
        if (refDic.Count == 0)
        {
            Debug.LogError("使用前先 构建资源的引用关系");
            return true;
        }
        return refDic.ContainsKey(assetPath);
    }

    public static HashSet<string> GetReference(string assetPath)
    {
        HashSet<string> reference;
        if (refDic.TryGetValue(assetPath, out reference))
        {
            return reference;
        }

        return null;
    }
}
