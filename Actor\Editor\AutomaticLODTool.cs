﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System;
using Nirvana;
using System.IO;

public class AutomaticLODTool : BaseEditorWindow
{
    private List<GameObject> list = new List<GameObject>();
    private Vector2 scrollerPos = new Vector2();
    private GameObject selectObj;
    private UnityEngine.Object folder;
    private float screenRelativeTransitionHeight = 0.5f;
    private float screenRelativeTransitionHeight2 = 0.05f;
    private int trianglesCount = 2000;
    private bool onlyChangeLOD = false;
    private float compression = 0.6f;

    [MenuItem("自定义工具/批量生成LOD")]

    private static void ShowWindow()
    {
        EditorWindow.GetWindow<AutomaticLODTool>(false, "批量生成LOD");
    }

    private void OnGUI()
    {
        folder = EditorGUILayout.ObjectField("选择一个文件夹", folder, typeof(UnityEngine.Object), false);
        screenRelativeTransitionHeight = EditorGUILayout.FloatField("LOD0区间[0-1]", screenRelativeTransitionHeight);
        screenRelativeTransitionHeight2 = EditorGUILayout.FloatField("LOD0区间[1-Culled]", screenRelativeTransitionHeight2);
        trianglesCount = EditorGUILayout.IntField("大于多少面", trianglesCount);
        onlyChangeLOD = EditorGUILayout.Toggle("只修改LOD参数", onlyChangeLOD);
        compression = EditorGUILayout.FloatField("压缩率[0-1]", compression);

        if (GUILayout.Button("GenerateLOD"))
        {
            if (null == folder)
            {
                return;
            }

            this.list.Clear();
            this.Search();
            AssetDatabase.SaveAssets();
        }
        EditorGUILayout.LabelField(string.Format("数量: {0}", list.Count));
        this.scrollerPos = EditorGUILayout.BeginScrollView(this.scrollerPos);
        foreach (var obj in this.list)
        {
            var style = EditorStyles.textField;
            if (obj == this.selectObj)
                style = EditorStyles.whiteLabel;
            if (GUILayout.Button(obj.name, style))
            {
                this.selectObj = obj;
                PingObj(obj);
            }
        }
        EditorGUILayout.EndScrollView();
    }

    private void Search()
    {
        string path = AssetDatabase.GetAssetPath(folder.GetInstanceID());
        string[] guids = AssetDatabase.FindAssets("t:GameObject", new string[] { path });
        if (guids.Length < 1)
        {
            return;
        }

        Step(guids, 0);
    }

    private void Step(string[] guids, int index)
    {
        if (index >= guids.Length)
        {
            AssetDatabase.SaveAssets();
            return;
        }

        var guid = guids[index];
        var path = AssetDatabase.GUIDToAssetPath(guid);
        var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
        Check(obj, () => { Step(guids, index + 1); });
    }

    private bool ShouldComputeMesh(GameObject gameObject)
    {
        bool flag = false;
        Mesh mesh = null;

        SkinnedMeshRenderer skinnedMeshRenderer = gameObject.GetComponentInChildren<SkinnedMeshRenderer>();
        if (null != skinnedMeshRenderer)
        {
            mesh = skinnedMeshRenderer.sharedMesh;
        }
        else
        {
            MeshFilter meshFilter = gameObject.GetComponent<MeshFilter>();
            if (null != meshFilter)
            {
                mesh = meshFilter.mesh;
            }
        }

        if (null != mesh)
        {
            flag = mesh.triangles.Length / 3 >= trianglesCount;
        }

        return flag;
    }

    private void Check(GameObject go, Action action)
    {
        if (onlyChangeLOD)
        {
            ChangeLOD(go);
            action();
            return;
        }

        ActorRender actorRender = go.GetComponent<ActorRender>();
        if (null != actorRender)
        {
            if (ShouldComputeMesh(go))
            {
                RemoveAnimatorOptimizer(go);
                ActorRenderEditor.AutoGenerateLOD(go, () =>
                {
                    AddLODGroup(go);
                    if (CheckLODRenderItem(actorRender))
                    {
                        PrefabUtility.ResetToPrefabState(actorRender.gameObject);
                        PrefabUtility.SetPropertyModifications(actorRender.gameObject, new PropertyModification[] { });
                    }
                    list.Add(go);
                    action();
                }, compression);
            }
            else
            {
                RemoveLODRenderItem(actorRender);
                RemoveLODGroup(go);
                action();
            }
        }
        else
        {
            action();
        }
    }

    private void AddLODGroup(GameObject prefab)
    {
        var meshPath = Path.Combine(Path.GetDirectoryName(AssetDatabase.GetAssetPath(prefab.GetInstanceID())), string.Format("{0}_LOD1.asset", prefab.name));
        Mesh meshLOD1 = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
        if (null == meshLOD1)
        {
            return;
        }

        var go = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
        
        Renderer originalRenderer = go.GetComponentInChildren<SkinnedMeshRenderer>();
        if (null == originalRenderer)
        {
            originalRenderer = go.GetComponentInChildren<MeshRenderer>();
        }

        if (null == originalRenderer)
        {
            DestroyImmediate(go);
            Debug.LogError(string.Format("预制体:{0} 找不到Renderer", prefab.name));
            return;
        }

        LODGroup group = go.GetOrAddComponent<LODGroup>();
        LOD[] lods = new LOD[2];

        lods[0] = new LOD();
        lods[0].screenRelativeTransitionHeight = screenRelativeTransitionHeight;
        lods[0].renderers = new Renderer[] { originalRenderer };

        string nodeName = originalRenderer.name + "_LOD1";
        if (meshPath.StartsWith("Assets/Game/Actors/Character/"))
        {
            nodeName = "Renderer_LOD";
        }

        Transform node = originalRenderer.transform.parent.Find(nodeName);
        if (null == node)
            node = new GameObject(nodeName).transform;

        node.SetParent(originalRenderer.transform.parent);
        node.localPosition = originalRenderer.transform.localPosition;
        node.localRotation = originalRenderer.transform.localRotation;
        node.localScale = originalRenderer.transform.localScale;

        UnityEditorInternal.ComponentUtility.CopyComponent(originalRenderer);
        UnityEditorInternal.ComponentUtility.PasteComponentAsNew(node.gameObject);
        
        SkinnedMeshRenderer newRenderer = node.GetComponent<SkinnedMeshRenderer>();
        newRenderer.sharedMesh = meshLOD1;

        // 用lowMaterial
        if (null != originalRenderer.sharedMaterial)
        {
            string path = AssetDatabase.GetAssetPath(originalRenderer.sharedMaterial.GetInstanceID());
            string matFileName = Path.GetFileNameWithoutExtension(path);
            string newMatName = string.Format("{0}_low.mat", matFileName);
            path = path.Replace(matFileName + ".mat", newMatName);
            Material lowMat = AssetDatabase.LoadAssetAtPath<Material>(path);
            if (lowMat)
                newRenderer.sharedMaterial = lowMat;
        }

        lods[1] = new LOD();
        lods[1].screenRelativeTransitionHeight = screenRelativeTransitionHeight2;
        lods[1].renderers = new Renderer[] { newRenderer };

        group.SetLODs(lods);

        PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
        DestroyImmediate(go);
    }

    private void RemoveLODGroup(GameObject prefab)
    {
        var go = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
        LODGroup group = go.GetComponent<LODGroup>();
        if (null == group)
        {
            DestroyImmediate(go);
            return;
        }

        var lods = group.GetLODs();
        for (int i = 1; i < lods.Length; ++i)
        {
            var lod = lods[i];
            foreach (var renderer in lod.renderers)
            {
                Mesh mesh = null;
                var skinnedMeshRenderer = renderer.GetComponent<SkinnedMeshRenderer>();
                if (null != skinnedMeshRenderer)
                {
                    mesh = skinnedMeshRenderer.sharedMesh;
                    if (null == skinnedMeshRenderer.rootBone)
                    {
                        DestroyImmediate(go);
                        return;
                    }
                }
                else
                {
                    var meshFilter = renderer.GetComponent<MeshFilter>();
                    if (null != meshFilter)
                    {
                        mesh = meshFilter.mesh;
                    }
                }

                if (null != mesh)
                {
                    var path = AssetDatabase.GetAssetPath(mesh.GetInstanceID());
                    AssetDatabase.DeleteAsset(path);
                }

                DestroyImmediate(renderer.gameObject);
            }
        }

        DestroyImmediate(group);

        PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
        DestroyImmediate(go);
    }

    private void RemoveAnimatorOptimizer(GameObject prefab)
    {
        var go = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
        AnimatorOptimizer animatorOptimizer = go.GetComponent<AnimatorOptimizer>();
        if (null != animatorOptimizer)
        {
            animatorOptimizer.Deoptimize();
            DestroyImmediate(animatorOptimizer);
        }

        PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
        DestroyImmediate(go);
    }

    private void Update()
    {
        ActorRenderEditor.UpdateEditor();
    }

    public static Mesh[] ComputeMesh(GameObject gameObject)
    {
        bool isSkinnedMesh = true;
        SkinnedMeshRenderer skinnedMeshRenderer = gameObject.GetComponentInChildren<SkinnedMeshRenderer>();
        if (null != skinnedMeshRenderer)
        {
            if (null == skinnedMeshRenderer.rootBone && null != gameObject.GetComponent<AnimatorOptimizer>())
            {
                Debug.LogError(string.Format("预制体 {0} 被AnimatorOptimizer优化过，无法自动生成LOD，请手动处理！", gameObject.name));
                return null;
            }
        }

        Renderer renderer = skinnedMeshRenderer;
        if (null == renderer)
        {
            isSkinnedMesh = false;
            renderer = gameObject.GetComponent<MeshRenderer>();
        }

        if (null == renderer)
        {
            Debug.LogError("Renderer is Null");
            return null;
        }

        AutomaticLOD automaticLOD = renderer.GetComponent<AutomaticLOD>();
        if (null == automaticLOD)
        {
            Debug.LogError("请先点击AutomaticLOD组件上的GenerateLODs按钮");
            return null;
        }

        if (automaticLOD.m_listLODLevels.Count < 1)
        {
            Debug.LogError("请先点击AutomaticLOD组件上的GenerateLODs按钮");
            return null;
        }

        Mesh[] meshList = new Mesh[automaticLOD.m_listLODLevels.Count - 1];
        for (int i = 1; i < automaticLOD.m_listLODLevels.Count; ++i)
        {
            Mesh newMesh = new Mesh();
            var mesh = automaticLOD.m_originalMesh;
            var lowMesh = automaticLOD.m_listLODLevels[i].m_mesh;

            newMesh.vertices = lowMesh.vertices;
            newMesh.triangles = lowMesh.triangles;
            newMesh.uv = lowMesh.uv;
            newMesh.normals = lowMesh.normals;
            newMesh.tangents = lowMesh.tangents;
            if (isSkinnedMesh)
            {
                newMesh.boneWeights = lowMesh.boneWeights;
                newMesh.bindposes = lowMesh.bindposes;
            }
            
            meshList[i - 1] = newMesh;
        }

        return meshList;
    }

    private void ChangeLOD(GameObject gameObject)
    {
        LODGroup group = gameObject.GetComponent<LODGroup>();
        if (null != group)
        {
            var lods = group.GetLODs();
            if (lods.Length > 1)
            {
                lods[0].screenRelativeTransitionHeight = screenRelativeTransitionHeight;
                lods[1].screenRelativeTransitionHeight = screenRelativeTransitionHeight2;
                group.SetLODs(lods);

                PrefabUtility.ResetToPrefabState(gameObject);
                PrefabUtility.SetPropertyModifications(gameObject, new PropertyModification[] { });
            }
        }
    }

    public static bool CheckLODRenderItem(ActorRender actorRender)
    {
        var list = actorRender.GetRenderList();
        bool flag = false;

        List<ActorRender.RenderItem> newList = new List<ActorRender.RenderItem>(list);
        var renderers = actorRender.GetComponentsInChildren<Renderer>(true);
        for (int i = 0; i < renderers.Length; ++i)
        {
            var rendere = renderers[i];
            if (rendere.name.Contains("_LOD"))
            {
                if (IsInActorRender(list, rendere.name))
                    continue;

                int index = rendere.name.IndexOf("_LOD");
                string originalRendererName = rendere.name.Substring(0, index);
                foreach (var originalItem in list)
                {
                    if (null == originalItem.renderer)
                    {
                        Debug.LogError(string.Format("{0}上的ActorRender组件上的Renderer为null", actorRender.name));
                        continue;
                    }

                    if (originalItem.renderer.name == originalRendererName)
                    {
                        ActorRender.RenderItem item = new ActorRender.RenderItem();
                        item.renderer = rendere;
                        //item.material = originalItem.material;
                        item.notCastShadow = originalItem.notCastShadow;
                        newList.Add(item);
                        flag = true;
                        break;
                    }
                }
            }
        }

        actorRender.SetRenderList(newList);

        return flag;
    }

    private static bool IsInActorRender(List<ActorRender.RenderItem> list, string name)
    {
        foreach (var item in list)
        {
            if (null != item.renderer && item.renderer.name == name)
                return true;
        }

        return false;
    }

    private void RemoveLODRenderItem(ActorRender actorRender)
    {
        var list = actorRender.GetRenderList();
        List<int> indexList = new List<int>();
        for (int i = 0; i < list.Count; ++i)
        {
            var item = list[i];
            if (null != item.renderer && item.renderer.name.Contains("_LOD"))
            {
                indexList.Add(i);
            }
        }

        if (indexList.Count > 0)
        {
            for (int i = indexList.Count - 1; i >= 0; --i)
            {
                int index = indexList[i];
                list.RemoveAt(index);
            }

            actorRender.SetRenderList(list);
            PrefabUtility.ResetToPrefabState(actorRender.gameObject);
            PrefabUtility.SetPropertyModifications(actorRender.gameObject, new PropertyModification[] { });
        }
    }
}

