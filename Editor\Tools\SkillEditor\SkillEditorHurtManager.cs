using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 击中管理器
/// 负责击中效果的配置和播放
/// </summary>
public class SkillEditorHurtManager
{
    #region 私有字段
    private SkillEditorPrefabDataConfig _dataConfig;
    private SkillEditorModelManager _modelManager;
    private SkillEditorSoundManager _soundManager;
    private int _selectHurtId = 0;
    private Vector2 _hurtScroll;
    #endregion

    #region 属性
    public int SelectedHurtId => _selectHurtId;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化击中管理器
    /// </summary>
    public void Initialize(SkillEditorPrefabDataConfig dataConfig, SkillEditorModelManager modelManager, SkillEditorSoundManager soundManager)
    {
        _dataConfig = dataConfig;
        _modelManager = modelManager;
        _soundManager = soundManager;
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制击中管理界面
    /// </summary>
    public void DrawHurtsWindow()
    {
        GUILayout.BeginVertical();
        DrawAddHurtButton();
        DrawHurtList();
        GUILayout.EndVertical();

        DrawDeleteHurtButton();
    }

    /// <summary>
    /// 绘制添加击中按钮
    /// </summary>
    private void DrawAddHurtButton()
    {
        if (GUILayout.Button("增加击中", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH),
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewHurt();
        }
    }

    /// <summary>
    /// 绘制击中列表
    /// </summary>
    private void DrawHurtList()
    {
        if (_dataConfig?.actorController?.hurts == null) return;

        _hurtScroll = SkillEditorGUI.DrawScrollList(
            _hurtScroll,
            _dataConfig.actorController.hurts,
            hurt => SkillEditorGUIConfig.GetDefaultButtonName(hurt.HurtBtnName),
            OnHurtSelected);
    }

    /// <summary>
    /// 绘制删除击中按钮
    /// </summary>
    private void DrawDeleteHurtButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect2);

        if (_dataConfig?.actorController?.hurts?.Count > 0)
        {
            if (GUILayout.Button("删除击中", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH),
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedHurt();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制击中详细配置
    /// </summary>
    public void DrawHurtDetails()
    {
        if (!IsValidHurtSelection()) return;

        var data = _dataConfig.actorController.hurts[_selectHurtId];

        DrawHurtBasicSettings(data);
        DrawHurtEffectSettings(data);
        DrawHurtSoundSettings(data);

        _dataConfig.actorController.hurts[_selectHurtId] = data;
    }

    /// <summary>
    /// 绘制多段击中配置
    /// </summary>
    public void DrawHitDetails()
    {
        if (!IsValidHurtSelection()) return;

        var data = _dataConfig.actorController.hurts[_selectHurtId];

        DrawHitBasicSettings(data);
        DrawHitEffectSettings(data);
        DrawHitSoundSettings(data);

        _dataConfig.actorController.hurts[_selectHurtId] = data;
    }
    #endregion

    #region 击中配置绘制
    /// <summary>
    /// 绘制击中基础设置
    /// </summary>
    private void DrawHurtBasicSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        data.HurtBtnName = SkillEditorGUI.DrawButtonNameField(data.HurtBtnName);
        data.Action = SkillEditorGUI.DrawProjectileEventSelector(data.Action);
    }

    /// <summary>
    /// 绘制击中特效设置
    /// </summary>
    private void DrawHurtEffectSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        EditorGUI.BeginChangeCheck();

        EffectControl hurtEffect = SkillEditorGUI.DrawEffectSelector(
            data.HurtEffectGoName,
            data.HurtEffect.GetAssetPath(),
            "击中Effect");

        data.HurtPosition = SkillEditorGUI.DrawEnumPopup(
            "击中Position",
            data.HurtPosition,
            SkillEditorGUIConfig.HurtPositions);

        data.HurtRotation = SkillEditorGUI.DrawEnumPopup(
            "击中Rotation",
            data.HurtRotation,
            SkillEditorGUIConfig.HitRotations);

        data.DelayHurtEffect = SkillEditorGUI.DrawDelayField("延迟播放时间", data.DelayHurtEffect);
        data.HurtFreeDelay = SkillEditorGUI.DrawDelayField("延迟删除时间", data.HurtFreeDelay);

        if (EditorGUI.EndChangeCheck())
        {
            UpdateHurtEffectAsset(data, hurtEffect);
        }
    }

    /// <summary>
    /// 绘制击中音效设置
    /// </summary>
    private void DrawHurtSoundSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        EditorGUILayout.Space();

        EditorGUI.BeginChangeCheck();
        AudioItem audioItem = SkillEditorGUI.DrawAudioSelector(
            data.HurtSoundAudioGoName,
            data.HurtSoundAudioAsset.GetAssetPath(),
            "击中音效");

        data.DelayHurtSound = SkillEditorGUI.DrawDelayField("击中音效 延迟播放时间", data.DelayHurtSound);

        if (EditorGUI.EndChangeCheck())
        {
            UpdateHurtSoundAsset(data, audioItem);
        }
    }

    /// <summary>
    /// 绘制多段击中基础设置
    /// </summary>
    private void DrawHitBasicSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        data.HitCount = EditorGUILayout.IntField("命中次数", data.HitCount);
        data.HitInterval = EditorGUILayout.DelayedTextField("命中间隔(每次时间以|分割) ", data.HitInterval);
        data.HitProportion = EditorGUILayout.DelayedTextField("命中比例(每次占比以|分割) ", data.HitProportion);
    }

    /// <summary>
    /// 绘制多段击中特效设置
    /// </summary>
    private void DrawHitEffectSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        EditorGUI.BeginChangeCheck();

        EffectControl hitEffect = SkillEditorGUI.DrawEffectSelector(
            data.HitEffectGoName,
            data.HitEffect.GetAssetPath(),
            "命中Effect");

        data.HitPosition = SkillEditorGUI.DrawEnumPopup(
            "命中Position",
            data.HitPosition,
            SkillEditorGUIConfig.HurtPositions);

        data.HitRotation = SkillEditorGUI.DrawEnumPopup(
            "命中Rotation",
            data.HitRotation,
            SkillEditorGUIConfig.HitRotations);

        data.DelayHitEffect = SkillEditorGUI.DrawDelayField("延迟播放时间", data.DelayHitEffect);
        data.HitFreeDelay = SkillEditorGUI.DrawDelayField("延迟删除时间", data.HitFreeDelay);

        if (EditorGUI.EndChangeCheck())
        {
            UpdateHitEffectAsset(data, hitEffect);
        }
    }

    /// <summary>
    /// 绘制多段击中音效设置
    /// </summary>
    private void DrawHitSoundSettings(SkillEditorPrefabDataConfig.HurtData data)
    {
        EditorGUILayout.Space();
        GUILayout.Label("命中音效配置:");

        _soundManager.DrawHitSoundList(data.hitSounds);
        _soundManager.DrawHitSoundButtons(data.hitSounds);
    }

    /// <summary>
    /// 更新击中特效资产
    /// </summary>
    private void UpdateHurtEffectAsset(SkillEditorPrefabDataConfig.HurtData data, EffectControl hurtEffect)
    {
        if (hurtEffect == null)
        {
            data.HurtEffect = AssetID.Empty;
            data.HurtEffectGoName = string.Empty;
        }
        else
        {
            data.HurtEffect = SkillEditorUtils.GetAssetID(hurtEffect);
            data.HurtEffectGoName = hurtEffect.name;
        }
    }

    /// <summary>
    /// 更新多段击中特效资产
    /// </summary>
    private void UpdateHitEffectAsset(SkillEditorPrefabDataConfig.HurtData data, EffectControl hitEffect)
    {
        if (hitEffect == null)
        {
            data.HitEffect = AssetID.Empty;
            data.HitEffectGoName = string.Empty;
        }
        else
        {
            data.HitEffect = SkillEditorUtils.GetAssetID(hitEffect);
            data.HitEffectGoName = hitEffect.name;
        }
    }

    /// <summary>
    /// 更新击中音效资产
    /// </summary>
    private void UpdateHurtSoundAsset(SkillEditorPrefabDataConfig.HurtData data, AudioItem audioItem)
    {
        if (audioItem == null)
        {
            data.HurtSoundAudioAsset = AssetID.Empty;
            data.HurtSoundAudioGoName = string.Empty;
        }
        else
        {
            data.HurtSoundAudioAsset = SkillEditorUtils.GetAssetID(audioItem);
            data.HurtSoundAudioGoName = audioItem.name;
        }
    }
    #endregion

    #region 击中操作
    /// <summary>
    /// 添加新击中
    /// </summary>
    private void AddNewHurt()
    {
        if (_dataConfig?.actorController?.hurts == null) return;

        var data = new SkillEditorPrefabDataConfig.HurtData();
        _dataConfig.actorController.hurts.Add(data);
    }

    /// <summary>
    /// 删除选中的击中
    /// </summary>
    private void DeleteSelectedHurt()
    {
        if (_selectHurtId == -1 || _dataConfig.actorController.hurts.Count == 0)
            return;

        _dataConfig.actorController.hurts.RemoveAt(_selectHurtId);
        _selectHurtId = _dataConfig.actorController.hurts.Count == 0
            ? -1
            : _dataConfig.actorController.hurts.Count - 1;
    }

    /// <summary>
    /// 击中选择回调
    /// </summary>
    private void OnHurtSelected(int index)
    {
        _selectHurtId = index;
    }

    /// <summary>
    /// 验证击中选择是否有效
    /// </summary>
    private bool IsValidHurtSelection()
    {
        return _selectHurtId != -1 &&
               _dataConfig?.actorController?.hurts != null &&
               _selectHurtId < _dataConfig.actorController.hurts.Count;
    }
    #endregion

    #region 击中效果播放
    /// <summary>
    /// 播放击中显示
    /// </summary>
    public void PlayHurtShow(string skillAction, Transform root, Transform hurtPoint, System.Action perHit)
    {
        if (_dataConfig?.actorController?.hurts == null) return;

        foreach (var hurt in _dataConfig.actorController.hurts)
        {
            if (hurt.Action != skillAction) continue;

            PlayHurtEffectIfNeeded(hurt, root, hurtPoint);
            PlayHurtSoundIfNeeded(hurt, hurtPoint);
            HandleHitEffects(hurt, root, hurtPoint, perHit);
        }
    }

    /// <summary>
    /// 如果需要则播放击中特效
    /// </summary>
    private void PlayHurtEffectIfNeeded(SkillEditorPrefabDataConfig.HurtData hurt, Transform root, Transform hurtPoint)
    {
        if (hurt.HurtEffect.IsEmpty) return;

        if (hurt.DelayHurtEffect > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(
                hurt.DelayHurtEffect,
                () => Scheduler.RunCoroutine(PlayHurtEffect(hurt, root, hurtPoint))));
        }
        else
        {
            Scheduler.RunCoroutine(PlayHurtEffect(hurt, root, hurtPoint));
        }
    }

    /// <summary>
    /// 如果需要则播放击中音效
    /// </summary>
    private void PlayHurtSoundIfNeeded(SkillEditorPrefabDataConfig.HurtData hurt, Transform hurtPoint)
    {
        if (hurt.HurtSoundAudioAsset.IsEmpty) return;

        if (hurt.DelayHurtSound > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(
                hurt.DelayHurtSound,
                () => _soundManager.PlaySoundAsset(hurt.HurtSoundAudioAsset, hurtPoint)));
        }
        else
        {
            _soundManager.PlaySoundAsset(hurt.HurtSoundAudioAsset, hurtPoint);
        }
    }

    /// <summary>
    /// 处理多段击中特效
    /// </summary>
    private void HandleHitEffects(SkillEditorPrefabDataConfig.HurtData hurt, Transform root, Transform hurtPoint, System.Action perHit)
    {
        if (hurt.HitCount > 0)
        {
            System.Action hitCallback = () =>
            {
                _soundManager.PlayHitSound(hurt.hitSounds);
                perHit?.Invoke();
            };

            if (hurt.DelayHitEffect > 0f)
            {
                Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(
                    hurt.DelayHitEffect,
                    () => Scheduler.RunCoroutine(PlayHitEffect(hurt, root, hurtPoint, hitCallback))));
            }
            else
            {
                Scheduler.RunCoroutine(PlayHitEffect(hurt, root, hurtPoint, hitCallback));
            }
        }
        else
        {
            perHit?.Invoke();
        }
    }

    /// <summary>
    /// 播放击中特效
    /// </summary>
    private IEnumerator PlayHurtEffect(SkillEditorPrefabDataConfig.HurtData data, Transform root, Transform hurtPoint)
    {
        yield return SpawnHurtEffectInstance(data, (effectInstance) =>
        {
            if (effectInstance == null) return;

            SetupHurtEffectTransform(effectInstance, data, root, hurtPoint);
            RegisterHurtEffectEvents(effectInstance, data);
            effectInstance.Play();
        });
    }

    /// <summary>
    /// 播放多段击中特效
    /// </summary>
    private IEnumerator PlayHitEffect(SkillEditorPrefabDataConfig.HurtData data, Transform root,
        Transform hurtPoint, System.Action perHit)
    {
        string[] hitIntervals = data.HitInterval.Split('|');

        for (int i = 0; i < data.HitCount; ++i)
        {
            yield return PlaySingleHitEffect(data, root, hurtPoint);
            perHit?.Invoke();

            if (i < hitIntervals.Length)
            {
                yield return new WaitForSeconds(SkillEditorUtils.SafeParseFloat(hitIntervals[i]));
            }
        }
    }

    /// <summary>
    /// 播放单次击中特效
    /// </summary>
    private IEnumerator PlaySingleHitEffect(SkillEditorPrefabDataConfig.HurtData data, Transform root, Transform hurtPoint)
    {
        if (data.HitEffect.IsEmpty) yield break;

        yield return SpawnHitEffectInstance(data, root, hurtPoint, (hitEffectInstance) =>
        {
            if (hitEffectInstance == null) return;

            SetupHitEffectTransform(hitEffectInstance, data, root, hurtPoint);
            RegisterHitEffectEvents(hitEffectInstance, data);
            hitEffectInstance.Play();
        });
    }

    /// <summary>
    /// 生成击中特效实例
    /// </summary>
    private IEnumerator SpawnHurtEffectInstance(SkillEditorPrefabDataConfig.HurtData data, System.Action<EffectControl> callback)
    {
        var wait = GameObjectPool.Instance.SpawnAsset(data.HurtEffect);
        yield return wait;

        var gameObject = wait.Instance;
        var instance = gameObject?.GetComponent<EffectControl>();

        if (instance == null)
        {
            if (gameObject != null)
                GameObjectPool.Instance.Free(gameObject);
            callback?.Invoke(null);
            yield break;
        }

        instance.Reset();
        callback?.Invoke(instance);
    }

    /// <summary>
    /// 生成击中特效实例
    /// </summary>
    private IEnumerator SpawnHitEffectInstance(SkillEditorPrefabDataConfig.HurtData data, Transform root, Transform hurtPoint, System.Action<EffectControl> callback)
    {
        var wait = GameObjectPool.Instance.SpawnAsset(data.HitEffect);
        yield return wait;

        var gameObject = wait.Instance;
        var instance = gameObject?.GetComponent<EffectControl>();

        if (instance == null || root == null || hurtPoint == null)
        {
            if (gameObject != null)
                GameObjectPool.Instance.Free(gameObject);
            callback?.Invoke(null);
            yield break;
        }

        instance.Reset();
        callback?.Invoke(instance);
    }

    /// <summary>
    /// 设置击中特效变换
    /// </summary>
    private void SetupHurtEffectTransform(EffectControl instance, SkillEditorPrefabDataConfig.HurtData data,
        Transform root, Transform hurtPoint)
    {
        Transform targetPos = GetHurtTargetPosition(data, root, hurtPoint);

        if (data.HurtRotation == SkillEditorPrefabDataConfig.HurtRotationEnum.Target)
        {
            instance.transform.SetPositionAndRotation(targetPos.position, targetPos.rotation);
        }
        else
        {
            SetupHurtEffectDirectionalRotation(instance, targetPos);
        }
    }

    /// <summary>
    /// 设置击中特效变换
    /// </summary>
    private void SetupHitEffectTransform(EffectControl instance, SkillEditorPrefabDataConfig.HurtData data,
        Transform root, Transform hurtPoint)
    {
        Transform targetPos = GetHurtTargetPosition(data, root, hurtPoint);

        if (data.HitRotation == SkillEditorPrefabDataConfig.HurtRotationEnum.Target)
        {
            instance.transform.SetPositionAndRotation(targetPos.position, targetPos.rotation);
        }
        else
        {
            SetupHurtEffectDirectionalRotation(instance, targetPos);
        }
    }

    /// <summary>
    /// 获取击中目标位置
    /// </summary>
    private Transform GetHurtTargetPosition(SkillEditorPrefabDataConfig.HurtData data, Transform root, Transform hurtPoint)
    {
        return data.HurtPosition == SkillEditorPrefabDataConfig.HurtPositionEnum.HurtPoint ? hurtPoint : root;
    }

    /// <summary>
    /// 设置击中特效方向旋转
    /// </summary>
    private void SetupHurtEffectDirectionalRotation(EffectControl instance, Transform targetPos)
    {
        if (_modelManager?.MainAnimator == null) return;

        var direction = targetPos.position - _modelManager.MainAnimator.transform.position;
        direction.y = 0.0f;
        instance.transform.SetPositionAndRotation(targetPos.position, Quaternion.LookRotation(direction));
    }

    /// <summary>
    /// 注册击中特效事件
    /// </summary>
    private void RegisterHurtEffectEvents(EffectControl instance, SkillEditorPrefabDataConfig.HurtData data)
    {
        instance.FinishEvent += () =>
        {
            Scheduler.RunCoroutine(SkillEditorUtils.FreeGameObjectCoroutine(instance.gameObject, data.HurtFreeDelay));
        };
    }

    /// <summary>
    /// 注册击中特效事件
    /// </summary>
    private void RegisterHitEffectEvents(EffectControl instance, SkillEditorPrefabDataConfig.HurtData data)
    {
        instance.FinishEvent += () =>
        {
            Scheduler.RunCoroutine(SkillEditorUtils.FreeGameObjectCoroutine(instance.gameObject, data.HitFreeDelay));
        };
    }
    #endregion
}
