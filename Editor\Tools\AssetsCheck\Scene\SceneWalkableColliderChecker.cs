﻿using UnityEngine;
using System.Text;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using Nirvana;

namespace AssetsCheck
{
    class SceneWalkableColliderChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private static string[] checkDirs = { "Assets/Game/Scenes/Map" };

        private static string materialPath = "Assets/Game/Misc/Material/EmptyRender.mat";

        override public string GetErrorDesc()
        {
            return string.Format("场景的地面可行走碰撞体规范化处理");
        }

        override protected void OnCheck()
        {

        }

        override protected void OnFix(string[] lines)
        {
            FixedSceneColliderInPaths(checkDirs);
        }

        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(asset);
                return builder;
            }
        }

        [MenuItem("自定义工具/美术专用/场景walkable碰撞体一键规范处理")]
        public static void OneKeyFixedSceneColliderInTool()
        {
            FixedSceneColliderInPaths(checkDirs);
        }

        [MenuItem("Assets/美术专用/场景walkable碰撞体一键规范处理", priority = 0)]
        public static void OneKeyFixedSceneCollider()
        {
            string[] checkDirs = null;
            if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
                {
                    int totalCount = Selection.instanceIDs.Length;
                    for (int i = 0; i < totalCount; i++)
                    {
                        int instanceID = Selection.instanceIDs[i];
                        string path = AssetDatabase.GetAssetPath(instanceID);

                        if (!path.StartsWith("Assets/Game/Scenes/Map"))
                            continue;

                        if (AssetDatabase.IsValidFolder(path))
                        {
                            checkDirs = new string[] { path };
                            FixedSceneColliderInPaths(checkDirs);
                        }
                        else
                        {
                            Debug.LogError("请选择文件夹进行操作");
                        }
                    }
                }
                else if (null != Selection.activeObject)
                {
                    string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                    if (!path.StartsWith("Assets/Game/Scenes/Map"))
                        return;

                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedSceneColliderInPaths(checkDirs);
                    }
                    else
                    {
                        Debug.LogError("请选择文件夹进行操作");
                    }
                }
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }

        public static void FixedSceneColliderInPaths(string[] checkDirs)
        {
            Material mat = AssetDatabase.LoadAssetAtPath(materialPath, typeof(Material)) as Material;
            if (mat == null)
            {
                Debug.LogError("load Assets/Game/Misc/Material/EmptyRender.mat fail, please check!");
                return;
            }

            int totalCount = 0;
            int curCount = 0;
            string[] guids = AssetDatabase.FindAssets("t:Scene", checkDirs);
            totalCount = guids.Length;
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                if (path.EndsWith("_Main.unity"))
                {
                    bool IsFindColliders = false;
                    Scene scene = EditorSceneManager.OpenScene(path);
                    GameObject[] root_objs = scene.GetRootGameObjects();
                    //处理场景walkable地面碰撞体
                    for (int i = 0; i < root_objs.Length; i++)
                    {
                        Transform colliderTrans = root_objs[i].gameObject.transform.Find("Colliders");
                        if (colliderTrans != null)
                        {
                            IsFindColliders = true;
                            MeshCollider[] meshCollider = colliderTrans.GetComponentsInChildren<MeshCollider>();
                            for (int j = 0; j < meshCollider.Length; j++)
                            {
                                GameObject gameobject = meshCollider[j].gameObject;
                                gameobject.layer = GameLayers.Walkable;
                                MeshRenderer renderer = meshCollider[j].transform.GetComponent<MeshRenderer>();
                                if (renderer != null)
                                {
                                    renderer.sharedMaterial = mat;
                                    PrefabType prefabType = PrefabUtility.GetPrefabType(gameobject as Object);
                                    if (prefabType == PrefabType.PrefabInstance)
                                    {
                                        string originPath = AssetDatabase.GetAssetPath(PrefabUtility.GetPrefabParent(gameobject));
                                        Object originPrefab = AssetDatabase.LoadAssetAtPath(originPath, typeof(Object)) as Object;
                                        if (originPrefab)
                                        {
                                            PrefabUtility.ReplacePrefab(gameobject, originPrefab, ReplacePrefabOptions.ConnectToPrefab);
                                        }
                                    }
                                    renderer.enabled = true;
                                    if (renderer.materials.Length >= 2)
                                    {
                                        Debug.LogErrorFormat("场景：{0} 碰撞体设置了2个材质球，请检查！", scene.name);
                                    }
                                }
                                else
                                {
                                    Debug.LogErrorFormat("场景：{0} 碰撞体：{1} 没有挂MeshRenderer组件，请检查！", scene.name, meshCollider[j].gameObject.name);
                                }
                            }
                            break;
                        }
                    }
                    if (!IsFindColliders)
                    {
                        Debug.LogErrorFormat("场景：{0}没有找到Colliders命名的节点，请检查！", scene.name);
                    }

                    bool IsFindModels = false;
                    //处理Model里设置了walkable的gameobject
                    for (int i = 0; i < root_objs.Length; i++)
                    {
                        Transform model = root_objs[i].gameObject.transform.Find("Models");
                        if (model != null)
                        {
                            IsFindModels = true;
                            Transform[] transforms = model.GetComponentsInChildren<Transform>(true);
                            for (int j = 0; j < transforms.Length; j++)
                            {
                                GameObject obj = transforms[j].gameObject;
                                if (obj.layer == GameLayers.Walkable)
                                {
                                    obj.layer = GameLayers.Default;
                                    PrefabType prefabType = PrefabUtility.GetPrefabType(obj as Object);
                                    if (prefabType == PrefabType.PrefabInstance)
                                    {
                                        string originPath = AssetDatabase.GetAssetPath(PrefabUtility.GetPrefabParent(obj));
                                        Object originPrefab = AssetDatabase.LoadAssetAtPath(originPath, typeof(Object)) as Object;
                                        if (originPrefab)
                                        {
                                            PrefabUtility.ReplacePrefab(obj, originPrefab, ReplacePrefabOptions.ConnectToPrefab);
                                        }
                                    }
                                }
                            }

                            break;
                        }
                    }
                    if (!IsFindModels)
                    {
                        Debug.LogErrorFormat("场景：{0}没有找到Models命名的节点，请检查！", scene.name);
                    }

                    GameObject HeroLight = GameObject.Find("Main/Hero light");
                    if (null != HeroLight)
                    {
                        Light light = HeroLight.GetOrAddComponent<Light>();
                        light.shadows = LightShadows.None;
                    }
                    else
                    {
                        Debug.LogErrorFormat("场景：{0}没有找到Hero light，请检查！", scene.name);
                    }

                    EditorSceneManager.MarkSceneDirty(scene);
                    EditorSceneManager.SaveScene(scene);
                    EditorSceneManager.CloseScene(scene, true);
                }

                curCount++;
                EditorUtility.DisplayProgressBar("正在处理中..."
                        , string.Format("{0} / {1}", curCount, totalCount)
                        , (float)curCount / (float)totalCount);
            }
        }
    }
}
