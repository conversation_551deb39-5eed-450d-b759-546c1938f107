﻿using UnityEngine;
using UnityEditor;

public static class UITool
{
    private static Vector2 AnchorMid = new Vector2(0.5f, 0.5f);
    private static Vector2 AnchorMin = Vector2.zero;
    private static Vector2 AnchorMax = Vector2.one;

    [MenuItem("Assets/修正锚点")]
    public static void AutoFixAnchor()
    {
        var gameObject = Selection.activeGameObject;
        if (gameObject == null)
        {
            return;
        }

        var rectTransform = gameObject.GetComponent<RectTransform>();
        if (rectTransform == null)
        {
            return;
        }

        var originPath = AssetDatabase.GetAssetPath(gameObject);

        if (rectTransform.anchorMin != AnchorMid || rectTransform.anchorMax != AnchorMid)
        {
            Debug.LogError("只修正锚点在中间的RectTransform");
            return;
        }

        gameObject = GameObject.Instantiate(gameObject);
        rectTransform = gameObject.GetComponent<RectTransform>();

        var pos = rectTransform.position;

        var factor = 1334 / 2.0f - rectTransform.rect.width / 2.0f;
        var left = factor - pos.x;
        var right = factor + pos.x;

        factor = 768 / 2.0f - rectTransform.rect.height / 2.0f;
        var top = factor - pos.y;
        var bottom = factor + pos.y;

        rectTransform.anchorMin = AnchorMin;
        rectTransform.anchorMax = AnchorMax;

        rectTransform.offsetMin = new Vector2(left, bottom);
        rectTransform.offsetMax = new Vector2(-right, -top);

        PrefabUtility.ReplacePrefab(gameObject, AssetDatabase.LoadAssetAtPath<GameObject>(originPath));

        Selection.activeGameObject = AssetDatabase.LoadAssetAtPath<GameObject>(originPath);

        GameObject.DestroyImmediate(gameObject);
    }
}
