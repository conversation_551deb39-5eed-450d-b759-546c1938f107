﻿using System.Collections.Generic;
using UnityEngine;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using UnityEditor;

public static class BuildText
{
    private static readonly string[] directoriesToSearch = new string[]
    {
        "Assets/Game/Lua/config",
        "Assets/Game/Lua/language"
    };

    private static readonly string[] filesToProcess = new string[]
    {
        "Assets/Game/Lua/gameui/common/common_ui_wg_datamanager.lua"
    };

    // 过滤
    private static readonly string rootPath = Application.dataPath.Replace("/Assets", "");
    private static readonly HashSet<string> blackList = new HashSet<string>()
    {
        "config_text.lua",
        "config_chatfilter.lua",
    };

    // 必定加入
    private static readonly HashSet<string> whiteList = new HashSet<string>()
    {
        "randname_auto.lua",
    };

    public static void Build()
    {
        Dictionary<char, int> charList = new Dictionary<char, int>();
        List<string> fileList = new List<string>();

        // 收集指定文件夹下的所有文件
        foreach (var dir in directoriesToSearch)
        {
            string filePath = Path.Combine(rootPath, dir).Replace("\\", "/");
            fileList.AddRange(GetAllFiles(filePath));
        }

        // 添加指定的文件
        foreach (var file in filesToProcess)
        {
            string filePath = Path.Combine(rootPath, file);
            if (File.Exists(filePath))
            {
                fileList.Add(filePath);
            }
        }

        for (int i = 0; i < fileList.Count; ++i)
        {
            var file = fileList[i];
            // 只处理 .lua 文件
            if (!file.EndsWith(".lua"))
                continue;

            string name = Path.GetFileName(file);
            if (blackList.Contains(name))
                continue;

            bool isWhiteList = whiteList.Contains(name);
            var fileString = OpenFile(file);
            // 使用正则表达式匹配中文字符
            var matches = Regex.Matches(fileString, "\"([\u4e00-\u9fa5])+\"");
            foreach (Match match in matches)
            {
                var charArray = match.Value.ToCharArray();
                foreach (var ch in charArray)
                {
                    // 跳过引号
                    if (ch == '\"')
                        continue;

                    if (!charList.ContainsKey(ch))
                    {
                        charList.Add(ch, isWhiteList ? 999 : 1);
                    }
                    else
                    {
                        if (isWhiteList)
                            charList[ch] = 999;
                        else
                            charList[ch] += 1;
                    }
                }
            }
        }

        // 构建最终字符串
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append("return \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
        // 只统计出现次数大于8次的
        foreach (var pair in charList)
        {
            if (pair.Value < 8)
                continue;

            stringBuilder.Append(pair.Key);
        }
        stringBuilder.Append("\"");

        File.WriteAllText(Path.Combine(Application.dataPath, "Game/Lua/config/config_text.lua"), stringBuilder.ToString());
        AssetDatabase.Refresh();
    }

    private static string[] GetAllFiles(string folder)
    {
        if (!IsFolder(folder))
            return null;

        List<string> fileList = new List<string>();
        Queue<string> folderQueue = new Queue<string>();
        folderQueue.Enqueue(folder);

        while (folderQueue.Count > 0)
        {
            var path = folderQueue.Dequeue();
            DirectoryInfo info = new DirectoryInfo(path);
            foreach (FileSystemInfo fsi in info.GetFileSystemInfos())
            {
                // 只处理 .lua 文件
                if (fsi is System.IO.FileInfo)
                {
                    if (fsi.FullName.EndsWith(".lua"))
                    {
                        fileList.Add(fsi.FullName);
                    }
                }
                else // 如果是文件夹，加入队列继续遍历
                {
                    folderQueue.Enqueue(fsi.FullName);
                }
            }
        }
        return fileList.ToArray();
    }

    // 判断是否是文件夹
    private static bool IsFolder(string path)
    {
        if (!File.Exists(path) && Directory.Exists(path))
            return true;
        return false;
    }

    private static string OpenFile(string filePath)
    {
        if (!File.Exists(filePath))
            return string.Empty;

        FileStream fs = new FileStream(filePath, FileMode.Open);
        byte[] bytes = new byte[fs.Length];
        fs.Read(bytes, 0, bytes.Length);
        string fileString = Encoding.UTF8.GetString(bytes);
        fs.Close();
        return fileString;
    }
}
