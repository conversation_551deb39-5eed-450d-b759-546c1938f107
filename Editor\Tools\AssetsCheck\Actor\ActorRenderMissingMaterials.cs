﻿using UnityEngine;
using UnityEditor;
using Nirvana;
using System.Text;
using System.IO;
using Game;
using System.Collections.Generic;

namespace AssetsCheck
{
    class ActorRenderMissingMaterials : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/Actors", "Assets/Game/Model", "Assets/Game/Effects", };

        override public string GetErrorDesc()
        {
            return string.Format("Actor上的渲染ActorRender 的材质丢失问题");
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);

            int count = 0;
            int total = guids.Length;

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                CheckItem checkItem = new CheckItem();
                checkItem.asset = path;

                ActorRender actorRender = gameobj.GetComponent<ActorRender>();
                if (actorRender != null)
                {
                    bool is_miss = this.ChaeckIsMissingMaterials(actorRender);
                 
                    if (is_miss)
                    {
                        this.outputList.Add(checkItem);
                    }
                }

                count++;
                EditorUtility.DisplayProgressBar("正在检查材质丢失引用...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }
            EditorUtility.ClearProgressBar();
        }


        //检测是否丢失材质
        private bool ChaeckIsMissingMaterials(ActorRender actorRender)
        {
            if (actorRender.RenderList.Count <= 0)
                return true;

            for (int i = 0; i < actorRender.RenderList.Count; i++)
            {
                ActorRender.RenderItem renderItem = actorRender.RenderList[i];

                if (renderItem.renderer == null)
                    return true;

                //if (renderItem.uiMaterials.Length <= 0)
                //    return true;
                //else
                //{
                //    for (int j = 0; j < renderItem.uiMaterials.Length; j++)
                //    {
                //        Material material = renderItem.uiMaterials[j];
                //        if (material == null)
                //            return true;
                //    }
                //}

                if (renderItem.pbrMaterials.Length <= 0)
                    return true;
                else
                {
                    for (int j = 0; j < renderItem.pbrMaterials.Length; j++)
                    {
                        Material material = renderItem.pbrMaterials[j];
                        if (material == null)
                            return true;
                    }
                }
            }
            
            return false;
        }


        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get
                {
                    return string.Format("{0}", this.asset);
                }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(this.asset);
                return builder;
            }
        }
    }
}
