﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditorInternal;
using System.Text;
using System.IO;
using Nirvana;
using AssetsCheck;

[CustomEditor(typeof(UIBundleDependChecker))]
public class UIBundleDependCheckerEditor : Editor
{
    private string fileName = "UIBundleDependCheck";

    private ReorderableList subItemsList;
    private SerializedProperty subItems;

    Color? elementDefaultColor = null;
    private string findName = "";
    private HashSet<int> highLightList = new HashSet<int>();

    public override void OnInspectorGUI()
    {
        this.serializedObject.Update();
        this.subItemsList.DoLayoutList();
        this.serializedObject.ApplyModifiedProperties();

        findName = EditorGUILayout.TextField(findName);
        if (GUILayout.Button("Find"))
        {
            SearchFolder(findName);
        }
    }

    private void OnEnable()
    {
        var serObj = this.serializedObject;
        this.subItems = serObj.FindProperty("list");
        this.subItemsList = new ReorderableList(serObj, this.subItems);
        if (elementDefaultColor == null)
        {
            elementDefaultColor = GUI.color;
        }

        this.subItemsList.drawHeaderCallback =
            rect => GUI.Label(rect, "Sub-Items:");
        this.subItemsList.elementHeightCallback =
            index => this.DrawSubItemsHeight(this.subItems, index);
        this.subItemsList.drawElementCallback =
            (rect, index, selected, focused) =>
            {
                this.DrawSubItems(
                    this.subItems, rect, index, selected, focused);
            };
        this.subItemsList.drawElementBackgroundCallback = (rect, index, isActive, isFocused) =>
        {
            if (highLightList.Contains(index))
            {
                GUI.color = new Color(0f, 1f, 0f, 0.8f);                                // 修改GUI颜色
            }
            else
            {
                GUI.color = (Color)elementDefaultColor;                                 // 还原GUI颜色
            }
            ReorderableList.defaultBehaviours.DrawElementBackground(rect, index, isActive, isFocused, true);
        };
    }

    private float DrawSubItemsHeight(
            SerializedProperty property, int index)
    {
        return 3 * EditorGUIUtility.singleLineHeight;
    }

    private void DrawSubItems(
            SerializedProperty property,
            Rect rect,
            int index,
            bool selected,
            bool focused)
    {
        var element = property.GetArrayElementAtIndex(index);

        // Get all properties.
        var prefabFloder =
            element.FindPropertyRelative("prefabFloder");
        var atlasFloder =
            element.FindPropertyRelative("atlasFloder");

        // Start rect line.
        var rectLine = new Rect(
            rect.x,
            rect.y,
            rect.width,
            EditorGUIUtility.singleLineHeight);

        EditorGUI.PropertyField(
            rectLine,
            prefabFloder,
            new GUIContent("Prefab路径:"));

        rectLine.y += EditorGUIUtility.singleLineHeight;
        EditorGUI.PropertyField(
            rectLine,
            atlasFloder,
            new GUIContent("图片路径:"));
    }

    private void SearchFolder(string name)
    {
        highLightList.Clear();
        UIBundleDependChecker checker = target as UIBundleDependChecker;
        for (int i = 0; i < checker.list.Count; ++i)
        {
            var item = checker.list[i];
            var floder = item.prefabFloder;
            if (null == floder)
                continue;

            var path = AssetDatabase.GetAssetPath(floder.GetInstanceID());
            if (Path.GetFileNameWithoutExtension(path) == name)
            {
                highLightList.Add(i);
            }
        }
    }
}
