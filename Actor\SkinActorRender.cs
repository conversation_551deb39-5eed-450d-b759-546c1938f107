﻿using Game;
using LuaInterface;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class SkinActorRender : MonoBehaviour {
    [SerializeField]
    private List<RenderItem> renderList = new List<RenderItem>();

    [NoToLua]
    public List<RenderItem> RenderList
    {
        get
        {
            return renderList;
        }
    }

#if UNITY_EDITOR
    [NoToLua]
    public void AutoFetch()
    {
        Renderer[] renderers = this.GetComponentsInChildren<Renderer>();
        List<RenderItem> list = new List<RenderItem>();
        foreach (var render in renderers)
        {
            if (render.GetComponent<ParticleSystem>() || render.GetComponentInParent<GameObjectAttach>())
            {
                continue;
            }

            foreach (var material in render.sharedMaterials)
            {
                if (null == material)
                {
                    Debug.LogErrorFormat("material is null , {0}", AssetDatabase.GetAssetPath(this.gameObject.GetInstanceID()));
                    continue;
                }

                if (!material.HasProperty("_MaterialStyle"))
                {
                    continue;
                }

                bool isMainRender = material.GetFloat("_MaterialStyle") == 1;

                RenderItem item = new RenderItem();
                item.highMaterial = render.sharedMaterial;
                if (!isMainRender)
                {
                    item.lowMaterial = null;
                }
                else
                {
                    string[] mat_stuffs = new string[] { "low", "mid" , "ui"};
                    for (int i = 0; i < mat_stuffs.Length; i++)
                    {
                        string path = AssetDatabase.GetAssetPath(material.GetInstanceID());
                        string matFileName = Path.GetFileNameWithoutExtension(path);
                        string newMatName = string.Format("{0}_{1}.mat", matFileName, mat_stuffs[i]);
                        path = path.Replace(matFileName + ".mat", newMatName);

                        Material qualityMat = AssetDatabase.LoadAssetAtPath<Material>(path);
                        if (null == qualityMat)
                        {
                            AssetDatabase.CopyAsset(AssetDatabase.GetAssetPath(material.GetInstanceID()), path);
                            qualityMat = AssetDatabase.LoadAssetAtPath<Material>(path);
                            AssetDatabase.ImportAsset(path, ImportAssetOptions.Default);

                            Debug.LogFormat("not foud low material, so create new {0}", path);
                        }

                        qualityMat.SetFloat("_SrcBlend", material.GetFloat("_SrcBlend"));
                        qualityMat.SetFloat("_DstBlend", material.GetFloat("_DstBlend"));
                        qualityMat.SetFloat("_ZWrite", material.GetFloat("_ZWrite"));
                        qualityMat.SetFloat("_Cutoff", material.GetFloat("_Cutoff"));
                        qualityMat.SetFloat("_CullMode", material.GetFloat("_CullMode"));
                        qualityMat.SetFloat("_MaterialStyle", material.GetFloat("_MaterialStyle"));
                        qualityMat.SetFloat("_RenderingMode", material.GetFloat("_RenderingMode"));

                        // 低品质材质质球基本所有都关闭
                        if (0 == i)
                        {
                            string[] keywords = qualityMat.shaderKeywords;
                            for (int m = 0; m < keywords.Length; m++)
                            {
                                if (keywords[m] == "ENABLE_ALPHA_TEST" || keywords[m] == "ENABLE_ALPHA_BLEND")
                                {
                                    continue;
                                }
                                qualityMat.DisableKeyword(keywords[m]);
                            }
                            item.lowMaterial = qualityMat;
                        }
                        // 中品质材质球去掉法线等
                        else if (1 == i)
                        {
                            qualityMat.DisableKeyword("ENABLE_NORMAL_TEX");
                            qualityMat.DisableKeyword("ENABLE_RIM");
                            qualityMat.DisableKeyword("ENABLE_RIM_LIGHT");
                            qualityMat.DisableKeyword("ENABLE_CHANNEL_MASK");
                            qualityMat.DisableKeyword("ENABLE_VIRTUAL_LIGHT");
                            qualityMat.DisableKeyword("ENABLE_VIRTUAL_LIGHT_DIR");

                            item.midMaterial = qualityMat;
                        }
                        else if (2 == i)
                        {
                            qualityMat.EnableKeyword("ENABLE_VIRTUAL_LIGHT");
                            qualityMat.EnableKeyword("ENABLE_VIRTUAL_LIGHT_DIR");
                            qualityMat.EnableKeyword("ENABLE_HD_TEX");
                            item.uiMaterial = qualityMat;
                        }
                    }

                }

                list.Add(item);
                break;
            }
        }

        this.renderList = list;
    }
#endif
}


[Serializable]
public struct RenderItem
{
    public Material highMaterial;
    public Material midMaterial;
    public Material lowMaterial;
    public Material uiMaterial;
    public bool notCastShadow;
}