﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


using UnityEditor;
using System.Text;

using Build;

public class ABMaterialMarkRule
{
    private static Dictionary<string, int> dicKeywordsIds = new Dictionary<string, int>();

    private static HashSet<int> keyword_id_set = new HashSet<int>();

    public static string TryGetMaterialName(string asset)
    {
        Material material = GetYYMaterial(asset);

        if (material == null)
        {
            return string.Empty;
        }
        return TryGetMaterialName(material,false);
    }

    public static string TryGetMaterialName(Material material,bool noFlod = true) { 
        if (dicKeywordsIds.Count <= 0)
        {
            dicKeywordsIds.Clear();
            keyword_id_set.Clear();
            BuildMaterial.GetKeywordIdDic(dicKeywordsIds, keyword_id_set);
        }

        //string[] keywords = material.shaderKeywords;

        var nameBuilder = new StringBuilder();
        if (!noFlod)
            nameBuilder.Append("materials/");
        nameBuilder.Append(material.shader.name.Replace('/', '_'));

        List<int> keyWordIdList = new List<int>();
        bool has_new = false;
        foreach (var keyword in material.shaderKeywords)
        {
            int keyword_id = 0;
            if (!dicKeywordsIds.TryGetValue(keyword, out keyword_id))
            {
                keyword_id = BuildMaterial.GetIncKeywordId(keyword_id_set);
                if (keyword_id <= 0)
                {
                    Debug.LogError("keyword_id  is erro");
                    return string.Empty;
                }

                dicKeywordsIds.Add(keyword, keyword_id);
                //添加到文件中
                has_new = true;
                //BuildMaterial.SaveKeywordIdDic(dicKeywordsIds);
                //
            }
            keyWordIdList.Add(keyword_id);
        }
        if (has_new)
            BuildMaterial.SaveKeywordIdDic(dicKeywordsIds);
            //
        // 排序
        keyWordIdList.Sort();
        for (int i = 0; i < keyWordIdList.Count; i++)
        {
            nameBuilder.Append('_');
            nameBuilder.Append(keyWordIdList[i]);
        }
        //Debug.Log("name is " + nameBuilder);
        return nameBuilder.ToString();
    }

    public static bool IsYYShaderPath(string asset)
    {
        if (asset.Contains("YYStandard")
            ||asset.Contains("YifStandard"))
        {
            return true;
        }
        return false;
    }

    public static bool IsYYShader(string shaderName)
    {
        //Debug.Log("IsYYShader is " + shaderName);
        if (shaderName.Contains("YY/YYStandard")
           || shaderName.Contains("YY/YYStandardMap")
           || shaderName.Contains("YY/YYStandardT4M")
           || shaderName.Contains("YifStandard"))
        {
            return true;
        }

        return false;
    }

    private static Material GetYYMaterial(string asset)
    {
        if (!asset.EndsWith(".mat"))
        {
            return null;
        }
        Material material = AssetDatabase.LoadAssetAtPath<Material>(asset);
        if (material == null)
        {
            return null;
        }
        if (IsYYShader(material.shader.name))
        {
            return material;
        }

        if(material.shader.name == "Standard")
        {
            material.shader = null;
            Debug.Log("standard mat is " + asset);
        }
        return null;
    }
}
