﻿
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using UnityEngine.Rendering;

class ModelAssetImporter : AssetPostprocessor
{
    private static string SceneEnviromentDir = "Assets/Game/Environments/";
    private static string EffectsDir = "Assets/Game/Effects/";
    private static string OpenReadWriteDIR = "Assets/Game/Effects/FBX/OpenReadWrite";
    private static readonly string ActorDir = "Assets/Game/Actors/";
    private static readonly string ModelDir = "Assets/Game/Model/";
    private static readonly string ArtExmaple = "Assets/Game/Scenes/ArtExample/";


    public void OnPreprocessModel()
    {
        var modelImporter = assetImporter as ModelImporter;

        ProcessMaterials(modelImporter);

        if (ImporterUtils.IsIgnoreImportRule(modelImporter))
        {
            return;
        }

        if (modelImporter.assetPath.StartsWith(ArtExmaple))
        {
            return;
        }
        
        AutoAnimationClip(modelImporter);

        if (modelImporter.assetPath.StartsWith(AssetBundleMarkRule.ActorsDir) || modelImporter.assetPath.StartsWith(AssetBundleMarkRule.ModelDir))
        {
            modelImporter.preserveHierarchy = true;
            SetAnimationClipLoop(modelImporter);
        }

        ProcessGlobalScele(modelImporter);
        ProcessIsReadable(modelImporter);
        ProcessResampleCurves(modelImporter);
        ProcessOptimizeMesh(modelImporter);
        ProcessAvatar(modelImporter);
        ProcessLightmapUV(modelImporter);
        ProcessMeshCompression(modelImporter);
        ProcessAnimationCompression(modelImporter);
        ProcessNormal(modelImporter);
    }

    public void OnPostprocessModel(GameObject model)
    {
        var modelImporter = assetImporter as ModelImporter;
        
        if (modelImporter.assetPath.StartsWith(ArtExmaple))
        {
            return;
        }
        
        Renderer[] renders = model.GetComponentsInChildren<Renderer>();
        MeshFilter[] meshFilters = model.GetComponentsInChildren<MeshFilter>();
        SkinnedMeshRenderer[] skinnedMeshRenderers = model.GetComponentsInChildren<SkinnedMeshRenderer>();
        string path = modelImporter.assetPath;
        ProcessRenderMaterial(renders, path);
        ProcessRenderShadow(renders, path);
        ProcessMeshChannels(meshFilters, path);
        ProcessSkinnedMeshRenderer(skinnedMeshRenderers, path);
    }

    // 处理render去掉材质球
    private static void ProcessRenderMaterial(Renderer[] renders, string path)
    {
        foreach (var render in renders)
        {
            render.sharedMaterials = new Material[render.sharedMaterials.Length];
        }
    }

    // 去掉阴影
    private static void ProcessRenderShadow(Renderer[] renders, string path)
    {
        if (path.StartsWith(SceneEnviromentDir) ||
            path.StartsWith(ActorDir) ||
            path.StartsWith(ModelDir))
        {
            return;
        }

        foreach (var render in renders)
        {
            render.receiveShadows = false;
            render.shadowCastingMode = ShadowCastingMode.Off;
        }
    }


    // 去掉顶点属性
    private static void ProcessMeshChannels(MeshFilter[] meshFilters, string path)
    {
        if (path.StartsWith(SceneEnviromentDir))
        {
            foreach (var meshFilter in meshFilters)
            {
                meshFilter.sharedMesh.colors = new Color[0];
                meshFilter.sharedMesh.uv3 = new Vector2[0];
                meshFilter.sharedMesh.uv4 = new Vector2[0];
            }
        }
    }

    // 禁用skinnedMotionVectors
    private static void ProcessSkinnedMeshRenderer(SkinnedMeshRenderer[] skinnedMeshRenderers, string path)
    {
        if (path.StartsWith(AssetBundleMarkRule.ActorsDir) || path.StartsWith(AssetBundleMarkRule.ModelDir))
        {
            foreach (var skinnedMeshRenderer in skinnedMeshRenderers)
            {
                skinnedMeshRenderer.skinnedMotionVectors = false;
            }
        }
    }


    private static void ProcessGlobalScele(ModelImporter assetImporter)
    {
        assetImporter.globalScale = 1;
    }


    private static void ProcessIsReadable(ModelImporter assetImporter)
    {
        if (assetImporter.assetPath.StartsWith(OpenReadWriteDIR))
        {
            assetImporter.isReadable = true;
            return;
        }
        
        if (assetImporter.assetPath.StartsWith(SceneEnviromentDir))
        {
            assetImporter.isReadable = false;
            return;
        }

        if (assetImporter.assetPath.StartsWith(AssetBundleMarkRule.ActorsDir)
            || assetImporter.assetPath.StartsWith(AssetBundleMarkRule.ModelDir))
        {
            assetImporter.isReadable = false;
            return;
        }

        if (ImporterUtils.CheckLabel(assetImporter, ImporterUtils.ReadableLabel))
        {
            return;
        }

        assetImporter.isReadable = false;
    }

    private static void ProcessResampleCurves(ModelImporter assetImporter)
    {
        assetImporter.resampleCurves = false;
    }

    private static void ProcessOptimizeMesh(ModelImporter assetImporter)
    {
        assetImporter.optimizeMesh = true;
    }

    private static void ProcessAvatar(ModelImporter assetImporter)
    {
        if (assetImporter.assetPath.StartsWith(ActorDir) 
            || assetImporter.assetPath.StartsWith(ModelDir) && assetImporter.assetPath.ToLower().Contains("@skin"))
        {
            assetImporter.animationType = ModelImporterAnimationType.Generic;
            assetImporter.avatarSetup = ModelImporterAvatarSetup.CreateFromThisModel;
        }
    }

    private static void ProcessLightmapUV(ModelImporter assetImporter)
    {
        if (assetImporter.assetPath.StartsWith(SceneEnviromentDir))
        {
            assetImporter.generateSecondaryUV = true;
        }
        else
        {
            assetImporter.generateSecondaryUV = false;
        }
    }

    private static void ProcessMeshCompression(ModelImporter assetImporter)
    {
        if (assetImporter.meshCompression < ModelImporterMeshCompression.Medium)
        {
            assetImporter.meshCompression = ModelImporterMeshCompression.Medium;
        }

        assetImporter.animationCompression = ModelImporterAnimationCompression.Optimal;
    }

    private static void ProcessAnimationCompression(ModelImporter assetImporter)
    {
        assetImporter.animationCompression = ModelImporterAnimationCompression.Optimal;

        // 处理A3角色模型动作武器节点变更+动作压缩误差，导致人物和武器动作不同步问题
        if (assetImporter.assetPath.StartsWith(ActorDir) && assetImporter.assetPath.Contains("idle"))
        {
            assetImporter.animationRotationError = 0;
            assetImporter.animationPositionError = 0;
            assetImporter.animationScaleError = 0;
        }
    }

    private static void ProcessMaterials(ModelImporter assetImporter)
    {
        assetImporter.materialImportMode = ModelImporterMaterialImportMode.None;
    }

    public static void ProcessNormal(ModelImporter assetImporter)
    {
        if (assetImporter.assetPath.StartsWith(EffectsDir))
        {
            assetImporter.importNormals = ModelImporterNormals.Import;
            assetImporter.importTangents = ModelImporterTangents.None;
        }
        else
        {
            //assetImporter.importNormals = ModelImporterNormals.Calculate;
            //assetImporter.normalSmoothingAngle = 80;
        }
    }

    static void AutoAnimationClip(ModelImporter importer)
    {
        var fileDir = Path.GetDirectoryName(importer.assetPath);
        var fileName = Path.GetFileNameWithoutExtension(importer.assetPath);
        var filePath = Path.Combine(fileDir, fileName + ".split");
        if (!File.Exists(filePath))
        {
            return;
        }

        if (importer.importedTakeInfos.Length == 0)
        {
            return;
        }

        var takeName = importer.importedTakeInfos[0].name;
        using (var reader = File.OpenText(filePath))
        {
            var clipAnimations = new List<ModelImporterClipAnimation>();
            while (!reader.EndOfStream)
            {
                var line = reader.ReadLine();
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                var tokens = line.Split(new char[] { ' ', '\t' }, System.StringSplitOptions.RemoveEmptyEntries);
                if (tokens.Length != 4)
                {
                    Debug.LogWarningFormat(
                        "The animation split file {0} format error: {1}",
                        fileName,
                        line);
                    continue;
                }

                var name = tokens[0];
                if (string.IsNullOrEmpty(name))
                {
                    Debug.LogWarningFormat(
                        "The animation split file {0} format error: {1}",
                        fileName,
                        line);
                    continue;
                }

                float start;
                if (!float.TryParse(tokens[1], out start))
                {
                    Debug.LogWarningFormat(
                        "The animation split file {0} format error: {1}",
                        fileName,
                        line);
                    continue;
                }

                float end;
                if (!float.TryParse(tokens[2], out end))
                {
                    Debug.LogWarningFormat(
                        "The animation split file {0} format error: {1}",
                        fileName,
                        line);
                    continue;
                }

                bool loop;
                if (!bool.TryParse(tokens[3], out loop))
                {
                    Debug.LogWarningFormat(
                        "The animation split file {0} format error: {1}",
                        fileName,
                        line);
                    continue;
                }

                var clip = new ModelImporterClipAnimation();
                clip.name = name;
                clip.firstFrame = start;
                clip.lastFrame = end;
                clip.loopTime = loop;
                clip.takeName = takeName;
                clipAnimations.Add(clip);
            }
            importer.clipAnimations = clipAnimations.ToArray();
        }
    }

    static void SetAnimationClipLoop(ModelImporter modelImporter)
    {
        string path = AssetDatabase.GetAssetPath(modelImporter);
        bool isLoop = CheckIsLoop(path);

        var clipAnimations = modelImporter.clipAnimations;
        foreach (var takeInfo in clipAnimations)
        {
            if (isLoop && takeInfo.loopTime == false)
            {
                takeInfo.loopTime = true;
            }
        }

        modelImporter.clipAnimations = clipAnimations;
    }

    static bool CheckIsLoop(string path)
    {
        var splits = path.Split('@');
        var aniNameSuffix = splits[splits.Length - 1];//取得@后面的动画名
        var aniNameNotSuffix = aniNameSuffix.Split('.')[0];//去掉后缀

        //动画包含下列关键词的动画循环
        if (aniNameNotSuffix.Contains("idle") ||
            aniNameNotSuffix.Contains("run") ||
            aniNameNotSuffix.Contains("walk") ||
            aniNameNotSuffix.Contains("mid"))
        {
            return true;
        }

        //动画名等于下列名的循环
        switch (aniNameNotSuffix)
        {
            case "fishing":
                return true;
            case "hug":
                return true;
            case "caiji":
                return true;
            case "chongci":
                return true;
            case "dunxia":
                return true;
        }
        return false;
    }
}

