using System;
using UnityEngine;
using UnityEngine.Assertions;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
#endif


[AddComponentMenu("新小地图相机组件")]
[ExecuteInEditMode]
[RequireComponent(typeof(Camera))]
public sealed class TopViewMapCamera : MonoBehaviour
{
    #region Public Properties
        
    /// <summary>
    /// 地图纹理
    /// </summary>
    public Texture2D MapTexture
    {
        get => mapTexture;
        set => mapTexture = value;
    }

    /// <summary>
    /// 地图纹理宽度
    /// </summary>
    public int MapTextureWidth => mapTextureWidth;

    /// <summary>
    /// 地图纹理高度
    /// </summary>
    public int MapTextureHeight => mapTextureHeight;

    #endregion

    #region Public Methods

    /// <summary>
    /// 设置忽略列表中物体的激活状态
    /// </summary>
    /// <param name="enabled">是否激活</param>
    public void SetIgnoreListEnabled(bool enabled)
    {
        if (ignoreList == null) return;
            
        foreach (GameObject obj in ignoreList)
        {
            if (obj != null)
            {
                obj.SetActive(enabled);
            }
        }
    }

    /// <summary>
    /// 快照当前相机的欧拉角Y值
    /// </summary>
    public void SnapCameraEulerAngles()
    {
        snapEulerAnglesY = transform.rotation.eulerAngles.y;
    }

    /// <summary>
    /// 将世界坐标转换为UV坐标
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <returns>UV坐标</returns>
    public Vector2 TransformWorldToUV(Vector3 worldPosition)
    {
        // 将世界坐标标准化到相机范围内
        float normalizedX = (worldPosition.x - cameraOrigin.x) / (2f * orthographicSize);
        float normalizedZ = (worldPosition.z - cameraOrigin.y) / (2f * orthographicSize);
            
        // 调整到以原点为中心
        normalizedX -= 0.5f;
        normalizedZ -= 0.5f;
            
        // 应用旋转变换
        Vector3 rotatedPosition = new Vector3(normalizedX, worldPosition.y, normalizedZ);
        float rotationY = snapEulerAnglesY;
        rotatedPosition = Quaternion.Euler(0f, -rotationY, 0f) * rotatedPosition;
            
        // 应用宽高比调整
        rotatedPosition.x *= aspectRatio;
            
        return new Vector2(rotatedPosition.x, rotatedPosition.z);
    }

    /// <summary>
    /// 将UV坐标转换为世界坐标
    /// </summary>
    /// <param name="uvPosition">UV坐标</param>
    /// <returns>世界坐标</returns>
    public Vector3 TransformUVToWorld(Vector2 uvPosition)
    {
        // 还原宽高比调整
        uvPosition.x /= aspectRatio;
            
        // 应用逆旋转变换
        Vector3 rotatedPosition = new Vector3(uvPosition.x, 0f, uvPosition.y);
        float rotationY = snapEulerAnglesY;
        rotatedPosition = Quaternion.Euler(0f, rotationY, 0f) * rotatedPosition;
            
        // 调整回标准化坐标系
        rotatedPosition.x += 0.5f;
        rotatedPosition.z += 0.5f;
            
        // 转换回世界坐标
        float worldX = rotatedPosition.x * 2f * orthographicSize + cameraOrigin.x;
        float worldZ = rotatedPosition.z * 2f * orthographicSize + cameraOrigin.y;
            
        return new Vector3(worldX, rotatedPosition.y, worldZ);
    }

    #endregion

    #region Unity Lifecycle

    private void Awake()
    {
        InitializeCamera();
        InitializeParameters();
            
        // 仅在运行时设置快照角度
        if (Application.isPlaying && Mathf.Approximately(snapEulerAnglesY, 0f))
        {
            SnapCameraEulerAngles();
        }
            
#if UNITY_EDITOR
        // 订阅编辑器事件
        EditorSceneManager.sceneSaved += OnSceneSaved;
#endif
    }

    private void OnDestroy()
    {
#if UNITY_EDITOR
        // 取消订阅编辑器事件
        EditorSceneManager.sceneSaved -= OnSceneSaved;
#endif
    }

    private void LateUpdate()
    {
        // 编辑模式下同步场景相机旋转
        if (!Application.isPlaying && sceneCamera != null)
        {
            float sceneCameraY = sceneCamera.transform.rotation.eulerAngles.y;
            transform.rotation = Quaternion.Euler(CAMERA_PITCH_ANGLE, sceneCameraY, 0f);
        }
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// 初始化相机组件
    /// </summary>
    private void InitializeCamera()
    {
        mapCamera = GetComponent<Camera>();
        Assert.IsNotNull(mapCamera, "MapCamera component is required!");
            
        // 自动配置为正交投影模式（适用于URP）
        if (!mapCamera.orthographic)
        {
            mapCamera.orthographic = true;
        }
            
        // 禁用相机渲染，因为这是用于坐标转换的辅助相机
        mapCamera.enabled = false;
    }

    /// <summary>
    /// 初始化相机参数
    /// </summary>
    private void InitializeParameters()
    {
        orthographicSize = mapCamera.orthographicSize;
        aspectRatio = (float)mapTextureHeight / (float)mapTextureWidth;
            
        Vector3 cameraPosition = mapCamera.transform.position;
        cameraOrigin = new Vector2(
            cameraPosition.x - orthographicSize, 
            cameraPosition.z - orthographicSize
        );
    }

#if UNITY_EDITOR
    /// <summary>
    /// 场景保存时的回调
    /// </summary>
    /// <param name="scene">保存的场景</param>
    private void OnSceneSaved(Scene scene)
    {
        string errorMessage = string.Empty;
            
        // 检查相机角度是否与快照角度一致
        if (snapEulerAnglesY > 0f && 
            !Mathf.Approximately(snapEulerAnglesY, transform.rotation.eulerAngles.y))
        {
            errorMessage = "TopViewMapCamera的角度与拍摄小地图时的角度不一致";
        }
            
        if (!string.IsNullOrEmpty(errorMessage))
        {
            EditorUtility.DisplayDialog("错误", errorMessage, "确定");
            Debug.LogError(errorMessage, this);
        }
    }
#endif

    #endregion

    #region Constants

    private const float CAMERA_PITCH_ANGLE = 90f;

    #endregion

    #region Serialized Fields

    [SerializeField]
    [Tooltip("地图纹理")]
    private Texture2D mapTexture;

    [SerializeField]
    [Tooltip("小地图纹理宽度")]
    private int mapTextureWidth = 512;

    [SerializeField]
    [Tooltip("小地图纹理高度")]
    private int mapTextureHeight = 512;

    [SerializeField]
    [Tooltip("场景相机")]
    private Camera sceneCamera;

    [SerializeField]
    [Tooltip("拍摄纹理时需要忽略的物体列表")]
    private GameObject[] ignoreList;

    [HideInInspector]
    [SerializeField]
    private float snapEulerAnglesY = 0f;

    #endregion

    #region Private Fields

    private Camera mapCamera;
    private Vector2 cameraOrigin;
    private float orthographicSize;
    private float aspectRatio;

    #endregion
}
