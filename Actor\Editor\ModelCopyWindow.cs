﻿using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using UnityEditor.Animations;
using System.Collections.Generic;
using static ModelAboutEditor;

public class ModelCopyWindow : OdinEditorWindow
{
    [MenuItem("自定义工具/策划工具/模型复用工具")]
    static void OpenWindow()
    {
        GetWindow<ModelCopyWindow>().Show();
    }

    [InfoBox("请注意:如果缺少动作或者武器,需要找动作处理,直接复制没用", InfoMessageType.Warning)]

    [InfoBox("请选择源模型", InfoMessageType.Error, "IsNullSrcModel")]
    [LabelText("源模型")]
    public GameObject srcModel;

    [Space]
    [LabelText("目标类型")]
    public ECopyModelType modelType;

    [LabelText("新编号")]
    [MinValue(1)]
    public int newNo;


    [DisableIf("IsNullSrcModel")]
    [But<PERSON>("复制", ButtonSizes.Large), GUIColor(0f, 0.8f, 1f, 1f)]
    void CopyModel()
    {

        var path = AssetDatabase.GetAssetPath(srcModel.GetInstanceID());
        var newPath = "";
        
        switch (modelType)
        {
            case ECopyModelType.TianShen:
                newPath = "Assets/Game/Model/Tianshen";
                break;
            case ECopyModelType.Boss:
                newPath = "Assets/Game/Model/Boss";
                break;
            case ECopyModelType.Mount:
                newPath = "Assets/Game/Model/Zuoqi";
                break;
            case ECopyModelType.Npc:
                newPath = "Assets/Game/Model/NPC";
                break;
            case ECopyModelType.Weapon:
                newPath = "Assets/Game/Model/Weapon";
                break;
            default:
                break;
        }
        if(newPath=="")
        {
            Debug.LogError("请选择目标类型");
            return;
        }
        var targetPath = $"{newPath}/{newNo}";
        if(AssetDatabase.IsValidFolder(targetPath))
        {
            Debug.LogError("目标文件夹已存在");
            return;
        }

        //创建文件夹
        AssetDatabase.CreateFolder(newPath, $"{newNo}");
        ////拷贝预制体
        AssetDatabase.CopyAsset(path, $"{targetPath}/{newNo}.prefab");
        ////加载预制体
        GameObject gameobject = AssetDatabase.LoadAssetAtPath($"{targetPath}/{newNo}.prefab", typeof(GameObject)) as GameObject;


        if (modelType== ECopyModelType.Npc)
        {
            AddNpcClick(gameobject, $"{targetPath}/{newNo}.prefab");
        }

        //创建控制器
        var controllerPath = $"{newPath}/Shared";
        var templateGuids = AssetDatabase.FindAssets("t:AnimatorController", new string[] { controllerPath });
        var baseControllerPath = AssetDatabase.GUIDToAssetPath(templateGuids[0]);
        var baseController = AssetDatabase.LoadAssetAtPath<AnimatorController>(baseControllerPath) as AnimatorController;

        var controller = new AnimatorOverrideController(baseController);
        AssetDatabase.CreateAsset(controller, $"{targetPath}/{newNo}_Controller.overrideController");

        //设置控制器
        var animator = gameobject.GetComponent<Animator>();
        animator.runtimeAnimatorController = controller;

        //设置动作
        var clipOverrides= new List<KeyValuePair<AnimationClip, AnimationClip>>();
        controller.GetOverrides(clipOverrides);

        var fatherPath = path.Replace($"/{srcModel.name}.prefab","");
        //Debug.Log(fatherPath);
        var clipGuids = AssetDatabase.FindAssets("t:AnimationClip", new string[] { fatherPath });
        //Debug.Log(clipGuids.Length);
        var clipDic = new Dictionary<string,AnimationClip>();
        for (int i = 0; i < clipGuids.Length; i++)
        {
            var clipPath = AssetDatabase.GUIDToAssetPath(clipGuids[i]);
            var clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(clipPath) as AnimationClip;
            clipDic.Add(clip.name,clip);
        }

        for (int i = 0; i < clipOverrides.Count; i++)
        {
            string animName = clipOverrides[i].Key.name;

            //因为命名不规范 同一个动作可能有多个名字
            //不同类型也会放不同的动作
            //获取这个key可以放的动作名字列表
            var clipNameList = GetClipNameListByTypeAndKey(modelType, animName);
            if(clipNameList!=null)
            {
                foreach (var clipName in clipNameList)
                {
                    if (clipDic.ContainsKey(clipName))
                    {
                        clipOverrides[i] = new KeyValuePair<AnimationClip, AnimationClip>(clipOverrides[i].Key, clipDic[clipName]);
                        break;
                    }
                }
            }
        }
        controller.ApplyOverrides(clipOverrides);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        Debug.Log("复制完成");
        
    }

    private bool IsNullSrcModel()
    {
        return srcModel == null;
    }

    private void AddNpcClick(GameObject gameobject,string assetPath)
    {
        GameObject gameObject_prefab = PrefabUtility.InstantiatePrefab(gameobject) as GameObject;
        var clickableObject = gameObject_prefab.GetComponent<ClickableObject>();
        if(clickableObject==null)
        {
            clickableObject = gameObject_prefab.AddComponent<ClickableObject>();
        }
        var clickable = gameObject_prefab.GetComponentInChildren<Clickable>();
        if (clickable==null)
        {
            var clickableGo = new GameObject("Clickable");
            clickableGo.transform.parent = gameObject_prefab.transform;
            clickableGo.AddComponent<CapsuleCollider>();
            var clickable_new = clickableGo.AddComponent<Clickable>();
            clickable_new.Owner = clickableObject;
        }
        PrefabUtility.ApplyPrefabInstance(gameObject_prefab,InteractionMode.AutomatedAction);
        DestroyImmediate(gameObject_prefab);
        Debug.LogWarning("需要调整Clickable的CapsuleCollider位置和大小");
    }
}