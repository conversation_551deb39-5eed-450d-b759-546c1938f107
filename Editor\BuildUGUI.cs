﻿using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

public class BuildUGUI : MonoBehaviour {

    private static string GetUnityUGUIDir()
    {
        return string.Format("{0}\\..\\Data\\UnityExtensions\\Unity\\GUISystem", EditorApplication.applicationPath);
    }

    private static string GetNewUGUIDir()
    {
        return string.Format("{0}\\..\\Tools\\NewUGUI", Application.dataPath);
    }

    private static string GetOldUGUIDir()
    {
        return string.Format("{0}\\..\\Tools\\OldUGUI", Application.dataPath);
    }

    private static string GePdb2mdbExe()
    {
        return string.Format("{0}\\..\\Data\\MonoBleedingEdge\\lib\\mono\\4.5\\pdb2mdb.exe", EditorApplication.applicationPath);
    }
    
    private static string GetMonoExe()
    {
        return string.Format("{0}\\..\\Data\\MonoBleedingEdge\\bin\\mono.exe", EditorApplication.applicationPath);
    }

    [MenuItem("UGUI自定义工具/测试")]
    public static void TestUGUI()
    {
        string outputDir = "D:\\youyanlib\\ugui2017.4.16f1\\Output";
        string[] targetDirs = new string[] { GetUnityUGUIDir(), GetNewUGUIDir() };
        for (int m = 0; m < targetDirs.Length; m++)
        {
            string targetDir = targetDirs[m];
            string[] list;
            if (0 == m)
            {
                list = new string[] { "UnityEngine.UI.dll", "UnityEngine.UI.pdb", "Editor\\UnityEditor.UI.dll" };
            }
            else
            {
                list = new string[] { "UnityEngine.UI.dll", "UnityEngine.UI.pdb", "Editor\\UnityEditor.UI.dll", "Standalone\\UnityEngine.UI.dll", "Standalone\\UnityEngine.UI.pdb" };
            }


            for (int i = 0; i < list.Length; i++)
            {
                File.Copy(string.Format("{0}/{1}", outputDir, list[i]), string.Format("{0}/{1}", targetDir, list[i]), true);
            }

            string argument = string.Format("{0} {1}", GePdb2mdbExe(), "UnityEngine.UI.dll");
            string cmd = GetMonoExe();
            RunProcess(cmd, argument, targetDir);
        }

        UnityEngine.Debug.LogErrorFormat("使用新UI");
    }


    [MenuItem("UGUI自定义工具/使用新UI")]
    public static void UseNewUGUI()
    {
        string outputDir = GetNewUGUIDir();
        string u3dDir = GetUnityUGUIDir();
        string[] list = new string[] { "UnityEngine.UI.dll", "UnityEngine.UI.pdb" };
        for (int i = 0; i < list.Length; i++)
        {
            File.Copy(string.Format("{0}/{1}", outputDir, list[i]), string.Format("{0}/{1}", u3dDir, list[i]), true);
        }

        string argument = string.Format("{0} {1}", GePdb2mdbExe(), "UnityEngine.UI.dll");
        string cmd = GetMonoExe();
        RunProcess(cmd, argument, u3dDir);
        UnityEngine.Debug.LogErrorFormat("使用新UI");
    }

    [MenuItem("UGUI自定义工具/使用旧UI")]
    public static void UseOldUGUI()
    {
        string outputDir = GetOldUGUIDir();
        string u3dDir = GetUnityUGUIDir();
        string[] list = new string[] { "UnityEngine.UI.dll"};
        for (int i = 0; i < list.Length; i++)
        {
            File.Copy(string.Format("{0}/{1}", outputDir, list[i]), string.Format("{0}/{1}", u3dDir, list[i]), true);
        }

        string argument = string.Format("{0} {1}", GePdb2mdbExe(), "UnityEngine.UI.dll");
        string cmd = GetMonoExe();
        RunProcess(cmd, argument, u3dDir);
        UnityEngine.Debug.LogErrorFormat("使用旧UI");
    }

    [MenuItem("UGUI自定义工具/使用新UIStandalone(打包用)")]
    public static void UseUIStandLone()
    {
        string outputDir = GetNewUGUIDir();
        string u3dDir = GetUnityUGUIDir();
        string[] list = new string[] {"Standalone\\UnityEngine.UI.dll", "Standalone\\UnityEngine.UI.pdb" };
        for (int i = 0; i < list.Length; i++)
        {
            File.Copy(string.Format("{0}/{1}", outputDir, list[i]), string.Format("{0}/{1}", u3dDir, list[i]), true);
        }
    }

    [MenuItem("UGUI自定义工具/使用旧UIStandalone(打包用)")]
    public static void UseOldUIStandLone()
    {
        string outputDir = GetNewUGUIDir();
        string u3dDir = GetUnityUGUIDir();
        string[] list = new string[] { "UnityEngine.UI.dll", "Standalone\\UnityEngine.UI.dll" };
        for (int i = 0; i < list.Length; i++)
        {
            File.Copy(string.Format("{0}/{1}", outputDir, list[i]), string.Format("{0}/{1}", u3dDir, list[i]), true);
        }
    }

    private static void RunProcess(
           string executable,
           string argument,
           string workingDirectory)
    {
        var startInfo = new ProcessStartInfo(executable, argument);
        startInfo.CreateNoWindow = true;
        startInfo.UseShellExecute = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.RedirectStandardError = true;
        startInfo.StandardOutputEncoding = Encoding.UTF8;
        startInfo.StandardErrorEncoding = Encoding.UTF8;
        startInfo.WorkingDirectory = workingDirectory;

        using (var proc = Process.Start(startInfo))
        {
            proc.OutputDataReceived += (sender, e) =>
            {
                var text = e.Data.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    UnityEngine.Debug.Log(text);
                }
            };

            proc.ErrorDataReceived += (sender, e) =>
            {
                var text = e.Data.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    UnityEngine.Debug.LogError(text);
                }
            };

            proc.BeginOutputReadLine();
            proc.WaitForExit();
            proc.Close();
        }
    }
}
