﻿using UnityEngine;
using System.Text;
using System.Collections.Generic;
using System.IO;

namespace UniFramework.Editor
{
    /// <summary>
    /// MemoryStream.ReadLine has an interesting oddity: it doesn't always advance the stream's position by the correct amount:
    /// http://social.msdn.microsoft.com/Forums/en-AU/Vsexpressvcs/thread/b8f7837b-e396-494e-88e1-30547fcf385f
    /// Solution? Custom line reader with the added benefit of not having to use streams at all.
    /// </summary>
    public class ByteReader
    {
        private byte[] m_Buffer;
        private int m_Offset = 0;

        public ByteReader(byte[] bytes) { m_Buffer = bytes; }
        public ByteReader(TextAsset asset) { m_Buffer = asset.bytes; }

        /// <summary>
        /// Read the contents of the specified file and return a Byte Reader to work with.
        /// </summary>
        static public ByteReader Open(string path)
        {
#if UNITY_EDITOR || (!UNITY_FLASH && !NETFX_CORE && !UNITY_WP8 && !UNITY_WP_8_1)
            var fs = File.OpenRead(path);

            if (fs != null) {
                fs.Seek(0, SeekOrigin.End);
                var buffer = new byte[fs.Position];
                fs.Seek(0, SeekOrigin.Begin);
                fs.Read(buffer, 0, buffer.Length);
                fs.Close();
                return new ByteReader(buffer);
            }
#endif
            return null;
        }

        /// <summary>
        /// Whether the buffer is readable.
        /// </summary>
        public bool CanRead { get { return m_Buffer != null && m_Offset < m_Buffer.Length; } }

        /// <summary>
        /// Read a single line from the buffer.
        /// </summary>
        static string ReadLine(byte[] buffer, int start, int count)
        {
            return Encoding.UTF8.GetString(buffer, start, count);
        }

        /// <summary>
        /// Read a single line from the buffer.
        /// </summary>
        public string ReadLine() { return ReadLine(true); }

        /// <summary>
        /// Read a single line from the buffer.
        /// </summary>
        public string ReadLine(bool skip_empty_lines)
        {
            var max = m_Buffer.Length;

            // Skip empty characters
            if (skip_empty_lines) {
                while (m_Offset < max && m_Buffer[m_Offset] < 32) {
                    ++m_Offset;
                }
            }

            var end = m_Offset;

            if (end < max) {
                for (; ; )
                {
                    if (end < max) {
                        int ch = m_Buffer[end++];
                        if (ch != '\n' && ch != '\r') {
                            continue;
                        }
                    }
                    else {
                        ++end;
                    }

                    var line = ReadLine(m_Buffer, m_Offset, end - m_Offset - 1);
                    m_Offset = end;
                    return line;
                }
            }
            m_Offset = max;
            return null;
        }

        /// <summary>
        /// Assume that the entire file is a collection of key/value pairs.
        /// </summary>
        public Dictionary<string, string> ReadDictionary()
        {
            var dict = new Dictionary<string, string>();
            var separator = new char[] { '=' };

            while (CanRead) {
                var line = ReadLine();
                if (line == null) {
                    break;
                }

                if (line.StartsWith("//")) {
                    continue;
                }

                var split = line.Split(separator, 2, System.StringSplitOptions.RemoveEmptyEntries);

                if (split.Length == 2) {
                    var key = split[0].Trim();
                    var val = split[1].Trim().Replace("\\n", "\n");
                    dict[key] = val;
                }
            }
            return dict;
        }

        static BetterList<string> ms_Temp = new BetterList<string>();

        /// <summary>
        /// Read a single line of Comma-Separated Values from the file.
        /// </summary>
        public BetterList<string> ReadCSV()
        {
            ms_Temp.Clear();
            var line = "";
            var insideQuotes = false;
            var wordStart = 0;

            while (CanRead) {
                if (insideQuotes) {
                    var s = ReadLine(false);
                    if (s == null) {
                        return null;
                    }

                    s = s.Replace("\\n", "\n");
                    line += "\n" + s;
                }
                else {
                    line = ReadLine(true);
                    if (line == null) {
                        return null;
                    }

                    line = line.Replace("\\n", "\n");
                    wordStart = 0;
                }

                for (int i = wordStart, imax = line.Length; i < imax; ++i) {
                    var ch = line[i];

                    if (ch == ',') {
                        if (!insideQuotes) {
                            ms_Temp.Add(line.Substring(wordStart, i - wordStart));
                            wordStart = i + 1;
                        }
                    }
                    else if (ch == '"') {
                        if (insideQuotes) {
                            if (i + 1 >= imax) {
                                ms_Temp.Add(line.Substring(wordStart, i - wordStart).Replace("\"\"", "\""));
                                return ms_Temp;
                            }

                            if (line[i + 1] != '"') {
                                ms_Temp.Add(line.Substring(wordStart, i - wordStart).Replace("\"\"", "\""));
                                insideQuotes = false;

                                if (line[i + 1] == ',') {
                                    ++i;
                                    wordStart = i + 1;
                                }
                            }
                            else {
                                ++i;
                            }
                        }
                        else {
                            wordStart = i + 1;
                            insideQuotes = true;
                        }
                    }
                }

                if (wordStart < line.Length) {
                    if (insideQuotes) {
                        continue;
                    }

                    ms_Temp.Add(line.Substring(wordStart, line.Length - wordStart));
                }
                return ms_Temp;
            }
            return null;
        }
    }
}
