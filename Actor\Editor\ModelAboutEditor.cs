﻿using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using Sirenix.OdinInspector;

public class ModelAboutEditor : Editor
{

    //匹配动作定义的各种骚名
    static Dictionary<string, List<string>> AnimatorClipTypeDic = new Dictionary<string, List<string>>()
    {
        { "idle", new List<string>() { "Idle", "idle" } },
        { "ui_idle", new List<string>() { "idle_ui", "ui_idle" } },
        { "weapon_idle", new List<string>() { "weapon_idle", "idle_weapon" } },
        { "rest", new List<string>() { "Rest", "rest" } },
        { "run", new List<string>() { "Run", "run" } },
        { "walk", new List<string>() { "Walk", "walk" } },
        { "die", new List<string>() { "Die", "die"} },
        { "dead", new List<string>() { "Dead", "dead", "Death", "death" } },
        { "combo1_1", new List<string>() { "combo01"} },
        { "combo1_1_back", new List<string>() { "combo01_back",} },
        { "combo1_2", new List<string>() { "combo02",} },
        { "combo1_2_back", new List<string>() { "combo02_back",} },
        { "combo1_3", new List<string>() { "combo03",} },
        { "combo1_3_back", new List<string>() { "combo03_back",} },
        { "combo1_4", new List<string>() { "combo04",} },
        { "combo1_4_back", new List<string>() { "combo04_back",} },
        { "attack1", new List<string>() { "Attack", "attack", "Attack01", "attack01" } },
        { "attack1_back", new List<string>() { "Attack01_back", "attack01_back", "Attack01_Back", "attack01_Back" } },
        { "attack2", new List<string>() { "Attack02", "attack02" } },
        { "attack2_back", new List<string>() { "Attack02_back", "attack02_back", "Attack02_Back", "attack02_Back" } },
        { "attack3", new List<string>() { "Attack03", "attack03" } },
        { "attack3_back", new List<string>() { "Attack03_back", "attack03_back", "Attack03_Back", "attack03_Back" } },
        { "attack4", new List<string>() { "Attack04", "attack04" } },
        { "attack4_back", new List<string>() { "Attack04_back", "attack04_back", "Attack04_Back", "attack04_Back" } },
        { "mount_idle_1", new List<string>() { "mount_idle1", } },
        { "mount_run_1", new List<string>() { "mount_run1", } },
        { "mount_idle_2", new List<string>() { "mount_idle2", } },
        { "mount_run_2", new List<string>() { "mount_run2", } },
        { "mount_idle_3", new List<string>() { "mount_idle3", } },
        { "mount_run_3", new List<string>() { "mount_run3", } },
        { "mount_idle_4", new List<string>() { "mount_idle4", } },
        { "mount_run_4", new List<string>() { "mount_run4", } },
        { "mount_idle_5", new List<string>() { "mount_idle5", } },
        { "mount_run_5", new List<string>() { "mount_run5", } },
        { "mount_idle_6", new List<string>() { "mount_idle6", } },
        { "mount_run_6", new List<string>() { "mount_run6", } },
        { "mount_idle_7", new List<string>() { "mount_idle7", } },
        { "mount_run_7", new List<string>() { "mount_run7", } },
        { "mount_idle_8", new List<string>() { "mount_idle8", } },
        { "mount_run_8", new List<string>() { "mount_run8", } },
        { "hurt", new List<string>() { "hit", } },
    };

    public enum EModelType
    {
        Npc,
        Boss,
        [LabelText("天神")]
        TianShen,
        [LabelText("坐骑")]
        Mount,
        [LabelText("武器")]
        Weapon,
        Monster,
        Wing,
        Child,
        Chongwu,
        Fabao,
        Zuoqi,
        YuShou,
        Beishi,
        WuHun,
        Halo,
    }


    // 特殊
    static Dictionary<EModelType, Dictionary<string, List<string>>> SpecialAnimationClipDic = new Dictionary<EModelType, Dictionary<string, List<string>>>()
    {
        { EModelType.Boss, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
                                                                { "magic1_1", new List<string>() { "skill_pre" } },
                                                                { "magic1_2", new List<string>() { "skill_mid" } },
                                                                { "magic1_3", new List<string>() { "skill_back" } },
        }},
        { EModelType.Monster, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Npc, new Dictionary<string, List<string>>() {
                                                                { "rest", AnimatorClipTypeDic["idle"] },
                                                                { "zuo", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Wing, new Dictionary<string, List<string>>() {
                                                                { "run", AnimatorClipTypeDic["idle"] },
                                                                { "scene_nor_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Child, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Chongwu, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Fabao, new Dictionary<string, List<string>>() {
                                                                { "IdleFight", AnimatorClipTypeDic["idle"] },
                                                                { "scene_nor_idle", AnimatorClipTypeDic["idle"] },
                                                                { "scene_fight_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Zuoqi, new Dictionary<string, List<string>>() {
                                                                { "mount_scene_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.TianShen, new Dictionary<string, List<string>>() {
                                                                { "combo1_1", new List<string>() { "Attack01", "attack01" } },
                                                                { "combo1_1_back", new List<string>() { "Attack01_back", "attack01_back" } },
                                                                { "combo1_2", new List<string>() { "Attack02", "attack02" } },
                                                                { "combo1_2_back", new List<string>() { "Attack02_back", "attack02_back" } },
                                                                { "combo1_3", new List<string>() { "Attack03", "attack03" } },
                                                                { "combo1_3_back", new List<string>() { "Attack03_back", "attack03_back" } },
                                                                { "attack1", new List<string>() { "Skill01", "skill01" } },
                                                                { "attack2", new List<string>() { "Skill02", "skill02" } },
                                                                { "attack3", new List<string>() { "Skill03", "skill03" } },
                                                                { "attack1_back", new List<string>() { "Skill01_back", "skill01_back" } },
                                                                { "attack2_back", new List<string>() { "Skill02_back", "skill02_back" } },
                                                                { "attack3_back", new List<string>() { "Skill03_back", "skill03_back" } },
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.YuShou, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
                                                                { "attack2", new List<string>() { "skill" } },
        }},
        { EModelType.Beishi, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.WuHun, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
        { EModelType.Halo, new Dictionary<string, List<string>>() {
                                                                { "ui_idle", AnimatorClipTypeDic["idle"] },
        }},
    };


    /// <summary>
    /// ModelCopyWindow
    /// </summary>
    public enum ECopyModelType
    {
        Npc,
        Boss,
        [LabelText("天神")]
        TianShen,
        [LabelText("坐骑")]
        Mount,
        [LabelText("武器")]
        Weapon,
    }
    static EModelType ModelCopyToModelType(ECopyModelType copyModelType)
    {
        switch (copyModelType)
        {
            case ECopyModelType.Npc:
                return EModelType.Npc;
            case ECopyModelType.Boss:
                return EModelType.Boss;
            case ECopyModelType.TianShen:
                return EModelType.TianShen;
            case ECopyModelType.Mount:
                return EModelType.Mount;
            case ECopyModelType.Weapon:
                return EModelType.Weapon;
            default:
                return EModelType.Npc;
        }
    }

    /// <summary>
    /// 获取对应模型的动作名字
    /// </summary>
    /// <param name="copyModelType"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    public static List<string> GetClipNameListByTypeAndKey(ECopyModelType copyModelType ,string key)
    {
        var specialDic = SpecialAnimationClipDic[ModelCopyToModelType(copyModelType)];
        for (int i = 0; i < specialDic.Count; i++)
        {
            if(specialDic.ContainsKey(key))
            {
                return specialDic[key];
            }
        }
        if(AnimatorClipTypeDic.ContainsKey(key))
        {
            return AnimatorClipTypeDic[key];
        }
        return null;
    }
}