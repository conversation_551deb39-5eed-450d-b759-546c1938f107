﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using TMPro;
using UniFramework.Editor;
using UnityEditor;
using UnityEngine;
using UnityEngine.TextCore;
using UnityEngine.TextCore.LowLevel;

public class BitmapFontCreator : EditorWindow
{
    [MenuItem("Tools/字体/艺术字生成器")]
    protected static void Init()
    {
        var window = EditorWindow.GetWindow<BitmapFontCreator>();
        window.titleContent = new GUIContent("艺术字生成器");
    }


    private static void FillTexture(Texture2D tex, Color c)
    {
        for (var x = 0; x < tex.width; ++x)
        {
            for (var y = 0; y < tex.width; ++y)
            {
                tex.SetPixel(x, y, c);
            }
        }
    }

    private static void CreateFontAssetFromDirectory(string font_name, string dir_path, int max_size = 1024, int padding = 1)
    {
        var images = AssetDatabase.FindAssets($"t:{nameof(Texture2D)}", new string[] { dir_path })
            .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
            .Select(path => AssetDatabase.LoadAssetAtPath<Texture2D>(path))
            .ToList();

        var artFontTableDataList = AssetDatabase.FindAssets($"t:{nameof(ArtFontTableData)}", new string[] { dir_path })
            .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
            .Select(path => AssetDatabase.LoadAssetAtPath<ArtFontTableData>(path))
            .ToList();

        ArtFontTableData artFontTableData = artFontTableDataList.Count > 0 ? artFontTableDataList[0] : null;
        var x = padding;
        var y = padding;

        var max_x = x;
        var max_char_height = 0;
        var cur_line_height = 0;
        foreach (var img in images)
        {
            if (x + img.width > max_size)
            {
                x = padding;
                y += cur_line_height + padding;
                cur_line_height = 0;
            }

            x += img.width + padding;
            max_x = Mathf.Max(max_x, x);
            cur_line_height = Mathf.Max(cur_line_height, img.height);
            max_char_height = Mathf.Max(max_char_height, img.height);
        }

        var font_size = max_char_height;

        var atlas_width = Mathf.NextPowerOfTwo(max_x + padding);
        var atlas_height = Mathf.NextPowerOfTwo(y + cur_line_height + padding);

        var atlas = new Texture2D(atlas_width, atlas_height, TextureFormat.RGBA32, false, false);
        atlas.alphaIsTransparency = true;
        atlas.wrapMode = TextureWrapMode.Clamp;
        atlas.name = $"{font_name} Atlas";
        FillTexture(atlas, Color.clear);

        var glyph_table = new List<Glyph>();
        var character_table = new List<TMP_Character>();

        x = padding;
        y = padding;
        cur_line_height = 0;
        foreach (var img in images)
        {
            if (x + img.width > max_size)
            {
                x = padding;
                y += cur_line_height + padding;
                cur_line_height = 0;
            }

            var unicode = img.name[0];
            if(artFontTableData!=null&& artFontTableData.table.ContainsKey(img.name))
            {
                unicode = artFontTableData.table[img.name];
            }
            var glyph = new Glyph()
            {
                index = unicode,
                glyphRect = new GlyphRect()
                {
                    x = x,
                    y = y,
                    width = img.width,
                    height = img.height,
                },
                metrics = new GlyphMetrics()
                {
                    width = img.width,
                    height = img.height,
                    horizontalBearingX = 0,
                    horizontalBearingY = (img.height - font_size) / 2,
                    horizontalAdvance = img.width,
                },
            };

            glyph_table.Add(glyph);
            character_table.Add(new TMP_Character(unicode, glyph));

            //不区分大小写
            if (char.IsLower(unicode))
            {
                var new_char = char.ToUpper(unicode);
                character_table.Add(new TMP_Character(new_char, glyph));
            }
            else if(char.IsUpper(unicode))
            {
                var new_char = char.ToLower(unicode);
                character_table.Add(new TMP_Character(new_char, glyph));
            }

            var pixels = img.GetPixels32();
            atlas.SetPixels32(x, y, img.width, img.height, pixels);

            x += img.width + padding;
            cur_line_height = Mathf.Max(cur_line_height, img.height);
        }

        atlas.Apply();
        TextMeshProHelper.CreateFontAsset(font_name, font_size, atlas, glyph_table, character_table);
    }

    [System.Serializable]
    private class Tool_CreateFromFolder
    {
        public string Folder = @"Assets/Game/UIs/TMP/ArtFont";
        public string FontName;
        public int MaxSize = 256;
        public int Padding = 1;
    }
    private Tool_CreateFromFolder m_Tool0 = new Tool_CreateFromFolder();
    private string[] m_SizeDisplayOptions = new string[] { "128", "256", "512", "1024", "2048", "4096" };
    private int[] m_SizeOptions = new int[] { 128, 256, 512, 1024, 2048, 4096 };

    [System.Serializable]
    private class Tool_CreateFromFnt
    {
        public string FontName;
        public TextAsset FntFile;
        public Texture2D AtlasFile;
    }
    private Tool_CreateFromFnt m_Tool1 = new Tool_CreateFromFnt();

    private void OnGUI()
    {
        var is_folder_valid = true;
        if (!Directory.Exists(m_Tool0.Folder) || string.IsNullOrEmpty(Path.GetFileNameWithoutExtension(m_Tool0.Folder)))
        {
            is_folder_valid = false;
        }

        GUI.color = is_folder_valid ? Color.white : Color.red;
        m_Tool0.Folder = EditorGUILayout.TextField("文件夹", m_Tool0.Folder);
        GUI.color = Color.white;
        if (!is_folder_valid)
        {
            EditorGUILayout.HelpBox("文件夹路径不存在", MessageType.Error);
        }

        var is_fontname_valid = !string.IsNullOrWhiteSpace(m_Tool0.FontName);
        GUI.color = is_fontname_valid ? Color.white : Color.red;
        m_Tool0.FontName = EditorGUILayout.TextField("字体名字", m_Tool0.FontName);
        GUI.color = Color.white;
        if (!is_fontname_valid)
        {
            EditorGUILayout.HelpBox("字体名字不能为空", MessageType.Error);
        }

        m_Tool0.MaxSize = EditorGUILayout.IntPopup("图集大小", m_Tool0.MaxSize, m_SizeDisplayOptions, m_SizeOptions);
        m_Tool0.Padding = EditorGUILayout.IntField("Padding", m_Tool0.Padding);

        if (is_folder_valid && is_fontname_valid)
        {
            EditorGUILayout.LabelField("输出路径", Path.Combine($@"Assets/Game/UIs/TMP/ArtFont/{m_Tool0.FontName}.asset"));
        }

        GUI.enabled = is_folder_valid && is_fontname_valid;
        if (GUILayout.Button("生成"))
        {
            CreateFontAssetFromDirectory(m_Tool0.FontName, m_Tool0.Folder, m_Tool0.MaxSize, m_Tool0.Padding);
        }

        if (GUILayout.Button("生成字符映射文件"))
        {
            ArtFontTableData asset = ScriptableObject.CreateInstance<ArtFontTableData>();
            AssetDatabase.CreateAsset(asset, @$"{m_Tool0.Folder}/ArtFontTableData.asset");
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        GUI.enabled = true;


        
    }
}
