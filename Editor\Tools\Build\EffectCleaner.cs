﻿using Build;
using Game;
using Nirvana;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public static class EffectCleaner
{
    [MenuItem("GameObject/特效专用/检查特效是否合格", priority = 12)]
    public static void CheckSelectEffectInRightMouse()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择特效对象");
            return;
        }

        CheckEffectIsBad(Selection.activeGameObject);
    }

    [MenuItem("GameObject/特效专用/优化特效", priority = 12)]
    public static void OptimieSelectEffectInHierarchy()
    {
        OptimieSelectEffect();
    }

    [MenuItem("Assets/特效专用/优化特效", priority = 12)]
    public static void CheckSelectEffectInProject()
    {
        OptimieSelectEffect();
    }

    [MenuItem("自定义工具/美术专用/优化特效")]
    public static void OptimieSelectEffect()
    {
        if (null != Selection.activeGameObject)
        {
            if (!ClearEffectObj(Selection.activeGameObject)) return;

            AssetDatabase.SaveAssets();
        }
        else
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (AssetDatabase.IsValidFolder(path))
            {
                ClearEffect(new string[] { path });
            }

        }

        Debug.Log("优化特效成功");
    }

    [MenuItem("自定义工具/美术专用/评测特效是否合格")]
    public static void CheckEffectIsBad()
    {
        if (Selection.activeGameObject)
        {
            CheckEffectIsBad(Selection.activeGameObject);
            return;
        }

        if (null != Selection.activeObject)
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (AssetDatabase.IsValidFolder(path))
            {
                string[] checkDirs = { path };
                string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
                foreach (var guid in guids)
                {
                    GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GUIDToAssetPath(guid));
                    CheckEffectIsBad(gameobj);
                }
            }
        }
    }

    private static void CheckEffectIsBad(GameObject gameObject)
    {
        if (null == gameObject)
        {
            Debug.LogError("请指定对象");
            return;
        }

        ParticleSystem[] particles = gameObject.GetComponentsInChildren<ParticleSystem>();
        if (EffectDynamicChecker.CheckEffectIsBad(gameObject, particles, null))
        {
            Debug.LogError("该特效不合格!!!!!");
        }
        else
        {
            Debug.Log("该特效合格");
        }
    }

    public static void ClearEffect(string[] checkDirs = null)
    {
        if (null == checkDirs)
        {
            checkDirs = new string[] { "Assets/Game/Effects" };
        }

        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == gameobj)
            {
                continue;
            }

            if (!ClearEffectObj(gameobj)) break;
        }

        ClearActorEffect();

        AssetDatabase.SaveAssets();
    }

    public static void ClearActorEffect()
    {
        string[] checkDirs = { "Assets/Game/Actors" };
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == gameobj)
            {
                continue;
            }

            if (!gameobj.GetComponent<ActorRender>())
            {
                if (!ClearEffectObj(gameobj)) break;
            }
        }
    }

    private static bool ClearEffectObj(GameObject gameobj, bool isSave = true)
    {
        if (null == gameobj) return false;

        var controlActive = gameobj.GetComponentInChildren<QualityControlActive>();
        if (null != controlActive && QualityConfig.QualityLevel != QualityConfig.GetMaxQualityLevel())
        {
            Debug.LogErrorFormat("优化失败！请切换到最高品质下再优化！");
            return false;
        }

        DelUnActiveChild(gameobj);
        DestroyUnuseComponent(gameobj);
        OptimizeParticleSystem(gameobj);
        OptimizeRenderer(gameobj);
        OptimizeEffectControl(gameobj);
        OptimizeEffectTrailRender(gameobj);
        OptimieRenderOrder(gameobj);
        //       CheckSubEmitters(gameobj);

        if (isSave)
        {
            PrefabUtility.ResetToPrefabState(gameobj);
            PrefabUtility.SetPropertyModifications(gameobj, new PropertyModification[] { });
        }
        return true;
    }

    private static void DelUnActiveChild(GameObject gameObject)
    {
        if (gameObject.GetComponentInChildren<Animator>()   // 动画会控制从隐藏到显示，不能删除
           || gameObject.GetComponentInChildren<UINameTable>())  // UINameTable会在代码中控制显示，不能删除 
        {
            return;
        }

        bool isDel = false;
        Transform[] transforms = gameObject.GetComponentsInChildren<Transform>(true);
        for (int i = 0; i < transforms.Length; i++)
        {
            if (null != transforms[i] && !transforms[i].gameObject.activeSelf && transforms[i].gameObject != gameObject)
            {
                isDel = true;
                GameObject.DestroyImmediate(transforms[i].gameObject, true);
            }
        }

        if (isDel)
        {
            Debug.LogFormat("DelUnActiveChild: {0}", gameObject.name);
        }
    }

    // 移除无用的组件
    private static void DestroyUnuseComponent(GameObject gameobj)
    {
        NirvanaRenderer[] components = gameobj.GetComponentsInChildren<NirvanaRenderer>(true);
        for (int j = 0; j < components.Length; j++)
        {
            Debug.LogFormat("DestroyUnuseComponent:NirvanaRenderer {0}", gameobj.name);
            GameObject.DestroyImmediate(components[j], true);
        }

        Camera[] cameras = gameobj.GetComponentsInChildren<Camera>(true);
        for (int j = 0; j < cameras.Length; j++)
        {
            Debug.LogFormat("DestroyUnuseComponent:Camera {0}", gameobj.name);
            GameObject.DestroyImmediate(cameras[j].gameObject, true);
        }

        Rigidbody[] rigidbodies = gameobj.GetComponentsInChildren<Rigidbody>(true);
        for (int i = 0; i < rigidbodies.Length; i++)
        {
            Debug.LogFormat("DestroyUnuseComponent:Rigidbody {0}", gameobj.name);
            GameObject.DestroyImmediate(rigidbodies[i], true);
        }

        PrefabReferenceHolder[] holders = gameobj.GetComponentsInChildren<PrefabReferenceHolder>(true);
        for (int i = 0; i < holders.Length; i++)
        {
            Debug.LogFormat("DestroyUnuseComponent:PrefabReferenceHolder {0}", gameobj.name);
            GameObject.DestroyImmediate(holders[i], true);
        }
    }

    // 优化粒子系统
    private static void OptimizeParticleSystem(GameObject gameobj)
    {
        ParticleSystem[] particleSystems = gameobj.GetComponentsInChildren<ParticleSystem>(true);
        for (int j = 0; j < particleSystems.Length; j++)
        {
            var particle = particleSystems[j];
            var main = particle.main;
            // 去掉预热
            if (main.prewarm) main.prewarm = false;

            // UiParticles组件使用maxParticles太大会有性能问题
            if (particle.gameObject.GetComponent<UiParticles.UiParticles>())
            {
                if (main.maxParticles > 50) main.maxParticles = 50;
            }

            // 去除直接隐render的paticle
            Renderer render = particle.gameObject.GetComponent<Renderer>();
            if (null != render && !render.enabled
                && !particle.gameObject.GetComponent<UiParticles.UiParticles>()
                && !particle.gameObject.GetComponentInChildren<Animator>()  // 动画会控制从隐藏到显示，不能删除
                && !particle.subEmitters.enabled)
            {
                GameObject.DestroyImmediate(particle, true);
                GameObject.DestroyImmediate(render, true);
            }

            // 材质球丢掉的直接删除
            if (null != render && null == render.sharedMaterial)
            {
                var uiparicle = particle.gameObject.GetComponent<UiParticles.UiParticles>();
                if (null != uiparicle) GameObject.DestroyImmediate(uiparicle, true);
                GameObject.DestroyImmediate(particle, true);
                GameObject.DestroyImmediate(render, true);
            }
        }
    }

    // 优化Renderer
    private static void OptimizeRenderer(GameObject gameobj)
    {
        Renderer[] renders = gameobj.GetComponentsInChildren<Renderer>();
        for (int i = 0; i < renders.Length; i++)
        {
            // 去掉材质球丢失的
            if (null == renders[i].sharedMaterial)
            {
                GameObject.DestroyImmediate(renders[i], true);
            }
        }
    }

    // 限制EffectController
    private static void OptimizeEffectControl(GameObject gameobj)
    {
        EffectControl ctrl = gameobj.GetComponent<EffectControl>();
        if (null == ctrl) return;

        if (ctrl.IsLooping)
        {
            ctrl.IsLooping = false;
            ctrl.Duration = 15;
        }
    }

    // 当有TrailRenderer时，该prefab只能在最外层加上一个TrailRendererController组件
    private static void OptimizeEffectTrailRender(GameObject gameobj)
    {
        TrailRenderer trailRender = gameobj.GetComponentInChildren<TrailRenderer>();
        if (null == trailRender) return;

        var renders = trailRender.GetComponentsInChildren<TrailRendererController>();
        for (int i = 0; i < renders.Length; i++)
        {
            if (renders[i].gameObject == gameobj)
            {
                continue;
            }

            GameObject.DestroyImmediate(renders[i], true);
        }

        gameobj.GetOrAddComponent<TrailRendererController>();
    }

    // 优化渲染排序。
    private static void OptimieRenderOrder(GameObject gameObject)
    {
        if (null == gameObject) return;

        ParticleSystem[] particles = gameObject.GetComponentsInChildren<ParticleSystem>(true);
        if (particles.Length <= 0) return;

        List<Renderer> renderers = new List<Renderer>();
        for (int i = 0; i < particles.Length; i++)
        {
            Renderer renderer = particles[i].GetComponent<Renderer>();
            if (null == renderer) continue;

            renderers.Add(renderer);
        }

        // 相同的材质球强制使用相同的Renderer
        Dictionary<Material, int> orderDic = new Dictionary<Material, int>();
        for (int i = 0; i < renderers.Count; i++)
        {
            var mat = renderers[i].sharedMaterial;
            if (!mat) continue;

            if (!orderDic.ContainsKey(mat))
            {
                orderDic.Add(mat, renderers[i].sortingOrder);
            }
            else
            {
                renderers[i].sortingOrder = orderDic[mat];
            }
        }

        renderers.Sort(CompareRenderer);
        int order = 1;
        for (int i = 0; i < renderers.Count; i++)
        {
            if (i == 0 || renderers[i].sharedMaterial == renderers[i - 1].sharedMaterial)
            {
                renderers[i].sortingOrder = order;
                continue;
            }

            if (renderers[i].sharedMaterial != renderers[i - 1].sharedMaterial)
            {
                ++order;
                renderers[i].sortingOrder = order;
            }
        }
    }

    private static int CompareRenderer(Renderer x, Renderer y)
    {
        if (x.sortingOrder > y.sortingOrder)
        {
            return 1;
        }
        else if (x.sortingOrder == y.sortingOrder)
            if (x.sharedMaterial.GetInstanceID() >= y.sharedMaterial.GetInstanceID())
            {
                return 1;
            }
            else
            {
                return -1;
            }
        else
        {
            return -1;
        }
    }

    // 优化渲染的填充率，只能一次性使用，否则面片会越来越小
    [MenuItem("GameObject/特效专用/优化面片大小（一次性使用）", priority = 13)]
    public static void OptimizeRenderFillRateInOnce()
    {
        var gameObject = Selection.activeGameObject;
        if (null == gameObject) return;
        ParticleSystem[] particles = gameObject.GetComponentsInChildren<ParticleSystem>(true);
        for (int i = 0; i < particles.Length; i++)
        {
            var particle = particles[i];
            var mainModule = particle.main;
            if (mainModule.startSize.constantMax <= 10) // 跟EffectDynamicChecker里的CheckIsOverFillRate一致
            {
                continue;
            }

            var render = particle.gameObject.GetComponent<Renderer>();
            var material = render.sharedMaterial;
            var texture = material.mainTexture as Texture2D;
            if (texture.width < 16 && texture.height < 16)
            {
                continue;
            }

            int[] depths = new int[] { 10, 20, 50, 100 };
            Rect minFillRect = new Rect(0, 0, texture.width, texture.height);
            for (int m = 0; m < depths.Length; m++)
            {
                Rect fillRect = TextureFillRateUtil.GetTextureFillRect(texture, depths[m]);
                if (fillRect.width < minFillRect.width)
                {
                    minFillRect.x = fillRect.x;
                    minFillRect.width = fillRect.width;
                }
                if (fillRect.height < minFillRect.height)
                {
                    minFillRect.y = fillRect.y;
                    minFillRect.height = fillRect.height;
                }
            }

            var scaleX = texture.width / minFillRect.width;
            var scaleY = texture.height / minFillRect.height;

            if (1 == scaleX && 1 == scaleY)
            {
                continue;
            }

            if (1.0f * minFillRect.width / texture.width > 0.85 && 1.0f * minFillRect.height / texture.height > 0.85) // 跟EffectDynamicChecker里的CheckIsOverFillRate一致
            {
                continue;
            }

            // 如果是已经开启了ENABLE_UV_TRANSFORM，则认为该面片是在该UV变换的基础上调了 的
            // 之所以创建拷贝__UV，是因为无法批量处理，容易发生错程。由动态检查出A让美术处理，但A的材质球可能被B使用, 而美术并不处理A
            // 以下每个材质球只会有一份拷贝
            Material uvMaterial = null;
            if (material.IsKeywordEnabled("ENABLE_UV_TRANSFORM") || material.name.EndsWith("__uv"))
            {
                uvMaterial = material;
            }
            else
            {
                string path = AssetDatabase.GetAssetPath(material.GetInstanceID());
                string matFileName = Path.GetFileNameWithoutExtension(path);
                string newMatName = string.Format("{0}__uv.mat", matFileName);
                path = path.Replace(matFileName + ".mat", newMatName);

                uvMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
                if (null == uvMaterial)
                {
                    AssetDatabase.CopyAsset(AssetDatabase.GetAssetPath(material.GetInstanceID()), path);
                    uvMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
                    AssetDatabase.ImportAsset(path, ImportAssetOptions.Default);
                    Debug.LogFormat("not foud material, so create new {0}", path);
                }
                render.sharedMaterial = uvMaterial;
            }

            uvMaterial.EnableKeyword("ENABLE_UV_TRANSFORM");
            var vector = uvMaterial.GetVector("_UVTransform");
            uvMaterial.SetVector("_UVTransform", new Vector4(scaleX, scaleY, vector.z, vector.w));

            var startSizeX = mainModule.startSizeX;
            var startSizeY = mainModule.startSizeY;
            startSizeX.constantMax = startSizeX.constantMax / scaleX;
            startSizeY.constantMax = startSizeY.constantMax / scaleY;
            mainModule.startSizeX = startSizeX;
            mainModule.startSizeY = startSizeY;
        }
    }
}
