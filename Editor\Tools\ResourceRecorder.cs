﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;

public sealed class ResourceRecorder : EditorWindow
{
    private static bool isRecording = false;
    private static Dictionary<string, Info> recordings = new Dictionary<string, Info>();
    private bool autoRecording = false;
    private bool showDetaile = false;
    private static Queue<Info> lastResources = new Queue<Info>();
    [MenuItem("自定义工具/分包工具/记录新手资源")]
    public static void RecordResource()
    {
        var window = EditorWindow.GetWindow<ResourceRecorder>(false, "记录新手资源");
        window.minSize = new Vector2(600, 400);
    }

    private void OnGUI()
    {
        if (null != GameRoot.Instance)
        {
            if (autoRecording)
            {
                autoRecording = false;
                Start();
            }
            EditorGUILayout.BeginHorizontal();
            if (isRecording)
            {
                if (GUILayout.Button("Stop"))
                {
                    Stop();
                }
            }
            else
            {
                if (GUILayout.Button("Start"))
                {
                    Start();
                }
            }

            if (GUILayout.Button("Clean"))
            {
                recordings.Clear();
            }

            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Save"))
            {
                var path = EditorUtility.SaveFilePanel(
                    "Save Recordings",
                     Application.dataPath,
                    "Resource Recordings",
                    "csv");
                if (!string.IsNullOrEmpty(path))
                {
                    Save(path);
                }
            }
        }
        else
        {
            isRecording = false;
            EditorGUILayout.HelpBox(
                "游戏未启动",
                MessageType.Info);

            autoRecording = GUILayout.Toggle(autoRecording, "启动游戏时自动开启");
        }

        if (isRecording)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("游戏运行时间： " + (int)Time.realtimeSinceStartup);
            EditorGUILayout.Space();

            showDetaile = GUILayout.Toggle(showDetaile, "Detail");
            if (!showDetaile)
            {
                return;
            }

            if (lastResources.Count > 0)
            {
                EditorGUILayout.BeginVertical();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("BundleName");
                EditorGUILayout.LabelField("AssetName");
                EditorGUILayout.LabelField("UseCount");
                EditorGUILayout.EndHorizontal();

                Info[] infos = lastResources.ToArray();
                for (int i = infos.Length - 1; i >= 0; --i)
                {
                    Info info = infos[i];
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(info.bundleName);
                    EditorGUILayout.LabelField(info.assetName);
                    EditorGUILayout.LabelField(info.useCount.ToString());
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }
    }

    private void Update()
    {
        if (isRecording)
            Repaint();
    }

    private static void Start()
    {
        if (isRecording)
            return;

        if (null == GameRoot.Instance)
            return;

        isRecording = true;
        EditorResourceMgr.loadAction = LoadAction;
    }

    private static void Stop()
    {
        isRecording = false;
        EditorResourceMgr.loadAction = null;
    }

    private static void LoadAction(string bundleName, string assetName)
    {
        if (string.IsNullOrEmpty(bundleName) || string.IsNullOrEmpty(assetName))
            return;

        Info info;
        if (!recordings.TryGetValue(bundleName, out info))
        {
            int level = GameRoot.Instance.GetMainRoleLevel();
            info = new Info();
            info.bundleName = bundleName;
            info.level = level;
            info.firstUseTimeStamp = (int)Time.realtimeSinceStartup;
            info.useCount = 1;
            info.assetName = assetName;
            recordings.Add(bundleName, info);
        }
        else
        {
            info.useCount = info.useCount + 1;
            info.assetName = assetName;
            recordings[bundleName] = info;
        }

        lastResources.Enqueue(info);
        if (lastResources.Count > 15)
        {
            lastResources.Dequeue();
        }
    }

    private static void Save(string path)
    {
        using (var file = File.Open(path, FileMode.Create, FileAccess.Write))
        using (var writer = new StreamWriter(file))
        {
            file.WriteByte(239); // 0xEF
            file.WriteByte(187); // 0xBB
            file.WriteByte(191); // 0xBF

            writer.WriteLine(
                "BundleName,Level,FirstUseTimeStamp,UseCount");
            foreach (var kv in recordings)
            {
                var bundleName = kv.Key;
                var info = kv.Value;

                writer.WriteLine(
                    bundleName + ',' +
                    info.level + ',' +
                    info.firstUseTimeStamp + ',' +
                    info.useCount + ',');
            }
        }
    }

    private static string CheckIsRoleFashionModel(string bundleName)
    {
        if (Regex.IsMatch(bundleName, "model/character") && 
            (Regex.IsMatch(bundleName, "/body") || Regex.IsMatch(bundleName, "hair") || Regex.IsMatch(bundleName, "/face")))
        {
            return bundleName;
        }
        return "";
    }

    private struct Info
    {
        public string bundleName;
        public int level;
        public int firstUseTimeStamp;
        public int useCount;
        public string assetName;
    }
}
