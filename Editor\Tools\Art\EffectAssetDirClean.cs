﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class EffectAssetDirClean
{

    [MenuItem("自定义工具/美术专用/整理特效文件夹")]
    public static void CleanEffectTextureDir()
    {
        CleanTextures();
        CleanMaterils();
        DelEmptyFolder();
    }

    private static void CleanTextures()
    {
        string targetDir = "Assets/Game/Effects2/Textures";
        string[] guids = AssetDatabase.FindAssets("t:texture", new string[] { "Assets/Game/Effects/Textures"});
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            string dirName = Path.GetDirectoryName(assetPath);
            if (!dirName.EndsWith("/Effects2/Textures") && !dirName.EndsWith("Effects2/Textures/Tex_UI"))
            {
                var texture = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                string oldPath = AssetDatabase.GetAssetPath(texture.GetInstanceID());
                string newPath = targetDir + "/" + Path.GetFileName(oldPath);
                if (AssetDatabase.LoadAssetAtPath<Texture>(newPath))
                {
                    var oldPath2 = oldPath.Replace(".", "_" + texture.GetInstanceID() + ".");
                    AssetDatabase.RenameAsset(oldPath, oldPath2);
                    newPath = targetDir + "/" + Path.GetFileName(oldPath2);
                    Debug.LogErrorFormat("已存在资源{0}，改名成{1}", oldPath, oldPath2);
                }
                AssetDatabase.MoveAsset(oldPath, newPath);
            }
        }
    }
    
    private static void CleanMaterils()
    {
        string targetDir = "Assets/Game/Effects2/Material";
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game/Effects2/Textures" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (!assetPath.Contains("/Tex_UI/"))
            {
                var material = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
                string oldPath = AssetDatabase.GetAssetPath(material.GetInstanceID());
                string newPath = targetDir + "/" + Path.GetFileName(oldPath);
                if (AssetDatabase.LoadAssetAtPath<Material>(newPath))
                {
                    var oldPath2 = oldPath.Replace(".", "_" + material.GetInstanceID() + ".");
                    AssetDatabase.RenameAsset(oldPath, oldPath2);
                    newPath = targetDir + "/" + Path.GetFileName(oldPath2);
                    Debug.LogErrorFormat("已存在资源{0}，改名成{1}", oldPath, newPath);
                }
                AssetDatabase.MoveAsset(oldPath, newPath);
            }
        }
    }

    private static void DelEmptyFolder()
    {
        string[] guids = AssetDatabase.FindAssets("t:folder", new string[] { "Assets/Game/Effects2/Textures" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (AssetDatabase.IsValidFolder(assetPath))
            {
                List<string> files = new List<string>();
                bool isEmptyDir = false;
                DirectoryUtil.GetAllFiles(assetPath, files);
                if (files.Count <= 0)
                {
                    isEmptyDir = true;
                }
                else
                {
                    bool isAllDir = true;
                    for (int i = 0; i < files.Count; i++)
                    {
                        if (!Directory.Exists(files[i]) && !files[i].EndsWith(".meta"))
                        {
                            isAllDir = false;
                        }
                    }

                    isEmptyDir = isAllDir;
                }

                if (isEmptyDir)
                {
                    AssetDatabase.DeleteAsset(assetPath);
                }
            }
        }
    }
}
