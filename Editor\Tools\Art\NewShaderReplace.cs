﻿using Codice.Client.BaseCommands;
using Nirvana;
using System;
using System.IO;
using TreeEditor;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class NewShaderReplace
{
    [MenuItem("Assets/美术专用/一键替换新特效Shader(EffectCf)")]
    public static void Fixed()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int length = Selection.instanceIDs.Length;
                int[] instanceIDs = Selection.instanceIDs;
                for (int i = 0; i < length; i++)
                {
                    int instanceID = instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedInPaths(checkDirs);
                    }
                    else
                    {
                        FixedSingle(path);
                    }
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedInPaths(checkDirs);
                }
                else
                {
                    FixedSingle(path);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedInPaths(string[] checkDirs)
    {
        string[] prefabGUIDs = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = prefabGUIDs.Length;
        int cutIndex = 0;
        foreach (var guid in prefabGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameObject = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath<GameObject>(path));
            if (null == gameObject)
            {
                continue;
            }

            FixedSingle(path);

            GameObject.DestroyImmediate(gameObject);

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }

        string[] materialGUIDs = AssetDatabase.FindAssets("t:material", checkDirs);
        cutIndex = 0;
        totalCount = materialGUIDs.Length;
        foreach (var guid in materialGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            FixedSingle(path);
        }
    }


    public static void FixedSingle(string filePath)
    {
        if (filePath.EndsWith(".mat"))
        {
            CreateNewSrpEffectMat(filePath);
        }
        else if (filePath.EndsWith(".prefab"))
        {
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(filePath);
            GameObject gameObject = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            try
            {
                ParticleSystemRenderer[] particleSystemRenderers = gameObject.GetComponentsInChildren<ParticleSystemRenderer>(true);
                if (particleSystemRenderers.Length > 0)
                {
                    for (int i = 0; i < particleSystemRenderers.Length; i++)
                    {
                        if (particleSystemRenderers[i].enabled)
                        {
                            Material[] materials = particleSystemRenderers[i].sharedMaterials;
                            for (int j = 0; j < materials.Length; j++)
                            {
                                if (null != materials[j] 
                                    && null != materials[j].shader 
                                    && materials[j].shader.name == "YifStandard/YifStandardParticle")
                                {
                                    string matPath = AssetDatabase.GetAssetPath(materials[j].GetInstanceID());
                                    Material material = CreateNewSrpEffectMat(matPath);
                                    if (material != null)
                                    {
                                        particleSystemRenderers[i].sharedMaterials[j] = material;
                                    }
                                }
                            }
                        }
                    }
                }

                UiParticles.UiParticles[] uiParticles = gameObject.GetComponentsInChildren<UiParticles.UiParticles>();
                if (uiParticles.Length > 0)
                {
                    for (int i = 0; i < uiParticles.Length; i++)
                    {
                        if (uiParticles[i].material != null 
                            && uiParticles[i].material.shader != null 
                            && uiParticles[i].material.shader.name == "YifStandard/YifStandardParticle")
                        {
                            string matPath = AssetDatabase.GetAssetPath(uiParticles[i].material.GetInstanceID());
                            Material material = CreateNewSrpEffectMat(matPath);
                            if (material != null)
                            {
                                uiParticles[i].material = material;
                            }
                        }
                    }
                }

                Image[] images = gameObject.GetComponentsInChildren<Image>();
                if (images.Length > 0)
                {
                    for (int i = 0; i < images.Length; i++)
                    {
                        if (null != images[i] 
                            && null != images[i].material 
                            && null != images[i].material.shader 
                            && images[i].material.shader.name == "YifStandard/YifStandardParticle")
                        {
                            string matPath = AssetDatabase.GetAssetPath(images[i].material.GetInstanceID());
                            Material material = CreateNewSrpEffectMat(matPath);
                            if (material != null)
                            {
                                images[i].material = material;
                            }
                        }
                    }
                }

                bool success = false;
                PrefabUtility.SaveAsPrefabAsset(gameObject, filePath, out success);
                if (!success)
                {
                    Debug.LogErrorFormat("保存预制体失败, filePath:{0}", filePath);
                }
                GameObject.DestroyImmediate(gameObject);
            }
            catch (Exception ex)
            {
                if (gameObject != null)
                {
                    GameObject.DestroyImmediate(gameObject);
                }
                Debug.LogError("filePath : " + filePath);
                Debug.LogError(ex.ToString());
                EditorUtility.ClearProgressBar();
            }
        }
    }

    public static Material CreateNewSrpEffectMat(string filePath, string s = "null")
    {
        if (string.IsNullOrEmpty(filePath))
        {
            Debug.LogError("filePath is Null, " + s);
            return null;
        }

        Material originMat = AssetDatabase.LoadAssetAtPath<Material>(filePath);
        if (null != originMat && null != originMat.shader && originMat.shader.name == "YifStandard/YifStandardParticle")
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            AssetDatabase.CreateAsset(new Material(Shader.Find("Srp/Standard/EffectCf")), filePath);
            Material newMat = AssetDatabase.LoadAssetAtPath(filePath, typeof(Material)) as Material;
            newMat.CopyPropertiesFromMaterial(originMat);
            CheckDecal(newMat);

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            return newMat;
        }

        CheckDecal(originMat);
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
        return originMat;
    }

    public static void CheckDecal(Material material)
    {
        if (material.IsKeywordEnabled("ENABLE_DECAL_ALL") || material.IsKeywordEnabled("ENABLE_DECAL_ALPHA"))
        {
            if (null == material.GetTexture("_DecalTex"))
            {
                material.DisableKeyword("ENABLE_DECAL_ALL");
                material.DisableKeyword("ENABLE_DECAL_ALPHA");
            }
        }
    }
}