﻿using System;
using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;
using Nirvana;
using System.IO;
using System.Text.RegularExpressions;

namespace AssetsCheck
{
    class ActorMissingConponentChecker : BaseChecker
    {
        private string[] checkDirs = new string[] {
            "Assets/Game/Actors",
            "Assets/Game/Model",
        };

        override public string GetErrorDesc()
        {
            return string.Format("检测Actors、Model路径下的预制体组件丢失问题");
        }

        protected override void OnCheck()
        {
            try
            {
                string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
                int count = 0;
                int total = guids.Length;
                foreach (var guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;

                    if (prefab)
                    {
                        bool isMiss = false;
                        Transform[] childTrans = prefab.GetComponentsInChildren<Transform>(true);
                        List<Transform> missComTrans = new List<Transform>();
                        foreach (Transform transform in childTrans)
                        {
                            Component[] components = transform.GetComponentsInChildren<Component>();
                            foreach (Component component in components)
                            {
                                if (component == null)
                                {
                                    missComTrans.Add(transform);
                                    isMiss = true;
                                    break;
                                }
                            }
                        }

                        if (isMiss)
                        {
                            CheckItem item = new CheckItem();
                            item.asset = path;
                            item.missComTrans = new string[missComTrans.Count];
                            int index = 0;
                            foreach (Transform trans in missComTrans)
                            {
                                item.missComTrans[index] = trans.gameObject.name;
                                index += 1;
                            }
                            this.outputList.Add(item);
                        }
                    }

                    count++;
                    EditorUtility.DisplayProgressBar("正在扫描预制体...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.ClearProgressBar();
        }

        protected override void OnFix(string[] lines)
        {
            int count = 0;
            int total = lines.Length;
            foreach (string line in lines)
            {
                string[] str = line.Split(',');
                GameObject prefab = AssetDatabase.LoadAssetAtPath(str[0].Replace("asset:", ""), typeof(GameObject)) as GameObject;
                if (prefab)
                {
                    DeleteMissingScripts(prefab);
                }
                count++;
                EditorUtility.DisplayProgressBar("正在修复预制体丢失脚本...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }
            FixedPrefabAnimatorEventDispatcher();

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public string[] missComTrans;

            public string MainKey
            {
                get
                {
                    return string.Format("{0}", this.asset);
                }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                string missComTransStr = "";
                int index = 0;
                foreach (string m in this.missComTrans)
                {
                    missComTransStr = missComTransStr + (index == 0 ? m : (", " + m));
                    index += 1;
                }
                builder.AppendFormat("asset:{0}, missComTrans:{1}", this.asset, missComTransStr);
                return builder;
            }
        }

        static void DeleteMissingScripts(GameObject obj)
        {
            Regex guidRegex = new Regex("m_Script: {fileID: (.*), guid: (?<GuidValue>.*?), type:");
            Regex fileRegex = new Regex("--- !u!(?<groupNum>.*?) &(?<fileID>.*?)\r\n");
            int fileStrLenght = 30;
            string filePath = AssetDatabase.GetAssetPath(obj);
            string s = File.ReadAllText(filePath);
            string groupSpilChar = "---";
            string fileStr = "";
            bool isChange = false;
            MatchCollection matchList = guidRegex.Matches(s);
            if (matchList != null)
            {
                for (int i = matchList.Count - 1; i >= 0; i--)
                {

                    string guid = matchList[i].Groups["GuidValue"].Value;
                    if (AssetDatabase.GUIDToAssetPath(guid) == "")
                    {
                        isChange = true;
                        int startIndex = s.LastIndexOf(groupSpilChar, matchList[i].Index);
                        int endIndex = s.IndexOf(groupSpilChar, matchList[i].Index);

                        Match fileMatch = fileRegex.Match(s.Substring(startIndex, fileStrLenght));
                        fileStr = "- " + fileMatch.Groups["groupNum"].Value + ": {fileID: " + fileMatch.Groups["fileID"].Value + "}\r\n  ";

                        s = s.Replace(s.Substring(startIndex, endIndex - startIndex), "");
                        s = s.Replace(fileStr, "");
                    }
                }
            }
            if (isChange)
            {
                File.WriteAllText(filePath, s);
            }
        }

        static void FixedPrefabAnimatorEventDispatcher()
        {
            string[] GUIs = AssetDatabase.FindAssets("t:prefab", new string[] {
                "Assets/Game/Model/NPC",
                "Assets/Game/Model/Boss",
                "Assets/Game/Model/Chongwu",
                "Assets/Game/Model/Child",
                "Assets/Game/Model/Fabao",
                "Assets/Game/Model/Shouhu",
                "Assets/Game/Model/",
                "Assets/Game/Model/",
                "Assets/Game/Model/",
                "Assets/Game/Model/",
            });

            int count = 0;
            int total = GUIs.Length;
            foreach (string guid in GUIs)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                ActorRender actorRender = prefab.GetComponent<ActorRender>();
                if (actorRender)
                {
                    prefab.GetOrAddComponent<AnimatorEventDispatcher>();
                }
                count++;
                EditorUtility.DisplayProgressBar("正在修复...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }
            EditorUtility.ClearProgressBar();
        }
    }
}