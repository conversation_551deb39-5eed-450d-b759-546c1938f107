﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using Nirvana;
using LuaInterface;
using UnityEngine.UI;
using System.IO;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

/// <summary>
/// 可挂接物体.
/// </summary>
public sealed class AttachObject : MonoBehaviour
{
    [SerializeField]
    public PhysiqueConfig[] physiqueConfig;

#if UNITY_EDITOR
    private int prof;
#endif
    private Transform attached;
    private Vector3 localPosition;
    private Quaternion localRotation;

#if UNITY_EDITOR
    /// <summary>
    /// Gets the attached transform.
    /// </summary>
    [NoToLua]
    public Transform Attached
    {
        get { return this.attached; }
        set { this.attached = value; }
    }

    /// <summary>
    /// Gets the local position.
    /// </summary>
    [NoToLua]
    public Vector3 LocalPosition
    {
        get { return this.localPosition; }
        set { this.localPosition = value; }
    }

    /// <summary>
    /// Gets the local rotation.
    /// </summary>
    [NoToLua]
    public Quaternion LocalRotation
    {
        get { return this.localRotation; }
        set { this.localRotation = value; }
    }
#endif

    public void SetAttached(Transform attached)
    {
        this.attached = attached;
    }

    public void CleanAttached()
    {
        this.attached = null;
    }

    public void SetTransform(int prof)
    {
#if UNITY_EDITOR
        this.prof = prof;
#endif
        if (this.physiqueConfig != null)
        {
            foreach (var i in this.physiqueConfig)
            {
                if (i.Prof == prof)
                {
                    var rotation = Quaternion.Euler(i.Rotation);
                    this.transform.localPosition = i.Position;
                    this.transform.localRotation = rotation;
                    this.transform.localScale = i.Scale;
                    this.localPosition = this.transform.localPosition;
                    this.localRotation = rotation;
                    break;
                }
            }
        }
    }

    private void Awake()
    {
        this.localPosition = this.transform.localPosition;
        this.localRotation = this.transform.localRotation;

        if (null != this.GetComponentInChildren<TrailRenderer>())
        {
            this.GetOrAddComponent<TrailRendererController>();
        }

        EffectOrderGroup.RefreshRenderOrder(this.gameObject);
    }

    private void LateUpdate()
    {
        if (this.attached == null)
        {
            return;
        }

        var offset = new Vector3(
            this.attached.transform.lossyScale.x * this.localPosition.x,
            this.attached.transform.lossyScale.y * this.localPosition.y,
            this.attached.transform.lossyScale.z * this.localPosition.z);
        var position = this.attached.transform.position +
            this.attached.transform.rotation * offset;
        var rotation = this.attached.transform.rotation * this.localRotation;
        this.transform.SetPositionAndRotation(position, rotation);
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        var prefabType = PrefabUtility.GetPrefabType(this.gameObject);
        if (Application.isPlaying && prefabType != PrefabType.Prefab)
        {
            this.SetTransform(this.prof);
        }
    }

    private int[] autoPackProfArr = { 1001, 1002, 1005, 1003, 1004, 0 };

    [NoToLua]
    public void AutoPick()
    {
        if (physiqueConfig.Length > 0)
        {
            for (int i = 0; i < physiqueConfig.Length; ++i)
            {
                var config = physiqueConfig[i];
                if (config.Prof == 0)
                {
                    PhysiqueConfig[] newPhysiqueConfigs = new PhysiqueConfig[autoPackProfArr.Length];
                    for (int j = 0; j < autoPackProfArr.Length; ++j)
                    {
                        newPhysiqueConfigs[j].Prof = autoPackProfArr[j];
                        newPhysiqueConfigs[j].Position = config.Position;
                        newPhysiqueConfigs[j].Rotation = config.Rotation;
                        newPhysiqueConfigs[j].Scale = config.Scale;
                    }

                    physiqueConfig = newPhysiqueConfigs;
                    break;
                }
            }
        }
    }
#endif

    [Serializable]
    public struct PhysiqueConfig
    {
        public int Prof;
        public Vector3 Position;
        public Vector3 Rotation;
        public Vector3 Scale;
    }

#if UNITY_EDITOR
    private int[] ConfigMapProfArr = { 1001, 1002, 1003, 1004, 1005, 1006, 1007 };
#endif
    //切换数据
    public void ChangePhysiqueConfig(int index, Vector3 pos, Vector3 rot, Vector3 sca)
    {
#if UNITY_EDITOR
        if (this.physiqueConfig != null)
        {
            this.physiqueConfig[index].Position = pos;
            this.physiqueConfig[index].Rotation = rot;
            this.physiqueConfig[index].Scale = sca;
        }

        int prof = ConfigMapProfArr[index];
        this.SetTransform(prof);
#endif
        //InputField inputField;
        //inputField.text
    }

    public Vector3 GetPhysiqueConfigPos(int index)
    {
#if UNITY_EDITOR
        if (this.physiqueConfig != null)
        {
            return this.physiqueConfig[index].Position;
        }
#endif
        return Vector3.zero;
    }

    public Vector3 GetPhysiqueConfigRot(int index)
    {
#if UNITY_EDITOR
        if (this.physiqueConfig != null)
        {
            return this.physiqueConfig[index].Rotation;
        }
#endif
        return Vector3.zero;
    }

    public Vector3 GetPhysiqueConfigSca(int index)
    {
#if UNITY_EDITOR
        if (this.physiqueConfig != null)
        {
            return this.physiqueConfig[index].Scale;
        }
#endif
        return Vector3.zero;
    }

    public void SaveSelfForPrefabs(string bundle_path, string asset_path)
    {
#if UNITY_EDITOR
        string base_root_path = "Assets/Game/";
        bundle_path = bundle_path.Replace("_prefab", "");
        base_root_path = Path.Combine(base_root_path, bundle_path, asset_path + ".prefab");
        GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(base_root_path);
        if (gameobj != null)
        {
            AttachObject attach_obj = gameobj.GetComponent<AttachObject>();
            attach_obj.physiqueConfig = this.physiqueConfig;
            EditorUtility.SetDirty(gameobj);
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }
        else
        {
            Debug.LogError("没找到预制体");
        }
#endif
    }
}
