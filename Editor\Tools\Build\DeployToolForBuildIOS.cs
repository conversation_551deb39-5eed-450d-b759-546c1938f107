﻿using System;
using UnityEditor;
using UnityEngine;
using Build;
using System.IO;

class DeployToolForBuildIOS : DeployTool
{

    [MenuItem("自定义工具/发布版本/iOS/一键加密/Release")]
    public static void OneKeyEncryptiOSReleaseForBuildEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyEncryptIOSForBuild(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/一键加密/Dev")]
    public static void OneKeyEncryptiOSDevForBuildEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyEncryptIOSForBuild(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/iOS/一键解密/Release")]
    public static void OneKeyDecryptiOSReleaseForBuildEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForIOSBuild(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/一键解密/Dev")]
    public static void OneKeyDecryptiOSDevForBuildEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForIOSBuild(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/iOS/一键超级打包(解密资源-打包资源-加密资源)/Release")]
    public static void OneKeyBuildForIOSFlRelease()
    {
        OneKeyBuildForIOS(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/一键超级打包(解密资源-打包资源-加密资源)/Dev")]
    public static void OneKeyBuildForIOSFlDev()
    {
        OneKeyBuildForIOS(BuildPlatType.IOSDev);
    }

    public static void OneKeyBuildForIOS(BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        if (OneKeyDecryptForIOSBuild(false, buildPlatType))
        {
            if (BuildAssets(buildPlatType))
            {
                OneKeyEncryptIOSForBuild(false, buildPlatType);
            }
            else
            {
                Debug.LogError("一键超级打包失败，请查看日志!!!");
            }
        }
        else
        {
            Debug.LogError("一键超级打包失败，请查看日志!!!");
        }
    }

    public static void OneKeyEncryptIOSForBuild(bool isCallByPy, BuildPlatType buildPlatType)
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
        try
        {
            //偏移加密AB
            if (!DeployToolForEncrypt.EncryptAssetBundle(isCallByPy, buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAssetBundle");
                return;
            }
            ReportBuildLog("EncryptAssets, DeployToolForEncrypt.EncryptAssetBundle");

            if (!DeployToolForEncrypt.EncryptAndZipVersionFile(buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");
                return;
            }
            ReportBuildLog("EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");

            if (!DeployToolForEncrypt.EncryptLuaFiles("", isCallByPy, buildPlatType))
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptLuaFiles");
                return;
            }
            ReportBuildLog("[Encrypt Success]EncryptAssets, DeployToolForEncrypt.EncryptLuaFiles");
        }
        catch (Exception ex)
        {
            Debug.LogError(ex);
            EditorUtility.ClearProgressBar();
            if (!isCallByPy)
            {
                ReportBuildLog("[Fail] EncryptAssets, DeployToolForBuild.OneKeyEncryptAndroidForBuild, Error Msg:" + ex.ToString());
            }
        }
        EditorUtility.ClearProgressBar();
    }

    public static bool OneKeyDecryptForIOSBuild(bool isCallByPy, BuildPlatType buildPlatType)
    {
        if (!DeployToolForEncrypt.DecryptLuaFiles("", isCallByPy, buildPlatType))
        {
            DeployTool.ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.DecryptLuaFiles");
            return false;
        }
        if (!DeployToolForEncrypt.EncryptAndZipVersionFile(buildPlatType))
        {
            DeployTool.ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.EncryptAndZipVersionFile");
            return false;
        }
        if (!DeployToolForEncrypt.DecryptAssetBundle(isCallByPy, buildPlatType))
        {
            DeployTool.ReportBuildLog("[Fail] EncryptAssets, DeployToolForEncrypt.DecryptAssetBundle");
            return false;
        }
        DeployTool.ReportBuildLog("EncryptAssets, OneKeyDecryptForAndroidBuild");
        return true;
    }

    //[MenuItem("自定义工具/发布版本/Audit/iOS审核包资源测试(iqa)")]
    //public static void OneKeyBuildFirIOSAuditIQA()
    //{
    //    try
    //    {
    //        EncryptMgr.OpenBase64EncryptInEditorMode(true);
    //        EncryptMgr.SetBase64EncryptKey("IJKLMNOPQRABCDEFGHSTUVWXYZrstuvwabcdefghijklmnopqxyz0123456789+/=");
    //        EncryptMgr.RefreshEncryptKey();

    //        //删除Audit下的打包资源，将iOS下的拷过去
    //        string auditBundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.Audit_iqa, "AssetBundle");
    //        DeleteFolder(false, auditBundlePath);
    //        string iosBundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.IOS, "AssetBundle");
    //        GetFilesAndDirs(false, iosBundlePath, auditBundlePath);

    //        //解密
    //        OneKeyDecryptForIOSBuild(false, BuildPlatType.Audit_iqa);

    //        //打包
    //        BuildAssets(BuildPlatType.Audit_iqa);
    //        //一键加密，混淆资源
    //        OneKeyEncryptIOSForBuild(false, BuildPlatType.Audit_iqa);
    //        DeployToolForEncrypt.Base64EncodingABPath(false, BuildPlatType.Audit_iqa);

    //        EncryptMgr.OpenBase64EncryptInEditorMode(false);
    //        EncryptMgr.SetBase64EncryptKey("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=");

    //        DeployTool.ReportBuildLog("[Base64Encoding Success]EncryptAssets, DeployToolForEncrypt.Base64EncodingABPath");
    //    }
    //    catch (Exception ex)
    //    {
    //        EncryptMgr.OpenBase64EncryptInEditorMode(false);
    //        EncryptMgr.SetBase64EncryptKey("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=");
    //        Debug.LogError(ex.ToString());
    //        DeployTool.ReportBuildLog("[Fail]EncryptAssets, DeployToolForEncrypt.Base64EncodingABPath");
    //    }
    //}

    //public static void OneKeyBuildFirIOSAuditIQAByPy()
    //{
    //    try
    //    {
    //        EncryptMgr.OpenBase64EncryptInEditorMode(true);
    //        EncryptMgr.SetBase64EncryptKey("IJKLMNOPQRABCDEFGHSTUVWXYZrstuvwabcdefghijklmnopqxyz0123456789+/=");
    //        EncryptMgr.RefreshEncryptKey();

    //        //删除Audit下的打包资源，将iOS下的拷过去
    //        string auditBundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.Audit_iqa, "AssetBundle");
    //        DeleteFolder(true, auditBundlePath);
    //        string iosBundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.IOS, "AssetBundle");
    //        GetFilesAndDirs(true, iosBundlePath, auditBundlePath);

    //        //解密
    //        OneKeyDecryptForIOSBuild(true, BuildPlatType.Audit_iqa);

    //        //打包
    //        BuildAssets(BuildPlatType.Audit_iqa);
    //        //一键加密，混淆资源
    //        OneKeyEncryptIOSForBuild(true, BuildPlatType.Audit_iqa);
    //        DeployToolForEncrypt.Base64EncodingABPath(true, BuildPlatType.Audit_iqa);

    //        EncryptMgr.OpenBase64EncryptInEditorMode(false);
    //        EncryptMgr.SetBase64EncryptKey("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=");

    //        DeployTool.ReportBuildLog("[Base64Encoding Success]EncryptAssets, DeployToolForEncrypt.Base64EncodingABPath");
    //    }
    //    catch (Exception ex)
    //    {
    //        EncryptMgr.OpenBase64EncryptInEditorMode(false);
    //        EncryptMgr.SetBase64EncryptKey("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=");
    //        Debug.LogError(ex.ToString());
    //        DeployTool.ReportBuildLog("[Fail]EncryptAssets, DeployToolForEncrypt.Base64EncodingABPath");
    //    }
    //}

    /// <summary>
    /// 打包工具调用检测资源接口
    /// </summary>
    public static void OneKeyExecuteCheckForIOSCallByPy()
    {
        ReportBuildLog("[ResourceCheckSuccess]Start Check Resource");
        AssetsCheck.ModelRoundedAnalysis analysis = new AssetsCheck.ModelRoundedAnalysis();
        if (analysis.ExecuteCheck(true))
        {
            ReportBuildLog("[ResourceCheckSuccess]Resource Tested Successfully");
        }
        else
        {
            ReportBuildLog("[ResourceCheckError]Check file client/u3d_proj/AssetsCheckModelRoundedAnalysis.txt");
        }
    }

    /// <summary>
    /// 打包工具调用接口-返利版本
    /// </summary>
    public static void OneKeyBuildForIOSCallByPy()
    {
        EncryptMgr.RefreshEncryptKey();
        OneKeyDecryptForIOSBuild(true, BuildPlatType.IOS);
        BuildAssets(BuildPlatType.IOS);
        OneKeyEncryptIOSForBuild(true, BuildPlatType.IOS);
    }

    private static void DeleteFolder(bool isCallByPy, string deleteDirectory)
    {

        try
        {
            if (Directory.Exists(deleteDirectory))
            {
                string[] deleteFiles = Directory.GetFileSystemEntries(deleteDirectory);
                int index = 0;
                int total = deleteFiles.Length;
                foreach (string deleteFile in deleteFiles)
                {
                    if (File.Exists(deleteFile))
                        File.Delete(deleteFile);
                    else
                        DeleteFolder(isCallByPy, deleteFile);

                    index++;
                    if (!isCallByPy)
                        EditorUtility.DisplayProgressBar("正在删除资源", string.Format("{0} / {1}", index, total), (float)index / (float)total);
                }
                Directory.Delete(deleteDirectory);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            if (!isCallByPy)
                EditorUtility.ClearProgressBar();
        }
        if (!isCallByPy)
            EditorUtility.ClearProgressBar();
    }

    private static void GetFilesAndDirs(bool isCallByPy, string srcDir, string destDir)
    {
        try
        {
            if (!Directory.Exists(destDir))
            {
                Directory.CreateDirectory(destDir);
            }

            string newPath;
            FileInfo fileInfo;
            string[] files = Directory.GetFiles(srcDir);
            int index = 0;
            int total = files.Length;
            foreach (string path in files)
            {
                fileInfo = new FileInfo(path);
                newPath = Path.Combine(destDir, fileInfo.Name);
                File.Copy(path, newPath, true);
                index++;
                if (!isCallByPy)
                    EditorUtility.DisplayProgressBar("正在拷贝文件", string.Format("{0} / {1}", index, total), (float)index / (float)total);
            }

            index = 0;
            string[] dirs = Directory.GetDirectories(srcDir);
            total = dirs.Length;
            foreach (string path in dirs)
            {
                DirectoryInfo directory = new DirectoryInfo(path);
                string newDir = Path.Combine(destDir, directory.Name);
                GetFilesAndDirs(isCallByPy, path + "\\", newDir + "\\");
                index++;
                if (!isCallByPy)
                    EditorUtility.DisplayProgressBar("正在拷贝目录", string.Format("{0} / {1}", index, total), (float)index / (float)total);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            if (!isCallByPy)
                EditorUtility.ClearProgressBar();
        }
        if (!isCallByPy)
            EditorUtility.ClearProgressBar();
    }
}