﻿using System.Collections.Generic;
using UnityEngine;
using Nirvana;

/// <summary>
/// 预制件数据配置类
/// 包含角色控制器、触发器效果、音效、震屏等配置数据
/// </summary>
public class SkillEditorPrefabDataConfig
{
    #region ActorController

    public ActorControllerData actorController = new ActorControllerData();

    /// <summary>
    /// 角色控制器数据结构
    /// </summary>
    public class ActorControllerData
    {
        public List<ProjectileData> projectiles = new List<ProjectileData>();           // 弹道列表
        public List<HurtData> hurts = new List<HurtData>();                             // 受击效果列表
    }

    /// <summary> 弹道数据 </summary>
    public class ProjectileData
    {
        public string Action = string.Empty;                                            // 关联的动作名称
        public HurtPositionEnum HurtPosition = 0;                                       // 伤害位置类型
        public AssetID Projectile = new AssetID("", "");                                // 弹道特效资源ID
        public string ProjectilGoName = string.Empty;                                   // 弹道游戏对象名称
        public string FromPosHierarchyPath = string.Empty;                              // 起始位置节点路径
        public float DelayProjectileEff = 0;                                            // 弹道特效延迟时间
        public float DeleProjectileDelay = 0;                                           // 弹道特效删除延迟
        public string ProjectilNodeHierarchyPath = string.Empty;                        // 弹道节点路径
        public string ProjectileBtnName = string.Empty;                                 // 弹道按钮名称（编辑器用）
    }

    /// <summary> 受击数据 </summary>
    public class HurtData
    {
        public string HurtBtnName = string.Empty;                                       // 受击按钮名称（编辑器用）

        public string Action = string.Empty;                                            // 关联的动作名称
        public AssetID HurtEffect = new AssetID("", "");                                // 受击特效资源ID
        public string HurtEffectGoName = string.Empty;                                  // 受击特效对象名称
        public HurtPositionEnum HurtPosition = 0;                                       // 受击位置类型
        public HurtRotationEnum HurtRotation = 0;                                       // 受击旋转类型
        public float HurtFreeDelay = 0;                                                 // 受击特效销毁延迟
        public float DelayHurtEffect = 0;                                               // 受击延迟时间

        public string HurtSoundAudioGoName = string.Empty;                                  // 音频对象名称
        public AssetID HurtSoundAudioAsset = new AssetID("", "");                           // 音频资源ID
        public float DelayHurtSound = 0;                                                // 受击音效延迟时间



        public int HitCount = 0;                                                        // 命中次数
        public float HitFreeDelay = 0;                                                  // 命中特效销毁延迟
        public string HitInterval = string.Empty;                                       // 命中间隔（格式："0.2|0.3"）
        public string HitProportion = string.Empty;                                     // 命中比例配置
        public float DelayHitEffect = 0;                                                // 命中延迟时间
        public AssetID HitEffect = new AssetID("", "");                                 // 命中特效资源ID
        public string HitEffectGoName = string.Empty;                                   // 命中特效对象名称
        public HurtPositionEnum HitPosition = 0;                                        // 命中位置类型
        public HurtRotationEnum HitRotation = 0;                                        // 命中旋转类型

        public List<HitSoundData> hitSounds = new List<HitSoundData>();                 // 命中随机音效列表
    }

    /// <summary> 命中音效数据结构 </summary>
    [System.Serializable]
    public class HitSoundData
    {
        public AssetID soundAsset = new AssetID("", "");  // 音效资源ID
        public string soundGoName = string.Empty;         // 音效对象名称
        public int weight = 50;                          // 触发权重（1-100）
    }

    /// <summary> 伤害位置枚举 </summary>
    public enum HurtPositionEnum
    {
        Root,
        HurtPoint,
    }

    /// <summary> 伤害方向枚举 </summary>
    public enum HurtRotationEnum
    {
        Target,
        HitDirection,
    }

    #endregion

    #region ActorTriggers
    /// <summary> 角色触发器数据 </summary>
    public ActorTriggersData actorTriggers = new ActorTriggersData();

    /// <summary>
    /// 触发器数据类
    /// </summary>
    public class ActorTriggersData
    {
        public List<TriggerEffect> effects = new List<TriggerEffect>();             // 特效列表
        public List<TriggerSound> sounds = new List<TriggerSound>();                // 音效列表
        public List<CameraShakeData> cameraShakes = new List<CameraShakeData>();    // 震屏列表
        public List<RadialBlurData> radialBlurs = new List<RadialBlurData>();       // 径向模糊列表
    }

    #region Effect
    /// <summary> 特效数组长度常量 </summary>
    public const int EFFECT_ARRAY_SIZE = 2;
    
    /// <summary> 特效数组元素 </summary>
    [System.Serializable]
    public class EffectArrayItem
    {
        public string effectGoName = string.Empty;                          // 特效对象名称
        public AssetID effectAsset = new AssetID("", "");                   // 特效资源ID
    }

    /// <summary> 触发特效配置 </summary>
    public class TriggerEffect
    {
        public string triggerEventName = string.Empty;                      // 触发事件名称
        public float triggerDelay = 0;                                      // 触发延迟时间
        public float triggerFreeDelay = 0;                                  // 特效销毁延迟
        public string effectGoName = string.Empty;                          // 特效对象名称
        public AssetID effectAsset = new AssetID("", "");                   // 特效资源ID
        public bool playerAtTarget = false;                                 // 是否在目标位置播放
        public string referenceNodeHierarchyPath = string.Empty;            // 参考节点路径
        public bool isAttach = false;                                       // 是否附着
        public bool isRotation = false;                                     // 是否同步旋转
        public bool isUseCustomTransform = false;                           // 是否自定义坐标
        public float offsetPosX = 0f;                                       // X轴偏移
        public float offsetPosY = 0f;                                       // Y轴偏移
        public float offsetPosZ = 0f;                                       // Z轴偏移
        public string triggerStopEvent = string.Empty;                      // 停止事件名称
        public string effectBtnName = string.Empty;                         // 特效按钮名称（编辑器用）
        public bool playerAtPos = false;                                    // 在目标点创建
        public bool ignoreParentScale = false;                              // 忽略父级缩放
        public EffectArrayItem[] effectArray = new EffectArrayItem[EFFECT_ARRAY_SIZE];      // 特效数组

        /// <summary> 构造函数初始化特效数组 </summary>
        public TriggerEffect()
        {
            effectArray = new EffectArrayItem[EFFECT_ARRAY_SIZE];
            for (int i = 0; i < EFFECT_ARRAY_SIZE; i++)
            {
                effectArray[i] = new EffectArrayItem();
            }
        }
    }
    #endregion

    #region Sound
    /// <summary> 触发音效配置 </summary>
    public class TriggerSound
    {
        public string soundEventName = string.Empty;                        // 音效事件名称
        public float soundDelay = 0;                                        // 播放延迟时间
        public AssetID soundAudioAsset = new AssetID("", "");               // 音频资源ID
        public string soundAudioGoName = string.Empty;                      // 音频对象名称
        public string soundBtnName = string.Empty;                          // 按钮名称（编辑器用）
        public bool soundIsMainRole = false;                                // 是否为主角音效
    }
    #endregion

    #region CameraShakeData
    /// <summary> 摄像机震动配置 </summary>
    public class CameraShakeData
    {

        public string CameraShakeBtnName = string.Empty;                    // 按钮名称（编辑器用）
        public string eventName = string.Empty;                             // 触发事件名称
        public int numberOfShakes = 2;                                      // 震动次数
        public float distance = 0.05f;                                      // 震动幅度
        public float speed = 0.50f;                                         // 震动速度（秒）
        public float delay = 0.20f;                                         // 触发延迟
        public float decay = 0.00f;                                         // 衰减系数（0-1）
    }
    #endregion

    #region RadialBlurData
    /// <summary> 径向模糊配置 </summary>
    public class RadialBlurData
    {
        public string btnName = string.Empty;                               // 按钮名称（编辑器用）
        public string eventName = string.Empty;                             // 事件名称
        public bool isRole = true;                                          // 展示点是否是玩家
        public string referenceNodeHierarchyPath = string.Empty;            // 参考节点路径
        public float delay = 0;                                             // 延迟时间
        public float strength = 0.0f;                                       // 模糊强度
        public float riseTime = 0.0f;                                       // 从0达到强度值的时间
        public float holdTime = 0.0f;                                       // 持续时间
        public float fallTime = 0.0f;                                       // 结束耗时
    }
    #endregion

    #endregion
}
