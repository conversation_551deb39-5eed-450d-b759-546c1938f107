﻿using UnityEngine;
using System.Collections.Generic;

namespace UniFramework.Editor
{
    /// <summary>
    /// BMFont reader. C# implementation of http://www.angelcode.com/products/bmfont/
    /// </summary>
    [System.Serializable]
    public class BMFont
    {
        [HideInInspector] [SerializeField] int m_Size = 16;          // How much to move the cursor when moving to the next line
        [HideInInspector] [SerializeField] int m_Base = 0;           // Offset from the top of the line to the base of each character
        [HideInInspector] [SerializeField] int m_Width = 0;          // Original width of the texture
        [HideInInspector] [SerializeField] int m_Height = 0;         // Original height of the texture
        [HideInInspector] [SerializeField] string m_SpriteName;

        // List of serialized glyphs
        [HideInInspector] [SerializeField] List<BMGlyph> m_Saved = new List<BMGlyph>();

        // Actual glyphs that we'll be working with are stored in a dictionary, making the lookup faster
        Dictionary<int, BMGlyph> m_Dict = new Dictionary<int, BMGlyph>();

        /// <summary>
        /// Whether the font can be used.
        /// </summary>
        public bool IsValid => m_Saved.Count > 0;

        /// <summary>
        /// Size of this font (for example 32 means 32 pixels).
        /// </summary>
        public int CharSize { get { return m_Size; } set { m_Size = value; } }

        /// <summary>
        /// Base offset applied to characters.
        /// </summary>
        public int BaseOffset { get { return m_Base; } set { m_Base = value; } }

        /// <summary>
        /// Original width of the texture.
        /// </summary>
        public int TexWidth { get { return m_Width; } set { m_Width = value; } }

        /// <summary>
        /// Original height of the texture.
        /// </summary>
        public int TexHeight { get { return m_Height; } set { m_Height = value; } }

        /// <summary>
        /// Number of valid glyphs.
        /// </summary>
        public int GlyphCount { get { return IsValid ? m_Saved.Count : 0; } }

        /// <summary>
        /// Original name of the sprite that the font is expecting to find (usually the name of the texture).
        /// </summary>
        public string SpriteName { get { return m_SpriteName; } set { m_SpriteName = value; } }

        /// <summary>
        /// Access to BMFont's entire set of glyphs.
        /// </summary>
        public List<BMGlyph> Glyphs { get { return m_Saved; } }

        /// <summary>
        /// Helper function that retrieves the specified glyph, creating it if necessary.
        /// </summary>
        public BMGlyph GetGlyph(int index, bool create_if_missing)
        {
            if (m_Dict.Count == 0) {
                // Populate the dictionary for faster access
                for (int i = 0, imax = m_Saved.Count; i < imax; ++i) {
                    var bmg = m_Saved[i];
                    m_Dict.Add(bmg.index, bmg);
                }
            }

            // Get the requested glyph
            BMGlyph glyph;
            // Saved check is here so that the function call is not needed if it's true
            if (!m_Dict.TryGetValue(index, out glyph) && create_if_missing) {
                glyph = new BMGlyph();
                glyph.index = index;
                m_Saved.Add(glyph);
                m_Dict.Add(index, glyph);
            }
            return glyph;
        }

        /// <summary>
        /// Retrieve the specified glyph, if it's present.
        /// </summary>
        public BMGlyph GetGlyph(int index) { return GetGlyph(index, false); }

        /// <summary>
        /// Clear the glyphs.
        /// </summary>
        public void Clear()
        {
            m_Dict.Clear();
            m_Saved.Clear();
        }

        /// <summary>
        /// Trim the glyphs, ensuring that they will never go past the specified bounds.
        /// </summary>
        public void Trim(int x_min, int y_min, int x_max, int y_max)
        {
            if (IsValid) {
                for (int i = 0, imax = m_Saved.Count; i < imax; ++i) {
                    var glyph = m_Saved[i];
                    if (glyph != null) {
                        glyph.Trim(x_min, y_min, x_max, y_max);
                    }
                }
            }
        }
    }
}
