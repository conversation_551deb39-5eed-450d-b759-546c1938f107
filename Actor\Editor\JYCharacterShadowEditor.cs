﻿using UnityEditor;
using UnityEngine;


[CustomEditor(typeof(JYCharacterShadow))]
public class JYCharacterShadowEditor : CharacterShadowEditor
{
    
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        
        var component = target as JYCharacterShadow;
        
        if (Application.isPlaying)
        {
            EditorGUILayout.LabelField("Actor Render List");
            EditorGUI.indentLevel++;
            EditorGUILayout.BeginVertical("Box");
            if (component.ActorRenders != null)
            {
                for (int i = 0; i < component.ActorRenders.Length; i++)
                {
                    EditorGUILayout.ObjectField(i.ToString(), component.ActorRenders[i], typeof(ActorRender));
                }
            }
            EditorGUILayout.EndVertical();
            EditorGUI.indentLevel--;
        }

        this.serializedObject.ApplyModifiedProperties();
    }
}
