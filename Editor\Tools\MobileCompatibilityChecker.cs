using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using MobileCompatibility;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace MobileCompatibility
{
    /// <summary>
    /// 移动端兼容性检测器核心类
    /// 集成并扩展MobileSkinnedMeshFixer的检测功能
    /// </summary>
    public static class MobileCompatibilityChecker
    {
        // Mesh兼容性缓存，避免重复检测
        private static Dictionary<Mesh, MeshCompatibilityInfo> meshCache = new Dictionary<Mesh, MeshCompatibilityInfo>();
        
        private struct MeshCompatibilityInfo
        {
            public int vertexCount;
            public int vertexStride;
            public VertexAttributeDescriptor[] vertexAttributes;
            public bool isCompatibleWithMobile;
            public string incompatibilityReason;
        }

        /// <summary>
        /// 执行完整的移动端兼容性检测
        /// </summary>
        public static MobileCompatibilityReport RunFullCompatibilityCheck(bool includeInactiveObjects = false)
        {
            var stopwatch = Stopwatch.StartNew();
            var report = new MobileCompatibilityReport();
            
            // 清理旧缓存
            meshCache.Clear();
            
            // 获取所有SkinnedMeshRenderer
            SkinnedMeshRenderer[] renderers = includeInactiveObjects ? 
                UnityEngine.Object.FindObjectsOfType<SkinnedMeshRenderer>(true) :
                UnityEngine.Object.FindObjectsOfType<SkinnedMeshRenderer>();
            
            report.Statistics.TotalScannedObjects = renderers.Length;
            report.Statistics.SkinnedMeshRendererCount = renderers.Length;
            
            // 检测每个SkinnedMeshRenderer
            foreach (var renderer in renderers)
            {
                CheckSkinnedMeshRenderer(renderer, report);
            }
            
            // 检测全局渲染设置
            CheckGlobalRenderSettings(report);
            
            stopwatch.Stop();
            report.Statistics.ScanDuration = stopwatch.ElapsedMilliseconds;
            
            return report;
        }

        /// <summary>
        /// 检测单个SkinnedMeshRenderer
        /// </summary>
        private static void CheckSkinnedMeshRenderer(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            if (renderer == null)
                return;

            // 1. 检测Mesh兼容性
            CheckMeshCompatibility(renderer, report);
            
            // 2. 检测骨骼系统
            CheckBoneSystem(renderer, report);
            
            // 3. 检测材质和Shader
            CheckMaterialShader(renderer, report);
            
            // 4. 检测渲染设置
            CheckRenderSettings(renderer, report);
            
            // 5. 检测性能相关
            CheckPerformanceSettings(renderer, report);
        }

        /// <summary>
        /// 检测Mesh兼容性
        /// </summary>
        private static void CheckMeshCompatibility(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            if (renderer.sharedMesh == null)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Error,
                    IssueType.MeshCompatibility,
                    "SkinnedMeshRenderer缺少Mesh引用",
                    renderer.gameObject,
                    renderer,
                    "为SkinnedMeshRenderer分配有效的Mesh",
                    "缺少Mesh会导致渲染失败",
                    9
                ));
                return;
            }

            var mesh = renderer.sharedMesh;
            
            // 检查缓存
            if (!meshCache.TryGetValue(mesh, out MeshCompatibilityInfo meshInfo))
            {
                meshInfo = AnalyzeMeshCompatibility(mesh);
                meshCache[mesh] = meshInfo;
            }

            if (!meshInfo.isCompatibleWithMobile)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Error,
                    IssueType.MeshCompatibility,
                    $"Mesh '{mesh.name}' 在移动端不兼容: {meshInfo.incompatibilityReason}",
                    renderer.gameObject,
                    renderer,
                    GetMeshCompatibilityFixSuggestion(meshInfo),
                    $"顶点数: {meshInfo.vertexCount}, Vertex Stride: {meshInfo.vertexStride}",
                    8
                ));
            }

            // 检测顶点数量警告
            if (meshInfo.vertexCount > 32768)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.Performance,
                    $"Mesh '{mesh.name}' 顶点数量较高 ({meshInfo.vertexCount})",
                    renderer.gameObject,
                    renderer,
                    "考虑使用LOD或优化Mesh以减少顶点数量",
                    "高顶点数量可能影响移动端性能",
                    6
                ));
            }
        }

        /// <summary>
        /// 分析Mesh兼容性
        /// </summary>
        private static MeshCompatibilityInfo AnalyzeMeshCompatibility(Mesh mesh)
        {
            var info = new MeshCompatibilityInfo
            {
                vertexCount = mesh.vertexCount,
                vertexAttributes = mesh.GetVertexAttributes(),
                isCompatibleWithMobile = true,
                incompatibilityReason = ""
            };

            // 计算vertex stride
            info.vertexStride = 0;
            foreach (var attr in info.vertexAttributes)
            {
                info.vertexStride += GetAttributeSize(attr.format) * attr.dimension;
            }

            // 检查顶点数量限制
            if (info.vertexCount > 65535)
            {
                info.isCompatibleWithMobile = false;
                info.incompatibilityReason = $"顶点数量 ({info.vertexCount}) 超过移动端限制 (65535)";
                return info;
            }

            // 检查vertex stride
            if (info.vertexStride > 64)
            {
                info.isCompatibleWithMobile = false;
                info.incompatibilityReason = $"Vertex stride ({info.vertexStride}) 对移动端过大";
                return info;
            }

            // 检查不支持的vertex attributes
            foreach (var attr in info.vertexAttributes)
            {
                if (!IsMobileSupportedAttribute(attr))
                {
                    info.isCompatibleWithMobile = false;
                    info.incompatibilityReason = $"包含不支持的vertex attribute: {attr.attribute}";
                    return info;
                }
            }

            return info;
        }

        /// <summary>
        /// 检测骨骼系统
        /// </summary>
        private static void CheckBoneSystem(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            // 检查骨骼数量
            if (renderer.bones != null && renderer.bones.Length > 32)
            {
                var level = renderer.bones.Length > 64 ? IssueLevel.Error : IssueLevel.Warning;
                report.AddIssue(new CompatibilityIssue(
                    level,
                    IssueType.BoneSystem,
                    $"骨骼数量 ({renderer.bones.Length}) 超过移动端推荐值 (32)",
                    renderer.gameObject,
                    renderer,
                    "减少骨骼数量或使用骨骼LOD系统",
                    $"当前骨骼数: {renderer.bones.Length}, 移动端推荐: ≤32",
                    renderer.bones.Length > 64 ? 8 : 5
                ));
            }

            // 检查质量设置中的骨骼权重
            if (QualitySettings.skinWeights == SkinWeights.Unlimited)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.BoneSystem,
                    "Quality Settings中骨骼权重设置为Unlimited",
                    renderer.gameObject,
                    renderer,
                    "在Quality Settings中将Skin Weights设置为FourBones",
                    "移动端通常只支持4个骨骼权重",
                    4
                ));
            }

            // 检查更新频率设置
            if (renderer.updateWhenOffscreen)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Info,
                    IssueType.Performance,
                    "启用了屏幕外更新 (updateWhenOffscreen)",
                    renderer.gameObject,
                    renderer,
                    "考虑禁用updateWhenOffscreen以提升性能",
                    "屏幕外更新会消耗额外的CPU资源",
                    3
                ));
            }
        }

        /// <summary>
        /// 检测材质和Shader兼容性
        /// </summary>
        private static void CheckMaterialShader(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            var materials = renderer.sharedMaterials;
            
            for (int i = 0; i < materials.Length; i++)
            {
                var material = materials[i];
                if (material == null)
                {
                    report.AddIssue(new CompatibilityIssue(
                        IssueLevel.Error,
                        IssueType.MaterialShader,
                        $"材质槽 {i} 为空",
                        renderer.gameObject,
                        renderer,
                        "为所有材质槽分配有效的材质",
                        "空材质槽会导致渲染错误",
                        7
                    ));
                    continue;
                }

                var shader = material.shader;
                if (shader == null)
                {
                    report.AddIssue(new CompatibilityIssue(
                        IssueLevel.Error,
                        IssueType.MaterialShader,
                        $"材质 '{material.name}' 缺少Shader",
                        renderer.gameObject,
                        renderer,
                        "为材质分配适当的Shader",
                        "缺少Shader会导致渲染失败",
                        8
                    ));
                    continue;
                }

                // 检查Shader兼容性
                CheckShaderCompatibility(material, shader, renderer, report);
            }
        }

        /// <summary>
        /// 检测Shader兼容性
        /// </summary>
        private static void CheckShaderCompatibility(Material material, Shader shader, SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            var shaderName = shader.name;
            
            // 检查是否使用了移动端不推荐的Shader
            if (shaderName.Contains("Standard") && !shaderName.Contains("Mobile"))
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.MaterialShader,
                    $"使用了非移动端优化的Standard Shader: '{shaderName}'",
                    renderer.gameObject,
                    renderer,
                    "考虑使用URP/Lit或URP/Simple Lit shader",
                    "Standard shader在移动端性能较差",
                    5
                ));
            }

            // 检查GPU Skinning相关关键字
            if (material.IsKeywordEnabled("_ENABLE_GPU_SKINNING"))
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Info,
                    IssueType.MaterialShader,
                    "材质启用了GPU Skinning关键字",
                    renderer.gameObject,
                    renderer,
                    "如果遇到兼容性问题，考虑禁用GPU Skinning",
                    "GPU Skinning在某些移动端设备上可能不稳定",
                    3
                ));
            }

            // 检查复杂的材质属性
            CheckMaterialComplexity(material, renderer, report);
        }

        /// <summary>
        /// 检测材质复杂度
        /// </summary>
        private static void CheckMaterialComplexity(Material material, SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            var shader = material.shader;
            var passCount = shader.passCount;
            
            if (passCount > 2)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.Performance,
                    $"Shader '{shader.name}' 包含多个Pass ({passCount})",
                    renderer.gameObject,
                    renderer,
                    "使用单Pass的Shader以提升性能",
                    $"多Pass渲染会增加Draw Call和GPU负载",
                    4
                ));
            }

            // 检查纹理数量
            var textureCount = 0;
            var propertyCount = ShaderUtil.GetPropertyCount(shader);
            for (int i = 0; i < propertyCount; i++)
            {
                if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                {
                    var texture = material.GetTexture(ShaderUtil.GetPropertyName(shader, i));
                    if (texture != null)
                        textureCount++;
                }
            }

            if (textureCount > 4)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Info,
                    IssueType.Performance,
                    $"材质使用了较多纹理 ({textureCount})",
                    renderer.gameObject,
                    renderer,
                    "考虑合并纹理或减少纹理使用",
                    "过多纹理会增加内存占用和带宽需求",
                    3
                ));
            }
        }

        /// <summary>
        /// 检测渲染设置
        /// </summary>
        private static void CheckRenderSettings(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            // 检查运动向量
            if (renderer.skinnedMotionVectors)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Info,
                    IssueType.RenderSettings,
                    "启用了运动向量 (Motion Vectors)",
                    renderer.gameObject,
                    renderer,
                    "如果不需要运动模糊效果，可以禁用以提升性能",
                    "运动向量会增加渲染开销",
                    2
                ));
            }

            // 检查阴影投射
            if (renderer.shadowCastingMode == ShadowCastingMode.TwoSided)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.RenderSettings,
                    "阴影投射模式设置为TwoSided",
                    renderer.gameObject,
                    renderer,
                    "考虑使用On模式以提升性能",
                    "TwoSided阴影投射会增加渲染开销",
                    4
                ));
            }

            // 检查光照探针
            if (renderer.lightProbeUsage == LightProbeUsage.UseProxyVolume)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.RenderSettings,
                    "使用了Light Probe Proxy Volume",
                    renderer.gameObject,
                    renderer,
                    "考虑使用BlendProbes模式",
                    "Proxy Volume在移动端可能影响性能",
                    3
                ));
            }
        }

        /// <summary>
        /// 检测性能相关设置
        /// </summary>
        private static void CheckPerformanceSettings(SkinnedMeshRenderer renderer, MobileCompatibilityReport report)
        {
            var mesh = renderer.sharedMesh;
            if (mesh == null) return;

            // 检查网格复杂度
            var triangleCount = mesh.triangles.Length / 3;
            if (triangleCount > 10000)
            {
                var level = triangleCount > 20000 ? IssueLevel.Warning : IssueLevel.Info;
                report.AddIssue(new CompatibilityIssue(
                    level,
                    IssueType.Performance,
                    $"Mesh三角面数量较高 ({triangleCount})",
                    renderer.gameObject,
                    renderer,
                    "考虑使用LOD系统或优化模型",
                    $"高面数模型会影响GPU性能",
                    triangleCount > 20000 ? 6 : 3
                ));
            }

            // 检查是否有LOD设置
            var lodGroup = renderer.GetComponentInParent<LODGroup>();
            if (lodGroup == null && triangleCount > 5000)
            {
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Info,
                    IssueType.Performance,
                    "高面数模型未配置LOD系统",
                    renderer.gameObject,
                    renderer,
                    "为复杂模型添加LOD系统",
                    "LOD可以在距离较远时降低渲染复杂度",
                    3
                ));
            }
        }

        /// <summary>
        /// 检测全局渲染设置
        /// </summary>
        private static void CheckGlobalRenderSettings(MobileCompatibilityReport report)
        {
            // 检查URP Asset设置
            var urpAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                // 创建一个虚拟的GameObject来报告全局问题
                var globalSettingsObject = new GameObject("Global Render Settings");
                
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Error,
                    IssueType.RenderSettings,
                    "未找到URP渲染管线Asset",
                    globalSettingsObject,
                    null,
                    "确保项目使用URP渲染管线",
                    "SkinnedMeshRenderer在URP下表现更稳定",
                    9
                ));
                
                // 立即销毁临时对象
                UnityEngine.Object.DestroyImmediate(globalSettingsObject);
            }

            // 检查Quality Settings
            if (QualitySettings.skinWeights == SkinWeights.Unlimited)
            {
                var globalSettingsObject = new GameObject("Quality Settings");
                
                report.AddIssue(new CompatibilityIssue(
                    IssueLevel.Warning,
                    IssueType.BoneSystem,
                    "Quality Settings中骨骼权重设置为Unlimited",
                    globalSettingsObject,
                    null,
                    "将Skin Weights设置为FourBones",
                    "移动端通常只支持4个骨骼权重",
                    5
                ));
                
                UnityEngine.Object.DestroyImmediate(globalSettingsObject);
            }
        }

        // 辅助方法
        private static bool IsMobileSupportedAttribute(VertexAttributeDescriptor attr)
        {
            switch (attr.attribute)
            {
                case VertexAttribute.Position:
                case VertexAttribute.Normal:
                case VertexAttribute.Tangent:
                case VertexAttribute.Color:
                case VertexAttribute.TexCoord0:
                case VertexAttribute.TexCoord1:
                case VertexAttribute.BlendWeight:
                case VertexAttribute.BlendIndices:
                    return true;
                default:
                    return false;
            }
        }

        private static int GetAttributeSize(VertexAttributeFormat format)
        {
            switch (format)
            {
                case VertexAttributeFormat.Float32: return 4;
                case VertexAttributeFormat.Float16: return 2;
                case VertexAttributeFormat.UNorm8: return 1;
                case VertexAttributeFormat.SNorm8: return 1;
                case VertexAttributeFormat.UNorm16: return 2;
                case VertexAttributeFormat.SNorm16: return 2;
                case VertexAttributeFormat.UInt8: return 1;
                case VertexAttributeFormat.SInt8: return 1;
                case VertexAttributeFormat.UInt16: return 2;
                case VertexAttributeFormat.SInt16: return 2;
                case VertexAttributeFormat.UInt32: return 4;
                case VertexAttributeFormat.SInt32: return 4;
                default: return 4;
            }
        }

        private static string GetMeshCompatibilityFixSuggestion(MeshCompatibilityInfo meshInfo)
        {
            if (meshInfo.vertexCount > 65535)
                return "重新导入模型时启用'Optimize Mesh'选项，或分割大型Mesh";
            
            if (meshInfo.vertexStride > 64)
                return "简化vertex attributes或使用更紧凑的数据格式";
            
            if (!string.IsNullOrEmpty(meshInfo.incompatibilityReason) && meshInfo.incompatibilityReason.Contains("attribute"))
                return "移除不必要的vertex attributes或使用标准的attribute类型";
            
            return "重新导入模型并启用移动端优化选项";
        }
    }
} 