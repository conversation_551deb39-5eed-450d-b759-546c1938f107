﻿using Game;
using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
public class ActorCleaner
{
    private static string[] checkDirs = { "Assets/Game/Actors" };
    public static string AlwaysAnimateLabel = "AlwaysAnimate";

    [MenuItem("自定义工具/资源检查/处理所有模型ui级别材质球虚拟方向光参数")]
    public static void FixAllModelUIMaterialVirtualLightDir()
    {
        var GUIDs = AssetDatabase.FindAssets("t:material", checkDirs);
        int totalCount = GUIDs.Length;
        int curCount = 0;
        foreach (string guid in GUIDs)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (string.IsNullOrEmpty(assetPath) || !assetPath.EndsWith("_ui.mat"))
            {
                continue;
            }

            var material = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
            if (material == null || material.shader == null || material.shader.name != "YifStandard/YifStandardActor")
            {
                continue;
            }

            material.EnableKeyword("ENABLE_VIRTUAL_LIGHT");
            material.EnableKeyword("ENABLE_VIRTUAL_LIGHT_DIR");
            material.SetVector("_VirtualLightDir", new Vector4(-20, 20, -25, 0));

            curCount = curCount + 1;
            EditorUtility.DisplayProgressBar("处理材质球虚拟方向光参数", string.Format("当前进度:{0}/{1}", curCount, totalCount), (float)curCount / (float)totalCount);
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();

        EditorUtility.ClearProgressBar();
    }

    [MenuItem("GameObject/策划专用/优化角色", priority = 12)]
    public static void OptimieSelectActorInHierarchy()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        OptimizeActor(Selection.activeGameObject, string.Empty);
    }

    [MenuItem("GameObject/策划专用/复制位置 ctrl shift z  %#z", priority = 13)]
    public static void CopyPositionSelectActorInHierarchy()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        CopyTransGoByType(Selection.activeGameObject,1, string.Empty);
    }

    [MenuItem("GameObject/策划专用/复制方向 ctrl shift x  %#x", priority = 14)]
    public static void CopyRotationSelectActorInHierarchy()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        CopyTransGoByType(Selection.activeGameObject, 2, string.Empty);
    }

    [MenuItem("GameObject/策划专用/复制缩放 ctrl shift c  %#c", priority = 15)]
    public static void CopyScaleSelectActorInHierarchy()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        CopyTransGoByType(Selection.activeGameObject, 3, string.Empty);
    }

    // type  1 复制position   2 复制 rotation  3 复制 scale
    private static void CopyTransGoByType(GameObject gameobj,int type, string path)
    {
        Transform trans = gameobj.GetComponent<Transform>();

        if (null == trans)
        {
            Debug.LogError("选择对象中没有 transform");
            return;
        }

        string context = trans.localPosition.x + "," + trans.localPosition.y + "," + trans.localPosition.z;

        if (type == 2)
        {
            context = trans.localRotation.x + "," + trans.localRotation.y + "," + trans.localRotation.z;
        }

        if(type == 3)
        {
            context = trans.localScale.x + "," + trans.localScale.y + "," + trans.localScale.z;
        }

        GUIUtility.systemCopyBuffer = context;

    }

    [MenuItem("Assets/策划专用/优化角色", priority = 12)]
    public static void OptimieSelectActorInProject()
    {
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        ClearActor();
                    }
                    else
                    {
                        OptimizeActor(Selection.gameObjects[i], string.Empty);
                    }
                    
                    EditorUtility.DisplayProgressBar("正在处理模型材质球..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    ClearActor();
                }
                else
                {
                    OptimizeActor(Selection.activeGameObject, string.Empty);
                }
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }
    }

    public static void ClearActor()
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == gameobj)
            {
                continue;
            }

            OptimizeActor(gameobj, path);
            cutIndex++;

            EditorUtility.DisplayProgressBar("正在处理模型材质球..."
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    private static void OptimizeActor(GameObject gameobj, string path)
    {
        path = string.IsNullOrEmpty(path) ? AssetDatabase.GetAssetPath(Selection.instanceIDs[0]) : path;
        GameObject instantiateObj = GameObject.Instantiate(gameobj);

        bool isPrefabDirty = false;
        DestroyUnuseComponent(path, instantiateObj, ref isPrefabDirty);
        ClearInvalidRenderMaterials(instantiateObj, ref isPrefabDirty);
        FixActorRender(path, instantiateObj, ref isPrefabDirty);
        FixAnimator(instantiateObj, ref isPrefabDirty);
        // FixMissingRefrence(path, gameobj, ref isPrefabDirty);
        // 是启开启GpuInstancing.注意，只有材质球仅当被MeshRender使用时才可以开启。否则将渲染不出
        HashSet<Material> enableMaterialGPUInstancing = new HashSet<Material>();
        OptimizeGpuInstancing(instantiateObj, enableMaterialGPUInstancing, ref isPrefabDirty);
        EnableMaterialGpuInstancing(enableMaterialGPUInstancing);

        if (isPrefabDirty)
        {
            PrefabUtility.SaveAsPrefabAssetAndConnect(instantiateObj, path, InteractionMode.AutomatedAction);
            PrefabUtility.SetPropertyModifications(instantiateObj, new PropertyModification[] { });
        }
        if (null != instantiateObj)
        {
            GameObject.DestroyImmediate(instantiateObj);
            instantiateObj = null;
        }
    }


    private static void DelUnActiveChild(GameObject gameObject)
    {
        if (gameObject.GetComponent<ActorRender>())
        {
            return;
        }

        Transform[] transforms = gameObject.GetComponentsInChildren<Transform>(true);
        for (int i = 0; i < transforms.Length; i++)
        {
            if (!transforms[i].gameObject.activeSelf)
            {
                GameObject.DestroyImmediate(transforms[i].gameObject, true);
            }
        }
    }

    // 移除无用的组件
    private static void DestroyUnuseComponent(string path, GameObject gameobj, ref bool isPrefabDirty)
    {
        // 不允许角色上直接使用ParticleSystem，用gameobjectAttach
        if (IsActorGameObj(gameobj))
        {
            ParticleSystem[] particleSystems = gameobj.GetComponentsInChildren<ParticleSystem>(true);
            for (int j = 0; j < particleSystems.Length; j++)
            {
                if (null != particleSystems[j] && particleSystems[j].gameObject)
                {
                    GameObject.DestroyImmediate(particleSystems[j].gameObject, true);
                    isPrefabDirty = true;
                }
            }

            TrailRenderer[] trialRenders = gameobj.GetComponentsInChildren<TrailRenderer>(true);
            for (int j = 0; j < trialRenders.Length; j++)
            {
                if (null != trialRenders[j] && trialRenders[j].gameObject)
                {
                    GameObject.DestroyImmediate(trialRenders[j].gameObject, true);
                    isPrefabDirty = true;
                }
            }
        }

        NirvanaRenderer[] components = gameobj.GetComponentsInChildren<NirvanaRenderer>(true);
        for (int j = 0; j < components.Length; j++)
        {
            GameObject.DestroyImmediate(components[j], true);
            isPrefabDirty = true;
        }

        Camera[] cameras = gameobj.GetComponentsInChildren<Camera>(true);
        for (int j = 0; j < cameras.Length; j++)
        {
            GameObject.DestroyImmediate(cameras[j].gameObject, true);
            isPrefabDirty = true;
        }

        SimpleShadow[] simpleShadows = gameobj.GetComponentsInChildren<SimpleShadow>(true);
        for (int j = 0; j < simpleShadows.Length; j++)
        {
            GameObject.DestroyImmediate(simpleShadows[j], true);
            isPrefabDirty = true;
        }

        Rigidbody[] rigidbodies = gameobj.GetComponentsInChildren<Rigidbody>(true);
        for (int i = 0; i < rigidbodies.Length; i++)
        {
            GameObject.DestroyImmediate(rigidbodies[i], true);
            isPrefabDirty = true;
        }

        PrefabReferenceHolder[] holders = gameobj.GetComponentsInChildren<PrefabReferenceHolder>(true);
        for (int i = 0; i < holders.Length; i++)
        {
            GameObject.DestroyImmediate(holders[i], true);
            isPrefabDirty = true;
        }

        // 不是特效则删除品质控制
        if (null == gameobj.GetComponentInChildren<ParticleSystem>())
        {
            QualityControlActive[] qualityActives = gameobj.GetComponentsInChildren<QualityControlActive>(true);
            for (int i = 0; i < qualityActives.Length; i++)
            {
                GameObject.DestroyImmediate(qualityActives[i], true);
                isPrefabDirty = true;
            }
        }

        ActorAttachEffect[] attachEffects = gameobj.GetComponentsInChildren<ActorAttachEffect>(true);
        for (int i = 0; i < attachEffects.Length; i++)
        {
            GameObject.DestroyImmediate(attachEffects[i], true);
            isPrefabDirty = true;
        }

        ActorController[] ctrls = gameobj.GetComponentsInChildren<ActorController>(true);
        for (int i = 0; i < ctrls.Length; i++)
        {
            GameObject.DestroyImmediate(ctrls[i], true);
            isPrefabDirty = true;
        }

        ActorTriggers[] actortriggers = gameobj.GetComponentsInChildren<ActorTriggers>(true);
        for (int i = 0; i < actortriggers.Length; i++)
        {
            GameObject.DestroyImmediate(actortriggers[i], true);
            isPrefabDirty = true;
        }

        LimitSceneEffects[] limitSceneEffects = gameobj.GetComponentsInChildren<LimitSceneEffects>(true);
        for (int i = 0; i < limitSceneEffects.Length; i++)
        {
            GameObject.DestroyImmediate(limitSceneEffects[i], true);
            isPrefabDirty = true;
        }

        OcclusionObject[] occlusionObjects = gameobj.GetComponentsInChildren<OcclusionObject>(true);
        for (int i = 0; i < occlusionObjects.Length; i++)
        {
            GameObject.DestroyImmediate(occlusionObjects[i], true);
            isPrefabDirty = true;
        }

        MaterialSwitcher[] switchers = gameobj.GetComponentsInChildren<MaterialSwitcher>(true);
        for (int i = 0; i < switchers.Length; i++)
        {
            GameObject.DestroyImmediate(switchers[i], true);
            isPrefabDirty = true;
        }

        ActorBlinker[] blinkers = gameobj.GetComponentsInChildren<ActorBlinker>(true);
        for (int i = 0; i < blinkers.Length; i++)
        {
            GameObject.DestroyImmediate(blinkers[i], true);
            isPrefabDirty = true;
        }

        ActorFadeout[] actorFadeouts = gameobj.GetComponentsInChildren<ActorFadeout>(true);
        for (int i = 0; i < actorFadeouts.Length; i++)
        {
            GameObject.DestroyImmediate(actorFadeouts[i], true);
            isPrefabDirty = true;
        }

        GameObjectAttach[] attachs = gameobj.GetComponentsInChildren<GameObjectAttach>(true);
        for (int i = 0; i < attachs.Length; i++)
        {
            if (attachs[i].IsGameobjectMissing())
            {
                GameObject.DestroyImmediate(attachs[i], true);
            }
        }
    }

    // 清理render上无效的材质球
    private static void ClearInvalidRenderMaterials(GameObject gameobj, ref bool isPrefabDirty)
    {
        bool isError = false;
        Renderer[] renders = gameobj.GetComponentsInChildren<Renderer>();
        for (int i = 0; i < renders.Length; i++)
        {
            if (renders[i].GetComponent<ParticleSystem>())
            {
                continue;
            }

            isError = false;
            Material[] materials = renders[i].sharedMaterials;
            for (int m = 0; m < materials.Length; m++)
            {
                if (materials[m] == null)
                {
                    isError = true;
                    break;
                }
            }

            if (isError)
            {
                List<Material> matrialList = new List<Material>();
                for (int m = 0; m < materials.Length; m++)
                {
                    if (materials[m] != null) matrialList.Add(materials[m]);
                }

                renders[i].sharedMaterials = matrialList.ToArray();
                isPrefabDirty = true;
            }
        }
    }

    private static void FixActorRender(string path, GameObject gameobj, ref bool isPrefabDirty)
    {
        if (null == gameobj.GetComponentInChildren<ParticleSystem>() && null == gameobj.GetComponentInChildren<TrailRenderer>())
        {
            // ActorRender超过1个全删除了
            ActorRender[] actorRenders = gameobj.GetComponentsInChildren<ActorRender>(true);
            if (actorRenders.Length > 1)
            {
                for (int i = 0; i < actorRenders.Length; i++)
                {
                    GameObject.DestroyImmediate(actorRenders[i], true);
                }
            }

            ActorRender actorRender = gameobj.GetOrAddComponent<ActorRender>();
            actorRender.AutoFetch();
            isPrefabDirty = true;
        }
    }

    private static void FixAnimator(GameObject gameobj, ref bool isPrefabDirty)
    {
        bool isAlwaysAnimate = ImporterUtils.CheckLabel(gameobj, AlwaysAnimateLabel);
        bool isSkinMeshAnimation = null != gameobj.GetComponentInChildren<SkinnedMeshRenderer>();

        Animator[] animators = gameobj.GetComponentsInChildren<Animator>();
        for (int i = 0; i < animators.Length; i++)
        {
            if (isAlwaysAnimate)
            {
                animators[i].cullingMode = AnimatorCullingMode.AlwaysAnimate;
                isPrefabDirty = true;
                continue;
            }
            //if (isSkinMeshAnimation)
            //{
            //    animators[i].cullingMode = AnimatorCullingMode.CullUpdateTransforms;
            //    isPrefabDirty = true;
            //}
        }
    }

    private static void FixMissingRefrence(string path, GameObject gameobj, ref bool isPrefabDirty)
    {
        var isMissing = false;
        var transforms = gameobj.GetComponentsInChildren<Transform>();
        for (int i = 0; i < transforms.Length; i++)
        {
            var components = transforms[i].GetComponents<Component>();
            var serializedObject = new SerializedObject(transforms[i].gameObject);
            var prop = serializedObject.FindProperty("m_Component");

            int index = 0;
            bool isDirty = false;
            for (var j = 0; j < components.Length; j++)
            {
                if (components[j] == null)
                {
                    prop.DeleteArrayElementAtIndex(index);
                    isDirty = true;
                }
                else
                {
                    index++;
                }
            }

            if (isDirty)
            {
                isMissing = true;
                serializedObject.ApplyModifiedProperties();
            }
        }

        if (isMissing && AssetDatabase.IsMainAsset(gameobj))
        {
            isPrefabDirty = false;
            PrefabUtility.ResetToPrefabState(gameobj);
            PrefabUtility.SetPropertyModifications(gameobj, new PropertyModification[] { });

            var newGameObj = GameObject.Instantiate<GameObject>(gameobj);
            PrefabUtility.ReplacePrefab(newGameObj, gameobj);
            GameObject.DestroyImmediate(newGameObj);
        }
    }

    private static void OptimizeGpuInstancing(GameObject gameobj, HashSet<Material> enableGPUInstancingMaterialSet, ref bool isPrefabDirty)
    {
        ActorRender actorRender = gameobj.GetComponent<ActorRender>();
        if (null == actorRender)
        {
            return;
        }

        List<ActorRender.RenderItem> itemList = actorRender.RenderList;
        for (int i = 0; i < itemList.Count; i++)
        {
            var item = itemList[i];
            bool isEnableGPUInstancing = item.renderer is MeshRenderer;
            if (isEnableGPUInstancing)
            {
                enableGPUInstancingMaterialSet.Add(item.renderer.sharedMaterial);
            }
            else
            {
                enableGPUInstancingMaterialSet.Remove(item.renderer.sharedMaterial);
                item.renderer.sharedMaterial.enableInstancing = false;
                item.renderer.sharedMaterial.DisableKeyword("INSTANCING_ON");
            }
        }
    }

    private static void EnableMaterialGpuInstancing(HashSet<Material> enableGPUInstancingMaterialSet)
    {
        foreach (var item in enableGPUInstancingMaterialSet)
        {
            item.enableInstancing = true;
            item.EnableKeyword("INSTANCING_ON");
        }
    }

    private static bool IsActorGameObj(GameObject gameobj)
    {
        if (null == gameobj.GetComponent<ActorAttachment>()
          && null == gameobj.GetComponent<AttachObject>()
           && null == gameobj.GetComponent<AttachSkinObject>()
           && null == gameobj.GetComponent<AnimatorOptimizer>()
              && null == gameobj.GetComponent<Animator>())
        {
            return false;
        }

        if (null == gameobj.GetComponentInChildren<SkinnedMeshRenderer>(true) && null == gameobj.GetComponentInChildren<MeshFilter>(true))
        {
            return false;
        }

        return true;
    }



    static MyComponentList pri_my_list = new MyComponentList();

    public class MyComponentList
    {
        public MyComponentList()
        {
        }

        public List<Component> gameObjList;
        public List<MyComponentList> nextList;
    }
    [MenuItem("GameObject/Copy All Components", priority = 1)]
    private static void CopyAllComponents()
    {
        if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        GetAllChilds(Selection.activeGameObject, pri_my_list);
    }

    [MenuItem("GameObject/Paste All Components", priority = 1)]
    private static void PasteAllComponents()
    {
       if (null == Selection.activeGameObject)
        {
            Debug.LogError("请选择对象");
            return;
        }

        GameObject[] tmpGameObj = Selection.gameObjects;
        foreach (var item in tmpGameObj)
        {
            PasteChildComponent(item, pri_my_list);
        }
    }

    private static void GetAllChilds(GameObject transformForSearch, MyComponentList next)
    {
        List<Component> childsOfGameobject = new List<Component>();
        next.gameObjList = childsOfGameobject;
        next.nextList = new List<MyComponentList>();

        foreach (var item in transformForSearch.GetComponents<Component>())
        {
            childsOfGameobject.Add(item);
        }
        foreach (Transform item in transformForSearch.transform)
        {
            MyComponentList tmpnext = new MyComponentList();
            GetAllChilds(item.gameObject, tmpnext);
            next.nextList.Add(tmpnext);
        }
        return;
    }


    private static void PasteChildComponent(GameObject gameObj, MyComponentList next)
    {
        if (next.gameObjList != null)
        {
            foreach (var copiedComponent in next.gameObjList)
            {
                gameObj.SetActive(copiedComponent.gameObject.activeSelf);
                if (!copiedComponent) continue;

                if (ContinueComponent(copiedComponent.GetType())) continue;

                UnityEditorInternal.ComponentUtility.CopyComponent(copiedComponent);

                if (gameObj.GetComponent(copiedComponent.GetType()) == null)
                {
                    UnityEditorInternal.ComponentUtility.PasteComponentAsNew(gameObj);
                }
                else if (!SpecialComponent(copiedComponent.GetType(), gameObj.GetComponent(copiedComponent.GetType())))
                {
                    UnityEditorInternal.ComponentUtility.PasteComponentValues(gameObj.GetComponent(copiedComponent.GetType()));
                }
            }
        }

        if (next.nextList != null)
        {
            List<Transform> TmpListTrans = new List<Transform>();
            foreach (Transform item in gameObj.transform)
            {
                TmpListTrans.Add(item);
            }
            int i = 0;
            foreach (var item in next.nextList)
            {
                if (i < TmpListTrans.Count)
                {
                    PasteChildComponent(TmpListTrans[i].gameObject, item);
                }
                i++;
            }
        }
    }
    private static bool ContinueComponent(System.Type type)
    {

        if (type == typeof(UINameTable) || type == typeof(UIEventTable) || type == typeof(UIVariable) || type == typeof(ScrollRect) || type == typeof(Button) || type == typeof(Toggle) || type == typeof(ToggleActivator))
        {
            return true;
        }
        return false;
    }
    private static bool SpecialComponent(System.Type type, Component component)
    {
        if (type == typeof(Text))
        {
            Text text = (Text)component;
            string s = text.text;
            UnityEditorInternal.ComponentUtility.PasteComponentValues(component);
            text.text = s;
            return true;
        }

        return false;
    }

}
