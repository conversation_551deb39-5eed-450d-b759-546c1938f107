﻿using UnityEditor;
using UnityEngine;

/// <summary>
/// 简化的技能编辑器事件绘制器
/// 只负责UI绘制逻辑，数据管理由配置类和生成器负责
/// </summary>
[CustomPropertyDrawer(typeof(ActorEventAttribute))]
public class SkillEditorEventDrawer : PropertyDrawer
{

    #region 静态属性访问器
    /// <summary>
    /// 模型类型枚举（从配置类获取）
    /// </summary>
    public static System.Type ModelType => typeof(SkillEditorEventConfig.ModelType);

    /// <summary>
    /// 模型类型显示名称数组
    /// </summary>
    public static string[] ModelTypeNames => SkillEditorEventConfig.ModelTypeNames;

    /// <summary>
    /// 动画数据缓存
    /// </summary>
    public static System.Collections.Generic.Dictionary<int, System.Collections.Generic.List<string>> animDataCache
        => SkillEditorEventGenerator.AnimDataCache;

    /// <summary>
    /// 事件名称数组
    /// </summary>
    public static string[] eventNames => SkillEditorEventGenerator.EventNames;

    /// <summary>
    /// 弹道事件名称数组
    /// </summary>
    public static string[] projectileEventNames => SkillEditorEventGenerator.ProjectileEventNames;

    /// <summary>
    /// 模型资源路径映射
    /// </summary>
    public static System.Collections.Generic.Dictionary<int, string> modelResPathMap
        => SkillEditorEventConfig.ModelResPathMap;

    /// <summary>
    /// 保存文件夹名称映射
    /// </summary>
    public static System.Collections.Generic.Dictionary<int, string> saveFolderNameMap
        => SkillEditorEventConfig.SaveFolderNameMap;

    /// <summary>
    /// 角色资源ID数组
    /// </summary>
    public static string[] roleResIds => SkillEditorEventConfig.RoleResIds;

    /// <summary>
    /// 战斗坐骑资源ID数组
    /// </summary>
    public static string[] fightMountResIds => SkillEditorEventConfig.FightMountResIds;
    #endregion

    #region UI绘制逻辑
    /// <summary>
    /// 绘制属性字段
    /// </summary>
    public override void OnGUI(Rect rect, SerializedProperty property, GUIContent label)
    {
        try
        {
            DrawPropertyField(rect, property, label);
        }
        catch (System.Exception ex)
        {
            EditorGUI.HelpBox(rect, $"绘制错误: {ex.Message}", MessageType.Error);
            UnityEngine.Debug.LogError($"SkillEditorEventDrawer OnGUI 错误: {ex}");
        }
    }

    /// <summary>
    /// 绘制属性字段的具体实现
    /// </summary>
    private void DrawPropertyField(Rect rect, SerializedProperty property, GUIContent label)
    {
        // 获取关联的模型类型属性
        SerializedProperty modelTypeProp = property.serializedObject.FindProperty("modelType");
        if (modelTypeProp == null)
        {
            EditorGUI.HelpBox(rect, "需要 modelType 属性来显示动画列表", MessageType.Error);
            return;
        }

        int modelTypeValue = modelTypeProp.intValue;

        // 验证模型类型
        if (!IsValidModelType(modelTypeValue))
        {
            EditorGUI.HelpBox(rect, "无效的模型类型", MessageType.Error);
            return;
        }

        // 获取动画列表
        var animList = GetAnimationList(modelTypeValue);
        if (animList == null || animList.Count == 0)
        {
            EditorGUI.HelpBox(rect, "该模型类型没有可用的动画", MessageType.Warning);
            return;
        }

        // 绘制下拉选择框
        DrawAnimationPopup(rect, property, animList);
    }

    /// <summary>
    /// 验证模型类型是否有效
    /// </summary>
    private bool IsValidModelType(int modelTypeValue)
    {
        return System.Enum.IsDefined(typeof(SkillEditorEventConfig.ModelType), modelTypeValue);
    }

    /// <summary>
    /// 获取指定模型类型的动画列表
    /// </summary>
    private System.Collections.Generic.List<string> GetAnimationList(int modelTypeValue)
    {
        return animDataCache.TryGetValue(modelTypeValue, out var animList) ? animList : null;
    }

    /// <summary>
    /// 绘制动画选择下拉框
    /// </summary>
    private void DrawAnimationPopup(Rect rect, SerializedProperty property, System.Collections.Generic.List<string> animList)
    {
        string[] animArray = animList.ToArray();

        // 查找当前值的索引
        int currentIndex = System.Array.IndexOf(animArray, property.stringValue);
        if (currentIndex < 0) currentIndex = 0; // 如果找不到，默认选择第一个

        // 绘制下拉框
        EditorGUI.BeginChangeCheck();
        int newIndex = EditorGUI.Popup(rect, property.displayName, currentIndex, animArray);

        if (EditorGUI.EndChangeCheck() && newIndex >= 0 && newIndex < animArray.Length)
        {
            property.stringValue = animArray[newIndex];
        }
    }
    #endregion


}
