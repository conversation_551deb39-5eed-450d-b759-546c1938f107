﻿using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana.Editor;
using System.Collections.Generic;
using Newtonsoft.Json;
using NewtonsoftJsonEx;

namespace Build
{
    public class ManifestBundleInfo
    {
        public List<string> deps = new List<string>();
        public string hash;
        public long size;
        public string crc;
    }

    // 导出manifest文件到lua文件用
    public class ManifestInfo
    {
        public Dictionary<string, ManifestBundleInfo> bundleInfos = new Dictionary<string, ManifestBundleInfo>();
        public string manifestHashCode;

        public string[] allBundles = null;
        public string projectName = string.Empty;
        public string[] GetAllAssetBundles()
        {
            if (null == allBundles)
            {
                allBundles = new string[bundleInfos.Count];
                int index = 0;
                foreach (var kv in bundleInfos)
                {
                    allBundles[index++] = kv.Key;
                }
            }
            return allBundles;

        }

        public string GetAssetBundleHash(string bundleName)
        {
            ManifestBundleInfo info;
            if (bundleInfos.TryGetValue(bundleName, out info))
            {
                return info.hash;
            }

            return string.Empty;
        }

        public long GetAssetBundleSize(string bundleName)
        {
            ManifestBundleInfo info;
            if (bundleInfos.TryGetValue(bundleName, out info))
            {
                return info.size;
            }

            return 0;
        }

        public List<string> GetAllDependencies(string bundleName)
        {
            ManifestBundleInfo info;
            if (bundleInfos.TryGetValue(bundleName, out info))
            {
                return info.deps;
            }

            return new List<string>();
        }

        public bool IsExistsAssetBundle(string bundleName)
        {
            return bundleInfos.ContainsKey(bundleName);
        }
    }

    public enum BuildPlatType
    {
        AndroidDev,
        IOSDev,
        WindowsDev,
        AuditDev,
        Android,
        IOS,
        Windows,
        Audit,
        AndroidGM,
    }

    public enum InstallBundleSize
    {
        sizeL = 0,
        sizeM,
        sizeS,
        sizeXS,
        sizeAll,
    }

    public class BuilderConfig : ScriptableObject
    {
        public static readonly string AssetBundlePath = "AssetBundle";
        public static readonly string LuaAssetBundlePath = "AssetBundle/LuaAssetBundle";

        // 获得AssetBundle的路径
        public static string GetAssetBundlePath(BuildPlatType buildPlatType, string pathSubfix = "AssetBundle")
        {
            if (BuildPlatType.AndroidDev == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundleDev/Android/" + pathSubfix);
            else if (BuildPlatType.IOSDev == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundleDev/iOS/" + pathSubfix);
            else if (BuildPlatType.WindowsDev == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundleDev/Windows/" + pathSubfix);
            else if (BuildPlatType.Android == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundle/Android/" + pathSubfix);
            else if (BuildPlatType.IOS == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundle/iOS/" + pathSubfix);
            else if (BuildPlatType.Windows == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundle/Windows/" + pathSubfix);
            else if (BuildPlatType.Audit == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundleDev/Audit/" + pathSubfix);
            else if (BuildPlatType.AndroidGM == buildPlatType)
                return Path.Combine(Application.dataPath, "../../AssetBundle/Android/" + pathSubfix);

            return string.Empty;
        }

        public static string GetOutputPlayerPath(BuildPlatType buildPlatType)
        {
            if (BuildPlatType.AndroidDev == buildPlatType || BuildPlatType.Android == buildPlatType)
            {
                return "Build/android/game.apk";
            }

            if (BuildPlatType.WindowsDev == buildPlatType || BuildPlatType.Windows == buildPlatType)
            {
                return "Build/windows/Game.exe";
            }

            return string.Empty;
        }

        public static string GetOutputProjectPath(BuildPlatType buildPlatType)
        {
            if (BuildPlatType.Android == buildPlatType)
            {
                return "../sdk/android/base";
            }

            else if (BuildPlatType.AndroidDev == buildPlatType)
            {
                return "../sdk/android/base_dev";
            }

            else if (BuildPlatType.AndroidGM == buildPlatType)
            {
                return "../sdk/android/base_gm";
            }

            else if (BuildPlatType.IOS == buildPlatType)
            {
                return "../sdk/ios/base";
            }
            else if (BuildPlatType.IOSDev == buildPlatType)
            {
                return "../sdk/ios/base_dev";
            }
#if UNITY_IOS
            else if (BuildPlatType.Audit == buildPlatType)
            {
                return "../sdk/ios/base_audit";
            }
#endif

            return string.Empty;
        }

        public static BuildSettingNew GetBuildSetting(BuildPlatType buildPlatType)
        {
            string name = string.Empty;
            if (BuildPlatType.AndroidDev == buildPlatType) name = "Android_Dev";
            if (BuildPlatType.WindowsDev == buildPlatType) name = "Windows_Dev";
            if (BuildPlatType.IOSDev == buildPlatType) name = "iOS_Dev";
            if (BuildPlatType.Android == buildPlatType || BuildPlatType.AndroidGM == buildPlatType) name = "Android";
            if (BuildPlatType.Windows == buildPlatType) name = "Windows";
            if (BuildPlatType.IOS == buildPlatType || BuildPlatType.Audit == buildPlatType) name = "iOS";

            return AssetDatabase.LoadAssetAtPath<BuildSettingNew>(string.Format("Assets/Game/Deploy/Dev/{0}.asset", name));
        }

        public static BuildDevice GetBuildDevice(BuildPlatType buildPlatType)
        {
            if (BuildPlatType.AndroidDev == buildPlatType || BuildPlatType.Android == buildPlatType || BuildPlatType.AndroidGM == buildPlatType)
                return BuildDevice.Android;
            if (BuildPlatType.IOSDev == buildPlatType || BuildPlatType.IOS == buildPlatType || BuildPlatType.Audit == buildPlatType)
                return BuildDevice.iOS;
            if (BuildPlatType.WindowsDev == buildPlatType || BuildPlatType.Windows == buildPlatType)
                return BuildDevice.Desktop;

            return BuildDevice.Desktop;
        }

        private static ManifestInfo manifestInfo = null;
        public static void ClearManifestInfo()
        {
            manifestInfo = null;
        }

        public static ManifestInfo GetManifestInfo(BuildPlatType buildPlatType)
        {
            if (null != manifestInfo) return manifestInfo;

            string path = BuilderConfig.GetBuildLogPath(string.Format("AssetBundle{0}.json", (int)buildPlatType));
            var jonsStr = File.ReadAllText(path);

            manifestInfo = JsonConvert.DeserializeObject<ManifestInfo>(jonsStr, JSONEditorHelper.RTJsonSerializerSettings);
            return manifestInfo;
        }

        private static ManifestInfo luaManifestInfo = null;
        public static void ClearLuaManifestInfo()
        {
            luaManifestInfo = null;
        }

        public static ManifestInfo GetLuaManifestInfo(BuildPlatType buildPlatType)
        {
            if (null != luaManifestInfo) return luaManifestInfo;

            string path = BuilderConfig.GetBuildLogPath(string.Format("LuaAssetBundle{0}.json", (int)buildPlatType));
            var jonsStr = File.ReadAllText(path);
            luaManifestInfo = JsonConvert.DeserializeObject<ManifestInfo>(jonsStr, JSONEditorHelper.RTJsonSerializerSettings);
            return luaManifestInfo;
        }

        public static string GetAssetBundleInstallListTxtName(InstallBundleSize sizeType)
        {
            if (InstallBundleSize.sizeL == sizeType) return "install_bundles_l";
            if (InstallBundleSize.sizeM == sizeType) return "install_bundles_m";
            if (InstallBundleSize.sizeS == sizeType) return "install_bundles_s";
            if (InstallBundleSize.sizeXS == sizeType) return "install_bundles_xs";
            if (InstallBundleSize.sizeAll == sizeType) return "install_bundles_all";

            return string.Empty;
        }

        public static string GetAssetBundleIntallFilterTxtName(InstallBundleSize sizeType)
        {
            if (InstallBundleSize.sizeL == sizeType) return "InstallBundles_L";
            if (InstallBundleSize.sizeM == sizeType) return "InstallBundles_M";
            if (InstallBundleSize.sizeS == sizeType) return "InstallBundles_S";
            if (InstallBundleSize.sizeXS == sizeType) return "InstallBundles_XS";

            return string.Empty;
        }

        public static string GetBuildLogPath(string logFile)
        {
            return string.Format("{0}/../Build/log/{1}", Application.dataPath, logFile);
        }
    }

    public struct AssetBundleManifestConfig
    {
        public static AssetBundleManifestConfig Empty;
        private AssetBundleManifest manifest;
        private string bundlePath;
        private Dictionary<string, uint> bundleHashSet;

        public AssetBundleManifestConfig(AssetBundleManifest manifest, string bundlePath)
        {
            this.manifest = manifest;
            this.bundlePath = bundlePath;
            bundleHashSet = new Dictionary<string, uint>();
        }

        public bool IsEmpty()
        {
            return (null == manifest);
        }

        public string[] GetAllAssetBundles()
        {
            return manifest.GetAllAssetBundles();
        }

        public string[] GetAllDependencies(string bundleName)
        {
            return manifest.GetAllDependencies(bundleName);
        }

        public string[] GetDirectDependencies(string bundleName)
        {
            return manifest.GetDirectDependencies(bundleName);
        }

        public uint GetAssetBundleHash(string bundleName)
        {
            uint hash;
            if (!bundleHashSet.TryGetValue(bundleName, out hash))
            {
                string filePath = Path.Combine(bundlePath, bundleName);
                if (File.Exists(filePath))
                {
                    hash = MD5.GetMD5FromFile(filePath);
                    bundleHashSet.Add(bundleName, hash);
                }
                else
                {
                    hash = 0;
                    Debug.LogError("GetAssetBundleHash Fail! File is not Exists! " + filePath);
                }
            }

            return hash;
        }
    }
}