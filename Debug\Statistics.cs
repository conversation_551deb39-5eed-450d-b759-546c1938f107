﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

namespace Nirvana
{
    using UnityEngine;
    using LuaInterface;

    /// <summary>
    /// The description of Statics.
    /// </summary>
    public class Statistics : MonoBehaviour
    {
        private LuaState luaState;
        public LuaState LuaState
        {
            get { return luaState; }
            set
            {
                luaState = value;
            }
        }

        static private float itemHeight = 30.0f;
        static private float itemWidth = 120.0f;

        private float timeCount = 0;
        private int frameCount = 0;
        private int fps = 0;

        private void Start()
        {
            
        }

        private void Update()
        {
            timeCount += Time.deltaTime;
            frameCount += 1;
        }

        private void OnGUI()
        {
            if (LuaState == null)
            {
                return;
            }

            float count = Mathf.FloorToInt(LuaState.LuaGC(LuaGCOptions.LUA_GCCOUNT, 0) * 100 / 1024.0f) / 100.0f;

            float factor = Screen.height / 768.0f;

            float singleItemHeight = itemHeight * factor;
            float singleItemWidth = itemWidth * factor;
            float curHeight = singleItemHeight;

            GUI.Label(new Rect(0, Screen.height - curHeight, singleItemWidth, singleItemHeight), string.Format("lua: {0}MB", count));
            if (GUI.Button(new Rect(singleItemWidth, Screen.height - curHeight, singleItemWidth, singleItemHeight), "Lua GC"))
            {
                LuaState.LuaGC(LuaGCOptions.LUA_GCCOLLECT);
            }
            curHeight += singleItemHeight;

            if (frameCount >= 10)
            {
                fps = Mathf.FloorToInt(1 / (timeCount / frameCount));
                frameCount = 0;
                timeCount = 0;
            }

            GUI.Label(new Rect(0, Screen.height - curHeight, singleItemWidth, singleItemHeight), "fps: " + fps);
            curHeight += singleItemHeight;
        }
    }
}
