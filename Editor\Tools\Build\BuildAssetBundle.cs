﻿using UnityEngine;
using UnityEditor;
using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Nirvana;
using Newtonsoft.Json;
using NewtonsoftJsonEx;

using UnityObject = UnityEngine.Object;
using DictHashSet = System.Collections.Generic.Dictionary<string, System.Collections.Generic.HashSet<string>>;
using System.Text;

namespace Build
{
    // 生成AssetBundle
    public static class BuildAssetBundle
    {
       // 记录assetbundle和assetbundle关联的asset信息
        class BuildInfo
        {
            public BuildInfo(string bundleName)
            {
                AssetBundleName = bundleName;
                AssetHashSet = new HashSet<string>();
            }

            public BuildInfo(string bundleName, string asset)
            {
                AssetBundleName = bundleName;
                AssetHashSet = new HashSet<string>();
                AssetHashSet.Add(asset);
            }

            public string AssetBundleName;
            public HashSet<string> AssetHashSet;
        }

        // 记录asset放在了哪个assetbundle里面
        class BuildAssetInfo
        {
            public BuildAssetInfo(string assetName)
            {
                AssetName = assetName;
                parentAssetSet = new HashSet<string>();
                parentAbSet = new HashSet<string>();
            }

            public string AssetName;
            public HashSet<string> parentAssetSet;
            public HashSet<string> parentAbSet;
        }

        static Dictionary<string, BuildAssetInfo> BuildAssetInfoDict = new Dictionary<string, BuildAssetInfo>();
        static Dictionary<string, BuildInfo> BuildInfoDict = new Dictionary<string, BuildInfo>();
        static DictHashSet DependenceInfo = new DictHashSet();
        static Dictionary<string, string> AssetToBundleNameDict = new Dictionary<string, string>();
        static Dictionary<string, string> EnvironmentsTextureBundleNameDict = new Dictionary<string, string>();

        // 构建每个文件的引用关系,这样可知道每个文件被哪些文件引用
        public static void CalcBuildAssetInfo(Dictionary<string, string> dict)
        {
            foreach (var item in dict)
            {
                string assetPath = item.Key;
                string bundleName = item.Value;

                string[] dependencies = BuildAssetsMgr.GetDependencies(assetPath);
                foreach (var dep in dependencies)
                {
                    if (string.IsNullOrEmpty(dep))
                    {
                        DeployTool.ReportBuildLog(string.Format("[Error] dep is nil, {0}", assetPath));
                    }

                    BuildAssetInfo buildAssetInfo;
                    if (!BuildAssetInfoDict.TryGetValue(dep, out buildAssetInfo))
                    {
                        buildAssetInfo = new BuildAssetInfo(dep);
                        BuildAssetInfoDict.Add(dep, buildAssetInfo);
                    }

                    if (!buildAssetInfo.parentAbSet.Contains(bundleName)
                        && !assetPath.EndsWith(".unity"))
                    {
                        buildAssetInfo.parentAbSet.Add(bundleName);
                    }

                    buildAssetInfo.parentAssetSet.Add(assetPath);
                }
            }
        }

        // 获得资源引用者的数量
        private static int GetAssetReferCount(string asset)
        {
            BuildAssetInfo buildAssetInfo;
            if (BuildAssetInfoDict.TryGetValue(asset, out buildAssetInfo))
            {
                return buildAssetInfo.parentAssetSet.Count;
            }

            return 0;
        }

        public static void GetAssetBuildInfo(string asset, string referBundleName)
        {
            string[] dependencies = BuildAssetsMgr.GetDependencies(asset);

            foreach (var depPath in dependencies)
            {
                int referCount = GetAssetReferCount(depPath);
                string bundleName;
                AssetToBundleNameDict.TryGetValue(depPath, out bundleName);

                //if (depPath.EndsWith(".cs") || depPath.EndsWith(".dll"))
                //{
                //    continue;
                //}

                bool flag = false;
                if (!flag && TryGetDepSpecialBundleName(depPath, ref bundleName)) flag = true;
                if (!flag && TryGetDepFbxBundleName(depPath, ref bundleName)) flag = true;
                if (!flag && TryGetDepTextureBundleName(depPath, ref bundleName)) flag = true;
                if (!flag && TryGetDeOtherBundleName(depPath, ref bundleName)) flag = true;
                
                if (!string.IsNullOrEmpty(bundleName))
                {
                    AddAssetToBuild(depPath, bundleName);
                }
            }
        }

        public static string GetAssetBundleName(string assetName)
        {
            string bundleName = string.Empty;
            // 以文件夹路径为ab
            var relativePath = assetName.Substring(AssetBundleMarkRule.BaseDir.Length + 1);
            bundleName = relativePath.Substring(0, relativePath.Length - Path.GetExtension(relativePath).Length);

            // 如果只有一个引用者则跟引用者打进同个包
            BuildAssetInfo buildAssetInfo;
            if (BuildAssetInfoDict.TryGetValue(assetName, out buildAssetInfo)
                && buildAssetInfo.parentAbSet.Count == 1)
            {
                bundleName = buildAssetInfo.parentAbSet.ToArray<string>()[0];
            }
            
            bundleName = FixAssetBundleName(bundleName);

            if (bundleName.StartsWith("environments/"))     // 将environments\f1_bs_yunyata\grounds\textures\简化成environments\f1_bs_yunyata
            {
                bundleName = GetL2RDirPath(bundleName, 2) + "_env";
            }
            else if(bundleName.StartsWith("actors/"))  // 将actors\role\1201\1201shared\chujijian_nan@rest简化成actors\role\1201\1201shared
            {
                bundleName = GetR2LDirPath(bundleName, 1) + "_ab";
            }

            return bundleName;
        }

        // 特殊文件类型
        private static bool TryGetDepSpecialBundleName(string depPath, ref string bundleName)
        {
            if (depPath.EndsWith(".shader"))
            {
                if(!depPath.Contains(ShaderLabOptimize.prefix))
                {
                    bundleName = "shaders";
                }

                return true;
            }

            if (depPath.StartsWith("Assets/Spine"))
            {
                bundleName = "spine_bundle";
                return true;
            }

            return false;
        }

        // 动画类型, 模型类型, bunleName不指定名字时, 即跟引用者打进同块内存（引擎会只有一个io)
        private static bool TryGetDepFbxBundleName(string depPath, ref string bundleName)
        {
            if (string.IsNullOrEmpty(bundleName) &&
                (depPath.EndsWith(".anim") || depPath.EndsWith(".FBX")))
            {
                int referCount = GetAssetReferCount(depPath);
                
                long fileSize = BuildAssetsMgr.GetAssetFileSize(depPath);
                // 角色的sharedfbx因为被引用太多打进公共包却没有意义
                bool flag = depPath.IndexOf("/Actors/") >= 0 && depPath.IndexOf("Shared/") >= 0;

                if (fileSize > 1 * 1024 * 1024)
                {
                    if (referCount > 20) bundleName = flag ? GetAssetBundleName(depPath) : "fbx_bundle_l";
                    else if (referCount > 3) bundleName = GetAssetBundleName(depPath);
                    else bundleName = string.Empty;
                }
                else if (fileSize > 500 * 1024)
                {
                    if (referCount > 10) bundleName = flag ? GetAssetBundleName(depPath) : "fbx_bundle_m";
                    else if (referCount > 4) bundleName = GetAssetBundleName(depPath);
                    else bundleName = string.Empty;
                }
                else if (fileSize > 100 * 1024)
                {
                    if (referCount > 5) bundleName = flag ? GetAssetBundleName(depPath) : "fbx_bundle_s";
                    else bundleName = string.Empty;
                }
                else
                {
                    if (referCount > 30) bundleName = flag ? GetAssetBundleName(depPath) : "fbx_bundle_s";
                    else bundleName = string.Empty;
                }

                return !string.IsNullOrEmpty(bundleName);
            }

            return false;
        }

        // 依赖的纹理, bunleName不指定名字时, 即跟引用者打进同块内存（引擎会只有一个io)
        private static bool TryGetDepTextureBundleName(string depPath, ref string bundleName)
        {
            if (string.IsNullOrEmpty(bundleName) &&
                (depPath.EndsWith(".tga") || depPath.EndsWith(".png")) && !depPath.StartsWith("Packages/"))
            {
                int referCount = GetAssetReferCount(depPath);
                int width = 0, height = 0;
                if (!BuildAssetsMgr.GetTextureSize(depPath, ref width, ref height))
                {
                    Debug.LogErrorFormat("TryGetDepTextureBundleName texture is nil, {0}", depPath);
                    return false;
                }

                // Environments目录下的Texture使用引用关系计算出来的AB名，目的是减小AB大小，尽量把相关联的Texture打进一块
                if (depPath.StartsWith(AssetBundleMarkRule.EnvironmentsDir))
                {
                    string textureBundleName;
                    if (EnvironmentsTextureBundleNameDict.TryGetValue(depPath, out textureBundleName))
                    {
                        if (!string.IsNullOrEmpty(textureBundleName))
                        {
                            bundleName = textureBundleName;
                            return true;
                        }
                    }
                }

                // 假设512 * 512尺寸的文件大小为1M
                if (width * height > 1024 * 1024)     // 长宽1024
                {
                    if (referCount > 0) bundleName = GetAssetBundleName(depPath);
                    else bundleName = string.Empty;
                }
                else if (width * height >= 512 * 512)   // 长宽512
                {
                    if (referCount > 20) bundleName = "texture_bundle_l";
                    else if (referCount > 3) bundleName = GetAssetBundleName(depPath);
                    else bundleName = string.Empty;
                }
                else if (width * height >= 256 * 256)  // 长宽256
                {
                    if (referCount > 20) bundleName = "texture_bundle_m";
                    else if (referCount > 4) bundleName = GetAssetBundleName(depPath);
                    else bundleName = string.Empty;
                }
                else if (width * height >= 128 * 128) // 长宽128
                {
                    if (referCount > 5) bundleName = "texture_bundle_s";
                    else bundleName = string.Empty;
                }
                else
                {
                    if (referCount > 30) bundleName = "fbx_bundle_s";
                    else bundleName = string.Empty;
                }

                return !string.IsNullOrEmpty(bundleName);
            }

            return false;
        }

        // 依赖的其他杂资源, bunleName不指定名字时, 即跟引用者打进同块内存（引擎会只有一个io)
        private static bool TryGetDeOtherBundleName(string depPath, ref string bundleName)
        {
            if (string.IsNullOrEmpty(bundleName))
            {
                int referCount = GetAssetReferCount(depPath);
                if (referCount > 30)
                {
                    bundleName = "misc_bundle";
                }

                return !string.IsNullOrEmpty(bundleName);
            }

            return false;
        }

        public static void AddAssetToBuild(string asset, string bundleName)
        {
            BuildInfo buildInfo;
            if (!BuildInfoDict.TryGetValue(bundleName, out buildInfo))
            {
                buildInfo = new BuildInfo(bundleName, asset);
                BuildInfoDict.Add(bundleName, buildInfo);
            }
            else
            {
                buildInfo.AssetHashSet.Add(asset);
            }
        }

        public static bool Build(BuildPlatType buildPlatType)
        {
            return Build(BuilderConfig.GetAssetBundlePath(buildPlatType, BuilderConfig.AssetBundlePath), buildPlatType);
        }

        private static bool Build(string outPath, BuildPlatType buildPlatType)
        {
            BuildAssetInfoDict.Clear();
            BuildInfoDict.Clear();
            DependenceInfo.Clear();
            AssetToBundleNameDict.Clear();
            EnvironmentsTextureBundleNameDict.Clear();

            DeployTool.ReportBuildLog("Build AssetBundle, Start");
            Dictionary<string, string> prefabDict = new Dictionary<string, string>();
            Dictionary<string, string> sceneDict = new Dictionary<string, string>();
            Dictionary<string, string> uiTextureDict = new Dictionary<string, string>();
            Dictionary<string, string> miscDict = new Dictionary<string, string>();

            DeployTool.ReportBuildLog("Build AssetBundle, Search AssetBundle In Porject");
            List<AssetItem> list = BuildAssetsMgr.GetBundleAssetItem();
            for (int i = 0; i < list.Count; i++)
            {
                var asset = list[i].path;
                var bundleName = list[i].bundleName;
                if (!asset.StartsWith(AssetBundleMarkRule.BaseDir))
                {
                    continue;
                }

                if (asset.EndsWith(".prefab"))
                {
                    if (prefabDict.ContainsKey(asset))
                    {
                        Debug.LogError("asset重複: " + asset);
                        continue;
                    }
                    prefabDict.Add(asset, bundleName);
                }
                else if (asset.EndsWith(".unity"))
                {
                    sceneDict.Add(asset, bundleName);
                }
                else if (asset.StartsWith(AssetBundleMarkRule.UIDir))
                {
                    if (asset.EndsWith(".png") || asset.EndsWith(".jpg") || asset.EndsWith(".tga"))
                    {
                        uiTextureDict.Add(asset, bundleName);
                    }
                    else
                    {
                        miscDict.Add(asset, bundleName);
                    }
                }
                else if (!bundleName.StartsWith("luajit/") && !bundleName.StartsWith("lua/"))
                {
                    // 这里不打lua的ab包，放到其他地方打
                    miscDict.Add(asset, bundleName);
                }

                AssetToBundleNameDict.Add(asset, bundleName);
            }

            DeployTool.ReportBuildLog("Build AssetBundle, CalcBuildAssetInfo");
            CalcBuildAssetInfo(miscDict);
            CalcBuildAssetInfo(uiTextureDict);
            CalcBuildAssetInfo(prefabDict);
            CalcBuildAssetInfo(sceneDict);

            CalcEnvironmentTextureABName(sceneDict);

            DeployTool.ReportBuildLog("Build AssetBundle, GetAssetBuildInfo");
            foreach (var asset in miscDict.Keys)
            {
                var bundleName = miscDict[asset];
                GetAssetBuildInfo(asset, bundleName);
                AddAssetToBuild(asset, bundleName);
            }

            foreach (var uiTexture in uiTextureDict.Keys)
            {
                var bundleName = uiTextureDict[uiTexture];
                AddAssetToBuild(uiTexture, bundleName);
            }

            foreach (var prefab in prefabDict.Keys)
            {
                var bundleName = prefabDict[prefab];
                GetAssetBuildInfo(prefab, bundleName);
                AddAssetToBuild(prefab, bundleName);
            }

            foreach (var scene in sceneDict.Keys)
            {
                var bundleName = sceneDict[scene];
                GetAssetBuildInfo(scene, bundleName);
                AddAssetToBuild(scene, bundleName);
            }

            List<AssetBundleBuild> assetBundleBuildList = new List<AssetBundleBuild>();
            foreach (var key in BuildInfoDict.Keys)
            {
                var buildInfo = BuildInfoDict[key];
                AssetBundleBuild build = new AssetBundleBuild();
                build.assetBundleName = FixAssetBundleName(buildInfo.AssetBundleName);
                build.assetNames = buildInfo.AssetHashSet.ToArray();

                if (!String.IsNullOrEmpty(build.assetBundleName))
                {
                    assetBundleBuildList.Add(build);
                }
            }

            if (!Directory.Exists(outPath))
            {
                Directory.CreateDirectory(outPath);
            }

            bool check_flag = true;
            for (int i = 0; i < assetBundleBuildList.Count; i++)
            {
                string path = Path.Combine(outPath, assetBundleBuildList[i].assetBundleName);
                if (Directory.Exists(path))
                {
                    Debug.LogErrorFormat("Build AssetBundle fail, because exist same name folder {0}", path);
                    check_flag = false;
                }
            }

            if (!check_flag)
            {
                return false;
            }

            DeployTool.ReportBuildLog("Build AssetBundle, BuildPipeline.BuildAssetBundles");
            AssetBundleManifest mainfest = BuildPipeline.BuildAssetBundles(outPath, assetBundleBuildList.ToArray(), BuildAssetBundleOptions.ChunkBasedCompression, EditorUserBuildSettings.activeBuildTarget);
            if (mainfest)
            {
                DeployTool.ReportBuildLog("Build AssetBundle, ExportManifestToLua");
                ExportManifestToLua(buildPlatType, mainfest, Path.Combine(outPath, "AssetBundle.lua"));
                // 压缩AssetBundle文件成zip格式的压缩包
                ZipUtils.ZipFile(Path.Combine(outPath, "AssetBundle.lua"), outPath);
                return true;
            }
            else
            {
                DeployTool.ReportBuildLog("[Fail] Build AssetBundle, BuildPipeline.BuildAssetBundles Fail!");
                return false;
            }
        }

        public static void BuildLua(BuildPlatType buildPlatType)
        {
            DeployTool.ReportBuildLog("Build AssetBundle, Search Lua AssetBundle In Porject");
            AssetDatabase.RemoveUnusedAssetBundleNames();

            Dictionary<string, BuildInfo> buildInfoDict = new Dictionary<string, BuildInfo>();

            string outPath = BuilderConfig.GetAssetBundlePath(buildPlatType, BuilderConfig.LuaAssetBundlePath);
            var bundleNames = AssetDatabase.GetAllAssetBundleNames();
            foreach (var bundleName in bundleNames)
            {
                if (!bundleName.StartsWith("lua/") && !bundleName.StartsWith("luajit/"))
                {
                    continue;
                }

                var assets = AssetDatabase.GetAssetPathsFromAssetBundle(bundleName);
                foreach (var asset in assets)
                {
                    BuildInfo buildInfo;
                    if (!buildInfoDict.TryGetValue(bundleName, out buildInfo))
                    {
                        buildInfo = new BuildInfo(bundleName);
                        buildInfoDict.Add(bundleName, buildInfo);
                    }

                    buildInfo.AssetHashSet.Add(asset);
                }
            }

            List<AssetBundleBuild> assetBundles = new List<AssetBundleBuild>();
            foreach (var bundleName in buildInfoDict.Keys)
            {
                AssetBundleBuild assetBundleBuild = new AssetBundleBuild();
                assetBundleBuild.assetBundleName = bundleName;
                assetBundleBuild.assetNames = buildInfoDict[bundleName].AssetHashSet.ToArray();
                assetBundles.Add(assetBundleBuild);
            }

            if (!Directory.Exists(outPath))
            {
                Directory.CreateDirectory(outPath);
            }

            DeployTool.ReportBuildLog("Build LuaAssetBundle, BuildPipeline.BuildAssetBundles");
            AssetBundleManifest manifest = BuildPipeline.BuildAssetBundles(
                outPath,
                assetBundles.ToArray(),
                BuildAssetBundleOptions.ChunkBasedCompression,
                EditorUserBuildSettings.activeBuildTarget);

            if (manifest)
            {
                DeployTool.ReportBuildLog("Build LuaAssetBundle, ExportManifestInfoToLua");
                ExportManifestInfoToLua(buildPlatType, manifest, Path.Combine(outPath, "LuaAssetBundle.lua"));

                // 压缩LuaAssetBundle文件成zip格式的压缩包
                ZipUtils.ZipFile(Path.Combine(outPath, "LuaAssetBundle.lua"), outPath);
            }
            else
            {
                DeployTool.ReportBuildLog("[Fail] Build LuaAssetBundle, BuildPipeline.BuildAssetBundles Fail");
            }
        }

        /// <summary>
        /// 写出资源热更文件
        /// </summary>
        public static void ExportManifestToLua(BuildPlatType buildPlatType, AssetBundleManifest assetBundleManifest, string outPath)
        {
            List<string> md5List = new List<string>();
            ManifestInfo manifestInfo = new ManifestInfo();
            var baseDir = Path.GetDirectoryName(outPath);
            var allAssetBundles = assetBundleManifest.GetAllAssetBundles();
            BuildAssetsMgr.RefreshABDic(allAssetBundles, false);
            foreach (var assetBundle in allAssetBundles)
            {
                ManifestBundleInfo manifestBundleInfo = new ManifestBundleInfo();
                manifestInfo.bundleInfos.Add(assetBundle, manifestBundleInfo);
                manifestBundleInfo.hash = BuildAssetsMgr.GetABMd5(assetBundle, false).ToString();
                manifestBundleInfo.crc = BuildAssetsMgr.GetABCRC(assetBundle, false).ToString();

                FileInfo fileInfo = new FileInfo(Path.Combine(baseDir, assetBundle));
                if (fileInfo.Exists)
                {
                    manifestBundleInfo.size = (buildPlatType == BuildPlatType.Android || buildPlatType == BuildPlatType.AndroidDev
                        || buildPlatType == BuildPlatType.IOS || buildPlatType == BuildPlatType.IOSDev
                        || buildPlatType == BuildPlatType.Audit || buildPlatType == BuildPlatType.AuditDev)
                        ? fileInfo.Length + EncryptMgr.GetEncryptKeyLength(assetBundle)
                        : fileInfo.Length;
                }

                var dependencies = assetBundleManifest.GetAllDependencies(assetBundle);
                for (int i = 0; i < dependencies.Length; ++i)
                {
                    if (!string.IsNullOrEmpty(dependencies[i]))
                    {
                        manifestBundleInfo.deps.Add(dependencies[i]);
                    }
                }

                md5List.Add(string.Format("{0} {1}", assetBundle, manifestBundleInfo.hash));
            }

            string projectName = GetProjectName(buildPlatType);
            manifestInfo.projectName = projectName;

            manifestInfo.manifestHashCode = assetBundleManifest.CalculateVersion();
            var jsonStr = JsonConvert.SerializeObject(manifestInfo, JSONEditorHelper.RTJsonSerializerSettings);
            var luaStr = "local empty = {}\nreturn\n" + JSon2Lua.IndentedJSonString2Lua(jsonStr, "empty");

            var directory = Path.GetDirectoryName(outPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(outPath, luaStr);
            File.WriteAllLines(Path.Combine(Path.GetDirectoryName(outPath), "md5_list.txt"), md5List.ToArray());
            File.WriteAllText(BuilderConfig.GetBuildLogPath(string.Format("AssetBundle{0}.json", (int)buildPlatType)), jsonStr);
        }

        /// <summary>
        /// 写出Lua热更文件
        /// </summary>
        private static void ExportManifestInfoToLua(BuildPlatType buildPlatType, AssetBundleManifest assetBundleManifest, string outPath)
        {
            ManifestInfo manifestInfo = new ManifestInfo();
            var baseDir = Path.GetDirectoryName(outPath);

            List<string> md5List = new List<string>();
            var allAssetBundles = assetBundleManifest.GetAllAssetBundles();
            BuildAssetsMgr.RefreshABDic(allAssetBundles, true);

            foreach (var assetBundle in allAssetBundles)
            {
                if (!String.IsNullOrEmpty(assetBundle))
                {
                    ManifestBundleInfo manifestBundleInfo = new ManifestBundleInfo();
                    manifestInfo.bundleInfos.Add(assetBundle, manifestBundleInfo);

                    manifestBundleInfo.hash = BuildAssetsMgr.GetABMd5(assetBundle, true).ToString();
                    manifestBundleInfo.crc = BuildAssetsMgr.GetABCRC(assetBundle, false).ToString();


                    FileInfo fileInfo = new FileInfo(Path.Combine(baseDir, assetBundle));
                    if (fileInfo.Exists)
                    {
                        manifestBundleInfo.size = (buildPlatType == BuildPlatType.Android || buildPlatType == BuildPlatType.AndroidDev
                            || buildPlatType == BuildPlatType.IOS || buildPlatType == BuildPlatType.IOSDev)
                                                    ? fileInfo.Length + EncryptMgr.GetEncryptKeyLength(assetBundle)
                                                    : fileInfo.Length;
                    }

                    var assets = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundle);
                    manifestBundleInfo.deps.AddRange(assets);

                    md5List.Add(string.Format("{0} {1}", assetBundle, manifestBundleInfo.hash));
                }
            }

            manifestInfo.manifestHashCode = assetBundleManifest.CalculateVersion();

            string projectName = GetProjectName(buildPlatType);
            Debug.LogFormat("ProjectName: {0}", projectName);
            DeployTool.ReportBuildLog(string.Format("ProjectName: {0}", projectName));
            manifestInfo.projectName = projectName;

            var jsonStr = JsonConvert.SerializeObject(manifestInfo, JSONEditorHelper.RTJsonSerializerSettings);
            var luaStr = "local empty = {}\nreturn\n" + JSon2Lua.IndentedJSonString2Lua(jsonStr, "empty");

            var directory = Path.GetDirectoryName(outPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(outPath, luaStr);
            File.WriteAllLines(Path.Combine(Path.GetDirectoryName(outPath), "lua_md5_list.txt"), md5List.ToArray());
            File.WriteAllText(BuilderConfig.GetBuildLogPath(string.Format("LuaAssetBundle{0}.json", (int)buildPlatType)), jsonStr);
        }

        public static string GetProjectName(BuildPlatType buildPlatType)
        {
            string languagePath = string.Format("{0}/language/used_language.lua", LuaConst.luaDir);
            if (!File.Exists(languagePath))
            {
                Debug.LogErrorFormat("[Fail] 丢失文件: {0}", languagePath);
                DeployTool.ReportBuildLog(string.Format("[Fail] 丢失文件: {0}", languagePath));
                return string.Empty;
            }

            bool find = false;
            string language = string.Empty;
            string[] lines = File.ReadAllLines(languagePath);
            for (int i = 0; i < lines.Length; ++i)
            {
                string line = lines[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                if (line.IndexOf("--") >= 0)
                {
                    continue;
                }

                var match = Regex.Match(line, "return \"(.*?)\"");
                if (match.Success)
                {
                    find = true;
                    language = match.Groups[1].Value;
                    break;
                }
            }

            if (!find)
            {
                Debug.LogError("[Fail] used_language.lua格式不正确");
                DeployTool.ReportBuildLog("[Fail] used_language.lua格式不正确");
                return string.Empty;
            }

            string platName = string.Empty;
            if (buildPlatType == BuildPlatType.Android || buildPlatType == BuildPlatType.AndroidDev)
            {
                platName = "android";
            }
            else if (buildPlatType == BuildPlatType.IOS || buildPlatType == BuildPlatType.IOSDev)
            {
                platName = "ios";
            }
            else if (buildPlatType == BuildPlatType.Windows || buildPlatType == BuildPlatType.WindowsDev)
            {
                platName = "windows";
            }
            else if (buildPlatType == BuildPlatType.Audit)
            {
#if UNITY_ANDROID
                platName = "android";
#elif UNITY_IOS
                platName = "ios";
#endif
            }

            if (string.IsNullOrEmpty(platName))
            {
                Debug.LogError("[Fail] 未知的BuildPlatType: " + buildPlatType);
                DeployTool.ReportBuildLog("[Fail] 未知的BuildPlatType: " + buildPlatType);
                return string.Empty;
            }

            string projectName = string.Format("{0}#{1}#{2}", "a2", language, platName);
            return projectName;
        }

        private static void RemoveRepeatFromBuildInfoDic(string assetName)
        {
            foreach (var item in BuildInfoDict)
            {
                if (item.Value.AssetHashSet.Contains(assetName))
                {
                    item.Value.AssetHashSet.Remove(assetName);
                }
            }
        }

        public static string GetL2RDirPath(string path, int depth)
        {
            if (depth == 0 || path.IndexOf("/") < 0) return path;
            int end = path.LastIndexOf("/");
            int index = 0;
            while (index <= end && depth > 0)
            {
                if (path[index] == '/')
                {
                    depth--;
                }

                index++;
            }

            return path.Substring(0, index - 1);
        }

        public static string GetR2LDirPath(string path, int depth)
        {
            if (depth == 0 || path.IndexOf("/") < 0) return path;
            int end = path.IndexOf("/");
            int index = path.Length - 1;
            while (index >= end && depth > 0)
            {
                if (path[index] == '/')
                {
                    depth--;
                }

                index--;
            }

            return path.Substring(0, index + 1);
        }

        private static string FixAssetBundleName(string bundleName)
        {
            bundleName = bundleName.Replace(" ", "");
            bundleName = bundleName.Replace("—", "-");
            bundleName = Regex.Replace(bundleName, "[\u4E00-\u9FA5]+", ""); //去除汉字
            bundleName = bundleName.ToLower();

            return bundleName;
        }

        private static void CalcEnvironmentTextureABName(Dictionary<string, string> sceneDict)
        {
            DictHashSet textureDict = new DictHashSet();
            DictHashSet textureGroupDict = new DictHashSet();
            foreach (var scene in sceneDict)
            {
                string assetPath = scene.Key;
                string[] sceneDependencies = BuildAssetsMgr.GetDependencies(assetPath);
                foreach (var mat in sceneDependencies)
                {
                    if (!mat.EndsWith(".mat"))
                    {
                        continue;
                    }

                    var matDependencies = BuildAssetsMgr.GetDependencies(mat);
                    foreach (var texture in matDependencies)
                    {
                        if (!IsEnvironmentsTexture(texture))
                        {
                            continue;
                        }

                        HashSet<string> matList;
                        if (!textureDict.TryGetValue(texture, out matList))
                        {
                            matList = new HashSet<string>();
                            textureDict.Add(texture, matList);
                        }
                        matList.Add(mat);
                    }
                }
            }

            foreach (var texture in textureDict.Keys)
            {
                BuildTextureGroup(texture, textureDict, textureGroupDict);
            }

            foreach (var kv in textureGroupDict)
            {
                string bundleName = CalcABNameByGroup(kv.Value);
                EnvironmentsTextureBundleNameDict.Add(kv.Key, bundleName);
            }
        }

        private static void BuildTextureGroup(string texture, DictHashSet textureDict, DictHashSet textureGroupDict)
        {
            if (textureGroupDict.ContainsKey(texture))
            {
                return;
            }

            HashSet<string> group = new HashSet<string>();
            LinkTextureGroup(texture, textureDict, textureGroupDict, group);
        }

        private static void LinkTextureGroup(string texture, DictHashSet textureDict, DictHashSet textureGroupDict, HashSet<string> group)
        {
            if (group.Contains(texture))
                return;

            group.Add(texture);
            textureGroupDict.Add(texture, group);

            HashSet<string> matList = textureDict[texture];
            foreach (var mat in matList)
            {
                if (!mat.EndsWith(".mat"))
                {
                    continue;
                }

                var matDependencies = BuildAssetsMgr.GetDependencies(mat);
                foreach (var dep in matDependencies)
                {
                    if (!IsEnvironmentsTexture(dep) || !IsSameEnvironment(texture, dep))
                    {
                        continue;
                    }

                    LinkTextureGroup(dep, textureDict, textureGroupDict, group);
                }
            }
        }

        private static bool IsEnvironmentsTexture(string path)
        {
            if (!path.EndsWith(".png") && !path.EndsWith(".tga"))
            {
                return false;
            }

            if (!path.StartsWith(AssetBundleMarkRule.EnvironmentsDir))
            {
                return false;
            }

            return true;
        }

        private static bool IsSameEnvironment(string pathA, string pathB)
        {
            pathA = pathA.Remove(0, AssetBundleMarkRule.EnvironmentsDir.Length + 1);
            pathB = pathB.Remove(0, AssetBundleMarkRule.EnvironmentsDir.Length + 1);
            var splitsA = pathA.Split('/');
            var splitsB = pathB.Split('/');

            var floaderA = null != splitsA && splitsA.Length > 0 ? splitsA[0] : pathA;
            var floaderB = null != splitsB && splitsB.Length > 0 ? splitsB[0] : pathB;

            return floaderA == floaderB;
        }

        private static string CalcABNameByGroup(HashSet<string> group)
        {
            string shortestPath = string.Empty;
            foreach (var texture in group)
            {
                if (string.IsNullOrEmpty(shortestPath) || texture.Length < shortestPath.Length
                    || (texture.Length == shortestPath.Length && texture.CompareTo(shortestPath) < 0))
                {
                    shortestPath = texture;
                }
            }

            var bundleName = shortestPath.Substring(AssetBundleMarkRule.BaseDir.Length + 1);
            var fileName = Path.GetFileNameWithoutExtension(shortestPath);
            bundleName = GetL2RDirPath(bundleName, 2) + "/" + fileName;
            bundleName = FixAssetBundleName(bundleName);
            return bundleName;
        }
    }
}
