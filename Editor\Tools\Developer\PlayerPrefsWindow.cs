﻿using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class PlayerPrefsWindow : OdinEditorWindow
{
    [MenuItem("Tools/调试工具箱/PlayerPrefs设置工具")]
    static void OpenWindow()
    {
        GetWindow<PlayerPrefsWindow>("PlayerPrefs设置工具").Show();
    }

    [InfoBox("有些功能用PlayerPrefs记录了，只会触发一次，不好测试，可以通过该工具手动设置")]
    public string key = "";

    [BoxGroup("int类型")]
    [LabelText("int类型")]
    public int num;

    [BoxGroup("int类型")]
    [Button("设置", ButtonSizes.Large)]
    [GUIColor(0f, 0.74f, 1f)]
    void NumberButton()
    {
        PlayerPrefs.SetInt(key, num);
        Debug.LogError("设置成功");
    }

    [BoxGroup("flost类型")]
    [LabelText("flost类型")]
    public int float_num;

    [BoxGroup("flost类型")]
    [Button("设置", ButtonSizes.Large)]
    [GUIColor(1f, 1f, 0.74f)]
    void FloatButton()
    {
        PlayerPrefs.SetFloat(key, float_num);
        Debug.LogError("设置成功");
    }

    [BoxGroup("字符串类型")]
    [LabelText("字符串类型")]
    public string value_str;

    [BoxGroup("字符串类型")]
    [Button("设置", ButtonSizes.Large)]
    [GUIColor(0.5f, 1, 0.7f)]
    void StringButton()
    {

        PlayerPrefs.SetString(key, value_str);
        Debug.LogError("设置成功");
    }


}
