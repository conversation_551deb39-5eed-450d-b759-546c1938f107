using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.Profiling;
using UnityEngine.Scripting;
using UnityEngine.Splines;
using UnityEngine.UI;
using Math = System.Math;
#if LETAI_TRUESHADOW
using LeTai.TrueShadow;
using LeTai.TrueShadow.PluginInterfaces;
#endif

[assembly: InternalsVisibleTo("UISplineRenderer.Editor", AllInternalsVisible = true)]

namespace UI_Spline_Renderer
{
    /// <summary>
    /// UV映射模式
    /// </summary>
    public enum UVMode
    {
        /// <summary>平铺模式</summary>
        Tile,
        /// <summary>每段重复模式</summary>
        RepeatPerSegment,
        /// <summary>拉伸模式</summary>
        Stretch
    }

    /// <summary>
    /// 偏移模式
    /// </summary>
    public enum OffsetMode
    {
        /// <summary>距离偏移</summary>
        Distance,
        /// <summary>标准化偏移</summary>
        Normalized
    }

    /// <summary>
    /// UI样条线渲染器
    /// 用于在Unity UI系统中渲染样条线，支持自定义宽度、颜色渐变、纹理映射等功能
    /// 可以在样条线的起始和结束位置添加图像装饰
    /// </summary>
    [RequireComponent(typeof(CanvasRenderer), typeof(SplineContainer))]
    [ExecuteInEditMode]
    [DisallowMultipleComponent]
    public class UISplineRenderer : MaskableGraphic
#if LETAI_TRUESHADOW
        , ITrueShadowCustomHashProvider
#endif
    {

        /// <summary>
        /// 线条纹理预设
        /// 用于快速设置常用的线条纹理类型
        /// </summary>
        public LineTexturePreset lineTexturePreset
        {
            get
            {
                if (m_Texture == UISplineRendererSettings.Instance.defaultLineTexture) return LineTexturePreset.Default;
                if (m_Texture == UISplineRendererSettings.Instance.uvTestLineTexture) return LineTexturePreset.UVTest;
                return LineTexturePreset.Custom;
            }
            set
            {
                switch (value)
                {
                    case LineTexturePreset.Default:
                        texture = UISplineRendererSettings.Instance.defaultLineTexture;
                        break;
                    case LineTexturePreset.UVTest:
                        texture = UISplineRendererSettings.Instance.uvTestLineTexture;
                        break;
                    case LineTexturePreset.Custom:
                        Debug.LogWarning("[UI Spline Renderer] If you want to change the line texture, " +
                                         "just set value to the \"texture\" property. " +
                                         "Then It will be automatically changed to LineTexturePreset.Custom");
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(value), value, null);
                }
            }
        }

        /// <summary>
        /// 起始图像预设
        /// 用于快速设置样条线起始点的图像类型
        /// </summary>
        public StartEndImagePreset startImagePreset
        {
            get => InternalUtility.GetCurrentStartImagePreset(startImageSprite);

            set
            {
                switch (value)
                {
                    case StartEndImagePreset.None:
                        startImageSprite = null;
                        break;
                    case StartEndImagePreset.Triangle:
                        startImageSprite = UISplineRendererSettings.Instance.triangleHead;
                        break;
                    case StartEndImagePreset.Arrow:
                        startImageSprite = UISplineRendererSettings.Instance.arrowHead;
                        break;
                    case StartEndImagePreset.EmptyCircle:
                        startImageSprite = UISplineRendererSettings.Instance.emptyCircleHead;
                        break;
                    case StartEndImagePreset.FilledCircle:
                        startImageSprite = UISplineRendererSettings.Instance.filledCircleHead;
                        break;
                    case StartEndImagePreset.Custom:
                        Debug.LogWarning("[UI Spline Renderer] If you want to change the start image, " +
                                         "just set value to the \"startImageSprite\" property. " +
                                         "Then It will be automatically changed to StartEndImagePreset.Custom");
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(value), value, null);
                }
            }
        }

        /// <summary>
        /// 结束图像预设
        /// 用于快速设置样条线结束点的图像类型
        /// </summary>
        public StartEndImagePreset endImagePreset
        {
            get => InternalUtility.GetCurrentStartImagePreset(endImageSprite);

            set
            {
                switch (value)
                {
                    case StartEndImagePreset.None:
                        endImageSprite = null;
                        break;
                    case StartEndImagePreset.Triangle:
                        endImageSprite = UISplineRendererSettings.Instance.triangleHead;
                        break;
                    case StartEndImagePreset.Arrow:
                        endImageSprite = UISplineRendererSettings.Instance.arrowHead;
                        break;
                    case StartEndImagePreset.EmptyCircle:
                        endImageSprite = UISplineRendererSettings.Instance.emptyCircleHead;
                        break;
                    case StartEndImagePreset.FilledCircle:
                        endImageSprite = UISplineRendererSettings.Instance.filledCircleHead;
                        break;
                    case StartEndImagePreset.Custom:
                        Debug.LogWarning("[UI Spline Renderer] If you want to change the end image, " +
                                         "just set value to the \"endImageSprite\" property. " +
                                         "Then It will be automatically changed to StartEndImagePreset.Custom");
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(value), value, null);
                }
            }
        }

        /// <summary>
        /// 颜色属性
        /// 设置样条线的基础颜色，如果启用递归颜色则会同时更新起始和结束图像的颜色
        /// </summary>
        public override Color color
        {
            get => base.color;
            set
            {
                base.color = value;
                if (recursiveColor)
                {
                    UpdateGraphicColors();
                    UpdateTrueShadowCustomHash();
                }
            }
        }

        /// <summary>
        /// 递归颜色设置
        /// 当为true时，颜色变化会同时应用到起始和结束图像
        /// </summary>
        public bool recursiveColor
        {
            get => _recursiveColor;
            set
            {
                _recursiveColor = value;
                if (true)
                {
                    UpdateGraphicColors();
                    UpdateTrueShadowCustomHash();
                }
            }
        }

        /// <summary>
        /// 分辨率设置
        /// 控制样条线的细分程度，值越高越平滑但性能消耗越大
        /// 范围：1-10
        /// </summary>
        public int resolution
        {
            get => _resolution;
            set
            {
                value = Mathf.Clamp(value, 1, 10);
                _segmentLength = value switch
                {
                    <= 1 => 0.01f,
                    2 => 0.02f,
                    3 => 0.05f,
                    4 => 0.08f,
                    5 => 0.12f,
                    6 => 0.18f,
                    7 => 0.24f,
                    8 => 0.32f,
                    9 => 0.40f,
                    >= 10 => 0.5f
                };

                _resolution = value;
                _needToResample = true;
                SetVerticesDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public override Texture mainTexture => m_Texture == null ? s_WhiteTexture : m_Texture;

        public Texture texture
        {
            get => m_Texture;
            set
            {
                if (value != null && m_Texture == value) return;
                m_Texture = value;
                SetMaterialDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public Sprite startImageSprite
        {
            get => _startImageSprite;
            set
            {
                _startImageSprite = value;
                UpdateStartEndImages(true);
            }
        }

        public Sprite endImageSprite
        {
            get => _endImageSprite;
            set
            {
                _endImageSprite = value;
                UpdateStartEndImages(false);
            }
        }

        public int vertexCount
        {
            get
            {
                var c = 0;
                for (int i = 0; i < splineContainer.Splines.Count; i++)
                {
                    c += splineContainer[i].CalcVertexCount(_segmentLength, clipRange);
                }

                return c;
            }
        }


        public float startImageSize
        {
            get => _startImageSize;
            set
            {
                var isNew = Math.Abs(_startImageSize - value) > 0.0001f;
                if (isNew)
                {
                    _startImageSize = value;
                    UpdateStartEndImages(true);
                }
            }
        }

        public OffsetMode startImageOffsetMode
        {
            get => _startImageOffsetMode;
            set
            {
                var isNew = _startImageOffsetMode != value;
                if (isNew)
                {
                    _startImageOffsetMode = value;
                    UpdateStartEndImages(true);
                }
            }
        }

        public float startImageOffset
        {
            get => _startImageOffset;
            set
            {
                var isNew = Math.Abs(_startImageOffset - value) > 0.0001f;
                if (isNew)
                {
                    _startImageOffset = value;
                    UpdateStartEndImages(true);
                }
            }
        }
        public float normalizedStartImageOffset
        {
            get => _normalizedStartImageOffset;
            set
            {
                var isNew = Math.Abs(_normalizedStartImageOffset - value) > 0.0001f;
                if (isNew)
                {
                    _normalizedStartImageOffset = value;
                    UpdateStartEndImages(true);
                }
            }
        }

        public float endImageSize
        {
            get => _endImageSize;
            set
            {
                var isNew = Math.Abs(_endImageSize - value) > 0.0001f;
                if (isNew)
                {
                    _endImageSize = value;
                    UpdateStartEndImages(false);
                }
            }
        }
        public OffsetMode endImageOffsetMode
        {
            get => _endImageOffsetMode;
            set
            {
                var isNew = _endImageOffsetMode != value;
                if (isNew)
                {
                    _endImageOffsetMode = value;
                    UpdateStartEndImages(false);
                }
            }
        }

        public float endImageOffset
        {
            get => _endImageOffset;
            set
            {
                var isNew = Math.Abs(_endImageOffset - value) > 0.0001f;
                if (isNew)
                {
                    _endImageOffset = value;
                    UpdateStartEndImages(false);
                }
            }
        }
        public float normalizedEndImageOffset
        {
            get => _normalizedEndImageOffset;
            set
            {
                var isNew = Math.Abs(_normalizedEndImageOffset - value) > 0.0001f;
                if (isNew)
                {
                    _normalizedEndImageOffset = value;
                    UpdateStartEndImages(false);
                }
            }
        }

        public bool recursiveMaterial
        {
            get => _recursiveMaterial;
            set
            {
                _recursiveMaterial = value;
                if (value)
                {
                    ManipulateOtherGraphics(x => x.material = material);
                    UpdateTrueShadowCustomHash();
                }
            }
        }

        public override Material material
        {
            get => base.material;
            set
            {
                base.material = value;
                if (recursiveMaterial)
                {
                    ManipulateOtherGraphics(x => x.material = value);
                    UpdateTrueShadowCustomHash();
                }
            }
        }


        public bool keepZeroZ
        {
            get => _keepZeroZ;
            set
            {
                _keepZeroZ = value;
                _needToResample = true;
                SetVerticesDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public bool keepBillboard
        {
            get => _keepBillboard;
            set
            {
                _keepBillboard = value;
                _needToResample = true;
                SetVerticesDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public float width
        {
            get => _width;
            set
            {
                _width = value;
                _needToResample = true;
                SetVerticesDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public UVMode uvMode
        {
            get => _uvMode;
            set
            {
                _uvMode = value;
                SetMaterialDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public Vector2 uvMultiplier
        {
            get => _uvMultiplier;
            set
            {
                _uvMultiplier = value;
                SetMaterialDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public Vector2 uvOffset
        {
            get => _uvOffset;
            set
            {
                _uvOffset = value;
                SetVerticesDirty();
                SetMaterialDirty();
                UpdateTrueShadowCustomHash();
            }
        }

        public Vector2 clipRange
        {
            get => _clipRange;
            set
            {
                _clipRange = value;
                _needToResample = true;
                SetVerticesDirty();
                UpdateTrueShadowCustomHash();
            }
        }


        /// <summary>样条线容器组件</summary>
        public SplineContainer splineContainer;
        /// <summary>起始图像列表</summary>
        public List<Image> startImages = new();
        /// <summary>结束图像列表</summary>
        public List<Image> endImages = new();

        /// <summary>颜色渐变</summary>
        [SerializeField] Gradient _colorGradient = new Gradient();
        /// <summary>宽度曲线</summary>
        [SerializeField] AnimationCurve _widthCurve = AnimationCurve.Linear(0, 1, 1, 1);

        /// <summary>保持Z轴为0</summary>
        [SerializeField] bool _keepZeroZ = true;
        /// <summary>保持面向屏幕</summary>
        [SerializeField] bool _keepBillboard = true;
        /// <summary>线条宽度</summary>
        [SerializeField] float _width = 10;
        /// <summary>UV映射模式</summary>
        [SerializeField] UVMode _uvMode;
        /// <summary>UV乘数</summary>
        [SerializeField] Vector2 _uvMultiplier = new Vector2(1, 1);
        /// <summary>UV偏移</summary>
        [SerializeField] Vector2 _uvOffset;
        /// <summary>裁剪范围</summary>
        [SerializeField] Vector2 _clipRange = new Vector2(0, 1);

        /// <summary>递归材质设置</summary>
        [SerializeField] bool _recursiveMaterial = true;
        /// <summary>递归颜色设置</summary>
        [SerializeField] bool _recursiveColor = true;
        /// <summary>分辨率</summary>
        [SerializeField] int _resolution = 5;
        /// <summary>段长度</summary>
        [SerializeField] float _segmentLength = 0.12f;
        /// <summary>纹理</summary>
        [SerializeField] Texture m_Texture;
        /// <summary>起始图像精灵</summary>
        [SerializeField] Sprite _startImageSprite;
        /// <summary>结束图像精灵</summary>
        [SerializeField] Sprite _endImageSprite;
        /// <summary>起始图像大小</summary>
        [SerializeField] float _startImageSize = 32;
        /// <summary>起始图像偏移模式</summary>
        [SerializeField] OffsetMode _startImageOffsetMode;
        [SerializeField] float _startImageOffset;
        [SerializeField] float _normalizedStartImageOffset;
        [SerializeField] float _endImageSize = 32;
        [SerializeField] OffsetMode _endImageOffsetMode;
        [SerializeField] float _endImageOffset;
        [SerializeField] float _normalizedEndImageOffset = 1;
        [SerializeField] bool _defaultTextureInitialized;

#pragma warning disable CS0414
        bool _needToResample;
#pragma warning restore CS0414

        // readonly List<BezierKnot> _nvSplineCache = new List<BezierKnot>();
        List<UIVertex> _vertices = new List<UIVertex>();
        List<int3> _triangles = new List<int3>();

        JobHandle _jobHandle;
        VertexHelper _vh;
#if LETAI_TRUESHADOW
        TrueShadow _trueShadow;
#endif


        #region override Methods

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器中验证组件属性时调用
        /// 当Inspector中的值发生变化时触发，用于实时更新渲染效果
        /// </summary>
        protected override void OnValidate()
        {
            base.OnValidate();
            SetVerticesDirty();
            SetMaterialDirty();
            UpdateTrueShadowCustomHash();
            UpdateGraphicColors();
        }
#endif

        /// <summary>
        /// 组件启用时调用
        /// 注册样条线变化事件监听器，初始化默认纹理，更新渲染状态
        /// </summary>
        protected override void OnEnable()
        {
            base.OnEnable();
            Spline.Changed += OnSplineChanged;
            SplineContainer.SplineAdded += OnSplineAddedOrRemoved;
            SplineContainer.SplineRemoved += OnSplineAddedOrRemoved;

            if (!_defaultTextureInitialized && m_Texture == null)
            {
                m_Texture = UISplineRendererSettings.Instance.defaultLineTexture;
            }

            SetVerticesDirty();
            SetMaterialDirty();

            UpdateRaycastTargetRect();
        }

        /// <summary>
        /// 组件开始时调用
        /// 在编辑器中处理预制体阶段的特殊情况
        /// </summary>
        protected override void Start()
        {
#if UNITY_EDITOR
            var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
            if (prefabStage != null && prefabStage.IsPartOfPrefabContents(gameObject))
            {
                UpdateRaycastTargetRect();
            }
#endif
        }

        /// <summary>
        /// 组件禁用时调用
        /// 取消注册事件监听器，清理渲染状态
        /// </summary>
        protected override void OnDisable()
        {
            base.OnDisable();
            Spline.Changed -= OnSplineChanged;
            SplineContainer.SplineAdded -= OnSplineAddedOrRemoved;
            SplineContainer.SplineRemoved -= OnSplineAddedOrRemoved;
            SetVerticesDirty();
            SetMaterialDirty();
        }

        /// <summary>
        /// 组件销毁时调用
        /// 清理所有起始和结束图像对象，防止内存泄漏
        /// </summary>
        protected override void OnDestroy()
        {
            base.OnDestroy();
            var c = startImages.Count;
            for (int i = 0; i < c; i++)
            {
                if (startImages[i] != null)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(startImages[i].gameObject);
                    }
                    else
                    {
                        DestroyImmediate(startImages[i].gameObject);
                    }
                }

                if (endImages[i] != null)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(endImages[i].gameObject);
                    }
                    else
                    {
                        DestroyImmediate(endImages[i].gameObject);
                    }
                }
            }
        }

        /// <summary>
        /// 标记顶点数据为脏状态，需要重新生成
        /// 触发样条线挤出作业来重新计算顶点
        /// </summary>
        public override void SetVerticesDirty()
        {
            base.SetVerticesDirty();
            DoExtrudeSplineJobAll();
        }

        /// <summary>
        /// 标记材质为脏状态，需要重新应用
        /// 同时更新其他图形组件的遮罩状态
        /// </summary>
        public override void SetMaterialDirty()
        {
            base.SetMaterialDirty();
            ManipulateOtherGraphics(x => x.maskable = maskable);
        }

        /// <summary>
        /// 填充网格数据的核心方法
        /// Unity UI系统调用此方法来生成渲染所需的顶点和三角形数据
        /// </summary>
        /// <param name="vh">顶点助手，用于构建网格</param>
        protected override void OnPopulateMesh(VertexHelper vh)
        {
            Profiler.BeginSample("UISplineRenderer.OnPopulateMesh", this);
            _vh ??= vh;
            vh.Clear();
            Draw();
            Profiler.EndSample();
        }

        /// <summary>
        /// 射线检测方法，用于UI交互
        /// 检测鼠标点击是否命中样条线区域
        /// </summary>
        /// <param name="sp">屏幕坐标点</param>
        /// <param name="eventCamera">事件摄像机</param>
        /// <returns>是否命中样条线</returns>
        public override bool Raycast(Vector2 sp, Camera eventCamera)
        {
            if (!base.Raycast(sp, eventCamera)) return false;
            return SplineRaycast(sp, eventCamera);
        }

        /// <summary>
        /// 样条线射线检测的具体实现
        /// 将屏幕坐标转换为本地坐标，然后检测是否在样条线宽度范围内
        /// </summary>
        /// <param name="sp">屏幕坐标点</param>
        /// <param name="eventCamera">事件摄像机</param>
        /// <returns>是否命中样条线</returns>
        bool SplineRaycast(Vector2 sp, Camera eventCamera)
        {
            Vector3 point = default;
            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                var flatten = new Vector3(sp.x, sp.y, transform.position.z);
                point = transform.InverseTransformPoint(flatten);
            }
            else
            {
                var wp = eventCamera.ScreenToWorldPoint(new Vector3(sp.x, sp.y, transform.position.z - eventCamera.transform.position.z));
                point = transform.InverseTransformPoint(wp);
                point.z = 0;
            }



            foreach (var spline in splineContainer.Splines)
            {
                var distance = SplineUtility.GetNearestPoint(spline, point, out var nearest, out var t);
                if (distance <= GetWidthAt(t))
                {
                    return true;
                }
            }

            return false;
        }

        // void DrawLine(Vector2 from, Vector2 to, Color c)
        // {
        //     Debug.DrawLine(from, to,                        c, 1);
        //     Debug.DrawLine(from, from + Vector2.up * 1,   c, 1);
        //     Debug.DrawLine(from, from + Vector2.right * 1,c, 1);
        //     Debug.DrawLine(from, from + Vector2.down * 1, c, 1);
        //     Debug.DrawLine(from, from + Vector2.left * 1, c, 1);
        // }

        /// <summary>
        /// 淡入淡出透明度动画
        /// 同时应用到起始和结束图像
        /// </summary>
        public override void CrossFadeAlpha(float alpha, float duration, bool ignoreTimeScale)
        {
            base.CrossFadeAlpha(alpha, duration, ignoreTimeScale);

            ManipulateOtherGraphics(x => x.CrossFadeAlpha(alpha, duration, ignoreTimeScale));
        }

        /// <summary>
        /// 淡入淡出颜色动画
        /// 同时应用到起始和结束图像
        /// </summary>
        public override void CrossFadeColor(Color targetColor, float duration, bool ignoreTimeScale, bool useAlpha)
        {
            base.CrossFadeColor(targetColor, duration, ignoreTimeScale, useAlpha);
            ManipulateOtherGraphics(x => x.CrossFadeColor(targetColor, duration, ignoreTimeScale, useAlpha));
        }

        /// <summary>
        /// 淡入淡出颜色动画（包含RGB控制）
        /// 同时应用到起始和结束图像
        /// </summary>
        public override void CrossFadeColor(Color targetColor, float duration, bool ignoreTimeScale, bool useAlpha, bool useRGB)
        {
            base.CrossFadeColor(targetColor, duration, ignoreTimeScale, useAlpha, useRGB);
            ManipulateOtherGraphics(x => x.CrossFadeColor(targetColor, duration, ignoreTimeScale, useAlpha, useRGB));
        }


        #endregion

        /// <summary>
        /// 样条线变化事件处理器
        /// 当样条线的节点被修改时调用，更新渲染状态
        /// </summary>
        /// <param name="spline">发生变化的样条线</param>
        /// <param name="knotIndex">变化的节点索引</param>
        /// <param name="modification">修改类型</param>
        void OnSplineChanged(Spline spline, int knotIndex, SplineModification modification)
        {
            var isMySpline = false;
            for (int i = 0; i < splineContainer.Splines.Count; i++)
            {
                if (splineContainer.Splines[i] == spline)
                {
                    isMySpline = true;

                    if (modification == SplineModification.KnotModified)
                    {
                        var knot = spline[knotIndex];
                        var pos = knot.Position;
                        if (keepZeroZ) knot.Position = new float3(pos.x, pos.y, 0);

                        spline.SetKnotNoNotify(knotIndex, knot);
                    }

                    break;
                }
            }

            if (!isMySpline) return;
            if (keepZeroZ) transform.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y, 0);


            SetVerticesDirty();
            SetMaterialDirty();

            UpdateRaycastTargetRect();

            UpdateTrueShadowCustomHash();

        }

        /// <summary>
        /// 样条线容器添加或移除样条线时的事件处理器
        /// 更新渲染状态和射线检测区域
        /// </summary>
        /// <param name="container">样条线容器</param>
        /// <param name="i">样条线索引</param>
        void OnSplineAddedOrRemoved(SplineContainer container, int i)
        {
            if (container != splineContainer) return;
            SetVerticesDirty();
            SetMaterialDirty();

            UpdateRaycastTargetRect();

            UpdateTrueShadowCustomHash();

        }

        /// <summary>
        /// 更新射线检测目标矩形区域
        /// 根据样条线的形状和宽度计算包围盒，设置RectTransform的大小和位置
        /// </summary>
        void UpdateRaycastTargetRect()
        {
            if (splineContainer.Splines.Count == 0) return;
            if (splineContainer.Splines.Count == 1 && splineContainer.Spline.Count < 2) return;
            var p0 = splineContainer.EvaluatePosition(0);
            var scale = transform.lossyScale.x;
            var bounds = new Bounds(p0, Vector3.one * (GetWidthAt(0) * scale));

            for (int i = 0; i < splineContainer.Splines.Count; i++)
            {
                var spline = splineContainer[i];

                var vertexCount = spline.CalcVertexCount(_segmentLength, clipRange);
                var sampleCount = vertexCount / 2;
                for (int j = 0; j < sampleCount; j++)
                {
                    var t = (float)j / (sampleCount - 1);
                    var p = splineContainer.EvaluatePosition(spline, t);
                    var b = new Bounds(p, Vector3.one * (GetWidthAt(t) * scale));

                    bounds.Encapsulate(b);
                }
            }

            if (splineContainer.Splines.Count == 1)
            {
                var p1 = splineContainer.EvaluatePosition(1);
                var b1 = new Bounds(p1, Vector3.one * (GetWidthAt(1) * scale));
                bounds.Encapsulate(b1);
            }
            if (float.IsNaN(bounds.center.x) || float.IsNaN(bounds.center.y) || float.IsNaN(bounds.center.z) ||
                float.IsNaN(bounds.size.x) || float.IsNaN(bounds.size.y) || float.IsNaN(bounds.size.z)) return;
            var pos = transform.position;
            bounds.center = new Vector3(bounds.center.x, bounds.center.y, pos.z);
            {
                var a = bounds.size;
                var b = canvas.transform.lossyScale;
                bounds.size = new Vector3(a.x / b.x, a.y / b.y, 1);
            }
            var parent = rectTransform.parent as RectTransform;

            rectTransform.sizeDelta = bounds.size;
            rectTransform.pivot = new Vector2(0.5f, 0.5f);
            rectTransform.position = bounds.center;
            rectTransform.SetPivotWithoutRect(pos);

            // 注释：这行看似无用的代码不要删除
            // Unity的RectTransform有一个奇怪的机制，在读取属性时才会初始化
            // 如果删除这行代码，复制对象时游戏对象的位置会发生变化
            var r = parent.rect;

            UpdateStartEndImages(true);
            UpdateStartEndImages(false);
        }

        /// <summary>
        /// 更新TrueShadow插件的自定义哈希值
        /// 用于优化阴影渲染性能，当样条线属性变化时更新哈希
        /// </summary>
        public void UpdateTrueShadowCustomHash()
        {
#if LETAI_TRUESHADOW
            if (_trueShadow == null) _trueShadow = GetComponent<TrueShadow>();
            if (_trueShadow == null) return;

            var knotCountToHash = 0;
            var verticesToHash = 0;
            for (int i = 0; i < splineContainer.Splines.Count; i++)
            {
                var spline = splineContainer.Splines[i];
                knotCountToHash += spline.Count;
                for (int j = 0; j < spline.Count; j++)
                {
                    var knot = spline[j];
                    verticesToHash += knot.Position.GetHashCode();
                    verticesToHash += knot.Rotation.GetHashCode();
                    verticesToHash += knot.TangentIn.GetHashCode();
                    verticesToHash += knot.TangentOut.GetHashCode();
                }
            }

            _trueShadow.CustomHash = LeTai.HashUtils.CombineHashCodes(
                texture == null ? 0 : texture.GetHashCode(),
                material == null ? 0 : material.GetHashCode(),
                color.GetHashCode(),
                _colorGradient.GetHashCode(),
                uvOffset.GetHashCode(),
                uvMultiplier.GetHashCode(),
                knotCountToHash,
                verticesToHash
            );
#endif
        }

        /// <summary>
        /// 绘制样条线的核心方法
        /// 将计算好的顶点和三角形数据添加到VertexHelper中
        /// </summary>
        void Draw()
        {
            var splineCount = splineContainer.Splines.Count;
            if (splineCount <= 0) return;
            if (width == 0) return;

            for (int i = 0; i < _vertices.Count; i++)
            {
                var vert = _vertices[i];
                _vh.AddVert(vert);
            }

            for (int i = 0; i < _triangles.Count; i++)
            {
                var tri = _triangles[i];
                _vh.AddTriangle(tri.x, tri.y, tri.z);
            }


        }

        /// <summary>
        /// 执行所有样条线的挤出作业
        /// 使用Unity Job System并行计算所有样条线的顶点数据
        /// </summary>
        void DoExtrudeSplineJobAll()
        {
#if !UNITY_EDITOR && !UNITY_WEBGL
        GarbageCollector.GCMode = GarbageCollector.Mode.Disabled;
#endif
            if (splineContainer == null) splineContainer = GetComponent<SplineContainer>();
            var vertCount = 0;
            _vertices.Clear();
            _triangles.Clear();


            for (int i = 0; i < splineContainer.Splines.Count; i++)
            {
                var spline = splineContainer[i];
                DoExtrudeSplineJob(spline, vertCount);
                vertCount += spline.CalcVertexCount(_segmentLength, clipRange);
            }

            _needToResample = false;
#if !UNITY_EDITOR && !UNITY_WEBGL
            GarbageCollector.GCMode = GarbageCollector.Mode.Enabled;
#endif
        }


        /// <summary>
        /// 执行单个样条线的挤出作业
        /// 创建Native数据结构并使用Job System计算顶点和三角形
        /// </summary>
        /// <param name="spline">要处理的样条线</param>
        /// <param name="startIdx">起始顶点索引</param>
        void DoExtrudeSplineJob(Spline spline, int startIdx)
        {
            var nSpline = new NativeSpline(spline, Allocator.TempJob);
            var gradient = _colorGradient.ToNative();


            var edgeCount = spline.CalcVertexCount(_segmentLength, clipRange) / 2;


            var widthCurve = new NativeCurve(_widthCurve, Allocator.TempJob);

            var pos = new NativeArray<float3>(edgeCount, Allocator.TempJob);
            var tan = new NativeArray<float3>(edgeCount, Allocator.TempJob);
            var nor = new NativeArray<float3>(edgeCount, Allocator.TempJob);


            var vertices = new NativeList<UIVertex>(Allocator.TempJob);
            var triangles = new NativeList<int3>(Allocator.TempJob);

            var job = new SplineExtrudeJob
            {
                spline = nSpline,
                widthCurve = widthCurve,
                startIdx = startIdx,
                keepBillboard = keepBillboard,
                keepZeroZ = keepZeroZ,
                clipRange = clipRange,
                uvMultiplier = uvMultiplier,
                uvOffset = uvOffset,
                color = color,
                colorGradient = gradient,
                uvMode = uvMode,
                width = width,
                triangles = triangles,
                vertices = vertices,
                edgeCount = edgeCount,
                evaluatedPos = pos,
                evaluatedTan = tan,
                evaluatedNor = nor
            };

            _jobHandle = job.Schedule();
            _jobHandle.Complete();

            for (int i = 0; i < vertices.Length; i++)
            {
                _vertices.Add(vertices[i]);
            }

            for (int i = 0; i < triangles.Length; i++)
            {
                _triangles.Add(triangles[i]);
            }

            triangles.Dispose();
            vertices.Dispose();
            widthCurve.Dispose();
            nSpline.Dispose();
            gradient.Dispose();

            pos.Dispose();
            tan.Dispose();
            nor.Dispose();

        }


        /// <summary>
        /// 更新图形颜色
        /// 根据递归颜色设置更新起始和结束图像的颜色
        /// </summary>
        void UpdateGraphicColors()
        {
            if (!_recursiveColor) return;

            var startImageOffsetT = _startImageOffset / splineContainer.CalculateLength();
            for (int i = 0; i < startImages.Count; i++)
            {
                startImages[i].color = GetColorAt(startImageOffsetT);
            }

            var endImageOffsetT = 1f - (_endImageOffset / splineContainer.CalculateLength());
            for (int i = 0; i < endImages.Count; i++)
            {
                endImages[i].color = GetColorAt(endImageOffsetT);
            }
        }

        /// <summary>
        /// 操作其他图形组件
        /// 对所有起始和结束图像执行指定的操作
        /// </summary>
        /// <param name="graphic">要执行的操作</param>
        void ManipulateOtherGraphics(Action<MaskableGraphic> graphic)
        {
            for (int i = 0; i < startImages.Count; i++)
            {
                graphic.Invoke(startImages[i]);
            }

            for (int i = 0; i < endImages.Count; i++)
            {
                graphic.Invoke(endImages[i]);
            }
        }

        /// <summary>
        /// 更新起始或结束图像
        /// 根据样条线数量动态创建或销毁图像对象，并设置其位置和旋转
        /// </summary>
        /// <param name="isStartImage">是否为起始图像</param>
        internal void UpdateStartEndImages(bool isStartImage)
        {
            var sprite = isStartImage ? _startImageSprite : _endImageSprite;
            var images = isStartImage ? startImages : endImages;
            var offset = isStartImage ? _startImageOffset : _endImageOffset;
            var size = isStartImage ? _startImageSize : _endImageSize;
            var offsetMode = isStartImage ? _startImageOffsetMode : _endImageOffsetMode;
            var nOffset = isStartImage ? _normalizedStartImageOffset : _normalizedEndImageOffset;

            if (sprite == null)
            {
                while (images.Count > 0)
                {
                    if (Application.isPlaying)
                        Destroy(images[^1].gameObject);
                    else
                        DestroyImmediate(images[^1].gameObject);

                    images.RemoveAt(images.Count - 1);
                }
            }
            else
            {
                var validSplineCount = splineContainer.Splines.Count(x => x.Count > 1);
                if (images.Count > validSplineCount)
                {
                    var diff = images.Count - validSplineCount;
                    for (int i = 0; i < diff; i++)
                    {
                        if (Application.isPlaying)
                        {
                            Destroy(images[^1].gameObject);
                        }
                        else
                        {
                            DestroyImmediate(images[^1].gameObject);
                        }

                        images.RemoveAt(images.Count - 1);
                    }
                }
                else if (images.Count < validSplineCount)
                {
                    var diff = validSplineCount - images.Count;
                    for (int i = 0; i < diff; i++)
                    {
                        var GO = new GameObject($"Spline UI Renderer - {(isStartImage ? "StartImage" : "EndImage")}[{i}]");
                        GO.transform.SetParent(transform);
                        GO.layer = LayerMask.NameToLayer("UI");
                        var img = GO.AddComponent<Image>();
                        img.SetNativeSize();
                        images.Add(img);
                    }
                }

                for (int i = 0; i < splineContainer.Splines.Count; i++)
                {
                    var spline = splineContainer[i];
                    if (spline.Count < 2) continue;

                    var img = images[i];
                    img.rectTransform.sizeDelta = Vector2.one * size;
                    img.sprite = sprite;

                    float3 pos;
                    quaternion rot;
                    float t;

                    var length = spline.GetLength();

                    if (offsetMode == OffsetMode.Distance)
                    {
                        t = isStartImage ? offset / length : 1 + offset / length;    
                    }
                    else
                    {
                        t = nOffset;
                    }
                    

                    var outward = t is < 0 or > 1;

                    if (outward)
                    {
                        var tt = isStartImage ? 0f : 1f;
                        if (isStartImage && t > 1) tt = 1;
                        else if (!isStartImage && t < 0) tt = 0;
                        if (offsetMode == OffsetMode.Normalized) tt = t;
                        
                        splineContainer.Evaluate(spline, tt, out pos, out var tan, out var up);

                        // resolve tangent
                        if ((Vector3)tan == Vector3.zero)
                        {
                            var p = splineContainer.EvaluatePosition(spline, isStartImage ? 0.01f : 0.99f);
                            tan = isStartImage ? p - pos : pos - p;
                        }


                        if (keepBillboard) rot = quaternion.LookRotation(new float3(0, 0, 1), tan);
                        else rot = quaternion.LookRotation(up, tan);


                        var outwardOffset = offset;
                        if(offsetMode == OffsetMode.Distance)
                        {
                            if (isStartImage && t > 1)
                            {
                                outwardOffset -= length;
                            }
                            else if (!isStartImage && t < 0)
                            {
                                outwardOffset += length;
                            }
                        }
                        else
                        {
                            outwardOffset = length * (t > 1 ? t - 1 : t);
                        }

                        pos = (Vector3)pos + (Quaternion)rot * (Vector3.up * outwardOffset);
                        
                        if (keepZeroZ) pos.z = transform.position.z;

                        if (recursiveColor) img.color = GetColorAt(isStartImage ? 0 : 1);
                    }
                    else
                    {
                        splineContainer.Evaluate(spline, t, out pos, out var tan, out var up);

                        // resolve tangent
                        if ((Vector3)tan == Vector3.zero)
                        {
                            var acc = spline.EvaluateAcceleration(t);
                            tan = isStartImage ? acc - pos : pos - acc;
                        }

                        if (keepZeroZ) pos.z = transform.position.z;

                        if (keepBillboard) rot = quaternion.LookRotation(new float3(0, 0, 1), tan);
                        else rot = quaternion.LookRotation(up, tan);

                        if (recursiveColor) img.color = GetColorAt(t);
                        
                    }
                    
                    images[i].transform.SetPositionAndRotation(
                        pos,
                        rot
                    );
                }
            }
        }


        /// <summary>
        /// 获取指定位置的颜色
        /// 根据颜色渐变和基础颜色计算最终颜色
        /// </summary>
        /// <param name="t">样条线上的位置参数(0-1)</param>
        /// <returns>计算后的颜色</returns>
        public Color GetColorAt(float t)
        {
            t = Mathf.Clamp01(t);
            return color * _colorGradient.Evaluate(t);
        }

        /// <summary>
        /// 获取指定位置的宽度
        /// 根据宽度曲线和基础宽度计算最终宽度
        /// </summary>
        /// <param name="t">样条线上的位置参数(0-1)</param>
        /// <returns>计算后的宽度</returns>
        public float GetWidthAt(float t)
        {
            return _widthCurve.Evaluate(t) * width;
        }

        /// <summary>
        /// 设置宽度动画曲线
        /// 更改宽度曲线并标记顶点为脏状态
        /// </summary>
        /// <param name="curve">新的宽度曲线</param>
        public void SetWidthCurve(AnimationCurve curve)
        {
            _widthCurve = curve;
            SetVerticesDirty();
        }

        /// <summary>
        /// 更改宽度曲线的单个关键帧
        /// 修改指定索引的关键帧并更新渲染
        /// </summary>
        /// <param name="index">关键帧索引</param>
        /// <param name="key">新的关键帧数据</param>
        public void ChangeWidthCurveKey(int index, Keyframe key)
        {
            _widthCurve.MoveKey(index, key);
            SetVerticesDirty();
        }

        /// <summary>
        /// 设置颜色渐变
        /// 更改颜色渐变并更新所有相关图形的颜色
        /// </summary>
        /// <param name="gradient">新的颜色渐变</param>
        public void SetColorGradient(Gradient gradient)
        {
            _colorGradient = gradient;
            SetVerticesDirty();
            UpdateGraphicColors();
        }

        /// <summary>
        /// 更改颜色渐变的单个透明度关键点
        /// 修改指定索引的透明度关键点并更新渲染
        /// </summary>
        /// <param name="index">关键点索引</param>
        /// <param name="key">新的透明度关键点数据</param>
        public void ChangeColorGradientAlphaKey(int index, GradientAlphaKey key)
        {
            _colorGradient.alphaKeys[index] = key;
            SetVerticesDirty();
            UpdateGraphicColors();
        }

        /// <summary>
        /// 更改颜色渐变的单个颜色关键点
        /// 修改指定索引的颜色关键点并更新渲染
        /// </summary>
        /// <param name="index">关键点索引</param>
        /// <param name="key">新的颜色关键点数据</param>
        public void ChangeColorGradientAlphaKey(int index, GradientColorKey key)
        {
            _colorGradient.colorKeys[index] = key;
            SetVerticesDirty();
            UpdateGraphicColors();
        }

        /// <summary>
        /// 强制更新渲染
        /// 立即重新计算所有顶点数据并重建UI布局
        /// </summary>
        public void ForceUpdate()
        {
            DoExtrudeSplineJobAll();
            Rebuild(CanvasUpdate.Layout);
        }

        /// <summary>
        /// 重新定向所有节点到屏幕方向
        /// 将所有样条线节点的朝向调整为面向屏幕
        /// </summary>
        [ContextMenu("ReorientKnots")]
        public void ReorientKnots()
        {
            splineContainer.ReorientKnots();
        }

        #region static API

        /// <summary>
        /// 使用预设一次性创建SplineContainer和UISplineRenderer
        /// 所有节点的切线模式都设置为TangentMode.AutoSmooth
        /// </summary>
        /// <param name="positions">节点的世界坐标位置，所有节点的旋转都是-forward方向</param>
        /// <param name="parent">要归属的UI游戏对象</param>
        /// <param name="isLocal">如果为true则positions是本地坐标，false则是世界坐标</param>
        /// <param name="lineTexture">线条纹理预设。如果要使用自定义纹理，请在创建对象后更改texture属性</param>
        /// <param name="startImage">起始图像预设（第一个点）。如果要使用自定义精灵，请在创建对象后更改startImageSprite属性</param>
        /// <param name="endImage">结束图像预设（最后一个点）。如果要使用自定义精灵，请在创建对象后更改endImageSprite属性</param>
        /// <returns>创建的UISplineRenderer组件</returns>
        public static UISplineRenderer Create(
            IEnumerable<Vector3> positions,
            RectTransform parent,
            bool isLocal = false,
            LineTexturePreset lineTexture = LineTexturePreset.Default,
            StartEndImagePreset startImage = StartEndImagePreset.None,
            StartEndImagePreset endImage = StartEndImagePreset.None)
        {
            var t = new GameObject("New UI Spline Renderer").AddComponent<RectTransform>();
            t.SetParent(parent, false);
            var container = t.gameObject.AddComponent<SplineContainer>();

            foreach (var p in positions)
            {
                var localP = isLocal ? p : container.transform.InverseTransformPoint(p);
                var knot = new BezierKnot(localP);
                container.Spline.Add(knot);
            }

            container.ReorientKnotsAndSmooth();

            var renderer = container.gameObject.AddComponent<UISplineRenderer>();
            renderer.lineTexturePreset = lineTexture;
            renderer.startImagePreset = startImage;
            renderer.endImagePreset = endImage;

            return renderer;
        }

        /// <summary>
        /// 使用自定义资源创建SplineContainer和UISplineRenderer
        /// 允许直接指定纹理和精灵资源
        /// </summary>
        /// <param name="positions">节点位置列表</param>
        /// <param name="parent">父级RectTransform</param>
        /// <param name="isLocal">是否使用本地坐标</param>
        /// <param name="lineTexture">线条纹理</param>
        /// <param name="startImage">起始图像精灵</param>
        /// <param name="endImage">结束图像精灵</param>
        /// <returns>创建的UISplineRenderer组件</returns>
        public static UISplineRenderer Create(
            IEnumerable<Vector3> positions,
            RectTransform parent,
            bool isLocal,
            Texture lineTexture,
            Sprite startImage,
            Sprite endImage)
        {
            var t = new GameObject("New UI Spline Renderer").AddComponent<RectTransform>();
            t.SetParent(parent, false);
            var container = t.gameObject.AddComponent<SplineContainer>();

            foreach (var p in positions)
            {
                var localP = isLocal ? p : container.transform.InverseTransformPoint(p);
                var knot = new BezierKnot(localP);
                container.Spline.Add(knot);
            }

            container.ReorientKnotsAndSmooth();

            var renderer = container.gameObject.AddComponent<UISplineRenderer>();
            renderer.texture = lineTexture;
            renderer.startImageSprite = startImage;
            renderer.endImageSprite = endImage;

            return renderer;
        }

        #endregion
    }
}