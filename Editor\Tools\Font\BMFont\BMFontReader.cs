﻿using UnityEngine;
using UnityEditor;
using System.Text;

namespace UniFramework.Editor
{
    /// <summary>
    /// Helper class that takes care of loading BMFont's glyph information from the specified byte array.
    /// This functionality is not a part of BMFont anymore because Flash export option can't handle System.IO functions.
    /// </summary>
    public static class BMFontReader
    {
        /// <summary>
        /// Helper function that retrieves the string value of the key=value pair.
        /// </summary>
        static string GetString(string s)
        {
            var idx = s.IndexOf('=');
            return idx == -1 ? "" : s.Substring(idx + 1);
        }

        /// <summary>
        /// Helper function that retrieves the integer value of the key=value pair.
        /// </summary>
        static int GetInt(string s)
        {
            var text = GetString(s);

            int val;
            int.TryParse(text, out val);
            return val;
        }

        /// <summary>
        /// Reload the font data.
        /// </summary>
        static public void Load(BMFont font, string name, byte[] bytes)
        {
            font.Clear();

            if (bytes != null) {
                var reader = new ByteReader(bytes);
                var separator = new char[] { ' ' };

                while (reader.CanRead) {
                    var line = reader.ReadLine();
                    if (string.IsNullOrEmpty(line)) {
                        break;
                    }

                    var split = line.Split(separator, System.StringSplitOptions.RemoveEmptyEntries);
                    var len = split.Length;

                    if (split[0] == "char") {
                        // Expected data style:
                        // char id=13 x=506 y=62 width=3 height=3 xoffset=-1 yoffset=50 xadvance=0 page=0 chnl=15

                        var channel = len > 10 ? GetInt(split[10]) : 15;

                        if (len > 9 && GetInt(split[9]) > 0) {
                            Debug.LogError("Your font was exported with more than one texture. Only one texture is supported by NGUI.\n" +
                                "You need to re-export your font, enlarging the texture's dimensions until everything fits into just one texture.");
                            break;
                        }

                        if (len > 8) {
                            var id = GetInt(split[1]);
                            var glyph = font.GetGlyph(id, true);

                            if (glyph != null) {
                                glyph.x = GetInt(split[2]);
                                glyph.y = GetInt(split[3]);
                                glyph.width = GetInt(split[4]);
                                glyph.height = GetInt(split[5]);
                                glyph.offsetX = GetInt(split[6]);
                                glyph.offsetY = GetInt(split[7]);
                                glyph.advance = GetInt(split[8]);
                                glyph.channel = channel;
                            }
                            else {
                                Debug.Log("Char: " + split[1] + " (" + id + ") is NULL");
                            }
                        }
                        else {
                            Debug.LogError("Unexpected number of entries for the 'char' field (" + name + ", " + split.Length + "):\n" + line);
                            break;
                        }
                    }
                    else if (split[0] == "kerning") {
                        // Expected data style:
                        // kerning first=84 second=244 amount=-5 

                        if (len > 3) {
                            var first = GetInt(split[1]);
                            var second = GetInt(split[2]);
                            var amount = GetInt(split[3]);

                            var glyph = font.GetGlyph(second, true);
                            if (glyph != null) {
                                glyph.SetKerning(first, amount);
                            }
                        }
                        else {
                            Debug.LogError("Unexpected number of entries for the 'kerning' field (" +
                                name + ", " + split.Length + "):\n" + line);
                            break;
                        }
                    }
                    else if (split[0] == "common") {
                        // Expected data style:
                        // common lineHeight=64 base=51 scaleW=512 scaleH=512 pages=1 packed=0 alphaChnl=1 redChnl=4 greenChnl=4 blueChnl=4

                        if (len > 5) {
                            font.CharSize = GetInt(split[1]);
                            font.BaseOffset = GetInt(split[2]);
                            font.TexWidth = GetInt(split[3]);
                            font.TexHeight = GetInt(split[4]);

                            var pages = GetInt(split[5]);

                            if (pages != 1) {
                                Debug.LogError("Font '" + name + "' must be created with only 1 texture, not " + pages);
                                break;
                            }
                        }
                        else {
                            Debug.LogError("Unexpected number of entries for the 'common' field (" +
                                name + ", " + split.Length + "):\n" + line);
                            break;
                        }
                    }
                    else if (split[0] == "page") {
                        // Expected data style:
                        // page id=0 file="textureName.png"

                        if (len > 2) {
                            font.SpriteName = GetString(split[2]).Replace("\"", "");
                            font.SpriteName = font.SpriteName.Replace(".png", "");
                            font.SpriteName = font.SpriteName.Replace(".tga", "");
                        }
                    }
                }
            }
        }
    }
}
