﻿using System.IO;
using System.Collections.Generic;
using Nirvana;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Rendering.PostProcessing;
using YYPostProcessing;

public class CGCameraOneKey
{
    [MenuItem("Assets/策划专用/CG一键规范优化")]
    public static void FixedCamera()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedCGInPaths(checkDirs);
                    }
                    else
                    {
                        PlayableDirector[] directors = Selection.gameObjects[i].GetComponentsInChildren<PlayableDirector>(true);
                        for (int j = 0; j < directors.Length; j++)
                        {
                            FixedCG(directors[j], path);
                        }
                    }

                    EditorUtility.DisplayProgressBar("正在处理模型材质球..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedCGInPaths(checkDirs);
                }
                else
                {
                    PlayableDirector[] directors = Selection.activeGameObject.GetComponentsInChildren<PlayableDirector>(true);
                    for (int i = 0; i < directors.Length; i++)
                    {
                        FixedCG(directors[i], path);
                    }
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedCGInPaths(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == gameobj)
            {
                continue;
            }

            PlayableDirector[] directors = gameobj.GetComponentsInChildren<PlayableDirector>(true);
            for (int j = 0; j < directors.Length; j++)
            {
                FixedCG(directors[j], path);
            }

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理CG"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }
    }

    static List<string> roleNames = new List<string>() { "1101001", "1102001", "1103001", "1104001", "3101001", "3102001", "3104001" };
    static Dictionary<string, string> roleWeaponNames = new Dictionary<string, string>()
    {
        { "1101001", "900101201" },
        { "1102001", "900301201" },
        { "1103001", "900200001" },
        { "1104001", "901400001" },
        { "3101001", "900101201" },
        { "3102001", "900301201" },
        { "3104001", "900400001" },
    };
    static Dictionary<string, Vector3> roleWeaponScale = new Dictionary<string, Vector3>()
    {
        { "1101001", new Vector3(0.6f, 0.6f, 0.6f) },
        { "1102001", new Vector3(0.6f, 0.6f, 0.6f) },
        { "1103001", new Vector3(0.7841243f, 0.7841243f, 0.7841243f) },
        { "1104001", new Vector3(0.6f, 0.6f, 0.6f) },
        { "3101001", new Vector3(0.6f, 0.6f, 0.6f) },
        { "3102001", new Vector3(0.6f, 0.6f, 0.6f) },
        { "3104001", new Vector3(0.6f, 0.6f, 0.6f) },
    };
    static string TianShenHelpCGPath = "Assets/Game/CG/F2_CG_xinshoucun/F2_CG_xinshoucun_tianshen_help";
    public static void FixedCG(PlayableDirector director, string filePath)
    {
        if (!filePath.StartsWith("Assets/Game/CG"))
        {
            Debug.LogError("只能处理 Assets/Game/CG 下的CG文件");
            return;
        }

        GameObject cgOriginObj = AssetDatabase.LoadAssetAtPath(filePath, typeof(GameObject)) as GameObject;
        GameObject cgObj = PrefabUtility.InstantiatePrefab(cgOriginObj) as GameObject;

        //绑定的主角模型，使用最高级别材质球(ui_material)
        Animator[] animators = cgObj.GetComponentsInChildren<Animator>();
        GameObject roleObj = null;
        for (int i = 0; i < animators.Length; i++)
        {
            string roleName = animators[i].gameObject.name;
            if (roleNames.Contains(roleName))
            {
                roleObj = animators[i].gameObject;
                animators[i].cullingMode = AnimatorCullingMode.AlwaysAnimate;
                SkinnedMeshRenderer[] skins = animators[i].GetComponentsInChildren<SkinnedMeshRenderer>();
                for (int j = 0; j < skins.Length; j++)
                {
                    SkinnedMeshRenderer skin = skins[j];
                    string path = AssetDatabase.GetAssetPath(skin.sharedMaterial.GetInstanceID());
                    string matFileName = Path.GetFileNameWithoutExtension(path).Replace("_ui", "").Replace("_pbr", "");
                    //优先pbr
                    string[] mat_type = { "pbr", "ui" };
                    bool isFindMatchMat = false;
                    foreach (string mat in mat_type)
                    {
                        string newMatName = string.Format("{0}_{1}.mat", matFileName, mat);
                        path = path.Replace(matFileName + ".mat", newMatName);
                        Material find_mat = AssetDatabase.LoadAssetAtPath(path, typeof(Material)) as Material;
                        if (find_mat != null)
                        {
                            isFindMatchMat = true;
                            skin.sharedMaterial = find_mat;
                            break;
                        }
                    }

                    if (!isFindMatchMat)
                    {
                        Debug.LogErrorFormat("没有找到模型{0}的ui材质球，请检查", roleName);
                        continue;
                    }
                }

                
                //武器
                string weaponName = roleWeaponNames[roleName];
                if (!string.IsNullOrEmpty(weaponName))
                {
                    bool isWeaponExist = false;
                    Transform weaponTrans = null;
                    if (roleName == "1104001" || roleName == "3104001")
                    {
                        weaponTrans = roleObj != null ? roleObj.transform.parent : null;
                        isWeaponExist = weaponTrans.Find(weaponName) != null;
                    }
                    else
                    {
                        ActorAttachment attach = animators[i].GetComponent<ActorAttachment>();
                        if (attach != null)
                        {
                            weaponTrans = attach.GetAttachPoint(6);
                            if (weaponTrans != null)
                            {
                                Transform[] points = weaponTrans.gameObject.GetComponentsInChildren<Transform>();
                                for (int k = 0; k < points.Length; k++)
                                {
                                    if (points[k].gameObject.name == weaponName)
                                    {
                                        isWeaponExist = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    GameObject weapon;
                    if (!isWeaponExist)
                    {
                        string path = string.Format(AssetBundleMarkRule.WeaponDir + "/{0}/{1}/{2}.prefab", weaponName.Substring(0, 4), weaponName.Substring(0, 7), weaponName);
                        weapon = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                        weapon = GameObject.Instantiate(weapon);
                        if (weapon)
                        {
                            weapon.transform.SetParent(weaponTrans);
                            weapon.transform.localPosition = Vector3.zero;
                            weapon.transform.localRotation = Quaternion.identity;
                            weapon.transform.localScale = roleWeaponScale[roleName] != null ? roleWeaponScale[roleName] : Vector3.one;
                            weapon.gameObject.name = weaponName;
                        }
                    }
                }
            }
        }

        //Camera处理
        Camera[] cameras = cgObj.GetComponentsInChildren<Camera>();
        for (int i = 0; i < cameras.Length; i++)
        {
            Camera camera = cameras[i];
            if (camera.gameObject.layer == GameLayers.Default)
            {
                if (filePath.StartsWith(TianShenHelpCGPath))
                {
                    //天神指引CG特殊处理
                    camera.cullingMask = ~(1 << GameLayers.UI3D
                                        | 1 << GameLayers.UIScene
                                        | 1 << GameLayers.UI3DEffect
                                        | 1 << GameLayers.UI3DTerrain);
                }
                else
                {
                    camera.cullingMask = ~(1 << GameLayers.UI3D
                                        | 1 << GameLayers.UIScene
                                        | 1 << GameLayers.UIEffect
                                        | 1 << GameLayers.UI3DEffect
                                        | 1 << GameLayers.UI3DTerrain);
                }

                PostProcessLayer postProcessLayer = camera.GetOrAddComponent<PostProcessLayer>();
                postProcessLayer.volumeLayer = 1 << LayerMask.NameToLayer("Role");

                CameraEnvLighting envLighting = camera.GetOrAddComponent<CameraEnvLighting>();
                envLighting.Fog = true;
                envLighting.AmbientLight = new Color(0, 0, 0, 0);
                envLighting.AmbientSkyColor = new Color(1, 1, 1, 0);
                envLighting.AmbientEquatorColor = new Color(1, 1, 1, 0);
                envLighting.AmbientGroundColor = new Color(0.737f, 0.737f, 0.737f, 0);
                envLighting.CustomReflection 
                    = AssetDatabase.LoadAssetAtPath("Assets/T4M/Shaders/Sources/toony lighting.psd", typeof(Cubemap)) as Cubemap;
#if UNITY_EDITOR
                CameraSnapshot shot = camera.GetOrAddComponent<CameraSnapshot>();
                shot.BlurPassShader = Shader.Find("Game/PostEffect/BlurPass");
#endif

                camera.GetOrAddComponent<VerticalFogAttach>();
                camera.GetOrAddComponent<FlareLayer>();

                WGPostProcessLayer yyPostLayer = camera.GetOrAddComponent<WGPostProcessLayer>();
                yyPostLayer.enabled = false;
                break;
            }
        }

        PrefabUtility.ReplacePrefab(cgObj, AssetDatabase.LoadAssetAtPath<GameObject>(filePath), ReplacePrefabOptions.ConnectToPrefab);
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();

        GameObject.DestroyImmediate(cgObj);
    }

}
