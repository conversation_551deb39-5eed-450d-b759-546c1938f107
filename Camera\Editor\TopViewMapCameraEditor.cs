using System.IO;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

[CustomEditor(typeof(TopViewMapCamera))]
internal sealed class TopViewMapCameraEditor : UnityEditor.Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        
        TopViewMapCamera mapCamera = target as TopViewMapCamera;
        Camera cameraComponent = mapCamera.GetComponent<Camera>();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("地图纹理生成", EditorStyles.boldLabel);
        
        // 检查相机配置
        if (!cameraComponent.orthographic)
        {
            EditorGUILayout.HelpBox("相机必须设置为正交投影模式", MessageType.Error);
            return;
        }
        
        // 显示当前配置信息
        EditorGUILayout.LabelField($"纹理尺寸: {mapCamera.MapTextureWidth} x {mapCamera.MapTextureHeight}");
        EditorGUILayout.LabelField($"正交尺寸: {cameraComponent.orthographicSize}");
        
        EditorGUILayout.Space();
        
        // 拍摄按钮
        if (GUILayout.Button("拍摄地图纹理", GUILayout.Height(30)))
        {
            CaptureMapTexture(mapCamera, cameraComponent);
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("点击按钮将拍摄当前视角的地图纹理并保存到场景目录", MessageType.Info);
    }

    /// <summary>
    /// 拍摄地图纹理
    /// </summary>
    /// <param name="mapCamera">地图相机组件</param>
    /// <param name="camera">相机组件</param>
    private void CaptureMapTexture(TopViewMapCamera mapCamera, Camera camera)
    {
        Scene activeScene = SceneManager.GetActiveScene();
        if (string.IsNullOrEmpty(activeScene.path))
        {
            EditorUtility.DisplayDialog("提示", "请先保存当前场景", "确定");
            return;
        }
        
        // 显示进度条
        EditorUtility.DisplayProgressBar("拍摄地图纹理", "准备拍摄...", 0.1f);
        
        try
        {
            // 创建RenderTexture
            RenderTexture renderTexture = CreateMapRenderTexture(mapCamera);
            
            // 备份相机状态
            bool originalEnabled = camera.enabled;
            bool originalActive = camera.gameObject.activeSelf;
            RenderTexture originalTarget = camera.targetTexture;
            
            try
            {
                // 设置拍摄状态
                camera.targetTexture = renderTexture;
                camera.enabled = true;
                camera.gameObject.SetActive(true);
                
                // 隐藏忽略对象并快照角度
                mapCamera.SetIgnoreListEnabled(false);
                mapCamera.SnapCameraEulerAngles();
                
                EditorUtility.DisplayProgressBar("拍摄地图纹理", "正在渲染...", 0.5f);
                
                // 延迟执行保存操作，确保渲染完成
                EditorApplication.delayCall += () => SaveCapturedTexture(mapCamera, camera, renderTexture, 
                    originalEnabled, originalActive, originalTarget, activeScene);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"拍摄地图纹理时发生错误: {e.Message}");
                RestoreCameraState(mapCamera, camera, originalEnabled, originalActive, originalTarget);
                EditorUtility.ClearProgressBar();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"创建RenderTexture时发生错误: {e.Message}");
            EditorUtility.ClearProgressBar();
        }
    }
    
    /// <summary>
    /// 创建用于地图拍摄的RenderTexture
    /// </summary>
    /// <param name="mapCamera">地图相机组件</param>
    /// <returns>配置好的RenderTexture</returns>
    private RenderTexture CreateMapRenderTexture(TopViewMapCamera mapCamera)
    {
        RenderTexture renderTexture = new RenderTexture(
            mapCamera.MapTextureWidth, 
            mapCamera.MapTextureHeight, 
            32, 
            RenderTextureFormat.ARGB32);
            
        renderTexture.hideFlags = HideFlags.DontSave;
        renderTexture.name = "TopViewMapCapture";
        renderTexture.anisoLevel = 9;
        renderTexture.filterMode = FilterMode.Trilinear;
        renderTexture.antiAliasing = 8;
        
        return renderTexture;
    }
    
    /// <summary>
    /// 保存拍摄的纹理
    /// </summary>
    private void SaveCapturedTexture(TopViewMapCamera mapCamera, Camera camera, RenderTexture renderTexture,
        bool originalEnabled, bool originalActive, RenderTexture originalTarget, Scene scene)
    {
        try
        {
            EditorUtility.DisplayProgressBar("拍摄地图纹理", "保存纹理文件...", 0.8f);
            
            string scenePath = scene.path;
            string sceneDirectory = scenePath.Substring(0, scenePath.LastIndexOf('.'));
            
            if (!Directory.Exists(sceneDirectory))
            {
                Directory.CreateDirectory(sceneDirectory);
            }
            
            string texturePath = Path.Combine(sceneDirectory, "minimap.png");
            
            // 使用扩展方法保存纹理
            if (SaveRenderTextureAsPNG(camera, texturePath))
            {
                Debug.Log($"地图纹理已保存到: {texturePath}");
                
                // 刷新资源数据库
                AssetDatabase.Refresh();
                
                // 加载并设置纹理
                string relativePath = GetRelativeAssetPath(texturePath);
                Texture2D mapTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(relativePath);
                if (mapTexture != null)
                {
                    mapCamera.MapTexture = mapTexture;
                    EditorSceneManager.MarkSceneDirty(scene);
                    Debug.Log("地图纹理已成功设置到组件");
                }
            }
            else
            {
                Debug.LogError($"无法保存地图纹理到: {texturePath}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存地图纹理时发生错误: {e.Message}");
        }
        finally
        {
            // 恢复相机状态
            RestoreCameraState(mapCamera, camera, originalEnabled, originalActive, originalTarget);
            EditorUtility.ClearProgressBar();
        }
    }
    
    /// <summary>
    /// 恢复相机状态
    /// </summary>
    private void RestoreCameraState(TopViewMapCamera mapCamera, Camera camera, 
        bool originalEnabled, bool originalActive, RenderTexture originalTarget)
    {
        mapCamera.SetIgnoreListEnabled(true);
        camera.enabled = originalEnabled;
        camera.gameObject.SetActive(originalActive);
        camera.targetTexture = originalTarget;
    }
    
    /// <summary>
    /// 保存RenderTexture为PNG文件
    /// </summary>
    private bool SaveRenderTextureAsPNG(Camera camera, string filePath)
    {
        try
        {
            camera.Render();
            
            RenderTexture.active = camera.targetTexture;
            Texture2D texture = new Texture2D(camera.targetTexture.width, camera.targetTexture.height, TextureFormat.RGB24, false);
            texture.ReadPixels(new Rect(0, 0, camera.targetTexture.width, camera.targetTexture.height), 0, 0);
            texture.Apply();
            
            byte[] pngData = texture.EncodeToPNG();
            File.WriteAllBytes(filePath, pngData);
            
            DestroyImmediate(texture);
            RenderTexture.active = null;
            
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存PNG文件失败: {e.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 获取相对于Assets目录的路径
    /// </summary>
    private string GetRelativeAssetPath(string absolutePath)
    {
        string assetsPath = Application.dataPath;
        if (absolutePath.StartsWith(assetsPath))
        {
            return "Assets" + absolutePath.Substring(assetsPath.Length);
        }
        return absolutePath;
    }
}

