﻿using UnityEngine;
using System.Collections.Generic;

namespace UniFramework.Editor
{
    /// <summary>
    /// Glyph structure used by BMFont. For more information see http://www.angelcode.com/products/bmfont/
    /// </summary>
    [System.Serializable]
    public class BMGlyph
    {
        public int index;   // Index of this glyph (used by BMFont)
        public int x;       // Offset from the left side of the texture to the left side of the glyph
        public int y;       // Offset from the top of the texture to the top of the glyph
        public int width;   // Glyph's width in pixels
        public int height;  // Glyph's height in pixels
        public int offsetX; // Offset to apply to the cursor's left position before drawing this glyph
        public int offsetY; // Offset to apply to the cursor's top position before drawing this glyph
        public int advance; // How much to move the cursor after printing this character
        public int channel; // Channel mask (in most cases this will be 15 (RGBA, 1+2+4+8)
        public List<int> kerning;

        /// <summary>
        /// Retrieves the special amount by which to adjust the cursor position, given the specified previous character.
        /// </summary>
        public int GetKerning(int previous_char)
        {
            if (kerning != null && previous_char != 0) {
                for (int i = 0, imax = kerning.Count; i < imax; i += 2) {
                    if (kerning[i] == previous_char) {
                        return kerning[i + 1];
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// Add a new kerning entry to the character (or adjust an existing one).
        /// </summary>
        public void SetKerning(int previous_char, int amount)
        {
            if (kerning == null) {
                kerning = new List<int>();
            }

            for (var i = 0; i < kerning.Count; i += 2) {
                if (kerning[i] == previous_char) {
                    kerning[i + 1] = amount;
                    return;
                }
            }

            kerning.Add(previous_char);
            kerning.Add(amount);
        }

        /// <summary>
        /// Trim the glyph, given the specified minimum and maximum dimensions in pixels.
        /// </summary>
        public void Trim(int x_min, int y_min, int x_max, int y_max)
        {
            var x1 = x + width;
            var y1 = y + height;

            if (x < x_min) {
                var offset = x_min - x;
                x += offset;
                width -= offset;
                offsetX += offset;
            }

            if (y < y_min) {
                var offset = y_min - y;
                y += offset;
                height -= offset;
                offsetY += offset;
            }

            if (x1 > x_max) {
                width -= x1 - x_max;
            }

            if (y1 > y_max) {
                height -= y1 - y_max;
            }
        }
    }
}
