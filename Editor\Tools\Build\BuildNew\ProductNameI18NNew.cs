﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using UnityEditor.iOS.Xcode;
using UnityEngine;

namespace Nirvana.Editor
{
    [CreateAssetMenu(fileName = "ProductNameI18NNew", menuName = "Nirvana/Build/ProductNameI18NNew")]
    public sealed class ProductNameI18NNew : ScriptableObject
    {
        internal void ActiveIOSSetting(string pathToBuiltProject)
        {
            string text = pathToBuiltProject + "/Info.plist";
            PlistDocument plistDocument = new PlistDocument();
            plistDocument.ReadFromFile(text);
            PlistElementDict plistElementDict = plistDocument.root.AsDict();
            plistElementDict.SetBoolean("LSHasLocalizedDisplayName", true);
            plistDocument.WriteToFile(text);
            foreach (NameEntry nameEntry in this.productNames)
            {
                string text2;
                bool flag = !IOSStrings.TryGetValue(nameEntry.Language, out text2);
                if (!flag)
                {
                    bool flag2 = string.IsNullOrEmpty(text2);
                    if (!flag2)
                    {
                        string text3 = string.Format("{0}/{1}.lproj", pathToBuiltProject, text2);
                        bool flag3 = !Directory.Exists(text3);
                        if (flag3)
                        {
                            Directory.CreateDirectory(text3);
                        }
                        string path = text3 + "/InfoPlist.strings";
                        string contents = "\"CFBundleDisplayName\" = \"" + nameEntry.Name + "\";\n";
                        File.WriteAllText(path, contents);
                    }
                }
            }
        }

        internal void ActiveAndroidSetting()
        {
            foreach (NameEntry nameEntry in this.productNames)
            {
                string text;
                bool flag = !AndroidStrings.TryGetValue(nameEntry.Language, out text);
                if (!flag)
                {
                    bool flag2 = string.IsNullOrEmpty(text);
                    if (!flag2)
                    {
                        string stringFile = string.Format("Assets/Plugins/Android/res/{0}/strings.xml", text);
                        this.WriteAppNameForAndroid(stringFile, nameEntry.Name);
                    }
                }
            }
        }

        private void WriteAppNameForAndroid(string stringFile, string productName)
        {
            bool flag = !File.Exists(stringFile);
            if (flag)
            {
                string directoryName = Path.GetDirectoryName(stringFile);
                bool flag2 = !Directory.Exists(directoryName);
                if (flag2)
                {
                    Directory.CreateDirectory(directoryName);
                }
                string format = "<resources>\r\n    <string name=\"app_name\">{0}</string> +\r\n</resources>";
                string contents = string.Format(format, productName);
                File.WriteAllText(stringFile, contents);
            }
            else
            {
                XmlDocument xmlDocument = new XmlDocument();
                xmlDocument.Load(stringFile);
                XmlElement xmlElement = xmlDocument["resources"];
                bool flag3 = xmlElement == null;
                if (flag3)
                {
                    string message = string.Format("The {0} format error.", stringFile);
                    throw new Exception(message);
                }
                bool flag4 = false;
                foreach (object obj in xmlElement.ChildNodes)
                {
                    XmlNode xmlNode = (XmlNode)obj;
                    bool flag5 = xmlNode.Name != "string";
                    if (!flag5)
                    {
                        XmlAttribute xmlAttribute = xmlNode.Attributes["name"];
                        bool flag6 = xmlAttribute == null;
                        if (!flag6)
                        {
                            bool flag7 = xmlAttribute.Value == "app_name";
                            if (flag7)
                            {
                                xmlNode.Value = productName;
                                flag4 = true;
                            }
                        }
                    }
                }
                bool flag8 = !flag4;
                if (flag8)
                {
                    XmlElement xmlElement2 = xmlDocument.CreateElement("string");
                    xmlElement2.SetAttribute("name", "app_name");
                    xmlElement2.InnerText = productName;
                    xmlElement.AppendChild(xmlElement2);
                }
                string directoryName2 = Path.GetDirectoryName(stringFile);
                bool flag9 = !Directory.Exists(directoryName2);
                if (flag9)
                {
                    Directory.CreateDirectory(directoryName2);
                }
                xmlDocument.Save(stringFile);
            }
        }

        private static readonly Dictionary<SystemLanguage, string> AndroidStrings = new Dictionary<SystemLanguage, string>
        {
            { SystemLanguage.Afrikaans, "values-af-rZA" },
            { SystemLanguage.Arabic, "values-ar-rEG" },
            { SystemLanguage.Basque, "values-eu-rES" },
            { SystemLanguage.Belarusian, "values-be-rBY" },
            { SystemLanguage.Bulgarian, "values-bg-rBG" },
            { SystemLanguage.Catalan, "values-ca-rES" },
            { SystemLanguage.Chinese, "values-zh-rCN" },
            { SystemLanguage.Czech, "values-cs-rCZ" },
            { SystemLanguage.Danish, "values-da-rDK" },
            { SystemLanguage.Dutch, "values-nl-rNL" },
            { SystemLanguage.English, "values-en-rUS" },
            { SystemLanguage.Estonian, "values-et-rEE" },
            { SystemLanguage.Faroese, "values-fo-rFO" },
            { SystemLanguage.Finnish, "values-fi-rFI" },
            { SystemLanguage.French, "values-fr-rFR" },
            { SystemLanguage.German, "values-de-rDE" },
            { SystemLanguage.Greek, "values-el-rGR" },
            { SystemLanguage.Hebrew, "values-iw-rIL" },
            { SystemLanguage.Hungarian, "values-hu-rHU" },
            { SystemLanguage.Icelandic, "values-is-rIS" },
            { SystemLanguage.Indonesian, "values-in-rID" },
            { SystemLanguage.Italian, "values-it-rIT" },
            { SystemLanguage.Japanese, "values-ja-rJP" },
            { SystemLanguage.Korean, "values-ko-rKR" },
            { SystemLanguage.Latvian, "values-lv-rLV" },
            { SystemLanguage.Lithuanian, "valueslt-rLT" },
            { SystemLanguage.Norwegian, "values-nb-rNO" },
            { SystemLanguage.Polish, "values-pl-rPL" },
            { SystemLanguage.Portuguese, "values-pt-rPT" },
            { SystemLanguage.Romanian, "values-ro-rRO" },
            { SystemLanguage.Russian, "values-ru-rRU" },
            { SystemLanguage.SerboCroatian, "values-sr-rRS" },
            { SystemLanguage.Slovak, "values-sk-rSK" },
            { SystemLanguage.Slovenian, "values-sl-rSI" },
            { SystemLanguage.Spanish, "values-es-rES" },
            { SystemLanguage.Swedish, "values-sv-rSE" },
            { SystemLanguage.Thai, "values-th-rTH" },
            { SystemLanguage.Turkish, "values–r-rTR" },
            { SystemLanguage.Ukrainian, "values-uk-rUA" },
            { SystemLanguage.Vietnamese, "values-vi-rVN" },
            { SystemLanguage.ChineseSimplified, "values-zh-rCN" },
            { SystemLanguage.ChineseTraditional, "values-zh-rTW"}
        };

        private static readonly Dictionary<SystemLanguage, string> IOSStrings = new Dictionary<SystemLanguage, string>
        {
            { SystemLanguage.Afrikaans, "" },
            { SystemLanguage.Arabic, "ar" },
            { SystemLanguage.Basque, "" },
            { SystemLanguage.Belarusian, "" },
            { SystemLanguage.Bulgarian, "" },
            { SystemLanguage.Catalan, "ca" },
            { SystemLanguage.Chinese, "zh-Hans" },
            { SystemLanguage.Czech, "cs" },
            { SystemLanguage.Danish, "da" },
            { SystemLanguage.Dutch, "nl" },
            { SystemLanguage.English, "en" },
            { SystemLanguage.Estonian, "" },
            { SystemLanguage.Faroese, "" },
            { SystemLanguage.Finnish, "fi" },
            { SystemLanguage.French, "fr" },
            { SystemLanguage.German, "de" },
            { SystemLanguage.Greek, "el" },
            { SystemLanguage.Hebrew, "he" },
            { SystemLanguage.Hungarian, "hu" },
            { SystemLanguage.Icelandic, "" },
            { SystemLanguage.Indonesian, "" },
            { SystemLanguage.Italian, "it" },
            { SystemLanguage.Japanese, "ja" },
            { SystemLanguage.Korean, "ko" },
            { SystemLanguage.Latvian, "" },
            { SystemLanguage.Lithuanian, "" },
            { SystemLanguage.Norwegian, "nb" },
            { SystemLanguage.Polish, "pl" },
            { SystemLanguage.Portuguese, "pt-PT" },
            { SystemLanguage.Romanian, "ro" },
            { SystemLanguage.Russian, "ru" },
            { SystemLanguage.SerboCroatian, "" },
            { SystemLanguage.Slovak, "sk" },
            { SystemLanguage.Slovenian, "" },
            { SystemLanguage.Spanish, "es" },
            { SystemLanguage.Swedish, "sv" },
            { SystemLanguage.Thai, "th" },
            { SystemLanguage.Turkish, "" },
            { SystemLanguage.Ukrainian, "uk" },
            { SystemLanguage.Vietnamese, "vi" },
            { SystemLanguage.ChineseSimplified, "zh-Hans" },
            { SystemLanguage.ChineseTraditional, "zh-Hans" }
        };

        [SerializeField]
        [Tooltip("The localization product names.")]
        private ProductNameI18NNew.NameEntry[] productNames;

        [Serializable]
        private struct NameEntry
        {
            [SerializeField]
            [Tooltip("The language for application.")]
            public SystemLanguage Language;

            [SerializeField]
            [Tooltip("The application name for this language.")]
            public string Name;
        }
    }
}
