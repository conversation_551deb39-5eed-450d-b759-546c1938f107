﻿#require_file#

#class_name# = #class_name# or BaseClass(BaseWGCtrl)

function #class_name#:__init()
	if #class_name#.Instance then
		print_error("[#class_name#]:Attempt to create singleton twice!")
	end

	#class_name#.Instance = self
    self.data = #data_class_name#.New()
    self.view = #view_class_name#.New(GuideModuleName.#view_class_name#)

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function #class_name#:__delete()
    self:UnRegisterAllEvents()

    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

    #class_name#.Instance = nil
end

--------------------------protocol_start---------------------------
function #class_name#:RegisterAllProtocols()
    self:RegisterProtocol(CSXXXReq)
	self:RegisterProtocol(SCXXXInfo, "OnSCXXXInfo")
end

function #class_name#:SendOperateReq(operate_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXXXReq)
	protocol.operate_type = operate_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

-- 接收协议
function #class_name#:OnSCXXXInfo(protocol)
    -- self.data:SetXXXInfo(protocol)
    -- RemindManager.Instance:Fire(RemindName.XXX)
end
----------------------------protocol_end---------------------------

----------------------------event_start----------------------------
function #class_name#:RegisterAllEvents()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function #class_name#:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function #class_name#:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        -- do
	end
end
-----------------------------event_end-----------------------------