using System.Collections;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 摄像机效果管理器
/// 负责震屏和径向模糊效果的配置和播放
/// </summary>
public class SkillEditorCameraEffectManager
{
    #region 私有字段
    private SkillEditorPrefabDataConfig _dataConfig;
    private SkillEditorModelManager _modelManager;
    private int _selectShakeId = 0;
    private int _selectRadialBlurId = 0;
    private Vector2 _shakeScroll;
    private Vector2 _radialBlurScroll;
    private Transform _volumeTransform;
    private VolumeController _volumeController;
    #endregion

    #region 属性
    public int SelectedShakeId => _selectShakeId;
    public int SelectedRadialBlurId => _selectRadialBlurId;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化摄像机效果管理器
    /// </summary>
    public void Initialize(SkillEditorPrefabDataConfig dataConfig, SkillEditorModelManager modelManager)
    {
        _dataConfig = dataConfig;
        _modelManager = modelManager;
        _volumeTransform = GameObject.Find(SkillEditorGUIConfig.VOLUME_NAME)?.transform;
    }
    #endregion

    #region 震屏管理
    /// <summary>
    /// 绘制震屏管理界面
    /// </summary>
    public void DrawCameraShakesWindow()
    {
        GUILayout.BeginVertical();
        DrawAddShakeButton();
        DrawShakeList();
        GUILayout.EndVertical();

        DrawDeleteShakeButton();
    }

    /// <summary>
    /// 绘制添加震屏按钮
    /// </summary>
    private void DrawAddShakeButton()
    {
        if (GUILayout.Button("增加Shake", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewShake();
        }
    }

    /// <summary>
    /// 绘制震屏列表
    /// </summary>
    private void DrawShakeList()
    {
        if (_dataConfig?.actorTriggers?.cameraShakes == null) return;

        _shakeScroll = SkillEditorGUI.DrawScrollList(
            _shakeScroll,
            _dataConfig.actorTriggers.cameraShakes,
            shake => SkillEditorGUIConfig.GetDefaultButtonName(shake.CameraShakeBtnName),
            OnShakeSelected);
    }

    /// <summary>
    /// 绘制删除震屏按钮
    /// </summary>
    private void DrawDeleteShakeButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect1);

        if (_dataConfig?.actorTriggers?.cameraShakes?.Count > 0)
        {
            if (GUILayout.Button("删除Shake", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedShake();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制震屏详细配置
    /// </summary>
    public void DrawShakeDetails()
    {
        if (!IsValidShakeSelection()) return;

        var cameraShake = _dataConfig.actorTriggers.cameraShakes[_selectShakeId];
        DrawShakeSettings(cameraShake);
    }

    /// <summary>
    /// 绘制震屏设置
    /// </summary>
    private void DrawShakeSettings(SkillEditorPrefabDataConfig.CameraShakeData cameraShake)
    {
        cameraShake.CameraShakeBtnName = SkillEditorGUI.DrawButtonNameField(cameraShake.CameraShakeBtnName);
        cameraShake.eventName = SkillEditorGUI.DrawEventSelector(cameraShake.eventName);
        cameraShake.numberOfShakes = EditorGUILayout.IntField("震屏次数", cameraShake.numberOfShakes);
        cameraShake.distance = EditorGUILayout.FloatField("震屏距离", cameraShake.distance);
        cameraShake.speed = EditorGUILayout.FloatField("震屏时间(毫秒)", cameraShake.speed);
        cameraShake.delay = SkillEditorGUI.DrawDelayField("延迟时间", cameraShake.delay);
        cameraShake.decay = EditorGUILayout.FloatField("衰减值(0-1的值)", cameraShake.decay);
    }

    /// <summary>
    /// 添加新震屏
    /// </summary>
    private void AddNewShake()
    {
        if (_dataConfig?.actorTriggers?.cameraShakes == null) return;

        var cameraShakeData = new SkillEditorPrefabDataConfig.CameraShakeData();
        _dataConfig.actorTriggers.cameraShakes.Add(cameraShakeData);
    }

    /// <summary>
    /// 删除选中的震屏
    /// </summary>
    private void DeleteSelectedShake()
    {
        if (_selectShakeId == -1 || _dataConfig.actorTriggers.cameraShakes.Count == 0)
            return;

        _dataConfig.actorTriggers.cameraShakes.RemoveAt(_selectShakeId);
        _selectShakeId = _dataConfig.actorTriggers.cameraShakes.Count == 0
            ? -1
            : _dataConfig.actorTriggers.cameraShakes.Count - 1;
    }

    /// <summary>
    /// 震屏选择回调
    /// </summary>
    private void OnShakeSelected(int index)
    {
        _selectShakeId = index;
    }

    /// <summary>
    /// 验证震屏选择是否有效
    /// </summary>
    private bool IsValidShakeSelection()
    {
        return _selectShakeId != -1 &&
               _dataConfig?.actorTriggers?.cameraShakes != null &&
               _selectShakeId < _dataConfig.actorTriggers.cameraShakes.Count;
    }
    #endregion

    #region 径向模糊管理
    /// <summary>
    /// 绘制径向模糊管理界面
    /// </summary>
    public void DrawRadialBlursWindow()
    {
        GUILayout.BeginVertical();
        DrawAddRadialBlurButton();
        DrawRadialBlurList();
        GUILayout.EndVertical();

        DrawDeleteRadialBlurButton();
    }

    /// <summary>
    /// 绘制添加径向模糊按钮
    /// </summary>
    private void DrawAddRadialBlurButton()
    {
        if (GUILayout.Button("增加模糊", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewRadialBlur();
        }
    }

    /// <summary>
    /// 绘制径向模糊列表
    /// </summary>
    private void DrawRadialBlurList()
    {
        if (_dataConfig?.actorTriggers?.radialBlurs == null) return;

        _radialBlurScroll = SkillEditorGUI.DrawScrollList(
            _radialBlurScroll,
            _dataConfig.actorTriggers.radialBlurs,
            blur => SkillEditorGUIConfig.GetDefaultButtonName(blur.btnName),
            OnRadialBlurSelected);
    }

    /// <summary>
    /// 绘制删除径向模糊按钮
    /// </summary>
    private void DrawDeleteRadialBlurButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect1);

        if (_dataConfig?.actorTriggers?.radialBlurs?.Count > 0)
        {
            if (GUILayout.Button("删除模糊", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedRadialBlur();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制径向模糊详细配置
    /// </summary>
    public void DrawRadialBlurDetails()
    {
        if (!IsValidRadialBlurSelection()) return;

        var radialBlur = _dataConfig.actorTriggers.radialBlurs[_selectRadialBlurId];
        DrawRadialBlurSettings(radialBlur);
    }

    /// <summary>
    /// 绘制径向模糊设置
    /// </summary>
    private void DrawRadialBlurSettings(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        radialBlur.btnName = SkillEditorGUI.DrawButtonNameField(radialBlur.btnName);
        radialBlur.eventName = SkillEditorGUI.DrawEventSelector(radialBlur.eventName);
        radialBlur.isRole = EditorGUILayout.Toggle("展示点是否是玩家", radialBlur.isRole);

        if (radialBlur.isRole)
        {
            DrawRadialBlurNodeSelector(radialBlur);
        }
        else
        {
            radialBlur.referenceNodeHierarchyPath = string.Empty;
        }

        DrawRadialBlurTimingSettings(radialBlur);
    }

    /// <summary>
    /// 绘制径向模糊节点选择器
    /// </summary>
    private void DrawRadialBlurNodeSelector(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        Transform node = SkillEditorGUI.DrawNodeSelector(
            "节点", 
            radialBlur.referenceNodeHierarchyPath, 
            _modelManager.MainAnimator?.transform);
        
        radialBlur.referenceNodeHierarchyPath = SkillEditorGUI.UpdateNodePath(
            node, 
            _modelManager.MainAnimator?.transform);
    }

    /// <summary>
    /// 绘制径向模糊时间设置
    /// </summary>
    private void DrawRadialBlurTimingSettings(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        radialBlur.delay = SkillEditorGUI.DrawDelayField("延迟时间", radialBlur.delay);
        radialBlur.strength = EditorGUILayout.FloatField("模糊强度", radialBlur.strength);
        radialBlur.riseTime = EditorGUILayout.FloatField("从0达到强度值的时间", radialBlur.riseTime);
        radialBlur.holdTime = EditorGUILayout.FloatField("持续时间", radialBlur.holdTime);
        radialBlur.fallTime = EditorGUILayout.FloatField("结束耗时", radialBlur.fallTime);
    }

    /// <summary>
    /// 添加新径向模糊
    /// </summary>
    private void AddNewRadialBlur()
    {
        if (_dataConfig?.actorTriggers?.radialBlurs == null) return;

        var radialBlurData = new SkillEditorPrefabDataConfig.RadialBlurData();
        _dataConfig.actorTriggers.radialBlurs.Add(radialBlurData);
    }

    /// <summary>
    /// 删除选中的径向模糊
    /// </summary>
    private void DeleteSelectedRadialBlur()
    {
        if (_selectRadialBlurId == -1 || _dataConfig.actorTriggers.radialBlurs.Count == 0)
            return;

        _dataConfig.actorTriggers.radialBlurs.RemoveAt(_selectRadialBlurId);
        _selectRadialBlurId = _dataConfig.actorTriggers.radialBlurs.Count == 0
            ? -1
            : _dataConfig.actorTriggers.radialBlurs.Count - 1;
    }

    /// <summary>
    /// 径向模糊选择回调
    /// </summary>
    private void OnRadialBlurSelected(int index)
    {
        _selectRadialBlurId = index;
    }

    /// <summary>
    /// 验证径向模糊选择是否有效
    /// </summary>
    private bool IsValidRadialBlurSelection()
    {
        return _selectRadialBlurId != -1 &&
               _dataConfig?.actorTriggers?.radialBlurs != null &&
               _selectRadialBlurId < _dataConfig.actorTriggers.radialBlurs.Count;
    }
    #endregion

    #region 效果播放
    /// <summary>
    /// 播放震屏事件
    /// </summary>
    public void PlayCameraShakeEvents(string eventName)
    {
        if (_dataConfig?.actorTriggers?.cameraShakes == null) return;

        foreach (var shake in _dataConfig.actorTriggers.cameraShakes)
        {
            if (shake.eventName == eventName)
            {
                PlayCameraShakeWithDelay(shake);
            }
        }
    }

    /// <summary>
    /// 播放径向模糊事件
    /// </summary>
    public void PlayRadialBlurEvents(string eventName)
    {
        if (_dataConfig?.actorTriggers?.radialBlurs == null) return;

        foreach (var radialBlur in _dataConfig.actorTriggers.radialBlurs)
        {
            if (radialBlur.eventName == eventName)
            {
                PlayRadialBlurWithDelay(radialBlur);
            }
        }
    }

    /// <summary>
    /// 带延迟播放震屏
    /// </summary>
    private void PlayCameraShakeWithDelay(SkillEditorPrefabDataConfig.CameraShakeData shake)
    {
        if (shake.delay > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(shake.delay, () => PlayCameraShake(shake)));
        }
        else
        {
            PlayCameraShake(shake);
        }
    }

    /// <summary>
    /// 播放震屏
    /// </summary>
    private void PlayCameraShake(SkillEditorPrefabDataConfig.CameraShakeData shake)
    {
        if (Camera.main?.isActiveAndEnabled == true)
        {
            CameraShake.Shake(
                shake.numberOfShakes,
                Vector3.one,
                Vector3.zero,
                shake.distance,
                shake.speed,
                shake.decay,
                1.0f,
                true);
        }
    }

    /// <summary>
    /// 带延迟播放径向模糊
    /// </summary>
    private void PlayRadialBlurWithDelay(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        if (radialBlur.delay > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(radialBlur.delay, () => PlayRadialBlur(radialBlur)));
        }
        else
        {
            PlayRadialBlur(radialBlur);
        }
    }

    /// <summary>
    /// 播放径向模糊
    /// </summary>
    private void PlayRadialBlur(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        if (_volumeTransform == null) return;

        _volumeController = _volumeTransform.GetComponent<VolumeController>();
        if (_volumeController == null || radialBlur == null) return;

        if (!string.IsNullOrEmpty(radialBlur.referenceNodeHierarchyPath))
        {
            PlayRadialBlurAtNode(radialBlur);
        }
        else
        {
            PlayRadialBlurAtScreenCenter(radialBlur);
        }
    }

    /// <summary>
    /// 在指定节点播放径向模糊
    /// </summary>
    private void PlayRadialBlurAtNode(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        Transform node = SkillEditorUtils.FindTransformFromPath(
            _modelManager.MainAnimator?.transform, 
            radialBlur.referenceNodeHierarchyPath);
        
        if (node != null)
        {
            _volumeController.DoRadialBlur(
                node.position,
                radialBlur.riseTime,
                radialBlur.holdTime,
                radialBlur.fallTime,
                radialBlur.strength);
        }
    }

    /// <summary>
    /// 在屏幕中心播放径向模糊
    /// </summary>
    private void PlayRadialBlurAtScreenCenter(SkillEditorPrefabDataConfig.RadialBlurData radialBlur)
    {
        Vector2 screenCenter = new Vector2(0.5f, 0.5f);
        _volumeController.DoRadialBlur(
            screenCenter,
            radialBlur.riseTime,
            radialBlur.holdTime,
            radialBlur.fallTime,
            radialBlur.strength);
    }
    #endregion
}
