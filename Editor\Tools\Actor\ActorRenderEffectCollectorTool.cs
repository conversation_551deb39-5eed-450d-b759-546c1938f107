using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;

/// <summary>
/// ActorRender特效收集工具
/// 遍历指定目录下的预制体，对包含ActorRender组件的预制体执行CollectEffectAttachments方法
/// </summary>
public class ActorRenderEffectCollectorTool : OdinEditorWindow
{
    [MenuItem("Tools/角色工具/ActorRender特效收集工具")]
    private static void OpenWindow()
    {
        GetWindow<ActorRenderEffectCollectorTool>("ActorRender特效收集工具").Show();
    }

    #region 界面配置

    [TitleGroup("目录设置")]
    [FolderPath]
    [LabelText("预制体目录")]
    [InfoBox("选择包含需要处理预制体的目录")]
    public string targetDirectory = "Assets/Game/Actors";

    [TitleGroup("目录设置")]
    [LabelText("包含子目录")]
    [InfoBox("是否递归遍历子目录")]
    public bool includeSubDirectories = true;

    #endregion

    #region 处理结果

    [TitleGroup("处理结果")]
    [ReadOnly]
    [LabelText("总预制体数量")]
    public int totalPrefabCount = 0;

    [TitleGroup("处理结果")]
    [ReadOnly]
    [LabelText("包含ActorRender的预制体")]
    public int actorRenderPrefabCount = 0;

    [TitleGroup("处理结果")]
    [ReadOnly]
    [LabelText("成功处理数量")]
    public int successCount = 0;

    [TitleGroup("处理结果")]
    [ReadOnly]
    [LabelText("失败数量")]
    public int failureCount = 0;

    [TitleGroup("处理结果")]
    [ReadOnly]
    [ShowIf("@processResults.Count > 0")]
    [ListDrawerSettings(ShowIndexLabels = true, ShowPaging = true, NumberOfItemsPerPage = 20)]
    [LabelText("处理日志")]
    public List<ProcessResult> processResults = new List<ProcessResult>();

    #endregion

    #region 操作按钮

    [TitleGroup("操作")]
    [Button("开始处理", ButtonSizes.Large)]
    [GUIColor(0.4f, 0.8f, 1f)]
    [DisableIf("@string.IsNullOrEmpty(targetDirectory)")]
    private void StartProcessing()
    {
        if (string.IsNullOrEmpty(targetDirectory))
        {
            EditorUtility.DisplayDialog("错误", "请选择目标目录", "确定");
            return;
        }

        if (!AssetDatabase.IsValidFolder(targetDirectory))
        {
            EditorUtility.DisplayDialog("错误", "选择的路径不是有效的资源目录", "确定");
            return;
        }

        ProcessPrefabs();
    }

    [TitleGroup("操作")]
    [Button("清空结果", ButtonSizes.Medium)]
    [EnableIf("@processResults.Count > 0")]
    private void ClearResults()
    {
        processResults.Clear();
        totalPrefabCount = 0;
        actorRenderPrefabCount = 0;
        successCount = 0;
        failureCount = 0;
    }

    #endregion

    #region 数据结构

    [System.Serializable]
    public class ProcessResult
    {
        [LabelText("预制体路径")]
        [ReadOnly]
        public string prefabPath;

        [LabelText("处理状态")]
        [ReadOnly]
        public ProcessStatus status;

        [LabelText("消息")]
        [ReadOnly]
        public string message;

        [LabelText("特效数量")]
        [ReadOnly]
        [ShowIf("@status == ProcessStatus.Success")]
        public int effectCount;

        public ProcessResult(string path, ProcessStatus status, string message = "", int effectCount = 0)
        {
            this.prefabPath = path;
            this.status = status;
            this.message = message;
            this.effectCount = effectCount;
        }
    }

    public enum ProcessStatus
    {
        [LabelText("成功")]
        Success,
        [LabelText("跳过(无ActorRender)")]
        Skipped,
        [LabelText("失败")]
        Failed
    }

    #endregion

    #region 核心处理逻辑

    private void ProcessPrefabs()
    {
        try
        {
            // 清空之前的结果
            ClearResults();

            // 查找所有预制体
            string[] searchDirs = { targetDirectory };
            string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab", searchDirs);
            
            totalPrefabCount = prefabGuids.Length;

            if (totalPrefabCount == 0)
            {
                EditorUtility.DisplayDialog("提示", "在指定目录中没有找到预制体", "确定");
                return;
            }

            Debug.Log($"[ActorRenderEffectCollector] 开始处理 {totalPrefabCount} 个预制体");

            // 处理每个预制体
            for (int i = 0; i < prefabGuids.Length; i++)
            {
                string guid = prefabGuids[i];
                string prefabPath = AssetDatabase.GUIDToAssetPath(guid);

                // 更新进度条
                float progress = (float)i / prefabGuids.Length;
                string progressTitle = $"处理预制体 ({i + 1}/{prefabGuids.Length})";
                string progressInfo = Path.GetFileName(prefabPath);
                
                if (EditorUtility.DisplayCancelableProgressBar(progressTitle, progressInfo, progress))
                {
                    Debug.Log("[ActorRenderEffectCollector] 用户取消了操作");
                    break;
                }

                // 过滤子目录
                if (!includeSubDirectories && !IsDirectChild(prefabPath, targetDirectory))
                {
                    continue;
                }

                ProcessSinglePrefab(prefabPath);
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // 显示完成对话框
            string resultMessage = $"处理完成！\n" +
                                 $"总预制体数: {totalPrefabCount}\n" +
                                 $"包含ActorRender: {actorRenderPrefabCount}\n" +
                                 $"成功处理: {successCount}\n" +
                                 $"失败: {failureCount}";

            EditorUtility.DisplayDialog("处理完成", resultMessage, "确定");
            Debug.Log($"[ActorRenderEffectCollector] {resultMessage}");
        }
        catch (Exception ex)
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError($"[ActorRenderEffectCollector] 处理过程中发生错误: {ex.Message}");
            EditorUtility.DisplayDialog("错误", $"处理过程中发生错误:\n{ex.Message}", "确定");
        }
    }

    private void ProcessSinglePrefab(string prefabPath)
    {
        try
        {
            // 加载预制体
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            if (prefab == null)
            {
                processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Failed, "无法加载预制体"));
                failureCount++;
                return;
            }

            // 检查是否包含ActorRender组件
            ActorRender actorRender = prefab.GetComponent<ActorRender>();
            if (actorRender == null)
            {
                processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Skipped, "未找到ActorRender组件"));
                return;
            }

            actorRenderPrefabCount++;

            // 实例化预制体进行处理
            GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            if (instance == null)
            {
                processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Failed, "无法实例化预制体"));
                failureCount++;
                return;
            }

            try
            {
                // 获取实例上的ActorRender组件
                ActorRender instanceActorRender = instance.GetComponent<ActorRender>();
                if (instanceActorRender == null)
                {
                    processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Failed, "实例中未找到ActorRender组件"));
                    failureCount++;
                    return;
                }

                // 调用CollectEffectAttachments方法
                instanceActorRender.CollectEffectAttachments();
                
                // 获取收集到的特效数量
                int effectCount = instanceActorRender.EffectAttachments.Count;

                // 应用修改到预制体
                PrefabUtility.ApplyPrefabInstance(instance, InteractionMode.AutomatedAction);

                // 标记为已修改并记录成功
                EditorUtility.SetDirty(prefab);
                processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Success, "成功收集特效数据", effectCount));
                successCount++;

                Debug.Log($"[ActorRenderEffectCollector] 成功处理: {prefabPath}, 收集到 {effectCount} 个特效");
            }
            finally
            {
                // 清理实例
                DestroyImmediate(instance);
            }
        }
        catch (Exception ex)
        {
            processResults.Add(new ProcessResult(prefabPath, ProcessStatus.Failed, $"异常: {ex.Message}"));
            failureCount++;
            Debug.LogError($"[ActorRenderEffectCollector] 处理预制体失败 {prefabPath}: {ex.Message}");
        }
    }

    private bool IsDirectChild(string filePath, string parentDirectory)
    {
        string normalizedFilePath = filePath.Replace('\\', '/');
        string normalizedParentDir = parentDirectory.Replace('\\', '/');
        
        if (!normalizedFilePath.StartsWith(normalizedParentDir))
            return false;

        string relativePath = normalizedFilePath.Substring(normalizedParentDir.Length);
        if (relativePath.StartsWith("/"))
            relativePath = relativePath.Substring(1);

        return !relativePath.Contains("/");
    }

    #endregion
} 