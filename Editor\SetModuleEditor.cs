﻿using System.Text.RegularExpressions;
using UnityEditor;
using UnityEditor.AnimatedValues;
using UnityEngine;
using UnityEngine.UI;

public abstract class SetModuleEditor : EditorWindow
{

    [MenuItem("Tools/UI Tools/Set Text &S")]
    static void ShowWindow()
    {
        Component[] components = Selection.activeGameObject.GetComponents<Component>();
        foreach (var component in components)
        {
            if (component is Text)
            {
                TextSet.ShowWindow();
            }
            else if (component is Camera)
            {
                CameraSetEditor.ShowWindow();
            }
        }
    }

    protected string jsonPath;          //保存Json文件的路径
    protected GameObject selectGo;      //被选中的游戏物体
    protected GUIStyle guiStyle;        //OnGUI函数需要的标题格式
    protected AnimBool fadeGroup;       //折叠动画
    protected Vector2 scroll;           //滚动条
    protected string newModuleName;     //新模板的命名

    protected virtual void Awake()
    {
        guiStyle = new GUIStyle();
        guiStyle.normal.textColor = Color.white;
        guiStyle.fontStyle = FontStyle.BoldAndItalic;

        fadeGroup = new AnimBool();
        fadeGroup.valueChanged.AddListener(Repaint);
    }

    void OnInspectorUpdate()
    {
        Repaint();
    }

    /// <summary>
    /// 根据类名找到对应的目录
    /// </summary>
    /// <param name="className"></param>
    /// <returns></returns>
    protected string SearchThisCSharpDirectory(string className)
    {
        string[] ids = AssetDatabase.FindAssets("t:Script");
        string tmpath = null;
        foreach (var id in ids)
        {
            tmpath = AssetDatabase.GUIDToAssetPath(id);
            if (Regex.IsMatch(tmpath, this.GetType().ToString()))
            {
                tmpath = Regex.Replace(tmpath, "cs", "json");
                return tmpath;
            }
        }
        return tmpath;
    }

    protected abstract void AddModule(string moduleName);
    protected abstract void RemoveModule(string moduleName);
    protected abstract void LoadModule(string moduleName);
}
