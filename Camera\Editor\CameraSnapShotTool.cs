﻿using System;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

class CameraSnapShotTool
{
    [MenuItem("自定义工具/Game视图截屏", priority = 0)]
    public static void CameraSnapShot()
    {
         UtilU3d.ScreenshotByPng((tex) => {
            string fileName = "Screenshot-" + DateTime.Now.TimeOfDay.ToString();
            fileName = fileName.Replace(":", "-").Replace(".", "-");
            string path = string.Format("{0}/{1}/{2}.png", Application.persistentDataPath, "Screenshot", fileName);
            //ScreenCapture.CaptureScreenshot(path);
            UnityEngine.Debug.Log("截图已保存：" + path);
            Process.Start("explorer.exe", Application.persistentDataPath.Replace("/", "\\") + "\\Screenshot");
            UtilU3d.SaveScreenshot(tex, path, saveByPNG:true);
        });
    }
}