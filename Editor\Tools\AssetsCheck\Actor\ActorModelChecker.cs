﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEditor;

namespace AssetsCheck
{
    class ActorModelChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/Actors", "Assets/Game/Model", };

        override public string GetErrorDesc()
        {
            return string.Format("1.角色相关模型网格没有压缩 2.isReadable被开启 3.没有开启optimizeMesh 4.没有开启weldVertices 5.resampleCurves被开启");
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:model", checkDirs);

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;

                CheckItem item = new CheckItem();
                item.asset = path;

                item.meshCompression = importer.meshCompression;
                item.readWriteEnabled = importer.isReadable;
                item.optimizeMesh = importer.optimizeMesh;
                item.weldVertices = importer.weldVertices;
                item.resampleCurves = importer.resampleCurves;

                if (item.meshCompression <= ModelImporterMeshCompression.Off
                    || item.readWriteEnabled 
                    || !item.optimizeMesh
                    || !item.weldVertices
                    || item.resampleCurves)
                {
                    this.outputList.Add(item);
                }
            }
        }

        protected override void OnFix(string[] lines)
        {
            int count = 0;
            int total = lines.Length;
            for (int i = 0; i < lines.Length; i++)
            {
                string[] path = lines[i].Split(' ');
                ModelImporter importer = AssetImporter.GetAtPath(path[0]) as ModelImporter;
                if (importer.isReadable || importer.resampleCurves)
                {
                    importer.isReadable = false;
                    importer.resampleCurves = false;
                    importer.SaveAndReimport();
                }
                count++;
                EditorUtility.DisplayProgressBar("修复FBX资源中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
            }
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public ModelImporterMeshCompression meshCompression;
            public bool readWriteEnabled;
            public bool optimizeMesh;
            public bool weldVertices;
            public bool resampleCurves;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(asset);
                if (meshCompression <= ModelImporterMeshCompression.Off)
                        builder.Append(string.Format("   meshCompression={0}", meshCompression));

                if (readWriteEnabled) builder.Append("   readWriteEnabled=true");
                if (!optimizeMesh) builder.Append("   optimizeMesh=false");
                if (!weldVertices) builder.Append("   weldVertices=false");
                if (resampleCurves) builder.Append("   resampleCurves=true");

                return builder;
            }
        }
    }
}
