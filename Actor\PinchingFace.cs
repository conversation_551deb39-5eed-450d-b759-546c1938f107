using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

[ExecuteAlways]
public class PinchingFace : MonoBehaviour
{
    public SkinnedMeshRenderer renderer;
    public List<ShapeWeight> shapeWeights = new List<ShapeWeight>();
    
    public Transform[] bones;
    
    public enum BoneShapeType
    {
        Vertical,
    }
    
    [Serializable]
    public class BoneShape
    {
        public int boneIndex;
        public BoneShapeType type;
        public float maxValue;
        public float minValue;
        public float middleValue;
    }

    [Serializable]
    public struct ShapeWeight
    {
        public string displayName;
        [HideInInspector]
        public int upShapeIndex;
        [HideInInspector]
        public int downShapeIndex;
        [Range(-100, 100)]
        public int weight;
        [SerializeField]
        public BoneShape[] boneShapes;
    }

    public class ShapeMap : Dictionary<string, (string, string)>
    { }

    private static ShapeMap s_ShapeMap = new ShapeMap()
    {
        {"眼睛大小", ("eye+", "eye-")},
        {"眼睛上下", ("eyepos+", "eyepos-")},
        {"眼睛角度", ("eyeangle+", "eyeangle-")},
        {"眼睛开合", ("eyeclose+", "eyeclose-")},
        {"嘴巴大小", ("mousesize+", "mousesize-")},
        {"嘴巴上下", ("mousepos+", "mousepos-")},
        {"鼻子大小", ("nosesize+", "nosesize-")},
        {"鼻子角度", ("noseangle+", "noseangle-")},
        {"眉毛角度", ("eyebrowangle+", "eyebrowangle-")},
        {"嘴角", ("mouthangle+", "mouthangle-")},
        {"脸颊", ("cheek+", "cheek-")},
        {"下巴", ("chin+", "chin-")},
    };

    public void OnEnable()
    {
        renderer = GetComponent<SkinnedMeshRenderer>();
    }
    
    public void Update()
    {
        for (int i = 0; i < shapeWeights.Count; i++)
        {
            var data = shapeWeights[i];
            float upShapeValue;
            float downShapeValue;
            if (data.weight >= 0)
            {
                upShapeValue = data.weight;
                downShapeValue = 0;
            }
            else
            {
                upShapeValue = 0;
                downShapeValue = -data.weight;
            }

            if (data.upShapeIndex != -1)
                renderer.SetBlendShapeWeight(data.upShapeIndex, upShapeValue);
            if (data.downShapeIndex != -1)
                renderer.SetBlendShapeWeight(data.downShapeIndex, downShapeValue);
            
            if (data.boneShapes != null)
            {
                for (int j = 0; j < data.boneShapes.Length; j++)
                {
                    var boneShape = data.boneShapes[j];
                    if (boneShape.boneIndex >= bones.Length)
                        continue;
                    
                    var bone = bones[boneShape.boneIndex];
                    if (bone == null)
                        continue;

                    float value;
                    if (data.weight >= 0)
                        value = Mathf.Lerp(boneShape.middleValue, boneShape.maxValue, data.weight * 0.01f);
                    else
                        value = Mathf.Lerp(boneShape.middleValue, boneShape.minValue, data.weight * -0.01f);
                       
                    switch (boneShape.type)
                    {
                        case BoneShapeType.Vertical:
                            Vector3 localPos = bone.localPosition;
                            localPos.y = value;
                            bone.localPosition = localPos;
                            break;
                    }
                }
            }
        }
    }
    
    [ContextMenu("Reset")]
    public void Reset()
    {
        Dictionary<string, int> shapeIndexDict = new Dictionary<string, int>();
        for (int i = 0; i < renderer.sharedMesh.blendShapeCount; i++)
        {
            shapeIndexDict.Add(renderer.sharedMesh.GetBlendShapeName(i), i);
        }
        
        shapeWeights.Clear();
        foreach (var pair in s_ShapeMap)
        {
            int upShapeIndex = -1;
            shapeIndexDict.TryGetValue(pair.Value.Item1, out upShapeIndex);
            int downShapeIndex = -1;
            shapeIndexDict.TryGetValue(pair.Value.Item2, out downShapeIndex);

            if (upShapeIndex != -1 && downShapeIndex != -1)
            {
                var data = new ShapeWeight()
                {
                    displayName = pair.Key,
                    upShapeIndex = upShapeIndex,
                    downShapeIndex = downShapeIndex,
                };
                shapeWeights.Add(data);
            }
        }
    }

}
