﻿using UnityEngine;
using UnityEditor;
using System.Text;
using System.Collections.Generic;
using Nirvana;

namespace AssetsCheck
{
    class EffectQualityControlActiveViolateQuoteCheck : BaseChecker
    {
        private string[] checkDirs = {
           "Assets/Game/Actor",
            "Assets/Game/Model",
            "Assets/Game/Effects2/Prefab",
        };

        public override string GetErrorDesc()
        {
            return "检测特效预制体上的QualityActiveControl绑定资源异常（绑定丢失 或 直接绑定了其他预制体）";
        }

        protected override void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);

            try
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guids[i]);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    if (prefab != null && prefab.GetComponent<ActorRender>() == null)
                    {
                        int instanceID = prefab.GetInstanceID();
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        QualityControlActive[] qualityControlActives = gameObject.GetComponentsInChildren<QualityControlActive>(true);
                        for (int j = 0; j < qualityControlActives.Length; j++)
                        {
                            var controls = qualityControlActives[j].GetControls();
                            if (null != controls)
                            {
                                foreach (var item in controls)
                                {
                                    if (item.Target == null || item.Target.GetInstanceID() > 0)
                                    {
                                        CheckItem checkItem = new CheckItem();
                                        checkItem.asset = assetPath;
                                        this.outputList.Add(checkItem);
                                        break;
                                    }
                                }
                            }
                        }

                        GameObject.DestroyImmediate(gameObject);
                    }

                    EditorUtility.DisplayProgressBar("正在批量检查预制体", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
        }

        protected override void OnFix(string[] lines)
        {
            string[] assets;
            if (lines == null || lines.Length == 0)
            {
                List<string> assetList = new List<string>();
                string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
                foreach (string guid in guids)
                {
                    string asset = AssetDatabase.GUIDToAssetPath(guid);
                    assetList.Add(asset);
                }
                assets = assetList.ToArray();
            }
            else
            {
                assets = lines;
            }

            int count = assets.Length;
            try
            {
                for (int i = 0; i < assets.Length; i++)
                {
                    string assetPath = assets[i];
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    bool isPrefabDirty = false;
                    if (prefab != null && prefab.GetComponent<ActorRender>() == null)
                    {
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        QualityControlActive[] qualityControlActives = gameObject.GetComponentsInChildren<QualityControlActive>(true);
                        for (int j = 0; j < qualityControlActives.Length; j++)
                        {
                            GameObject node = qualityControlActives[j].gameObject;
                            QualityControlActive qualityControlActive = qualityControlActives[j];
                            ControlItem[] controls = qualityControlActive.GetControls();
                            List<ControlItem> list = new List<ControlItem>();
                            if (null != controls)
                            {
                                foreach (var item in controls)
                                {
                                    if (item.Target == null || item.Target.GetInstanceID() > 0)
                                    {
                                        isPrefabDirty = true;
                                    }
                                    else
                                    {
                                        list.Add(item);
                                    }
                                }

                                if (isPrefabDirty)
                                {
                                    qualityControlActives[j].SetControls(list.ToArray());
                                }
                            }
                        }

                        if (isPrefabDirty)
                        {
                            PrefabUtility.SaveAsPrefabAsset(gameObject, assetPath);
                        }
                        GameObject.DestroyImmediate(gameObject);
                    }

                    EditorUtility.DisplayProgressBar("正在批量修改预制体", string.Format("{0}/{1}", i, count), (float)i / (float)count);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(string.Format("{0}", asset));

                return builder;
            }
        }
    }
}
