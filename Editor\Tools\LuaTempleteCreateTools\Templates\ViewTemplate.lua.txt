﻿#class_name# = #class_name# or BaseClass(SafeBaseView)

function #class_name#:__init()
#__init#
end

function #class_name#:__delete()

end

function #class_name#:OpenCallBack()

end

function #class_name#:CloseCallBack()

end

function #class_name#:LoadCallBack()
#LoadCallBack#
end

function #class_name#:ReleaseCallBack()
#ReleaseCallBack#
end

function #class_name#:LoadIndexCallBack(index)
#LoadIndexCallBack#
end

function #class_name#:ShowIndexCallBack(index)
#ShowIndexCallBack#
end

function #class_name#:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
        #OnFlush#
        end
    end
end