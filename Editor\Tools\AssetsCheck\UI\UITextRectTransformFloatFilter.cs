﻿using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System;
using System.Collections.Generic;

namespace AssetsCheck
{
    /// <summary>
    /// Text组件RectTransform的宽高和pos.x,y有小数点会导致界面上显示模糊，这里批量处理掉（四舍五入）
    /// </summary>
    class UITextRectTransformFloatFilter : BaseChecker
    {
        [MenuItem("Assets/界面预设专用/去除Text组件RectTransform小数点", priority = 12)]
        public static void DoUITextRectTransformFloatFilterWithSelection()
        {
            UITextRectTransformFloatFilter filter = new UITextRectTransformFloatFilter();
            if (null != Selection.activeObject || null != Selection.gameObjects && Selection.gameObjects.Length > 0)
            {
                if (null != Selection.gameObjects && Selection.gameObjects.Length > 0)
                {
                    for (int i = 0; i < Selection.gameObjects.Length; i++)
                    {
                        GameObject gameObject = Selection.gameObjects[i];
                        string path = AssetDatabase.GetAssetPath(gameObject);
                        if (!path.StartsWith("Assets/Game/UIs/View"))
                        {
                            continue;
                        }

                        if (AssetDatabase.IsValidFolder(path))
                        {
                            filter.OnFilterExecute(new string[] { path });
                        }
                        else
                        {
                            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                            filter.FixGameobject(gameobj);
                        }
                    }
                }
                else if (null != Selection.activeObject)
                {
                    string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                    if (!path.StartsWith("Assets/Game/UIs/View"))
                    {
                        return;
                    }

                    if (AssetDatabase.IsValidFolder(path))
                    {
                        filter.OnFilterExecute(new string[] { path });
                    }
                    else
                    {
                        GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        filter.FixGameobject(gameobj);
                    }
                }

                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();
            }
        }

        [MenuItem("自定义工具/资源检查/去除界面预设体Text组件RectTransform小数点")]
        public static void DoUITextRectTransformFloatFilter()
        {
            UITextRectTransformFloatFilter filter = new UITextRectTransformFloatFilter();
            filter.OnFilterExecute();
        }

        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/UIs/View" };

        override public string GetErrorDesc()
        {
            return string.Format("检测Text组件RectTransform各参数存在小数点问题");
        }

        private void OnFilterExecute(string[] _checkDirs = null)
        {
            if (null == _checkDirs)
            {
                _checkDirs = this.checkDirs;
            }

            string[] guids = AssetDatabase.FindAssets("t:prefab", _checkDirs);
            int curIndex = 0;
            int assetCount = guids.Length;
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                this.FixGameobject(gameobj);

                curIndex++;
                EditorUtility.DisplayProgressBar("正在处理中，路径：" + path,
                       string.Format("{0} / {1}", curIndex, assetCount), (float)curIndex / (float)assetCount);
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }

        private void FixGameobject(GameObject gameobj)
        {
            if (gameobj == null)
            {
                return;
            }

            List<GameObject> componentObjs = new List<GameObject>();
            Text[] textComponents = gameobj.GetComponentsInChildren<Text>(true);
            for (int i = 0; i < textComponents.Length; i++)
            {
                Text text = textComponents[i];
                RectTransform rectTransform = text.gameObject.GetComponent<RectTransform>();
                if (rectTransform != null)
                {
                    FixRectTransform(rectTransform);
                }

                GameObject textParentObj = text.transform.parent.gameObject;
                if (!componentObjs.Contains(textParentObj))
                {
                    componentObjs.Add(textParentObj);
                }
            }

            for (int i = 0; i < componentObjs.Count; i++)
            {
                GameObject gameObject = componentObjs[i];
                RectTransform rectTransform = gameObject.GetComponent<RectTransform>();
                if (rectTransform != null)
                {
                    FixRectTransform(rectTransform);
                }
                else
                {
                    Transform transform = gameObject.GetComponent<Transform>();
                    if (transform != null)
                    {
                        FixTransform(transform);
                    }
                }
            }
        }

        private void FixTransform(Transform transform)
        {
            transform.localPosition = new Vector3(Round(transform.localPosition.x)
                , Round(transform.localPosition.y)
                , Round(transform.localPosition.z));
            transform.localEulerAngles = new Vector3(Round(transform.localEulerAngles.x)
                , Round(transform.localEulerAngles.y)
                , Round(transform.localEulerAngles.z));
            transform.localScale = new Vector3(Round(transform.localScale.x)
                , Round(transform.localScale.y)
                , Round(transform.localScale.z));
        }

        private void FixRectTransform(RectTransform rectTransform)
        {
            float Width = rectTransform.sizeDelta.x;
            float Height = rectTransform.sizeDelta.y;

            rectTransform.localPosition = new Vector3(Round(rectTransform.localPosition.x)
                , Round(rectTransform.localPosition.y)
                , Round(rectTransform.localPosition.z));
            rectTransform.anchoredPosition = new Vector3(Round(rectTransform.anchoredPosition.x)
                , Round(rectTransform.anchoredPosition.y));
            rectTransform.sizeDelta = new Vector2(Round(Width), Round(Height));
        }

        private float Round(float v)
        {
            bool isNegative = false;
            if (v < 0)
            {
                isNegative = true;
                v = -v;
            }
            double Int = Math.Round(v, MidpointRounding.AwayFromZero);
            v = isNegative ? - (float)Int : (float)Int;

            return v;
        }
    }
}