﻿using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using Nirvana;
using System.Collections.Generic;
using Game;

class ClearAttachModel : Editor
{
    private static string operate_folder = "Assets/Game/Effects";
    /// <summary>
    /// 文件夹下的所有预制
    /// </summary>
    private static Dictionary<string, GameObject> all_prefabs = new Dictionary<string, GameObject>();

    /// <summary>
    /// 文件夹下的所有材质
    /// </summary>
    private static Dictionary<string, Material> all_materials = new Dictionary<string, Material>();

    //获取所有的预制体
    private static void GetAllPrefabs(string[] sub_folders)
    {
        if (sub_folders.Length <= 0)
        {
            return;
        }

        string[] guids = AssetDatabase.FindAssets("t:prefab", sub_folders);
        if (guids.Length > 0)
        {
            for (int j = 0; j < guids.Length; j++)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[j]);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                int number = 0;
                int.TryParse(gameobj.name, out number);
                if (number != 0)
                {
                    all_prefabs.Add(path, gameobj);
                    EditorUtility.DisplayProgressBar("资源添加中", string.Format("（正在添加资源{0}/{1}）", j, guids.Length), (float)j / (float)guids.Length);
                }
            }
        }
    }


    [MenuItem("自定义工具/策划工具/一键检测模型Materials状态")]
    private static void CheckModelMaterialsStatus()
    {
        if (string.IsNullOrEmpty(operate_folder))
        {
            return;
        }

        string[] sub_folders = new string[] { operate_folder };
        all_materials.Clear();
        GetAllMaterials(sub_folders);
        if (all_materials.Count <= 0)
        {
            return;
        }

        int index = 1;
        string result = "";
        foreach (KeyValuePair<string, Material> materials_list in all_materials)
        {
            index = index++;
            result += CheckMaterialsStatus(materials_list.Key, materials_list.Value);
            EditorUtility.DisplayProgressBar("模型检测中", string.Format("正在处理（{0}/{1}）", index, all_materials.Count), (float)index / (float)all_materials.Count);
        }
        putlog(result);
        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
    }



    //获取所有的预制体
    private static void GetAllMaterials(string[] sub_folders)
    {
        if (sub_folders.Length <= 0)
        {
            return;
        }

        for (int i = 0; i < sub_folders.Length; i++)
        {
            string[] guids = Directory.GetFiles(sub_folders[i], "*.mat", SearchOption.AllDirectories);
            if (guids.Length > 0)
            {
                for (int j = 0; j < guids.Length; j++)
                {
                    string asset_path = guids[j].Replace(Application.dataPath, "");
                    Material prefabs_res = AssetDatabase.LoadAssetAtPath(asset_path, typeof(Material)) as Material;
                    all_materials.Add(asset_path, prefabs_res);
                    EditorUtility.DisplayProgressBar("资源添加中", string.Format("（正在添加资源{0}/{1}）", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
        }
    }

    private static string CheckMaterialsStatus(string path, Material aim_mat)
    {
        string single_str = "";
        if (aim_mat.shader.name != "Srp/Standard/SrpRole_PbrCf")
        {
            single_str = string.Format("材质路径：{0}---[{1}]材质的Shader不为PbrCf！\r\n", path, aim_mat.name);
            return single_str;
        }

        if (aim_mat.shader.name == "Srp/Standard/SrpRole_PbrCf")
        {
            float _metallic = aim_mat.GetFloat("_MetallicScale");
            float _roughness_scale = aim_mat.GetFloat("_GlossMapScale");
            float _roughness = aim_mat.GetFloat("_Glossiness");
            float _channel_mask = aim_mat.GetFloat("_ChannelMaskStrength");

            if ((_roughness_scale == 0 && _roughness == 0) && _channel_mask == 1 && _metallic == 0)
            {
                single_str = string.Format("材质路径：{0}---[{1}]材质的未配置\r\n", path, aim_mat.name);
                return single_str;
            }
            //single_str = string.Format("材质路径：{0}---[{1}]材质的已配置, 撒花！\r\n", path, aim_mat.name);
        }

        return single_str;
    }



    private static void putlog(string str)
    {
        TextEditor te = new TextEditor();
        te.text = str;
        te.SelectAll();
        te.Copy();
        UnityEditor.EditorUtility.DisplayDialog("提示", "已检测完成，并把检测结果复制至剪切板", "确认");
    }
}
