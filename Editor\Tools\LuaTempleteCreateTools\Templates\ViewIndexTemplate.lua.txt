﻿function #class_name#:Load#index_calss_name#CallBack()

end

function #class_name#:Release#index_calss_name#CallBack()

end

function #class_name#:Show#index_calss_name#IndexCallBack()

end

function #class_name#:On#index_calss_name#Flush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:Flush#index_calss_name#View()
        end
    end
end

function #class_name#:Flush#index_calss_name#View()

end