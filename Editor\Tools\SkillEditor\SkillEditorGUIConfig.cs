using UnityEngine;

/// <summary>
/// 技能编辑器配置类
/// 集中管理所有常量、路径和配置信息
/// </summary>
public static class SkillEditorGUIConfig
{
    #region UI常量
    public const int SCROLL_WIDTH = 200;
    public const int SCROLL_HEIGHT = 400;
    public const int BUTTON_WIDTH = 150;
    public const int BUTTON_HEIGHT = 30;
    public const int WINDOW_PADDING = 10;
    #endregion

    #region 路径常量
    public const string SAVE_FOLDER = "/Game/Lua/config/prefab_data/";
    public const string ACTOR_FOLDER_1 = "Assets/Game/Actors";
    public const string ACTOR_FOLDER_2 = "Assets/Game/Model";
    public const string GUNDAM_PATH = "Assets/Game/Model/jijia/";
    #endregion

    #region 窗口布局
    public static readonly Rect UpWindowRect = new Rect(230, 180, 300, 300);
    public static readonly Rect MiddleRect = new Rect(210, 650, 330, 300);
    public static readonly Rect MiddleRect2 = new Rect(550, 650, 400, 300);
    public static readonly Rect ButtonRect = new Rect(660, 110, 700, 400);
    public static readonly Rect DeleteBtnRect1 = new Rect(450, 140, 150, 30);
    public static readonly Rect DeleteBtnRect2 = new Rect(450, 615, 150, 30);
    #endregion

    #region 工具栏名称
    public static readonly string[] ToolBarNames = { "特效", "音效", "震屏", "径向模糊" };
    public static readonly string[] ActorToolbarNames = { "击中" };
    public static readonly string[] HurtPositions = { "Root", "Hurt Root" };
    public static readonly string[] HitRotations = { "Target", "HitDirection" };
    #endregion

    #region 摄像机视角配置
    public static readonly Vector3 FarViewPosition = new Vector3(-9.6f, 7.1f, 15f);
    public static readonly Vector3 NearViewPosition = new Vector3(-0.6f, 7.6f, -13.6f);
    public static readonly Vector3 FarViewRotation = new Vector3(20f, 153.17f, 0);
    public static readonly Vector3 NearViewRotation = new Vector3(20, 0, 0);
    #endregion

    #region 场景对象名称
    public const string MAIN_CAMERA_NAME = "Main Camera";
    public const string VOLUME_NAME = "Volume";
    public const string MODEL_CONTAINER_NAME = "Model";
    public const string SKILL_EDITOR_SCENE_NAME = "SkillEditor";
    #endregion

    #region 限制常量
    public const int MAX_SOUND_COUNT = 2;
    public const int MAX_HIT_SOUNDS = 2;
    public const int EFFECT_ARRAY_SIZE = SkillEditorPrefabDataConfig.EFFECT_ARRAY_SIZE;
    #endregion

    #region 默认值
    public const string DEFAULT_BUTTON_NAME = "按钮";
    public const float DEFAULT_DELAY = 0f;
    public const int DEFAULT_WEIGHT = 50;
    #endregion

    #region 枚举定义
    public enum TriggerToolbar
    {
        Effects = 0,        // 特效
        Sounds = 1,         // 音效
        CamerasShakes = 2,  // 震屏
        RadialBlurs = 3,    // 径向模糊
    }

    public enum ActorToolbar
    {
        Hurts = 0,          // 击中
        Projectiles = 1,    // 弹道
        Others = 2          // 其他
    }
    #endregion

    #region 验证方法
    /// <summary>
    /// 验证当前场景是否为技能编辑器场景
    /// </summary>
    public static bool IsValidScene()
    {
        try
        {
            var currentScene = UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene();
            bool isValid = currentScene.name == SKILL_EDITOR_SCENE_NAME;

            if (!isValid)
            {
                UnityEngine.Debug.LogWarning($"当前场景 '{currentScene.name}' 不是技能编辑器场景 '{SKILL_EDITOR_SCENE_NAME}'");
            }

            return isValid;
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"验证场景时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取默认按钮名称
    /// </summary>
    public static string GetDefaultButtonName(string name)
    {
        return string.IsNullOrEmpty(name) ? DEFAULT_BUTTON_NAME : name;
    }
    #endregion
}
