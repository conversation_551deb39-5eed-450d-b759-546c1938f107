﻿using System;
using System.IO;
using UnityEngine;

internal static class MacroTextNew
{
    internal static string ProcessSystemMacro(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return text;
        }

        DateTime now = DateTime.Now;
        text = text.Replace("{now}", now.ToString());
        text = text.Replace("{nowTicks}", now.Ticks.ToString());
        text = text.Replace("{unityVersion}", Application.unityVersion);
        text = text.Replace("{appVersion}", Application.version);
        return text;
    }

    internal static string ReplaceDirText(string text, string placeholder, string dirname)
    {
        if (!string.IsNullOrEmpty(dirname))
        {
            return text.Replace(placeholder, dirname);
        }

        if (text.Contains(placeholder))
        {
            string text2 = "/" + placeholder;
            if (text.Contains(text2))
            {
                return text.Replace(text2, string.Empty);
            }

            return text.Replace(placeholder, string.Empty);
        }

        return text;
    }

    internal static string ProcessPathMacro(string text, string path)
    {
        if (string.IsNullOrEmpty(text))
        {
            return text;
        }

        string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(path);
        text = text.Replace("{filename}", fileNameWithoutExtension);
        string directoryName = Path.GetDirectoryName(path);
        string fileName = Path.GetFileName(directoryName);
        text = text.Replace("{lastdir}", fileName);
        string dirname = string.Empty;
        string dirname2 = string.Empty;
        string dirname3 = string.Empty;
        string[] array = path.Split('/', '\\');
        if (array.Length != 0 && array[0] != Path.GetFileName(path))
        {
            dirname = array[0];
        }

        if (array.Length > 1 && array[1] != Path.GetFileName(path))
        {
            dirname2 = array[1];
        }

        if (array.Length > 2 && array[2] != Path.GetFileName(path))
        {
            dirname3 = array[2];
        }

        text = ReplaceDirText(text, "{firstdir}", dirname);
        text = ReplaceDirText(text, "{seconddir}", dirname2);
        text = ReplaceDirText(text, "{thirddir}", dirname3);
        return text;
    }
}