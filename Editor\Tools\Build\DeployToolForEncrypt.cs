﻿using Nirvana.Editor;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using Build;
using System.Runtime.InteropServices;

class DeployToolForEncrypt : DeployTool
{
    static void EncryptAndroidAssetBundleFaBan(BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, buildPlatType);
        EncryptAndZipVersionFile(buildPlatType);
        EncryptLuaFiles("", false, buildPlatType);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Android/Android AssetBundle全部加密(发版,偏移)/Release")]
    public static void EncryptAndroidAssetBundleReleaseFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndroidAssetBundleFaBan(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Android/Android AssetBundle全部加密(发版,偏移)/Dev")]
    public static void EncryptAndroidDevAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndroidAssetBundleFaBan(BuildPlatType.AndroidDev);
    }

    static void DecryptAndroidAssetBundleFaBan(BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, buildPlatType);
        EncryptAndZipVersionFile(buildPlatType, false);
        DecryptAssetBundle(false, buildPlatType);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Android/解密所有资源(还原发版文件,偏移)/Release")]
    public static void DecryptAndroidAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAndroidAssetBundleFaBan(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Android/解密所有资源(还原发版文件,偏移)/Dev")]
    public static void DecryptAndroidDevAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAndroidAssetBundleFaBan(BuildPlatType.AndroidDev);
    }

    static void EncryptiOSAssetBundleFaBan(BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, buildPlatType);
        EncryptAndZipVersionFile(buildPlatType);
        EncryptLuaFiles("", false, buildPlatType);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/iOS/AssetBundle全部加密(发版,偏移)/Release")]
    public static void EncryptiOSReleaseAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptiOSAssetBundleFaBan(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/iOS/AssetBundle全部加密(发版,偏移)/Dev")]
    public static void EncryptiOSDevAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptiOSAssetBundleFaBan(BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/iOS/解密所有资源(还原发版文件,偏移)/Release")]
    public static void DecryptiOSReleaseAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptiOSAssetBundleFaBan(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/iOS/解密所有资源(还原发版文件,偏移)/Dev")]
    public static void DecryptiOSDevAssetBundleFaBan()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptiOSAssetBundleFaBan(BuildPlatType.IOSDev);
    }

    static void DecryptiOSAssetBundleFaBan(BuildPlatType buildPlatType)
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.IOS);
        EncryptAndZipVersionFile(BuildPlatType.IOS, false);
        DecryptAssetBundle(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/AssetBundle加密/Release")]
    public static void EncryptAndroidReleaseAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/AssetBundle加密/Dev")]
    public static void EncryptAndroidDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/AssetBundle解密/Release")]
    public static void DecryptAndroidReleaseAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAssetBundle(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/AssetBundle解密/Dev")]
    public static void DecryptAndroidDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAssetBundle(false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/偏移加密Lua文件/Release")]
    public static void EncryptAndroidReleaseLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptLuaFiles("", false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/偏移加密Lua文件/Dev")]
    public static void EncryptAndroidDevLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptLuaFiles("", false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/偏移解密Lua文件/Release")]
    public static void DecryptAndroidReleaseLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/Android/偏移解密Lua文件/Dev")]
    public static void DecryptAndroidDevLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/AssetBundle加密/Release")]
    public static void EncryptiOSReleaseAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/AssetBundle加密/Dev")]
    public static void EncryptiOSDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/AssetBundle解密/Release")]
    public static void DecryptiOSReleaseAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAssetBundle(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/AssetBundle解密/Dev")]
    public static void DecryptiOSDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptAssetBundle(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/偏移加密Lua文件/Release")]
    public static void EncryptiOSReleaseLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptLuaFiles("", false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/偏移加密Lua文件/Dev")]
    public static void EncryptiOSDevLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptLuaFiles("", false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/偏移解密Lua文件/Release")]
    public static void DecryptiOSReleaseLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/iOS/偏移解密Lua文件/Dev")]
    public static void DecryptiOSDevLuaFiles()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.IOSDev);
    }

    public static bool EncryptAssetBundle(bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        try
        {
            string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
            string[] files = Directory.GetFiles(assetbundlePath, "*.*", SearchOption.AllDirectories);
            int count = files.Length;
            int cur_index = 0;
            foreach (string file in files)
            {
                string _file = file.Replace("\\", "/");
                if (GetIsOffsetEncryptPass(_file))
                {
                    continue;
                }

                string assetbundleName = GetAssetBundleNameByPath(_file);
                if (!OffsetEncryptAB(_file, assetbundleName))
                {
                    Debug.LogError($"OffsetEncryptAB Failed, file:{_file}, assetbundleName:{assetbundleName}");
                }

                if (!isCallByPy)
                {
                    cur_index += 1;
                    EditorUtility.DisplayProgressBar("AssetBundle偏移加密中", string.Format("{0} / {1}", cur_index, count), (float)((float)cur_index / (float)count));
                }
            }
            EditorUtility.ClearProgressBar();
            ReportBuildLog($"BuildAssets, EncryptAssetBundle file count:{cur_index}");
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            return false;
        }
        return true;
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/查找并解密二次加密问题资源/安卓/Release")]
    public static void FindErrorAndroidReleaseFile()
    {
        FindErrorFile(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/查找并解密二次加密问题资源/安卓/Dev")]
    public static void FindErrorAndroidDevFile()
    {
        FindErrorFile(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/查找并解密二次加密问题资源/iOS/Release")]
    public static void FindErroriOSReleaseFile()
    {
        FindErrorFile(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/查找并解密二次加密问题资源/iOS/Dev")]
    public static void FindErroriOSDevFile()
    {
        FindErrorFile(BuildPlatType.IOSDev);
    }

    public static void FindErrorFile(BuildPlatType buildPlatType)
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
        DirectoryInfo directoryInfo = new DirectoryInfo(assetbundlePath);
        string[] fileInfos = Directory.GetFiles(assetbundlePath, "*.*", SearchOption.AllDirectories);
        byte[] key = EncryptMgr.GetEncryptKey();
        try
        {
            for (int i = 0; i < fileInfos.Length; i++)
            {
                string filePath = fileInfos[i].Replace("\\", "/");
                var buffer = File.ReadAllBytes(filePath);
                bool isFine = true;
                for (int j = 0; j < key.Length; j++)
                {
                    if (key[j] != buffer[j])
                    {
                        isFine = false;
                        break;
                    }
                }
                buffer = null;
                if (isFine)
                {
                    OffsetDecryptAB(filePath, GetAssetBundleNameByPath(filePath));
                }

                EditorUtility.DisplayProgressBar("排查加密资源中...", string.Format("{0} / {1}", i, fileInfos.Length), (float)((float)i / (float)fileInfos.Length));
            }
            EditorUtility.ClearProgressBar();
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
        }
    }

    public static bool DecryptAssetBundle(bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        try
        {
            string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
            DirectoryInfo directoryInfo = new DirectoryInfo(assetbundlePath);
            string[] fileInfos = Directory.GetFiles(assetbundlePath, "*.*", SearchOption.AllDirectories);

            int count = fileInfos.Length;
            int cur_index = 0;
            foreach (string file in fileInfos)
            {
                string filePath = file.Replace("\\", "/");
                if (GetIsOffsetEncryptPass(filePath))
                {
                    continue;
                }

                OffsetDecryptAB(filePath, GetAssetBundleNameByPath(filePath));
                if (!isCallByPy)
                {
                    EditorUtility.DisplayProgressBar("【偏移】AssetBundle全部解密中", string.Format("{0} / {1}", cur_index, count), (float)((float)cur_index / (float)count));
                }
                cur_index++;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
            return false;
        }
        ReportBuildLog("EncryptAssets, DeployToolForEncrypt.DecryptAssetBundle");
        EditorUtility.ClearProgressBar();
        return true;
    }

    public static bool EncryptLuaFiles(string targetPath = "", bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        string luaAssetBundlePath = Path.Combine(BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/"), "LuaAssetBundle");
        string[] fileInfos = Directory.GetFiles(luaAssetBundlePath, "*.*", SearchOption.AllDirectories);

        int total = fileInfos.Length;
        int index = 0;
        string luaFilePath = "";
        try
        {
            foreach (string file in fileInfos)
            {
                luaFilePath = file.Replace("\\", "/");
                if (GetIsOffsetEncryptPass(luaFilePath))
                {
                    continue;
                }

                string assetbundleName = GetAssetBundleNameByPath(luaFilePath);
                if (!OffsetEncryptAB(luaFilePath, assetbundleName))
                {
                    Debug.LogError($"OffsetEncryptAB Failed, luaFilePath:{luaFilePath}, assetbundleName:{assetbundleName}");
                }

                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("加密Lua版本文件中...", string.Format("{0} / {1}", index, total), (float)index / (float)total);
                }
            }
            EditorUtility.ClearProgressBar();
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            Debug.LogErrorFormat("luaFilePath:{0}", luaFilePath);
            EditorUtility.ClearProgressBar();
            if (isCallByPy)
            {
                ReportBuildLog(string.Format("luaFilePath:{0}, Exception Info:{1}", luaFilePath, ex.ToString()));
            }
            return false;
        }
        return true;
    }

    public static bool DecryptLuaFiles(string targetPath = "", bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        string luaAssetBundlePath = Path.Combine(BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/"), "LuaAssetBundle");
        string[] fileInfos = Directory.GetFiles(luaAssetBundlePath, "*.*", SearchOption.AllDirectories);

        int total = fileInfos.Length;
        int index = 0;
        string luaFilePath = "";
        try
        {
            foreach (string file in fileInfos)
            {
                luaFilePath = file.Replace("\\", "/");
                if (GetIsOffsetEncryptPass(luaFilePath))
                {
                    continue;
                }

                string assetbundleName = GetAssetBundleNameByPath(luaFilePath);
                if (!OffsetDecryptAB(luaFilePath, assetbundleName))
                {
                    Debug.LogError($"OffsetDecryptAB Failed, luaFilePath:{luaFilePath}, assetbundleName:{assetbundleName}");
                }

                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("加密Lua版本文件中...", string.Format("{0} / {1}", index, total), (float)index / (float)total);
                }
            }
            EditorUtility.ClearProgressBar();
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            Debug.LogErrorFormat("luaFilePath:{0}", luaFilePath);
            EditorUtility.ClearProgressBar();
            if (isCallByPy)
            {
                ReportBuildLog(string.Format("luaFilePath:{0}, Exception Info:{1}", luaFilePath, ex.ToString()));
            }
            return false;
        }
        return true;
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/选择文件进行加密")]
    public static void OffsetEncryptAssetBundleFile()
    {
        EncryptMgr.RefreshEncryptKey();
        OpenFileName openFile = GetOpenFileName("选择文件进行【偏移】加密");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);
            string filePath = openFile.file.Replace("\\", "/");
            OffsetEncryptAB(filePath, GetAssetBundleNameByPath(filePath));
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/选择文件进行解密")]
    public static void OffsetDecryptAssetBundleFile()
    {
        EncryptMgr.RefreshEncryptKey();
        OpenFileName openFile = GetOpenFileName("选择选择文件进行【偏移】解密");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);
            string filePath = openFile.file.Replace("\\", "/");
            OffsetDecryptAB(filePath, GetAssetBundleNameByPath(filePath));
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/选择偏移加密文件进行加载")]
    public static void LoadOffsetEncryptAssetBundleFile()
    {
        OpenFileName openFile = GetOpenFileName("选择【偏移】加密文件进行加载");

        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);

            AssetBundle.UnloadAllAssetBundles(true);

            string path = openFile.file;
            byte[] key = EncryptMgr.GetEncryptKey();
            AssetBundle bundle = AssetBundle.LoadFromFile(path, 0, (ulong)key.Length);
            if (bundle)
            {
                foreach (string asset in bundle.GetAllAssetNames())
                {
                    Debug.LogError("" + asset);
                }
            }
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/偏移加密/选择偏移加密文件进行异步加载")]
    public static void AsyncLoadOffsetEncryptAssetBundleFile()
    {
        EditorCoroutineRunner.StartEditorCoroutine(CallAsyncLoadOffsetEncryptAssetBundleFile());
    }

    public static IEnumerator<AssetBundleCreateRequest> CallAsyncLoadOffsetEncryptAssetBundleFile()
    {
        OpenFileName openFile = GetOpenFileName("选择【偏移】加密文件进行加载");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);

            AssetBundle.UnloadAllAssetBundles(true);

            string path = "G:/apk/a1_fb_1v1_tietu001_d";
            byte[] key = EncryptMgr.GetEncryptKey();
            AssetBundleCreateRequest bundle = AssetBundle.LoadFromFileAsync(path, 0, (ulong)key.Length);
            yield return bundle;
            var assetBundle = bundle.assetBundle;
            if (assetBundle != null)
            {
                if (assetBundle.GetAllAssetNames().Length > 0)
                {
                    foreach (string s in assetBundle.GetAllAssetNames())
                    {
                        Debug.LogError(s);
                    }
                }
                if (assetBundle.GetAllScenePaths().Length > 0)
                {
                    foreach (string s in assetBundle.GetAllScenePaths())
                    {
                        Debug.LogError(s);
                    }
                }
            }
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/选择文件进行加密", priority = 1)]
    public static void XOREncryptFile()
    {
        EncryptMgr.RefreshEncryptKey();
        OpenFileName openFile = GetOpenFileName("选择文件进行【异或】加密");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);

            XOREncryptAB(openFile.file, true);
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/选择文件进行解密", priority = 2)]
    public static void XORDecodeFile()
    {
        EncryptMgr.RefreshEncryptKey();
        OpenFileName openFile = GetOpenFileName("选择文件进行【异或】解密");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(openFile.fileTitle);

            XOREncryptAB(openFile.file, false);
        }
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/Android/加密并压缩Lua版本文件")]
    public static void EncryptAndZipAndroidLuaVersionFile()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipLuaVersionFile(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/Android/加密并压缩版本文件")]
    public static void EncryptAndZipAndroidVersionFileForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipVersionFile(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/Android/加密file_list文件")]
    public static void EncryptFileList()
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.Android);
        string filePath = string.Format(assetbundlePath + "../../../../sdk/android/dev/src/main/assets/{0}.txt", EncryptMgr.GetEncryptPath("file_list")).Replace("\\", "/");
        if (!File.Exists(filePath))
        {
            string fileslistPath = (assetbundlePath + "../../../../sdk/android/dev/src/main/assets/file_list.txt").Replace("\\", "/");
            if (!File.Exists(fileslistPath))
            {
                Debug.LogErrorFormat("{0} not exist", filePath);
                return;
            }
            File.Copy(fileslistPath, filePath);
            File.Delete(fileslistPath);
        }

        XOREncryptAB(filePath);
        ReportBuildLog("EncryptAssets, DeployToolForEncrypt.EncryptFileList");
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/iOS/加密并压缩Lua版本文件")]
    public static void EncryptAndZipiOSLuaVersionFile()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipLuaVersionFile(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/异或加密/iOS/加密并压缩版本文件")]
    public static void EncryptAndZipiOSVersionFileForEditor()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipVersionFile(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/安卓/更新安卓工程资源并出包")]
    public static void EncryptPath()
    {
        if (!DeployToolForBuildAndroid.CallBatFile(Application.dataPath + "/../../sdk/android/", "call_build_android.bat"))
            return;
        EncryptFileList();
        DeployToolForBuildAndroid.CallBatFile(Application.dataPath + "/../../sdk/android/", "call_build_android_pack.bat");
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Android/加密资源目录/Release")]
    public static void Base64EncodingAndroidABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Android/加密资源目录/Dev")]
    public static void Base64EncodingAndroidDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Android/还原加密资源目录/Release")]
    public static void Base64DecodingAndroidABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Android/还原加密资源目录/Dev")]
    public static void Base64DecodingAndroidDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/选择文件进行文件名解密")]
    public static void Base64DecodingSelectFile()
    {
        OpenFileName openFile = GetOpenFileName("选择文件进行文件名Base64解密");
        if (openFile != null && LocalDialog.GetSaveFileName(openFile))
        {
            Debug.LogError(openFile.file);
            Debug.LogError(EncryptMgr.GetDecryptPath2Base64(openFile.fileTitle.Replace(".lua", "")));
        }
    }

    //[MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Android/加密安卓dev项目资源目录")]
    //public static void Base64EncodingAndroidProjABPath(BuildPlatType buildPlatType)
    //{
    //    string abPath = BuilderConfig.GetAssetBundlePath(BuildPlatType.AndroidDev).Replace("\\", "/");
    //    string andDevProjPath = abPath + "/../../../sdk/android/dev/src/main/assets/AssetBundle/";
    //    DirectoryInfo DInfo = new DirectoryInfo(andDevProjPath);
    //    string andDevProjFullPath = DInfo.FullName.Replace("\\", "/");
    //    var dirs = DInfo.GetDirectories("*", SearchOption.AllDirectories);
    //    var files = DInfo.GetFiles("*", SearchOption.AllDirectories);

    //    try
    //    {
    //        int index = 0;
    //        int total = dirs.Length;
    //        for (int i = 0; i < dirs.Length; i++)
    //        {
    //            string dir = dirs[i].FullName.Replace("\\", "/");
    //            string tobase64dir = dir.Replace(andDevProjFullPath, "");
    //            string base64dir = EncryptMgr.GetEncryptPath2Base64Editor(tobase64dir);
    //            string newDir = string.Format("{0}/{1}", abPath, base64dir);

    //            if (!Directory.Exists(newDir))
    //                Directory.CreateDirectory(newDir);
    //            index += 1;
    //            EditorUtility.DisplayCancelableProgressBar("正在修改安卓项目资源文件夹路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
    //        }

    //        index = 0;
    //        total = files.Length;
    //        for (int i = 0; i < files.Length; i++)
    //        {
    //            string file = files[i].FullName.Replace("\\", "/");
    //            string tobase64File = file.Replace(andDevProjFullPath, "");
    //            string base64File = EncryptMgr.GetEncryptPath2Base64Editor(tobase64File);
    //            string newFilePath = string.Format("{0}/{1}", abPath, base64File);
    //            File.Copy(file, newFilePath);
    //            index += 1;
    //            EditorUtility.DisplayCancelableProgressBar("正在修改安卓项目资源文件路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
    //        }

    //        index = 0;
    //        total = files.Length;
    //        for (int i = 0; i < files.Length; i++)
    //        {
    //            if (File.Exists(files[i].FullName))
    //            {
    //                files[i].Delete();
    //            }
    //            index += 1;
    //            EditorUtility.DisplayCancelableProgressBar("正在删除文件", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
    //        }

    //        index = 0;
    //        total = dirs.Length;
    //        for (int i = 0; i < dirs.Length; i++)
    //        {
    //            if (Directory.Exists(dirs[i].FullName))
    //            {
    //                dirs[i].Delete(true);
    //            }
    //            index += 1;
    //            EditorUtility.DisplayCancelableProgressBar("正在删除文件夹", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        Debug.LogError(ex.ToString());
    //        EditorUtility.ClearProgressBar();
    //        return;
    //    }

    //    string file_list_path = andDevProjFullPath + "../file_list.txt";
    //    string file_list_base64File = EncryptMgr.GetEncryptPath2Base64Editor("file_list");
    //    string new_file_list_path = string.Format("{0}../{1}.txt", andDevProjFullPath, file_list_base64File);
    //    if (File.Exists(new_file_list_path))
    //    {
    //        File.Delete(new_file_list_path);
    //    }
    //    File.Copy(file_list_path, new_file_list_path);
    //    File.Delete(file_list_path);

    //    ReportBuildLog("EncryptAssets, DeployToolForEncrypt.Base64EncodingAndroidProjABPath");
    //    EditorUtility.ClearProgressBar();
    //}

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/IOS/加密资源目录/Release")]
    public static void Base64EncodingIOSABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/IOS/加密资源目录/Dev")]
    public static void Base64EncodingIOSDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/IOS/还原加密资源目录/Release")]
    public static void Base64DecodingIOSABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/IOS/还原加密资源目录/Dev")]
    public static void Base64DecodingIOSDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Audit/加密资源目录/Release")]
    public static void Base64EncodingAuditABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Audit/加密资源目录/Dev")]
    public static void Base64EncodingAuditDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64EncodingABPath(false, BuildPlatType.AuditDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Audit/还原加密资源目录/Release")]
    public static void Base64DecodingAuditABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Base64加密/Audit/还原加密资源目录/Dev")]
    public static void Base64DecodingAuditDevABPath()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.AuditDev);
    }

    public static bool Base64EncodingABPath(bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.AndroidDev)
    {
        string abPath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
        DirectoryInfo DInfo = new DirectoryInfo(abPath);
        string abFullPath = DInfo.FullName.Replace("\\", "/") + "/";
        var dirs = DInfo.GetDirectories("*", SearchOption.AllDirectories);
        var files = DInfo.GetFiles("*", SearchOption.AllDirectories);
        try
        {
            int index = 0;
            int total = dirs.Length;
            for (int i = 0; i < dirs.Length; i++)
            {
                string dir = dirs[i].FullName.Replace("\\", "/");
                string tobase64dir = dir.Replace(abFullPath, "");
                string base64dir = buildPlatType == BuildPlatType.Audit
                                ? EncryptMgr.GetStreamingEncryptPath2Base64Editor(tobase64dir)
                                : EncryptMgr.GetEncryptPath2Base64Editor(tobase64dir);
                string newDir = string.Format("{0}/{1}", abPath, base64dir);

                if (!Directory.Exists(newDir))
                    Directory.CreateDirectory(newDir);

                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在修改文件夹路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = files.Length;
            for (int i = 0; i < files.Length; i++)
            {
                string file = files[i].FullName.Replace("\\", "/");
                string tobase64File = file.Replace(abFullPath, "");
                if (tobase64File.Contains(".DS_Store"))
                {
                    continue;
                }
                string base64File = buildPlatType == BuildPlatType.Audit
                                ? EncryptMgr.GetStreamingEncryptPath2Base64Editor(tobase64File)
                                : EncryptMgr.GetEncryptPath2Base64Editor(tobase64File);
                string newFilePath = string.Format("{0}/{1}", abPath, base64File);
                File.Copy(file, newFilePath);
                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在修改文件路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = files.Length;
            for (int i = 0; i < files.Length; i++)
            {
                if (File.Exists(files[i].FullName))
                {
                    files[i].Delete();
                }
                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在删除文件", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = dirs.Length;
            for (int i = 0; i < dirs.Length; i++)
            {
                if (Directory.Exists(dirs[i].FullName))
                {
                    dirs[i].Delete(true);
                }
                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在删除文件夹", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }
        }
        catch (Exception ex)
        {
            if (isCallByPy)
            {
                ReportBuildLog(string.Format("Exception Info:{0}", ex.ToString()));
            }
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
            return false;
        }

        ZipVersionFile(true, buildPlatType);
        EditorUtility.ClearProgressBar();
        ReportBuildLog("EncryptAssets, DeployToolForEncrypt.Base64EncodingABPath");
        return true;
    }

    public static bool Base64DecodingABPath(bool isCallByPy = false, BuildPlatType buildPlatType = BuildPlatType.AndroidDev)
    {
        string abPath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
        DirectoryInfo DInfo = new DirectoryInfo(abPath);
        string abFullPath = DInfo.FullName.Replace("\\", "/") + "/";
        var dirs = DInfo.GetDirectories("*", SearchOption.AllDirectories);
        var files = DInfo.GetFiles("*", SearchOption.AllDirectories);
        try
        {
            int index = 0;
            int total = files.Length;
            for (int i = 0; i < files.Length; i++)
            {
                string file = files[i].FullName.Replace("\\", "/");
                string tobase64File = file.Replace(abFullPath, "");
                if (tobase64File.Contains(".DS_Store"))
                {
                    continue;
                }
                string base64File = EncryptMgr.GetDecryptPath2Base64(tobase64File);
                string newFilePath = string.Format("{0}/{1}", abPath, base64File);
                string folder = newFilePath.Substring(0, newFilePath.LastIndexOf("/"));
                if (!Directory.Exists(folder))
                {
                    Directory.CreateDirectory(folder);
                }
                File.Copy(file, newFilePath);

                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在修改文件路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = dirs.Length;
            for (int i = 0; i < dirs.Length; i++)
            {
                string dir = dirs[i].FullName.Replace("\\", "/");
                string base64dir = dir.Replace(abFullPath, "");
                string origindir = EncryptMgr.GetDecryptPath2Base64(base64dir);
                string newDir = string.Format("{0}/{1}", abPath, origindir);

                if (!Directory.Exists(newDir))
                    Directory.CreateDirectory(newDir);

                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在还原文件夹路径", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = files.Length;
            for (int i = 0; i < files.Length; i++)
            {
                if (File.Exists(files[i].FullName))
                {
                    files[i].Delete();
                }
                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在删除文件", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }

            index = 0;
            total = dirs.Length;
            for (int i = 0; i < dirs.Length; i++)
            {
                if (Directory.Exists(dirs[i].FullName))
                {
                    dirs[i].Delete(true);
                }
                if (!isCallByPy)
                {
                    index += 1;
                    EditorUtility.DisplayCancelableProgressBar("正在删除文件夹", string.Format("{0}/ {1}", index, total), (float)index / (float)total);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
            return false;
        }

        ZipVersionFile(false, buildPlatType);

        ReportBuildLog("EncryptAssets, DeployToolForEncrypt.Base64DecodingABPath");
        EditorUtility.ClearProgressBar();
        return true;
    }

    public static void EncryptAndZipLuaVersionFile(BuildPlatType buildPlatType, bool isEncrypt = true)
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType);
        string[] filesPath = new string[] { (assetbundlePath + "/LuaAssetBundle/LuaAssetBundle.lua").Replace("\\", "/") };
        foreach (string path in filesPath)
        {
            XOREncryptAB(path, isEncrypt);
        }

        string[] zipsPath = new string[] { (assetbundlePath + "/LuaAssetBundle/").Replace("\\", "/") };
        string zipPath = filesPath[0].Replace(".lua", ".zip");
        ZipUtils.ZipFile(filesPath[0], zipsPath[0]);
    }

    public static bool EncryptAndZipVersionFile(BuildPlatType buildPlatType, bool isEncrypt = true)
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("\\", "/");
        string[] filesPath = new string[] { (assetbundlePath + "/LuaAssetBundle/LuaAssetBundle.lua").Replace("\\", "/"),
                                            (assetbundlePath + "/AssetBundle.lua").Replace("\\", "/"),};
        try
        {
            foreach (string path in filesPath)
            {
                XOREncryptAB(path, isEncrypt);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            return false;
        }

        ZipVersionFile(false, buildPlatType);
        return true;
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/压缩Android版本文件")]
    public static void ZipAndroidVersionFile()
    {
        ZipVersionFile(false, BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/压缩iOS版本文件")]
    public static void ZipiOSVersionFile()
    {
        ZipVersionFile(false, BuildPlatType.IOS);
    }

    public static void ZipVersionFile(bool isNeedEncryptPath = false, BuildPlatType buildPlatType = BuildPlatType.Android)
    {
        string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType);
        string[] filesPath = new string[] {
            (assetbundlePath + (isNeedEncryptPath ? (buildPlatType == BuildPlatType.Audit ? EncryptMgr.GetStreamingEncryptPath2Base64Editor("/AssetBundle.lua") : EncryptMgr.GetEncryptPath2Base64Editor("/AssetBundle.lua")):"/AssetBundle.lua")).Replace("\\", "/"),
            (assetbundlePath + (isNeedEncryptPath ? (buildPlatType == BuildPlatType.Audit ? EncryptMgr.GetStreamingEncryptPath2Base64Editor("/LuaAssetBundle/LuaAssetBundle.lua") : EncryptMgr.GetEncryptPath2Base64Editor("/LuaAssetBundle/LuaAssetBundle.lua")):"/LuaAssetBundle/LuaAssetBundle.lua")).Replace("\\", "/"),};
        string[] zipsPath = new string[] { assetbundlePath.Replace("\\", "/"),
                                          (assetbundlePath + (isNeedEncryptPath ? (buildPlatType == BuildPlatType.Audit ? EncryptMgr.GetStreamingEncryptPath2Base64Editor("/LuaAssetBundle/") : EncryptMgr.GetEncryptPath2Base64Editor("/LuaAssetBundle/")):"/LuaAssetBundle/")).Replace("\\", "/") + "/" ,};

        for (int i = 0; i < 2; i++)
        {
            string zipPath = filesPath[i].Replace(".lua", ".zip");
            ZipUtils.ZipFile(filesPath[i], zipsPath[i]);
        }
    }

    private static OpenFileName GetOpenFileName(string title)
    {
        OpenFileName openFile = new OpenFileName();
        openFile.structSize = Marshal.SizeOf(openFile);
        openFile.file = new string(new char[256]);
        openFile.maxFile = openFile.file.Length;
        openFile.fileTitle = new string(new char[64]);
        openFile.maxFileTitle = openFile.fileTitle.Length;
        openFile.initialDir = Application.streamingAssetsPath.Replace('\\', '/');
        openFile.title = title;
        openFile.flags = 0x00080000 | 0x00001000 | 0x00000800 | 0x00000200 | 0x00000008;

        return openFile;
    }

    private static bool OffsetEncryptAB(string path, string assetbundleName)
    {
        try
        {
            string base64Code = EncryptMgr.XORAndBase64Encode(assetbundleName, EncryptMgr.Base64KeyCode);
            byte[] key = string.IsNullOrEmpty(assetbundleName) ? EncryptMgr.GetEncryptKey() : GetAscii(base64Code);
            string encryptHeadString = string.IsNullOrEmpty(assetbundleName) ? EncryptMgr.GetStreamingEncryptyString() : Convert.ToBase64String(Encoding.ASCII.GetBytes(base64Code));
            var buffer = File.ReadAllBytes(path);

            ASCIIEncoding asciiEncoding = new ASCIIEncoding();
            int encryptHeadLength = 7 + key.Length * 3;
            string fileHeadStr = asciiEncoding.GetString(buffer, 0, encryptHeadLength);
            if (fileHeadStr.Equals($"{encryptHeadString}{encryptHeadString}{encryptHeadString}UnityFS"))
            {
                return true;
            }

            byte[] buffer2;
            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (BinaryWriter binaryWriter = new BinaryWriter(memoryStream))
                {
                    binaryWriter.Write(key);
                    binaryWriter.Write(key);
                    binaryWriter.Write(key);
                    binaryWriter.Write(buffer);
                    binaryWriter.Close();
                }
                buffer2 = memoryStream.GetBuffer();
                memoryStream.Close();
            }

            File.WriteAllBytes(path, buffer2);
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            Debug.LogErrorFormat("path:{0}", path);
            return false;
        }
    }

    private static bool OffsetDecryptAB(string path, string assetbundleName)
    {
        string base64Code = EncryptMgr.XORAndBase64Encode(assetbundleName, EncryptMgr.Base64KeyCode);
        byte[] key = string.IsNullOrEmpty(assetbundleName) ? EncryptMgr.GetEncryptKey() : GetAscii(base64Code);
        string encryptHeadString = string.IsNullOrEmpty(assetbundleName) ? EncryptMgr.GetStreamingEncryptyString() : Convert.ToBase64String(Encoding.ASCII.GetBytes(base64Code));

        var buffer = File.ReadAllBytes(path);

        ASCIIEncoding asciiEncoding = new ASCIIEncoding();
        int encryptHeadLength = 7 + key.Length * 3;
        string fileHeadStr = asciiEncoding.GetString(buffer, 0, encryptHeadLength);
        if (!fileHeadStr.Equals($"{encryptHeadString}{encryptHeadString}{encryptHeadString}UnityFS"))
        {
            return true;
        }

        byte[] buffer2;
        using (MemoryStream memoryStream = new MemoryStream(buffer))
        {
            using (BinaryReader binaryWriter = new BinaryReader(memoryStream))
            {
                long ms_num = memoryStream.Length - key.Length;
                binaryWriter.ReadBytes(key.Length * 3);
                buffer2 = binaryWriter.ReadBytes((int)ms_num);
                binaryWriter.Close();
            }
            memoryStream.Close();
        }

        File.WriteAllBytes(path, buffer2);
        return true;
    }

    public static void XOREncryptAB(string path, bool isEncrypt = true)
    {
        var data = File.ReadAllBytes(path);

        //校验是否已经加密or解密
        ASCIIEncoding asciiEncoding = new ASCIIEncoding();
        string fileHeadStr = asciiEncoding.GetString(data, 0, 11);
        if (isEncrypt && !fileHeadStr.Equals("local empty") || !isEncrypt && fileHeadStr.Equals("local empty"))
        {
            return;
        }

        EncryptStream fileStream = new EncryptStream(path, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None, data.Length, false);
        byte[] key = EncryptMgr.GetEncryptKey();
        fileStream.SetEncryptKey(key);
        fileStream.Write(data, 0, data.Length);
        fileStream.Close();
        fileStream.Dispose();
    }

    private static HashSet<string> offsetEncryptSuffixFilter = new HashSet<string>() { ".zip", ".txt", ".manifest" };
    private static HashSet<string> offsetEncryptFileNameFilter = new HashSet<string>() { "AssetBundle", "AssetBundle.lua", "LuaAssetBundle", "LuaAssetBundle.lua" };
    public static bool GetIsOffsetEncryptPass(string file)
    {
        string _file = file.Replace("\\", "/");
        if (offsetEncryptSuffixFilter.Contains(Path.GetExtension(_file)))
        {
            return true;
        }

        foreach (string f in offsetEncryptFileNameFilter)
        {
            if (_file.EndsWith(f))
            {
                return true;
            }
        }

        return false;
    }

    #region 审核服

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部加密(发版,偏移+base64)/Release", priority = 1)]
    public static void EncryptAuditAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.Audit);
        EncryptAndZipVersionFile(BuildPlatType.Audit);
        EncryptLuaFiles("", false, BuildPlatType.Audit);
        Base64EncodingABPath(false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部加密(发版,偏移+base64)/Dev", priority = 1)]
    public static void EncryptAuditDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.AuditDev);
        EncryptAndZipVersionFile(BuildPlatType.AuditDev);
        EncryptLuaFiles("", false, BuildPlatType.AuditDev);
        Base64EncodingABPath(false, BuildPlatType.AuditDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/还原发版文件,偏移+base64逆步骤/Release", priority = 2)]
    public static void DecryptAuditAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.Audit);
        DecryptLuaFiles("", false, BuildPlatType.Audit);
        EncryptAndZipVersionFile(BuildPlatType.Audit, false);
        DecryptAssetBundle(false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/还原发版文件,偏移+base64逆步骤/Dev", priority = 2)]
    public static void DecryptAuditDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        Base64DecodingABPath(false, BuildPlatType.AuditDev);
        DecryptLuaFiles("", false, BuildPlatType.AuditDev);
        EncryptAndZipVersionFile(BuildPlatType.AuditDev, false);
        DecryptAssetBundle(false, BuildPlatType.AuditDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部加密(偏移)/Release", priority = 3)]
    public static void OffsetEncryptAuditAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.Audit);
        EncryptAndZipVersionFile(BuildPlatType.Audit);
        EncryptLuaFiles("", false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部加密(偏移)/Dev", priority = 3)]
    public static void OffsetEncryptAuditDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAssetBundle(false, BuildPlatType.AuditDev);
        EncryptAndZipVersionFile(BuildPlatType.AuditDev);
        EncryptLuaFiles("", false, BuildPlatType.AuditDev);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部解密(偏移)/Release", priority = 4)]
    public static void OffsetDecryptAuditAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.Audit);
        EncryptAndZipVersionFile(BuildPlatType.Audit, false);
        DecryptAssetBundle(false, BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/AssetBundle全部解密(偏移)/Dev", priority = 4)]
    public static void OffsetDecryptAuditDevAssetBundle()
    {
        EncryptMgr.RefreshEncryptKey();
        DecryptLuaFiles("", false, BuildPlatType.AuditDev);
        EncryptAndZipVersionFile(BuildPlatType.AuditDev, false);
        DecryptAssetBundle(false, BuildPlatType.AuditDev);
    }


    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/加密并压缩版本文件/Release", priority = 5)]
    public static void EncryptAndZipAuditVersionFile()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipVersionFile(BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/AB资源加密/Audit/加密并压缩版本文件/Dev", priority = 5)]
    public static void EncryptAndZipAuditDevVersionFile()
    {
        EncryptMgr.RefreshEncryptKey();
        EncryptAndZipVersionFile(BuildPlatType.AuditDev);
    }

    #endregion

    private static string AssetBundleStr = "AssetBundle/";
    public static string GetAssetBundleNameByPath(string path)
    {
        int AssetsIndex = path.LastIndexOf(AssetBundleStr);
        return path.Substring(AssetsIndex + AssetBundleStr.Length, path.Length - AssetBundleStr.Length - AssetsIndex).Replace("/LuaAssetBundle/", "").Replace("LuaAssetBundle/", "");
    }

    public static byte[] GetAscii(string s)
    {
        byte[] ascii_s = Encoding.ASCII.GetBytes(s);
        byte[] base64_b = Encoding.ASCII.GetBytes(Convert.ToBase64String(ascii_s));
        return base64_b;
    }

}