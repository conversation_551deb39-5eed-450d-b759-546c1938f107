using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 技能编辑器事件名称生成器
/// </summary>
public static class SkillEditorEventGenerator
{
    #region 缓存
    private static Dictionary<int, List<string>> _animDataCache;
    private static string[] _eventNames;
    private static string[] _projectileEventNames;
    private static bool _initialized = false;

    /// <summary>
    /// 动画数据缓存
    /// </summary>
    public static Dictionary<int, List<string>> AnimDataCache
    {
        get
        {
            EnsureInitialized();
            return _animDataCache;
        }
    }

    /// <summary>
    /// 事件名称数组
    /// </summary>
    public static string[] EventNames
    {
        get
        {
            EnsureInitialized();
            return _eventNames;
        }
    }

    /// <summary>
    /// 弹道事件名称数组
    /// </summary>
    public static string[] ProjectileEventNames
    {
        get
        {
            EnsureInitialized();
            return _projectileEventNames;
        }
    }

    /// <summary>
    /// 确保已初始化
    /// </summary>
    private static void EnsureInitialized()
    {
        if (!_initialized)
        {
            Initialize();
            _initialized = true;
        }
    }
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化所有数据
    /// </summary>
    public static void Initialize()
    {
        GenerateAnimDataCache();
        GenerateEventNames();
        GenerateProjectileEventNames();
    }

    /// <summary>
    /// 强制重新初始化
    /// </summary>
    public static void Reinitialize()
    {
        _initialized = false;
        Initialize();
    }
    #endregion

    #region 动画数据生成
    /// <summary>
    /// 生成动画数据缓存
    /// </summary>
    private static void GenerateAnimDataCache()
    {
        _animDataCache = new Dictionary<int, List<string>>();

        foreach (SkillEditorEventConfig.ModelType modelType in Enum.GetValues(typeof(SkillEditorEventConfig.ModelType)))
        {
            if (modelType == SkillEditorEventConfig.ModelType.None) continue;

            var animations = GenerateAnimationsForModel(modelType);
            _animDataCache[(int)modelType] = animations.ToList();
        }
    }

    /// <summary>
    /// 为指定模型类型生成动画列表
    /// </summary>
    private static IEnumerable<string> GenerateAnimationsForModel(SkillEditorEventConfig.ModelType modelType)
    {
        var animations = new List<string>();

        // 添加基础动作
        if (modelType != SkillEditorEventConfig.ModelType.Yushou) // 驭兽有特殊的基础动作
        {
            animations.AddRange(SkillEditorEventConfig.BaseActions);
        }

        // 添加特殊动作
        if (SkillEditorEventConfig.SpecialActions.TryGetValue(modelType, out var specialActions))
        {
            animations.AddRange(specialActions);
        }

        // 添加范围动作
        if (SkillEditorEventConfig.ActionRanges.TryGetValue(modelType, out var actionRanges))
        {
            foreach (var kvp in actionRanges)
            {
                animations.AddRange(GenerateRangeActions(kvp.Key, kvp.Value.start, kvp.Value.end));
            }
        }

        // 添加复合动作
        if (SkillEditorEventConfig.CompositeActionRanges.TryGetValue(modelType, out var compositeRanges))
        {
            foreach (var kvp in compositeRanges)
            {
                animations.AddRange(GenerateCompositeActions(
                    kvp.Key, 
                    kvp.Value.mainStart, kvp.Value.mainEnd, 
                    kvp.Value.subStart, kvp.Value.subEnd));
            }
        }

        return animations.Distinct().OrderBy(x => x);
    }

    /// <summary>
    /// 生成范围动作
    /// </summary>
    private static IEnumerable<string> GenerateRangeActions(string prefix, int start, int end)
    {
        for (int i = start; i <= end; i++)
        {
            yield return $"{prefix}{i}";
        }
    }

    /// <summary>
    /// 生成复合动作
    /// </summary>
    private static IEnumerable<string> GenerateCompositeActions(string prefix, int mainStart, int mainEnd, int subStart, int subEnd)
    {
        for (int main = mainStart; main <= mainEnd; main++)
        {
            for (int sub = subStart; sub <= subEnd; sub++)
            {
                yield return $"{prefix}{main}_{sub}";
            }
        }
    }
    #endregion

    #region 事件名称生成
    /// <summary>
    /// 生成事件名称数组
    /// </summary>
    private static void GenerateEventNames()
    {
        var eventNames = new List<string>();

        // 添加基础事件
        eventNames.AddRange(GetBaseEventNames());

        // 添加步伐事件
        eventNames.AddRange(GetFootstepEvents());
        eventNames.AddRange(GetMountStepEvents());

        // 添加动作事件
        eventNames.AddRange(GenerateActionEvents());

        // 添加特殊事件
        eventNames.AddRange(GetSpecialEvents());

        _eventNames = eventNames.Distinct().OrderBy(x => x).ToArray();
    }

    /// <summary>
    /// 获取基础事件名称
    /// </summary>
    private static IEnumerable<string> GetBaseEventNames()
    {
        return new[] { "[none]", "die" };
    }

    /// <summary>
    /// 获取步伐事件
    /// </summary>
    private static IEnumerable<string> GetFootstepEvents()
    {
        return new[] { "footstep/left", "footstep/right" };
    }

    /// <summary>
    /// 获取坐骑步伐事件
    /// </summary>
    private static IEnumerable<string> GetMountStepEvents()
    {
        return Enumerable.Range(1, 4).Select(i => $"mountstep/{i}");
    }

    /// <summary>
    /// 生成动作事件
    /// </summary>
    private static IEnumerable<string> GenerateActionEvents()
    {
        var events = new List<string>();

        // 生成基础攻击事件
        events.AddRange(GeneratePhaseEvents("attack", 1, 16));

        // 生成复合攻击事件
        events.AddRange(GeneratePhaseEvents("attack", 1, 3, 1, 5));

        // 生成魔法事件
        events.AddRange(GeneratePhaseEvents("magic", 1, 4, 1, 3, hasHitPhase: false));

        // 生成连击事件
        events.AddRange(GeneratePhaseEvents("combo", 1, 5, 1, 5));

        // 生成自定义击中事件
        events.AddRange(GeneratePhaseEvents("customized_hit_", 5, 10, hasHitPhase: false));

        // 生成奥义攻击事件
        events.AddRange(GeneratePhaseEvents("esoterica_attack_", 1, 10, hasHitPhase: false));

        // 生成自定义动作事件
        events.AddRange(GeneratePhaseEvents("custom_action_", 1, 12, hasHitPhase: false));

        // 生成冲刺事件
        events.AddRange(GeneratePhaseEvents("rush_", 1, 3));

        return events;
    }

    /// <summary>
    /// 生成阶段事件（begin/hit/end）
    /// </summary>
    private static IEnumerable<string> GeneratePhaseEvents(string prefix, int start, int end, bool hasHitPhase = true)
    {
        var events = new List<string>();
        
        for (int i = start; i <= end; i++)
        {
            string actionName = $"{prefix}{i}";
            events.Add($"{actionName}/begin");
            
            if (hasHitPhase && !SkillEditorEventConfig.ActionsWithoutHit.Contains(prefix))
            {
                events.Add($"{actionName}/hit");
            }
            
            events.Add($"{actionName}/end");
        }
        
        return events;
    }

    /// <summary>
    /// 生成复合阶段事件
    /// </summary>
    private static IEnumerable<string> GeneratePhaseEvents(string prefix, int mainStart, int mainEnd, int subStart, int subEnd, bool hasHitPhase = true)
    {
        var events = new List<string>();
        
        for (int main = mainStart; main <= mainEnd; main++)
        {
            for (int sub = subStart; sub <= subEnd; sub++)
            {
                string actionName = $"{prefix}{main}_{sub}";
                events.Add($"{actionName}/begin");
                
                if (hasHitPhase && !SkillEditorEventConfig.ActionsWithoutHit.Contains(prefix))
                {
                    events.Add($"{actionName}/hit");
                }
                
                events.Add($"{actionName}/end");
            }
        }
        
        return events;
    }

    /// <summary>
    /// 获取特殊事件
    /// </summary>
    private static IEnumerable<string> GetSpecialEvents()
    {
        return new[]
        {
            "touzi/begin", "touzi/hit", "touzi/end",
            "touzi2/begin", "touzi2/hit", "touzi2/end",
            "chuchang/begin", "chuchang/hit", "chuchang/end",
            "jinbi_lizi/begin", "jinbi_lizi/hit", "jinbi_lizi/end",
            "rest/begin", "rest/end",
            "die/begin", "die/end",
            "mount_attack1/begin", "mount_attack1/hit1", "mount_attack1/hit2", "mount_attack1/end",
            "mount_attack2/begin", "mount_attack2/hit", "mount_attack2/end",
            "front_begin/begin",
            "fly_soilder_attack1/begin", "fly_soilder_attack1/hit1", "fly_soilder_attack1/hit2", "fly_soilder_attack1/end",
            "fly_soilder_attack2/begin", "fly_soilder_attack2/hit", "fly_soilder_attack2/end",
            "fall_rest/begin", "fall_rest/end",
            "transformation/begin", "transformation/end",
            "prizedraw/begin", "prizedraw/end",
            "qinggng_rush/begin", "qinggng_rush/end"
        };
    }
    #endregion

    #region 弹道事件生成
    /// <summary>
    /// 生成弹道事件名称数组
    /// </summary>
    private static void GenerateProjectileEventNames()
    {
        var projectileEvents = new List<string>();

        // 基础攻击
        projectileEvents.AddRange(Enumerable.Range(1, 9).Select(i => $"attack{i}"));

        // 魔法技能
        projectileEvents.AddRange(GenerateProjectileEvents("magic", 1, 2, 1, 3));

        // 连击技能
        projectileEvents.AddRange(GenerateProjectileEvents("combo", 1, 2, 1, 4));

        // 特殊技能
        projectileEvents.AddRange(new[]
        {
            "touzi", "touzi2", "chuchang", "jinbi_lizi", "rest", "die", "front_begin",
            "mount_attack1", "mount_attack2", "fly_soilder_attack1", "fly_soilder_attack2"
        });

        _projectileEventNames = projectileEvents.Distinct().OrderBy(x => x).ToArray();
    }

    /// <summary>
    /// 生成弹道事件
    /// </summary>
    private static IEnumerable<string> GenerateProjectileEvents(string prefix, int mainStart, int mainEnd, int subStart, int subEnd)
    {
        for (int main = mainStart; main <= mainEnd; main++)
        {
            for (int sub = subStart; sub <= subEnd; sub++)
            {
                yield return $"{prefix}{main}_{sub}";
            }
        }
    }
    #endregion

    #region 公共方法
    /// <summary>
    /// 获取指定模型类型的动画列表
    /// </summary>
    public static List<string> GetAnimationsForModel(SkillEditorEventConfig.ModelType modelType)
    {
        EnsureInitialized();
        return _animDataCache.TryGetValue((int)modelType, out var animations) 
            ? animations 
            : new List<string>();
    }

    /// <summary>
    /// 检查事件名称是否存在
    /// </summary>
    public static bool IsValidEventName(string eventName)
    {
        EnsureInitialized();
        return _eventNames.Contains(eventName);
    }

    /// <summary>
    /// 检查弹道事件名称是否存在
    /// </summary>
    public static bool IsValidProjectileEventName(string eventName)
    {
        EnsureInitialized();
        return _projectileEventNames.Contains(eventName);
    }
    #endregion
}
