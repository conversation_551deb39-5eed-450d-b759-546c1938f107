﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class AudioCleaner
{
    public static void ClearAudio()
    {
        var checkDirs = new string[] { "Assets/Game/Audios" };
        string[] guids = AssetDatabase.FindAssets("t:audioitem", checkDirs);
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            AudioItem audioItem = AssetDatabase.LoadAssetAtPath<AudioItem>(path);
            if (null == audioItem)
            {
                continue;
            }

            if (!audioItem.IsValid())
            {
                Debug.LogErrorFormat("missing {0}", path);
            }
        }
    }
}
