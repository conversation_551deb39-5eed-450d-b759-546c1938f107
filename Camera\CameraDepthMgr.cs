﻿using Nirvana;
using UnityEngine;
using System.Collections.Generic;


public class CameraDepthMgr : Nirvana.Singleton<CameraDepthMgr>
{
    private int count = 0;
    private HashSet<DepthCamera> depthCameraSet = new HashSet<DepthCamera>();

    public bool IsOpenCameraDepth
    {
        get { return count > 0; }
    }

    public void OpenCameraDepth()
    {
        ++count;
        UpdateStatus();
    }

    public void CloseCameraDepth()
    {
        --count;
        UpdateStatus();
    }

    public void Add(DepthCamera camera)
    {
        depthCameraSet.Add(camera);
    }

    public void Remove(DepthCamera camera)
    {
        depthCameraSet.Remove(camera);
    }

    public void OnGameStop()
    {
        depthCameraSet.Clear();
    }

    private void UpdateStatus()
    {
        NotifyCamera(IsOpenCameraDepth);
    }

    private void NotifyCamera(bool isOn)
    {
        foreach (var camera in depthCameraSet)
        {
            camera.SetIsOpenDepth(isOn);
        }
    }
}
