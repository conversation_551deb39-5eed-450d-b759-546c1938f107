﻿using Nirvana;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using Component = UnityEngine.Component;

public class TemplateUITools : BaseEditorWindow
{
    private string changePath = "Assets/Game/UIs/View";

    private Vector3 scroll_pos;

    // 按钮
    private Sprite btnImage;
    private Sprite oldBtnImage;
    private Sprite newRedPointImage;
    private Sprite redPointImage;
    private Font btnFont;
    private int btnFontSize = 22;
    private float textFixWidth = -1;
    private Color btnTextColor = Color.white;
    private Color gradientUpColor = Color.black;
    private Color gradientDownColor = Color.black;
    private Color outlineColor = Color.black;
    private Color shadowColor = Color.black;
    private Vector2 shadowDistance = new Vector2(1, -1);
    private Vector2 btnTextPos = new Vector2();
    private Vector2 btnTextSize = new Vector2();
    private Vector2 btnImageSize = new Vector2();
    private Vector2 btnRedPointPos = new Vector2();
    private bool isChangeBtn = false;
    private bool needChangeOldBtn = false;
    private bool needTextExpand = false;
    private bool needGradient = false;
    private bool needOutLine = false;
    private bool needShadow = false;
    private bool needChangeRedImage = false;
    private int btnValueType = -1;

    // 标签
    private bool isChangeLabel = false;
    private bool needChangeOldLabel = false;
    private bool needLabelRichText = false;
    private Sprite labelImage;
    private Sprite oldLabelImage;
    private Font labelFont;
    private int labelFontSize = 22;
    private Vector2 labelTextPos = new Vector2();
    private Vector2 labelTextSize = new Vector2();
    private Vector2 labelImageSize = new Vector2();
    private Color labelTextColor = Color.white;
    private int labelValueType = -1;

    // 战力
    private int capValueType = -1;
    private bool isChangeCap = false;
    private Sprite capImage;
    private Sprite oldCapImage;
    private Font capFont;
    //private Sprite capIconImage;
    //private Sprite oldCapIconImage;
    private int capFontSize = 0;
    private Color capFontColor = Color.white;
    private bool capNeedGradient = false;
    private Color capGradientUpColor = Color.white;
    private Color capGradientDownColor = Color.white;
    private Vector2 capImageSize = new Vector2();



    private enum BtnType
    {
        L_1, L_2, M_1, M_2, S_1, S_2
    }

    private enum LabelType
    {
        flag_green, flag_red,
    }

    private enum CapType
    {
        common, max, marry
    }

    [MenuItem("Tools/换UI工具/一键替换预制体UI")]
    private static void ShowWindow()
    {
        EditorWindow.GetWindow<TemplateUITools>(false, "一键替换预制体UI（需要先配置所有新的参数）");
    }

    private void OnGUI()
    {
        scroll_pos = EditorGUILayout.BeginScrollView(scroll_pos);

        changePath = EditorGUILayout.TextField("修改路径: ", changePath);
        isChangeBtn = EditorGUILayout.ToggleLeft("改变按钮", isChangeBtn);
        if (isChangeBtn)
        {
            needChangeOldBtn = EditorGUILayout.ToggleLeft("改变旧按钮", needChangeOldBtn);
            if (needChangeOldBtn)
            {
                oldBtnImage = EditorGUILayout.ObjectField("旧按钮图片（注意选择样式）: ", oldBtnImage, typeof(Sprite), true) as Sprite;
            }

            BtnValueType = EditorGUILayout.IntPopup("按钮样式: ", BtnValueType, new string[] { "L_1", "L_2", "M_1", "M_2", "S_1", "S_2" }, new int[] { 0, 1, 2, 3, 4, 5 });
            if (BtnValueType == -1)
            {
                BtnValueType = 0;
            }

            BtnImage = EditorGUILayout.ObjectField("按钮图片: ", BtnImage, typeof(Sprite), true) as Sprite;
            btnFont = EditorGUILayout.ObjectField("按钮字体: ", btnFont, typeof(Font), true) as Font;
            btnFontSize = EditorGUILayout.IntField("文字 Size:", btnFontSize);
            btnTextColor = EditorGUILayout.ColorField("文字 Color: ", btnTextColor);

            btnTextPos = EditorGUILayout.Vector2Field("文字Anchored Pos: ", btnTextPos);

            btnTextSize = EditorGUILayout.Vector2Field("文字Size Delta: ", btnTextSize);


            needTextExpand = EditorGUILayout.ToggleLeft("需要字间距", needTextExpand);
            if (needTextExpand)
            {
                textFixWidth = EditorGUILayout.FloatField("字间距", textFixWidth);
            }

            needGradient = EditorGUILayout.ToggleLeft("need Gradient", needGradient);
            if (needGradient)
            {
                gradientUpColor = EditorGUILayout.ColorField("文字上渐变色: ", gradientUpColor);
                gradientDownColor = EditorGUILayout.ColorField("文字下渐变色: ", gradientDownColor);
            }

            needOutLine = EditorGUILayout.ToggleLeft("need OutLine", needOutLine);
            if (needOutLine)
            {
                outlineColor = EditorGUILayout.ColorField("OutLine Color: ", outlineColor);
            }

            needShadow = EditorGUILayout.ToggleLeft("need Shadow ", needShadow);
            if (needShadow)
            {
                shadowColor = EditorGUILayout.ColorField("Shadow Color: ", shadowColor);
                shadowDistance = EditorGUILayout.Vector2Field("Shadow Distance: ", shadowDistance);
            }

            redPointImage = EditorGUILayout.ObjectField("红点图片: ", redPointImage, typeof(Sprite), true) as Sprite;

            needChangeRedImage = EditorGUILayout.ToggleLeft("需要替换红点图片 ", needChangeRedImage);
            if (needChangeRedImage) {
                newRedPointImage = EditorGUILayout.ObjectField("新红点图片: ", newRedPointImage, typeof(Sprite), true) as Sprite;
            }

            btnRedPointPos = EditorGUILayout.Vector2Field("红点pos（右上对齐）: ", btnRedPointPos);
        }

        isChangeLabel = EditorGUILayout.ToggleLeft("改变标签 ", isChangeLabel);
        if (isChangeLabel)
        {
            needChangeOldLabel = EditorGUILayout.ToggleLeft("改变旧标签 ", needChangeOldLabel);
            if (needChangeOldLabel)
            {
                oldLabelImage = EditorGUILayout.ObjectField("旧标签图片: ", oldLabelImage, typeof(Sprite), true) as Sprite;
            }

            LabelValueType = EditorGUILayout.IntPopup("标签样式: ", LabelValueType, new string[] { "flag_green", "flag_red", }, new int[] { 0, 1, });
            if (LabelValueType == -1)
            {
                LabelValueType = 0;
            }

            LabelImage = EditorGUILayout.ObjectField("标签图片: ", LabelImage, typeof(Sprite), true) as Sprite;
            labelFont = EditorGUILayout.ObjectField("标签字体: ", labelFont, typeof(Font), true) as Font;
            needLabelRichText = EditorGUILayout.ToggleLeft("文字是否需要 Rich Text", needLabelRichText);
            labelFontSize = EditorGUILayout.IntField("文字 Size:", labelFontSize);
            labelTextColor = EditorGUILayout.ColorField("文字 Color: ", labelTextColor);
            labelTextPos = EditorGUILayout.Vector2Field("文字 Anchored Pos: ", labelTextPos);
            labelTextSize = EditorGUILayout.Vector2Field("文字 Size Delta: ", labelTextSize);
        }

        isChangeCap = EditorGUILayout.ToggleLeft("改变战力 ", isChangeCap);
        if (isChangeCap)
        {
            CapValueType = EditorGUILayout.IntPopup("战力样式: ", CapValueType, new string[] { "common", "max", "marry" }, new int[] { 0, 1, 2 });
            if (CapValueType == -1)
            {
                CapValueType = 0;
            }

            oldCapImage = EditorGUILayout.ObjectField("旧战力图片: ", oldCapImage, typeof(Sprite), true) as Sprite;
            //oldCapIconImage = EditorGUILayout.ObjectField("旧战力文字图片: ", oldCapIconImage, typeof(Sprite), true) as Sprite;
            CapImage = EditorGUILayout.ObjectField("战力图片: ", CapImage, typeof(Sprite), true) as Sprite;
            //capIconImage = EditorGUILayout.ObjectField("战力文字图片: ", capIconImage, typeof(Sprite), true) as Sprite;
            capImageSize = EditorGUILayout.Vector2Field("父节点 Size Delta: ", capImageSize);
            capFont = EditorGUILayout.ObjectField("新战力字体: ", capFont, typeof(Font), true) as Font;
            capFontSize = EditorGUILayout.IntField("文字 Size:", capFontSize);
            capFontColor = EditorGUILayout.ColorField("文字 Color: ", capFontColor);
            capNeedGradient = EditorGUILayout.ToggleLeft("need Gradient", capNeedGradient);
            if (capNeedGradient)
            {
                capGradientUpColor = EditorGUILayout.ColorField("文字上渐变色: ", capGradientUpColor);
                capGradientDownColor = EditorGUILayout.ColorField("文字下渐变色: ", capGradientDownColor);
            }
        }

        GUILayout.Space(10);
        if (GUILayout.Button("ChangeAll"))
        {
            if (CheckSetting())
            {
                ChangeAll();
            }
        }

        EditorGUILayout.EndScrollView();
    }

    private Sprite BtnImage
    {
        set
        {
            if (btnImage != value)
            {
                btnImage = value;
                UpdateBtnImageSize();
            }
        }
        get
        {
            return btnImage;
        }
    }

    private int BtnValueType
    {
        set
        {
            if (btnValueType != value)
            {
                btnValueType = value;
                BtnTypeChange();
            }
        }
        get
        {
            return btnValueType;
        }
    }

    private void UpdateBtnImageSize()
    {
        if (isChangeBtn)
        {
            if (null != btnImage)
            {
                btnImageSize = new Vector2(btnImage.rect.width, btnImage.rect.height);
                btnTextSize = new Vector2(btnImage.rect.width, btnImage.rect.height);
            }
        }
    }

    private static string GameDir = "Assets/Game/";
    private static string UiDir = GameDir + "UIs/";
    private void BtnTypeChange()
    {
        needTextExpand = false;
        needGradient = false;
        needOutLine = false;
        needShadow = false;
        needChangeRedImage = true;

        if (BtnValueType == (int)BtnType.L_1)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_5.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_2.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);
            btnFontSize = 24;
            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnTextPos = Vector2.zero;

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(5, 5);

        }
        else if (BtnValueType == (int)BtnType.L_2)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_4.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_1.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);
            btnFontSize = 24;
            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnTextPos = Vector2.zero;

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(5, 5);
        }
        else if (BtnValueType == (int)BtnType.M_1)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_7.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_4.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnFontSize = 24;
            btnTextPos = new Vector2(0, 1);

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(4, 4);
        }
        else if (BtnValueType == (int)BtnType.M_2)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_6.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_3.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnFontSize = 24;
            btnTextPos = new Vector2(0, 1);

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(4, 4);
        }
        else if (BtnValueType == (int)BtnType.S_1)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_9.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_25.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnFontSize = 22;
            btnTextPos = new Vector2(0, 1);

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(4, 4);
        }
        else if (BtnValueType == (int)BtnType.S_2)
        {
            Color nowColor;
            string resString = UiDir + "Images/Button/a3_ty_btn_8.png";
            BtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Button/a2_ty_anniu_26.png";
            oldBtnImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_L.TTF";
            btnFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            ColorUtility.TryParseHtmlString("#001020FF", out nowColor);
            btnTextColor = nowColor;
            btnFontSize = 22;
            btnTextPos = new Vector2(0, 1);

            needTextExpand = true;
            textFixWidth = 60;

            resString = UiDir + "ImagesA2/Common/a2_ty_hongdian.png";
            redPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "Images/Common/a3_ty_hd.png";
            newRedPointImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);
            btnRedPointPos = new Vector2(4, 4);
        }
    }

    private int LabelValueType
    {
        set
        {
            if (labelValueType != value)
            {
                labelValueType = value;
                LabelTypeChange();
            }
        }
        get
        {
            return labelValueType;
        }
    }

    private Sprite LabelImage
    {
        set
        {
            if (labelImage != value)
            {
                labelImage = value;
                UpdateLabelImageSize();
            }
        }
        get
        {
            return labelImage;
        }
    }

    private void UpdateLabelImageSize()
    {
        if (isChangeLabel)
        {
            if (null != labelImage)
            {
                labelImageSize = new Vector2(labelImage.rect.width, labelImage.rect.height);
                labelTextSize = new Vector2(labelImage.rect.width, labelImage.rect.height);
            }
        }
    }

    private void LabelTypeChange()
    {
        if (LabelValueType == (int)LabelType.flag_green)
        {
            string resString = UiDir + "Images/Common/a3_ty_tlabel_2.png";
            LabelImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Common/a2_ty_bq_2.png";
            oldLabelImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_M.TTF";
            labelFont = AssetDatabase.LoadAssetAtPath<Font>(resString);
            labelFontSize = 26;
            Color nowColor;
            ColorUtility.TryParseHtmlString("#FFFFFFFF", out nowColor);
            labelTextColor = nowColor;
            labelTextPos = Vector2.zero;
            needLabelRichText = false;
        }
        else if (LabelValueType == (int)LabelType.flag_red)
        {
            string resString = UiDir + "Images/Common/a3_ty_tlabel_1.png";
            LabelImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Common/a2_ty_bq_3.png";
            oldLabelImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_M.TTF";
            labelFont = AssetDatabase.LoadAssetAtPath<Font>(resString);
            labelFontSize = 26;
            Color nowColor;
            ColorUtility.TryParseHtmlString("#FFFFFFFF", out nowColor);
            labelTextColor = nowColor;
            labelTextPos = Vector2.zero;
            needLabelRichText = false;
        }
    }

    private Sprite CapImage
    {
        set
        {
            if (capImage != value)
            {
                capImage = value;
                UpdateCapImageSize();
            }
        }
        get
        {
            return capImage;
        }
    }

    private void UpdateCapImageSize()
    {
        if (isChangeCap)
        {
            if (null != capImage)
            {
                capImageSize = new Vector2(capImage.rect.width, capImage.rect.height);
            }
            else
            {
                capImageSize = Vector2.zero;
            }
        }
    }

    private int CapValueType
    {
        set
        {
            if (capValueType != value)
            {
                capValueType = value;
                CapTypeChange();
            }
        }
        get
        {
            return capValueType;
        }
    }

    private void CapTypeChange()
    {
        if (CapValueType == (int)CapType.common)
        {
            string resString = UiDir + "Images/Common/a1_ty_zhanli.png";
            oldCapImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            //resString = UiDir + "Images/Common/a1_ty_xingzhan.png";
            //oldCapIconImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Common/a2_ty_zdl_2.png";
            CapImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            //resString = UiDir + "ImagesA2/Common/a2_ty_zdl_2_zl.png";
            //capIconImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_M.TTF";
            //resString = UiDir + "Fonts/bs_zhanli/bs_zhanli_text.fontsettings";
            capFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            Color nowColor;
            //ColorUtility.TryParseHtmlString("#FF9797FF", out nowColor);
            //capFontColor = nowColor;
            capFontSize = 34;
            capFontColor = Color.white;
            capNeedGradient = true;
            ColorUtility.TryParseHtmlString("#FBFAE7FF", out nowColor);
            capGradientUpColor = nowColor;
            ColorUtility.TryParseHtmlString("#F8E084FF", out nowColor);
            capGradientDownColor = nowColor;


        }
        else if (CapValueType == (int)CapType.max)
        {
            string resString = UiDir + "Images/Common/a1_ty_zhanli.png";
            oldCapImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            //resString = UiDir + "Images/Common/a1_ty_xingmanji.png";
            //oldCapIconImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "ImagesA2/Common/a2_ty_zdl_2.png";
            CapImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            //resString = UiDir + "ImagesA2/Common/a2_ty_zdl_2_mz.png";
            //capIconImage = AssetDatabase.LoadAssetAtPath<Sprite>(resString);

            resString = UiDir + "TTF/FZSSKSJW_M.TTF";
            //resString = UiDir + "Fonts/bs_zhanli/bs_zhanli_text.fontsettings";
            capFont = AssetDatabase.LoadAssetAtPath<Font>(resString);

            //ColorUtility.TryParseHtmlString("#FF9797FF", out nowColor);
            //capFontColor = nowColor;
            capFontSize = 34;
            capFontColor = Color.white;
            capNeedGradient = true;
            ColorUtility.TryParseHtmlString("#FBFAE7FF", out Color nowColor);
            capGradientUpColor = nowColor;
            ColorUtility.TryParseHtmlString("#F8E084FF", out nowColor);
            capGradientDownColor = nowColor;
        }
    }




    private bool CheckSetting()
    {
        if (isChangeBtn)
        {
            if (needChangeOldBtn && oldBtnImage == null)
            {
                this.ShowNotification(new GUIContent("旧按钮图片为空"));
                return false;
            }

            if (null == btnImage)
            {
                this.ShowNotification(new GUIContent("按钮图片为空"));
                return false;
            }

            if (null == redPointImage)
            {
                this.ShowNotification(new GUIContent("红点图片为空"));
                return false;
            }

            if (needChangeRedImage &&  null == newRedPointImage)
            {
                this.ShowNotification(new GUIContent("新红点图片为空"));
                return false;
            }

            if (null == btnFont)
            {
                this.ShowNotification(new GUIContent("按钮字体为空"));
                return false;
            }
        }

        if (isChangeLabel)
        {
            if (needChangeOldLabel && oldLabelImage == null)
            {
                this.ShowNotification(new GUIContent("旧标签图片为空"));
                return false;
            }

            if (null == labelImage)
            {
                this.ShowNotification(new GUIContent("标签图片为空"));
                return false;
            }

            if (null == labelFont)
            {
                this.ShowNotification(new GUIContent("标签字体为空"));
                return false;
            }
        }

        if (isChangeCap)
        {
            if (null == oldCapImage)
            {
                this.ShowNotification(new GUIContent("旧战力图片为空"));
                return false;
            }

            //if (null == oldCapIconImage)
            //{
            //    this.ShowNotification(new GUIContent("旧战力文字图片为空"));
            //    return false;
            //}

            if (null == capImage)
            {
                this.ShowNotification(new GUIContent("战力图片为空"));
                return false;
            }

            //if (null == capIconImage)
            //{
            //    this.ShowNotification(new GUIContent("战力文字图片为空"));
            //    return false;
            //}

            if (null == capFont)
            {
                this.ShowNotification(new GUIContent("战力字体为空"));
                return false;
            }
        }

        if (isChangeBtn || isChangeLabel || isChangeCap)
        {
            return true;
        }

        return false;
    }

    public void ChangeAll()
    {
        string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { changePath });
        int endIndex = guids.Length;
        if (endIndex < 1)
        {
            return;
        }

        int count = 0;
        try
        {
            for (int i = 0; i < endIndex; i++)
            {
                var guid = guids[i];
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var obj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                if (isChangeBtn)
                {
                    this.ChangeBtn(obj);
                }

                if (isChangeLabel)
                {
                    this.ChangeLabel(obj);
                }

                if (isChangeCap)
                {
                    this.ChangeCap(obj, path);
                }

                count++;
                EditorUtility.DisplayProgressBar("处理中...", string.Format("{0}/{1}", count, endIndex), (float)count / (float)endIndex);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    private void ChangeBtn(GameObject obj)
    {
        bool isChange = false;
        var all_images = obj.GetComponentsInChildren<Image>(true);
        for (int a = 0; a < all_images.Length; a++)
        {
            var image = all_images[a];
            if ((needChangeOldBtn && image.sprite == oldBtnImage) || image.sprite == btnImage)
            {

                image.sprite = btnImage;
                image.SetNativeSize();
                image.rectTransform.sizeDelta = btnImageSize;

                // 修改文本
                var btn_child_texts = image.transform.GetComponentsInChildren<Text>(true);
                //for (int t = 0; t < btn_child_texts.Length; t++)

                if (btn_child_texts.Length > 0)
                {
                    var btn_child_text = btn_child_texts[0];
                    //if (btn_child_text.font == btnFont)
                    if (btn_child_text != null)
                    {
                        btn_child_text.font = btnFont;
                        btn_child_text.rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                        btn_child_text.rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                        btn_child_text.rectTransform.pivot = new Vector2(0.5f, 0.5f);
                        btn_child_text.rectTransform.anchoredPosition = btnTextPos;
                        btn_child_text.rectTransform.sizeDelta = btnTextSize;

                        btn_child_text.color = btnTextColor;
                        btn_child_text.fontSize = btnFontSize;

                        if (btn_child_text.HasComponent<UITextExpand>())
                        {
                            DestroyImmediate(btn_child_text.GetComponent<UITextExpand>(), true);
                        }

                        if (btn_child_text.HasComponent<UIGradient>())
                        {
                            DestroyImmediate(btn_child_text.GetComponent<UIGradient>(), true);
                        }

                        if (btn_child_text.HasComponent<Outline>())
                        {
                            DestroyImmediate(btn_child_text.GetComponent<Outline>(), true);
                        }

                        if (btn_child_text.HasComponent<Shadow>())
                        {
                            DestroyImmediate(btn_child_text.GetComponent<Shadow>(), true);
                        }

                        // 文字间距
                        if (needTextExpand)
                        {
                            string text = btn_child_text.text;
                            text = text.Replace(" ", "");
                            btn_child_text.text = text;
                            UITextExpand uITextExpand = btn_child_text.GetOrAddComponent<UITextExpand>();
                            uITextExpand.fitWidth = textFixWidth;
                        }

                        // 渐变
                        if (needGradient)
                        {
                            UIGradient uIGradient = btn_child_text.GetOrAddComponent<UIGradient>();
                            uIGradient.Color1 = gradientUpColor;
                            uIGradient.Color2 = gradientDownColor;
                        }

                        // 外发光
                        if (needOutLine)
                        {
                            Outline outline = btn_child_text.GetOrAddComponent<Outline>();
                            outline.effectColor = outlineColor;
                        }

                        // 阴影
                        if (needShadow)
                        {
                            Shadow shadow = btn_child_text.GetOrAddComponent<Shadow>();
                            shadow.effectColor = shadowColor;
                            shadow.effectDistance = shadowDistance;
                        }

                        isChange = true;

                        if (btn_child_text.HasComponent<UITextExpand>())
                        {
                            UITextExpand uITextExpand = btn_child_text.GetComponent<UITextExpand>();
                            uITextExpand.fitWidth = textFixWidth;
                            isChange = true;
                        }
                    }
                }

                var btn_child_images = image.transform.GetComponentsInChildren<Image>(true);
                for (int r = 0; r < btn_child_images.Length; r++)
                {
                    var btn_child_image = btn_child_images[r];
                    if (btn_child_image.sprite == redPointImage)
                    {
                        if (needChangeRedImage) {
                            btn_child_image.sprite = newRedPointImage;
                            btn_child_image.SetNativeSize();
                        }

                        btn_child_image.rectTransform.anchorMin = new Vector2(1, 1);
                        btn_child_image.rectTransform.anchorMax = new Vector2(1, 1);
                        btn_child_image.rectTransform.pivot = new Vector2(1, 1);
                        btn_child_image.rectTransform.anchoredPosition = btnRedPointPos;
                        isChange = true;
                    }
                }
            }
        }

        if (isChange)
        {
            EditorUtility.SetDirty(obj);
        }
    }

    private void ChangeLabel(GameObject obj)
    {
        bool isChange = false;
        var all_images = obj.GetComponentsInChildren<Image>(true);
        for (int a = 0; a < all_images.Length; a++)
        {
            var image = all_images[a];
            if ((needChangeOldLabel && image.sprite == oldLabelImage) || image.sprite == LabelImage)
            //if (image.sprite == btnImage)
            {
                image.sprite = LabelImage;
                image.SetNativeSize();
                image.rectTransform.sizeDelta = labelImageSize;
                image.type = Image.Type.Simple;

                // 修改文本
                var btn_child_texts = image.transform.GetComponentsInChildren<Text>(true);
                for (int t = 0; t < btn_child_texts.Length; t++)
                {
                    var btn_child_text = btn_child_texts[t];
                    btn_child_text.font = labelFont;
                    btn_child_text.supportRichText = needLabelRichText;
                    btn_child_text.rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                    btn_child_text.rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                    btn_child_text.rectTransform.pivot = new Vector2(0.5f, 0.5f);
                    btn_child_text.rectTransform.anchoredPosition = labelTextPos;
                    btn_child_text.rectTransform.sizeDelta = labelTextSize;

                    btn_child_text.color = labelTextColor;
                    btn_child_text.fontSize = labelFontSize;

                    isChange = true;
                }
            }
        }

        if (isChange)
        {
            PrefabUtility.ResetToPrefabState(obj.gameObject);
            PrefabUtility.SetPropertyModifications(obj.gameObject, new PropertyModification[] { });
        }
    }


    private void ChangeCap(GameObject obj, string path)
    {
        bool isChange = false;
        var all_images = obj.GetComponentsInChildren<Image>(true);

        for (int a = 0; a < all_images.Length; a++)
        {
            var image = all_images[a];
            if (image.sprite == oldCapImage || image.sprite == CapImage)
            {
                GameObject objParent = image.gameObject.transform.parent.gameObject;
                var all_texts = objParent.GetComponentsInChildren<Text>(true);

                if (all_texts.Length > 1)
                {
                    Debug.LogError("---这预制体 战力里有两个文本 单独处理---" + obj.name);
                    continue;
                }
                else if (all_texts.Length == 1)
                {
                    isChange = true;
                }
            }
        }

        if (isChange)
        {
            GameObject prefab = (GameObject)PrefabUtility.InstantiatePrefab(obj);
            PrefabUtility.UnpackPrefabInstance(prefab, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
            var all_prefab_images = prefab.GetComponentsInChildren<Image>(true);
            int change_num = 0;

            for (int a = 0; a < all_prefab_images.Length; a++)
            {
                var image = all_prefab_images[a];
                if (image.sprite == oldCapImage || image.sprite == CapImage)
                {
                    //bool canChange = false;
                    GameObject objParent = image.gameObject.transform.parent.gameObject;
                    //var all_icons = objParent.GetComponentsInChildren<Image>(true);

                    var all_texts = objParent.GetComponentsInChildren<Text>(true);
                    var text = all_texts[0];
                    RemoveComponent(text.gameObject, false);
                    text.transform.SetParent(objParent.transform);
                    text.rectTransform.sizeDelta = new Vector2(240, 40);
                    text.rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                    text.rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                    text.rectTransform.pivot = new Vector2(0.5f, 0.5f);
                    text.rectTransform.anchoredPosition = new Vector2(58, -6);
                    text.font = capFont;
                    text.color = capFontColor;
                    text.fontSize = capFontSize;
                    text.supportRichText = false;
                    text.raycastTarget = false;

                    // 渐变
                    if (capNeedGradient)
                    {
                        UIGradient uIGradient = text.GetOrAddComponent<UIGradient>();
                        uIGradient.Color1 = capGradientUpColor;
                        uIGradient.Color2 = capGradientDownColor;
                    }

                    text.alignment = TextAnchor.MiddleLeft;
                    DeleteItem(text.gameObject, objParent.name, text.name);
                    DeleteItem(objParent, objParent.name, text.name);

                    // 父节点
                    RemoveComponent(objParent, false);
                    var parentRect = objParent.GetComponent<RectTransform>();
                    parentRect.sizeDelta = capImageSize;

                    // 背景
                    GameObject bgImgObj = new GameObject();
                    bgImgObj.gameObject.name = "cap_bg";
                    bgImgObj.transform.SetParent(objParent.transform);
                    var bgRect = bgImgObj.GetOrAddComponent<RectTransform>();
                    bgRect.anchoredPosition = Vector2.zero;
                    bgRect.anchorMin = new Vector2(0.5f, 0.5f);
                    bgRect.anchorMax = new Vector2(0.5f, 0.5f);
                    bgRect.pivot = new Vector2(0.5f, 0.5f);
                    var bgImg = bgImgObj.GetOrAddComponent<Image>();
                    bgImg.sprite = CapImage;
                    bgImg.type = Image.Type.Simple;
                    bgImg.SetNativeSize();
                    bgImg.raycastTarget = false;

                    // 战力 icon
                    //GameObject iconObj = new GameObject();
                    //iconObj.gameObject.name = "zhan_li";
                    //var iconRect = iconObj.GetOrAddComponent<RectTransform>();
                    //iconRect.anchoredPosition = new Vector2(0, -8);
                    //iconRect.anchorMin = new Vector2(0.5f, 0.5f);
                    //iconRect.anchorMax = new Vector2(0.5f, 0.5f);
                    //iconRect.pivot = new Vector2(0.5f, 0.5f);

                    //var iconImg = iconObj.GetOrAddComponent<Image>();
                    //iconImg.sprite = capIconImage;
                    //iconImg.type = Image.Type.Simple;
                    //iconImg.SetNativeSize();
                    //iconImg.raycastTarget = false;
                    ////iconObj.transform.SetParent(contObj.transform);
                    //iconObj.transform.SetAsFirstSibling();

                    // 战力 value
                    //text.transform.SetParent(contObj.transform);
                    //text.transform.SetAsLastSibling();

                    text.transform.SetAsLastSibling();

                    change_num++;

                }
             }

            if (change_num > 0)
            {
                PrefabUtility.SaveAsPrefabAsset(prefab, path);
            }
   
            DestroyImmediate(prefab);

        }
    }

    private void DeleteItem(GameObject obj, string noDeleteName1, string noDeleteName2)
    {
        List<GameObject> objList = new List<GameObject>();
        foreach (Transform child in obj.transform)
        {
            string name = child.name;
            if (name != noDeleteName1 && name != noDeleteName2)
            {
                objList.Add(child.gameObject);
            }
        }

        if (objList.Count > 0)
        {
            foreach (GameObject child in objList)
            {
                Debug.LogError(child.name);
                DestroyImmediate(child, true);
            }
        }
    }

    public static void RemoveComponent(GameObject gameObject, bool noRemoveElement)
    {
        List<UnityEngine.Component> comList = new();
        foreach (var component in gameObject.GetComponents<Component>())
        {
            string compName = component.GetType().Name.ToString();
            if (compName != "RectTransform" && compName != "Text" && compName != "Image" && compName != "CanvasRenderer"
                && compName != "Canvas" && compName != "GraphicRaycaster" && compName != "UIOverrideOrder" && compName != "UINameTable")
            {
                if (!noRemoveElement || (noRemoveElement && compName != "LayoutElement"))
                    comList.Add(component);
            }
        }

        if (comList.Count > 0)
        {
            //Debug.LogError("RemoveComponent：" + gameObject.name + "     Count:" + comList.Count);
            foreach (Component item in comList)
            {
                //item.GetType().Name.ToString():Transform/item.GetType().FullName.ToString():UnityEngine.Transform
                //Debug.LogError("RemoveComponent：" + gameObject.name + "    Name:" + item.GetType().Name.ToString() + "    FullName:" + item.GetType().FullName.ToString());
                //Debug.Log("RemoveComponent：" + gameObject.name + "/" + item.GetType());
                DestroyImmediate(item, true);
            }
        }
    }
}


