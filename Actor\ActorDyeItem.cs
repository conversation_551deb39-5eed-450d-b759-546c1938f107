using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName ="NewActorDyeItem", menuName = "Actor/ActorDyeItem")]
public class ActorDyeItem : ScriptableObject
{
    [SerializeField]
    private ChangeSkin.SkinType skin_Type;

    [SerializeField]
    public Material[] materials;

    public ChangeSkin.SkinType SkinType
    {
        get
        {
            return this.skin_Type;
        }
    }

    public Material[] Materials
    {
        get
        {
            return this.materials;
        }
    }
}
