﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using UnityEngine.Profiling;
using LuaInterface;
using System.Text;
using UnityEngine.Rendering;

public static class TextureWindow
{
    private enum Tag
    {
        Texture, Mesh, Shader, Bundle, Material, GameObject
    }

    private static bool showDetails = false;
    private static bool useProfiler = true;
    private static Tag curTag = Tag.Texture;

    private static Vector2 scroller = Vector2.zero;
    private static int curTexureTotalCount = 0;
    private static int curMeshTotalCount = 0;
    private static int curMatTotalCount = 0;
    private static int curGameobjTotalCount = 0;
    private static int curShaderTotalCount = 0;
    private static int curBundleTotalCount = 0;

    private static long curTotalBytes = 0;
    private static long curTotalTextureBytes = 0;
    private static long curTotalMeshBytes = 0;
    private static long curTotalShaderBytes = 0;
    private static long curTotalBundleBytes = 0;
    private static long curTotalMatBytes = 0;
    private static long curTotalGameObjectBytes = 0;
    private static long curPssBytes = 0;

    private static List<MemoryItem> textureList = new List<MemoryItem>();
    private static List<MemoryItem> meshList = new List<MemoryItem>();
    private static List<MemoryItem> gameobjList = new List<MemoryItem>();
    private static List<MemoryItem> shaderList = new List<MemoryItem>();
    private static List<MemoryItem> bundleList = new List<MemoryItem>();
    private static List<MemoryItem> materialList = new List<MemoryItem>();

    private static int totalTextureCount = 0;
    private static int totalMeshCount = 0;
    private static int totalMatCount = 0;
    private static int totalGameobjCount = 0;
    private static int totalShaderCount = 0;
    private static int totalBundleCount = 0;

    private static long totalBytes = 0;
    private static long totalTextureBytes = 0;
    private static long totalMeshBytes = 0;
    private static long totalShaderBytes = 0;
    private static long totalBundleBytes = 0;
    private static long totalMatBytes = 0;
    private static long totalGameObjectBytes = 0;
    private static long totalPssBytes = 0;

    private static float frameTimeStamp = 0;

    private static List<MemoryItem> savedTextureList = new List<MemoryItem>();
    private static List<MemoryItem> savedMeshList = new List<MemoryItem>();
    private static List<MemoryItem> savedGameobjList = new List<MemoryItem>();
    private static List<MemoryItem> savedShaderList = new List<MemoryItem>();
    private static List<MemoryItem> savedBundleList = new List<MemoryItem>();
    private static List<MemoryItem> savedMaterialList = new List<MemoryItem>();

    private static int savedTotalTextureCount = 0;

    private static long savedTotalBytes = 0;
    private static long savedTotalTextureBytes = 0;
    private static long savedTotalMeshBytes = 0;
    private static long savedTotalShaderBytes = 0;
    private static long savedTotalBundleBytes = 0;
    private static long savedPssBytes = 0;

    private static float savedFrameTimeStamp = 0;
    private static float timer = 0;
    private static int curUpdateIndex = 0;
    private static HashSet<Object> map = new HashSet<Object>();

    private const int RGBA_PIXEL_SIZE = 4;
    private const int VERT_SIZE = 120;

    private static RectTransform block;
    private static RectTransform Block
    {
        get
        {
            if (null != block)
            {
                return block;
            }

            var view = GameObject.Find("GameRoot/BaseView");
            if (null != view)
            {
                var baseView = UnityEngine.GameObject.Instantiate(view);
                UnityEngine.GameObject.DontDestroyOnLoad(baseView);
                baseView.GetComponent<Canvas>().sortingOrder = short.MaxValue;
                var root = baseView.transform.Find("Root");
                var go = new GameObject();
                go.transform.SetParent(root, false);
                go.AddComponent<Nirvana.UIBlock>();

                block = (RectTransform)go.transform;
                block.pivot = new Vector2(0, 1);
                block.anchorMin = new Vector2(0, 1);
                block.anchorMax = new Vector2(0, 1);
                block.anchoredPosition = Vector2.zero;

                return block;
            }

            return null;
        }
    }

    [NoToLua]
    public static string GetGraphicMemoryInfo(bool forceCollect)
    {
        if (forceCollect)
            CollectAll(false);

        return GetInfo();
    }

    private static string GetInfo()
    {
        StringBuilder builder = new StringBuilder();

        builder.AppendFormat("TextureCount:{0}, TextureBytes:{1}, ", curTexureTotalCount, FormatBytes(curTotalTextureBytes));
        builder.AppendFormat("MeshCount:{0}, MeshBytes:{1}, ", curMeshTotalCount, FormatBytes(curTotalMeshBytes));
        builder.AppendFormat("ShaderCount:{0}, ShaderBytes:{1}, ", curShaderTotalCount, FormatBytes(curTotalShaderBytes));
        builder.AppendFormat("BundleCount:{0}, BundleBytes:{1}, ", curBundleTotalCount, FormatBytes(curTotalBundleBytes));
        builder.AppendFormat("MaterialCount:{0}, GameObjectCount:{1}", curMatTotalCount, curGameobjTotalCount);
        builder.AppendFormat("Pss:{0}", FormatBytes(curPssBytes));

        return builder.ToString();
    }

    [NoToLua]
    public static void OnGUI()
    {
        if (!showDetails)
        {
            if (null != Block)
                Block.sizeDelta = new Vector2(100, 50);
        }
        else
        {
            if (null != Block)
                Block.sizeDelta = new Vector2(Screen.width, Screen.height);
        }

        if (GUI.Button(new Rect(0, 0, 100, 50), "展开"))
        {
            showDetails = !showDetails;
        }

        if (Debug.isDebugBuild && GUI.Button(new Rect(100, 0, 100, 50), string.Format("useProfiler{0}", useProfiler)))
        {
            useProfiler = !useProfiler;
        }

        Rect area = new Rect(0, 50, 450, 150);
        if (showDetails)
        {
            area = new Rect(0, 50, 1000, Screen.height - 50);
        }

        GUI.Box(area, "");
        GUILayout.BeginArea(area);
        GUILayout.BeginVertical();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("运行时间:{0}", (int)Time.realtimeSinceStartup), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("总显存:{0}", FormatBytes(curTotalBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("PSS:{0}", FormatBytes(curPssBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("实时Texture数量:{0}", curTexureTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("实时Texture显存:{0}", FormatBytes(curTotalTextureBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("实时Mesh数量:{0}", curMeshTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("实时Mesh显存:{0}", FormatBytes(curTotalMeshBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("实时Mat数量:{0}", curMatTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("实时Go数量:{0}", curGameobjTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("实时Shader数量:{0}", curShaderTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("实时Shader内存:{0}", FormatBytes(curTotalShaderBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label(string.Format("实时AB数量:{0}", curBundleTotalCount), WindowGUIStyle.ContentStyle);
        GUILayout.Label(string.Format("实时AB内存:{0}", FormatBytes(curTotalBundleBytes)), WindowGUIStyle.ContentStyle);
        GUILayout.EndHorizontal();

        if (showDetails)
        {
            curTag = (Tag)GUILayout.Toolbar((int)curTag, new string[5] { "Texture", "Mesh", "Shader", "Bundle", "Material" }, WindowGUIStyle.ButtonTextStyle);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("清理", WindowGUIStyle.ButtonTextStyle))
            {
                System.GC.Collect();
                Resources.UnloadUnusedAssets();
            }

            if (GUILayout.Button("刷新", WindowGUIStyle.ButtonTextStyle))
            {
                CollectAll(true);
            }

            if (GUILayout.Button("记录", WindowGUIStyle.ButtonTextStyle))
            {
                SaveList();
            }

            if (GUILayout.Button("对比", WindowGUIStyle.ButtonTextStyle))
            {
                CompareList();
            }
            GUILayout.EndHorizontal();
        }

        GUILayout.EndVertical();

        List<MemoryItem> showList;
        switch (curTag)
        {
            case Tag.Mesh:
                showList = meshList;
                break;
            case Tag.Shader:
                showList = shaderList;
                break;
            case Tag.Bundle:
                showList = bundleList;
                break;
            case Tag.Material:
                showList = materialList;
                break;
            default:
                showList = textureList;
                break;
        }

        if (showDetails && showList.Count > 0)
        {
            GUILayout.Space(10f);

            GUILayout.BeginVertical();
            GUILayout.BeginHorizontal();
            GUILayout.Label(string.Format("时间戳:{0}", (int)frameTimeStamp), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("总显存:{0}", FormatBytes(totalBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("PSS:{0}", FormatBytes(totalPssBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Texture显存:{0}", FormatBytes(totalTextureBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Mesh显存:{0}", FormatBytes(totalMeshBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            GUILayout.Label(string.Format("Shader内存:{0}", FormatBytes(totalShaderBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Texture数量:{0}", totalTextureCount), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Mesh数量:{0}", totalMeshCount), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Mat数量:{0}", totalMatCount), WindowGUIStyle.ContentStyle);
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            GUILayout.Label(string.Format("Go数量:{0}", totalGameobjCount), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("Shader数量:{0}", totalShaderCount), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("AB数量:{0}", totalBundleCount), WindowGUIStyle.ContentStyle);
            GUILayout.Label(string.Format("AB内存:{0}", FormatBytes(totalBundleBytes)), WindowGUIStyle.ContentStyle);
            GUILayout.EndHorizontal();
            GUILayout.EndVertical();

            if (GUILayout.Button("保存", WindowGUIStyle.ButtonTextStyle))
            {
                Save(textureList, "Texture");
                Save(meshList, "Mesh");
                Save(materialList, "Material");
                SaveAssetBundleLeak();
            }

            scroller = GUILayout.BeginScrollView(scroller);
            string memoryTag = curTag == Tag.Bundle ? "显存" : "内存";
            for (int i = 0; i < showList.Count; ++i)
            {
                MemoryItem item = showList[i];

                if (i > 500)
                {
                    break;
                }

                if (GUILayout.Button(string.Format("{0} {1}:{2} {3}", item.name, memoryTag, item.size, item.log), WindowGUIStyle.ContentStyle))
                {

                }
            }
            GUILayout.EndScrollView();
        }

        GUILayout.EndArea();
    }

    private static string FormatBytes(double bytes)
    {
        if (bytes < 1024)
        {
            return string.Format("{0}B", bytes);
        }
        else if (bytes < 1024 * 1024)
        {
            return string.Format("{0}KB", (bytes / 1024).ToString("0.0"));
        }
        else
        {
            return string.Format("{0}MB", (bytes / 1024 / 1024).ToString("0.0"));
        }
    }

    private static long GetObjectRuntimeMemory(Object obj, Tag tag)
    {
        if (Debug.isDebugBuild && useProfiler)
            return Profiler.GetRuntimeMemorySizeLong(obj);

        if (tag == Tag.Texture)
        {
            return GetTextureMemory(obj);
        }
        else if (tag == Tag.Mesh)
        {
            return GetMeshMemory(obj);
        }
        else
        {
            return 0;
        }
    }

    private static double GetTextureFactor(TextureFormat format)
    {
#if UNITY_IOS
        if (format == TextureFormat.ARGB32)
            return 1f;
        else if (format == TextureFormat.RGB24)
            return 0.75f;
        else if (format == TextureFormat.RGBA4444 || format == TextureFormat.ARGB4444 || format == TextureFormat.RGB565)
            return 0.5f;
        else if (format == TextureFormat.Alpha8 || format == TextureFormat.R8)
            return 0.25f;
        else if (format == TextureFormat.ETC2_RGBA8)
            return 0.25f;
        else if (format == TextureFormat.DXT5)
            return 0.25f;
        else if (format == TextureFormat.DXT1)
            return 0.125f;
        else if (format == TextureFormat.ETC2_RGB || format == TextureFormat.ETC2_RGBA1)
            return 0.125f;
        else if (format == TextureFormat.ASTC_6x6 || format == TextureFormat.ASTC_6x6)
            return 0.1125f;
        else if (format == TextureFormat.ASTC_8x8 || format == TextureFormat.ASTC_8x8)
            return 0.0625f;
        else
            return 1f;
#else
        if (format == TextureFormat.ARGB32)
            return 1f;
        else if (format == TextureFormat.RGB24)
            return 0.75f;
        else if (format == TextureFormat.RGBA4444 || format == TextureFormat.ARGB4444 || format == TextureFormat.RGB565)
            return 0.5f;
        else if (format == TextureFormat.Alpha8 || format == TextureFormat.R8)
            return 0.25f;
        else if (format == TextureFormat.ETC2_RGBA8)
            return 0.25f;
        else if (format == TextureFormat.DXT5)
            return 0.25f;
        else if (format == TextureFormat.DXT1)
            return 0.125f;
        else if (format == TextureFormat.ETC2_RGB || format == TextureFormat.ETC2_RGBA1 || format == TextureFormat.ETC_RGB4Crunched)
            return 0.125f;
        else if (format == TextureFormat.ASTC_6x6 || format == TextureFormat.ASTC_6x6)
            return 0.1125f;
        else if (format == TextureFormat.ASTC_8x8 || format == TextureFormat.ASTC_8x8)
            return 0.0625f;
        else
            return 1f;
#endif
    }

    private static long GetTexture2DMemory(Texture2D texture2D)
    {
        if (null == texture2D)
            return 0;

        
        long bytes = (long)(texture2D.width * texture2D.height * RGBA_PIXEL_SIZE * GetTextureFactor(texture2D.format));
        if (texture2D.mipmapCount > 1)
            bytes += bytes / 3;

        return bytes;
    }

    private static int GetRenderTextureFactor(RenderTextureFormat format)
    {
        if (format == RenderTextureFormat.ARGBFloat || format == RenderTextureFormat.ARGBInt)
            return 4;
        else if (format == RenderTextureFormat.ARGB64 || format == RenderTextureFormat.ARGBHalf)
            return 2;
        else if (format == RenderTextureFormat.ARGB32)
            return 1;
        else
            return 1;
    }

    private static int GetRenderTextureDepthSize(int depthBits)
    {
        if (depthBits <= 0)
            return 0;
        else if (depthBits <= 16)
            return RGBA_PIXEL_SIZE / 2;
        else
            return RGBA_PIXEL_SIZE;
    }

    private static long GetRenderTextureMemory(RenderTexture rt)
    {
        if (null == rt)
            return 0;

        int colorSize = 0;
        if (rt.format == RenderTextureFormat.Depth)
            colorSize = 0;
        else if (rt.format == RenderTextureFormat.Shadowmap)
            colorSize = 0;
        else
            colorSize = RGBA_PIXEL_SIZE * GetRenderTextureFactor(rt.format);

        long bytes = rt.width * rt.height * colorSize;

        TextureDimension dimension = rt.descriptor.dimension;
        if (dimension == TextureDimension.Tex3D || dimension == TextureDimension.Tex2DArray || dimension == TextureDimension.CubeArray)
            bytes *= rt.descriptor.volumeDepth;
        else if (dimension == TextureDimension.Cube)
            bytes *= 6;

        if (rt.descriptor.autoGenerateMips && rt.descriptor.useMipMap)
            bytes += bytes / 3;

        bytes += rt.width * rt.height * GetRenderTextureDepthSize(rt.depth);

        bytes *= rt.antiAliasing;
        return bytes;
    }

    private static long GetTextureMemory(Object obj)
    {
        Texture2D texture2D = obj as Texture2D;
        if (null != texture2D)
        {
            return GetTexture2DMemory(texture2D);
        }
        else
        {
            RenderTexture rt = obj as RenderTexture;
            if (null != rt)
            {
                return GetRenderTextureMemory(rt);
            }
        }

        Texture texture = obj as Texture;
        if (null == texture)
            return 0;

        long bytes = texture.width * texture.height * RGBA_PIXEL_SIZE;
        return bytes;
    }

    private static List<BoneWeight> boneWeights;
    private static long GetMeshMemory(Object obj)
    {
        Mesh mesh = obj as Mesh;
        if (null == mesh)
            return 0;

        long bytes = mesh.vertexCount * VERT_SIZE;

        if (null == boneWeights)
            boneWeights = new List<BoneWeight>();
        else
            boneWeights.Clear();

        mesh.GetBoneWeights(boneWeights);
        if (boneWeights.Count > 0)
            bytes += (long)(bytes * 1.333);

        if (mesh.indexFormat == IndexFormat.UInt16)
            bytes /= 2;

        return bytes;
    }

    [NoToLua]
    public static void Update()
    {
        DoUpdate();
    }

    private static void DoUpdate()
    {
        if (!Application.isPlaying)
        {
            return;
        }

        if (Time.realtimeSinceStartup < timer)
        {
            return;
        }

        if (curUpdateIndex == 0)
        {
            map.Clear();
        }

        Collect((Tag)curUpdateIndex++, false);

        if (curUpdateIndex >= 6)
        {
            curUpdateIndex = 0;
            curPssBytes = pss.GetUsedPssMemory() * 1024 * 1024;
            curTotalBytes = curTotalMeshBytes + curTotalTextureBytes;
            timer = Time.realtimeSinceStartup + 1f;
        }
    }

    private static void Collect(Tag tag, bool isSave)
    {
        System.Type type;
        switch (tag)
        {
            case Tag.Bundle:
                type = typeof(AssetBundle);
                Collect(tag, type, isSave, ref curTotalBundleBytes, ref curBundleTotalCount, bundleList, ref totalBundleBytes, ref totalBundleCount);
                break;
            case Tag.Material:
                type = typeof(Material);
                Collect(tag, type, isSave, ref curTotalMatBytes, ref curMatTotalCount, materialList, ref totalMatBytes, ref totalMatCount);
                break;
            case Tag.Mesh:
                type = typeof(Mesh);
                Collect(tag, type, isSave, ref curTotalMeshBytes, ref curMeshTotalCount, meshList, ref totalMeshBytes, ref totalMeshCount);
                break;
            case Tag.Shader:
                type = typeof(Shader);
                Collect(tag, type, isSave, ref curTotalShaderBytes, ref curShaderTotalCount, shaderList, ref totalShaderBytes, ref totalShaderCount);
                break;
            case Tag.GameObject:
                type = typeof(GameObject);
                Collect(tag, type, isSave, ref curTotalGameObjectBytes, ref curGameobjTotalCount, gameobjList, ref totalGameObjectBytes, ref totalGameobjCount);
                break;
            default:
                type = typeof(Texture);
                Collect(tag, type, isSave, ref curTotalTextureBytes, ref curTexureTotalCount, textureList, ref totalTextureBytes, ref totalTextureCount);
                break;
        }
    }

    private static void Collect(Tag tag, System.Type type, bool isSave, ref long curByte, ref int curCount, List<MemoryItem> list, ref long totalByte, ref int totalCount)
    {
        curByte = 0;

        if (isSave)
        {
            list.Clear();
        }

        Object[] objects = Resources.FindObjectsOfTypeAll(type);
        curCount = objects.Length;

        if (tag != Tag.GameObject)
        {
            for (int i = 0; i < objects.Length; ++i)
            {
                Object obj = objects[i];
                if (map.Contains(obj))
                {
                    continue;
                }
                map.Add(obj);

                long bytes = GetObjectRuntimeMemory(obj, tag);
                if (tag == Tag.Mesh)
                {
                    // 剔除GUI产生的Mesh
                    if (bytes < 40960 && (string.IsNullOrEmpty(obj.name) || obj.name == "TextMesh"))
                    {
                        --curCount;
                        continue;
                    }
                }

                curByte += bytes;

                if (!isSave)
                    continue;

                string size = FormatBytes(bytes);
                string log = string.Empty;
                if (tag == Tag.Texture)
                {
                    Texture2D texture2D = obj as Texture2D;
                    if (null != texture2D)
                    {
                        log = string.Format("{0}*{1} {2}", texture2D.width, texture2D.height, texture2D.format);
                    }
                    else
                    {
                        RenderTexture rt = obj as RenderTexture;
                        if (null != rt)
                        {
                            log = string.Format("{0}*{1} {2}", rt.width, rt.height, rt.format);
                        }
                        else
                        {
                            Texture texture = (Texture)obj;
                            log = string.Format("{0}*{1}", texture.width, texture.height);
                        }
                    }
                }
                else if (tag == Tag.Mesh)
                {
                    Mesh mesh = (Mesh)obj;
                    log = string.Format("vertex:{0}", mesh.vertexCount);
                }

                MemoryItem item = new MemoryItem(obj.name, size, bytes, log);
                item.InstanceID = obj.GetInstanceID();
                list.Add(item);
            }

            if (isSave)
            {
                list.Sort((MemoryItem a, MemoryItem b) =>
                {
                    return b.bytes.CompareTo(a.bytes);
                });

                totalCount = curCount;
                totalByte = curByte;
            }
        }
        else if(isSave)
        {
            for (int i = 0; i < objects.Length; ++i)
            {
                Object obj = objects[i];
                if (map.Contains(obj))
                {
                    continue;
                }
                map.Add(obj);

                MemoryItem item = new MemoryItem(obj.name, "unkonw", 0, string.Empty);
                item.InstanceID = obj.GetInstanceID();
                list.Add(item);
            }

            totalCount = curCount;
            totalByte = curByte;
        }
    }

    private static void CollectAll(bool isSave)
    {
        if (!Application.isPlaying)
        {
            return;
        }

        map.Clear();
        for (int i = 0; i < 6; ++i)
        {
            Collect((Tag)i, isSave);
        }

        curPssBytes = pss.GetUsedPssMemory() * 1024 * 1024;

        curTotalBytes = curTotalMeshBytes + curTotalTextureBytes + curTotalShaderBytes;

        if (isSave)
        {
            frameTimeStamp = Time.realtimeSinceStartup;
            totalBytes = curTotalBytes;
            totalPssBytes = curPssBytes;
        }
    }

    private static void SaveList()
    {
        savedTextureList.Clear();
        savedMeshList.Clear();
        savedGameobjList.Clear();
        savedShaderList.Clear();
        savedBundleList.Clear();
        savedMaterialList.Clear();

        for (int i = 0; i < textureList.Count; ++i)
        {
            savedTextureList.Add(textureList[i]);
        }

        for (int i = 0; i < meshList.Count; ++i)
        {
            savedMeshList.Add(meshList[i]);
        }

        for (int i = 0; i < gameobjList.Count; ++i)
        {
            savedGameobjList.Add(gameobjList[i]);
        }

        for (int i = 0; i < shaderList.Count; ++i)
        {
            savedShaderList.Add(shaderList[i]);
        }

        for (int i = 0; i < bundleList.Count; ++i)
        {
            savedBundleList.Add(bundleList[i]);
        }

        for (int i = 0; i < materialList.Count; ++i)
        {
            savedMaterialList.Add(materialList[i]);
        }

        savedFrameTimeStamp = frameTimeStamp;
        savedTotalTextureCount = totalTextureCount;

        savedTotalBytes = totalBytes;
        savedPssBytes = totalPssBytes;
        savedTotalTextureBytes = totalTextureBytes;
        savedTotalMeshBytes = totalMeshBytes;
        savedTotalShaderBytes = totalShaderBytes;
        savedTotalBundleBytes = totalBundleBytes;
    }

    private static void GetLuaResourceMap(out HashSet<int> resourceMap, out HashSet<int> instanceMap)
    {
        resourceMap = new HashSet<int>();
        instanceMap = new HashSet<int>();

        if (null == GameRoot.Instance)
            return;

        int[] resources = GameRoot.Instance.GetAllResources();
        if (resources.Length <= 0)
            return;

        foreach (int resID in resources)
        {
            if (!resourceMap.Contains(resID))
                resourceMap.Add(resID);
        }

        int[] gameobjects = GameRoot.Instance.GetAllGameObject();
        foreach (int instanceID in gameobjects)
        {
            if (!instanceMap.Contains(instanceID))
                instanceMap.Add(instanceID);
        }
    }

    private static void CompareList()
    {
        if (savedTextureList.Count <= 0)
        {
            return;
        }

        HashSet<int> resourceMap, instanceMap;
        GetLuaResourceMap(out resourceMap, out instanceMap);

        long diffBytes = totalBytes - savedTotalBytes;
        int diffCount = totalTextureCount - savedTotalTextureCount;
        float diffTime = frameTimeStamp - savedFrameTimeStamp;
        StringBuilder builder = new StringBuilder();
        builder.AppendFormat("游戏运行时间：{0}\n", Time.realtimeSinceStartup);
        builder.AppendFormat("{0}\n\n", GetInfo());

        builder.AppendFormat("DiffTime:{0} DiffCount:{1} DiffBytes:{2}\n", diffTime, diffCount, FormatBytes(diffBytes));

        builder.Append("\n\n------------------------Texture----------------------------\n");
        List<MemoryItem> textureAddList, textureReduceList;
        CompareList(savedTextureList, textureList, out textureAddList, out textureReduceList);

        builder.AppendFormat("TextureAddList\n");
        builder.Append(ListToString(textureAddList));
        builder.AppendFormat("\nTextureReduceList\n");
        builder.Append(ListToString(textureReduceList));

        List<string> references = FindTextureReferences(textureAddList, resourceMap, instanceMap);
        if (references.Count > 0)
        {
            builder.Append("\n\n------------------------Lua TraceBack----------------------------\n");

            for (int i = 0; i < references.Count; ++i)
            {
                string reference = references[i];
                builder.AppendFormat("\t{0}", reference);
            }
        }

        builder.Append("\n\n------------------------Mesh----------------------------\n");
        List<MemoryItem> meshAddList, meshReduceList;
        CompareList(savedMeshList, meshList, out meshAddList, out meshReduceList);

        builder.AppendFormat("MeshAddList\n");
        builder.Append(ListToString(meshAddList));
        builder.AppendFormat("\nMeshReduceList\n");
        builder.Append(ListToString(meshReduceList));

        references = FindTextureReferences(meshAddList, resourceMap, instanceMap);
        if (references.Count > 0)
        {
            builder.Append("\n\n------------------------Lua TraceBack----------------------------\n");

            for (int i = 0; i < references.Count; ++i)
            {
                string reference = references[i];
                builder.AppendFormat("\t{0}", reference);
            }
        }

        builder.Append("\n\n------------------------Material----------------------------\n");
        List<MemoryItem> materialAddList, materialReduceList;
        CompareList(savedMaterialList, materialList, out materialAddList, out materialReduceList);

        builder.AppendFormat("MaterialAddList\n");
        builder.Append(ListToString(materialAddList));
        builder.AppendFormat("\nMaterialReduceList\n");
        builder.Append(ListToString(materialReduceList));

        references = FindMaterialReferences(materialAddList, resourceMap, instanceMap);
        if (references.Count > 0)
        {
            builder.Append("\n\n------------------------Lua TraceBack----------------------------\n");

            for (int i = 0; i < references.Count; ++i)
            {
                string reference = references[i];
                builder.AppendFormat("\t{0}", reference);
            }
        }

        builder.Append("\n\n------------------------Shader----------------------------\n");
        List<MemoryItem> shaderAddList, shaderReduceList;
        CompareList(savedShaderList, shaderList, out shaderAddList, out shaderReduceList);

        builder.AppendFormat("ShaderAddList\n");
        builder.Append(ListToString(shaderAddList));
        builder.AppendFormat("\nShaderReduceList\n");
        builder.Append(ListToString(shaderReduceList));

        builder.Append("\n\n------------------------AssetBundle----------------------------\n");
        List<MemoryItem> bundleAddList, bundleReduceList;
        CompareList(savedBundleList, bundleList, out bundleAddList, out bundleReduceList, true);

        builder.AppendFormat("AssetBundleAddList\n");
        builder.Append(ListToString(bundleAddList));
        builder.AppendFormat("\nAssetBundleReduceList\n");
        builder.Append(ListToString(bundleReduceList));

        builder.Append("\n\n------------------------GameObject----------------------------\n");
        List<MemoryItem> gameobjAddList, gameobjReduceList;
        CompareList(savedGameobjList, gameobjList, out gameobjAddList, out gameobjReduceList);

        builder.AppendFormat("GameobjAddList\n");
        builder.Append(ListToString(gameobjAddList));
        builder.AppendFormat("\nGameobjReduceList\n");
        builder.Append(ListToString(gameobjReduceList));

        references = FindGameObjectReferences(gameobjAddList, resourceMap, instanceMap);
        if (references.Count > 0)
        {
            builder.Append("\n\n------------------------Lua TraceBack----------------------------\n");

            for (int i = 0; i < references.Count; ++i)
            {
                string reference = references[i];
                builder.AppendFormat("\t{0}", reference);
            }
        }

        string leak = GetAssetBundleLeak();
        if (!string.IsNullOrEmpty(leak))
        {
            builder.Append("\n\n------------------------AssetBundleLeak----------------------------\n");
            builder.Append(leak);
        }

        string path = Path.Combine(Application.persistentDataPath, "TextureCompare.txt");
        File.WriteAllText(path, builder.ToString());
    }

    private static void CompareList(List<MemoryItem> oldList, List<MemoryItem> newList, out List<MemoryItem> addList, out List<MemoryItem> reduceList, bool flag = false)
    {
        HashSet<int> mapOld = new HashSet<int>();
        foreach (var item in oldList)
        {
            mapOld.Add(item.InstanceID);
        }

        HashSet<int> mapNew = new HashSet<int>();
        foreach (var item in newList)
        {
            mapNew.Add(item.InstanceID);
        }

        addList = new List<MemoryItem>();
        for (int i = 0; i < newList.Count; ++i)
        {
            MemoryItem item = newList[i];
            int instanceID = item.InstanceID;
            if (!mapOld.Contains(instanceID))
            {
                addList.Add(item);
            }
        }

        reduceList = new List<MemoryItem>();
        for (int i = 0; i < oldList.Count; ++i)
        {
            MemoryItem item = oldList[i];
            int instanceID = item.InstanceID;
            if (!mapNew.Contains(instanceID))
            {
                reduceList.Add(item);
            }
        }

        if (flag)
        {
            List<MemoryItem> listA = new List<MemoryItem>();
            List<MemoryItem> listB = new List<MemoryItem>();
            foreach (var addItme in addList)
            {
                foreach (var reduceItem in reduceList)
                {
                    if (addItme.name == reduceItem.name)
                    {
                        listA.Add(addItme);
                        listB.Add(reduceItem);
                        break;
                    }
                }
            }

            foreach (var addItem in listA)
            {
                addList.Remove(addItem);
            }

            foreach (var reduceItem in listB)
            {
                reduceList.Remove(reduceItem);
            }
        }
    }

    private static string ListToString(List<MemoryItem> list)
    {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < list.Count; ++i)
        {
            MemoryItem item = list[i];
            builder.AppendFormat("\t[{0}]Name:{1},Bytes:{2}\n", item.InstanceID, item.name, item.size);
        }

        return builder.ToString();
    }

    private static void Save(List<MemoryItem> list, string fileName)
    {
        string directoryPath = Path.Combine(Application.persistentDataPath, "TextureSnapShot");
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }

        System.DateTime now = System.DateTime.Now;
        string path = Path.Combine(directoryPath, string.Format("{0}_{1}.txt", fileName, now.ToLongTimeString().Replace(':', '_')));

        StringBuilder builder = new StringBuilder();

        builder.AppendFormat("游戏运行时间：{0}\n", Time.realtimeSinceStartup);
        builder.AppendFormat("{0}\n\n", GetInfo());
        builder.Append(ListToString(list));

        HashSet<int> resourceMap, instanceMap;
        GetLuaResourceMap(out resourceMap, out instanceMap);
        List<string> references = FindTextureReferences(list, resourceMap, instanceMap);
        if (references.Count > 0)
        {
            builder.Append("\n\n------------------------Lua TraceBack----------------------------\n");

            for (int i = 0; i < references.Count; ++i)
            {
                string reference = references[i];
                builder.AppendFormat("{0}", reference);
            }
        }

        File.WriteAllText(path, builder.ToString());
    }

    private static void SaveAssetBundleLeak()
    {
        string leak = GetAssetBundleLeak();
        if (string.IsNullOrEmpty(leak))
            return;

        string directoryPath = Path.Combine(Application.persistentDataPath, "TextureSnapShot");
        System.DateTime now = System.DateTime.Now;
        string path = Path.Combine(directoryPath, string.Format("AssetBundleLeak_{0}.txt", now.ToLongTimeString().Replace(':', '_')));
        File.WriteAllText(path, leak);
    }

    private static string GetAssetBundleLeak()
    {
        if (GameRoot.Instance)
        {
            StringBuilder builder = new StringBuilder();
            string[] leaks = GameRoot.Instance.GetAssetBundleLeak();
            if (null == leaks)
                return string.Empty;

            for (int i = 0; i < leaks.Length; ++i)
            {
                string leak = leaks[i];
                string title = "assetbundle_leak";
                if (i == 1)
                {
                    title = "assetbundle_leak_detail";
                }
                else if (i == 2)
                {
                    title = "res_pool_leak";
                }

                builder.Append(title);
                builder.Append("\n");
                builder.Append(leak);
                builder.Append("\n");
            }

            return builder.ToString();
        }

        return string.Empty;
    }

    private static List<string> FindTextureReferences(List<MemoryItem> textureList, HashSet<int> resourceMap, HashSet<int> instanceMap)
    {
        List<string> references = new List<string>();

        Sprite[] sprites = Resources.FindObjectsOfTypeAll(typeof(Sprite)) as Sprite[];
        Dictionary<int, List<Sprite>> spriteMap = new Dictionary<int, List<Sprite>>();
        foreach (Sprite sprite in sprites)
        {
            if (!sprite.packed)
                continue;

            List<Sprite> spriteList;
            int textureInstanceID = sprite.texture.GetInstanceID();
            if (!spriteMap.TryGetValue(textureInstanceID, out spriteList))
            {
                spriteList = new List<Sprite>();
                spriteMap.Add(textureInstanceID, spriteList);
            }

            spriteList.Add(sprite);
        }

        Dictionary<int, HashSet<Material>> materialsMap = new Dictionary<int, HashSet<Material>>();
        Dictionary<GameObject, HashSet<int>> gameObjectsMap = new Dictionary<GameObject, HashSet<int>>();

#if UNITY_EDITOR
        Material[] materials = Resources.FindObjectsOfTypeAll(typeof(Material)) as Material[];
        foreach (Material material in materials)
        {

            UnityEditor.SerializedObject so = new UnityEditor.SerializedObject(material);
            var iter = so.GetIterator();
            while (iter.NextVisible(true))
            {
                if (iter.propertyType == UnityEditor.SerializedPropertyType.ObjectReference)
                {
                    if (iter.objectReferenceValue != null)
                    {
                        int objectInstanceID = iter.objectReferenceValue.GetInstanceID();
                        HashSet<Material> map;
                        if (!materialsMap.TryGetValue(objectInstanceID, out map))
                        {
                            map = new HashSet<Material>();
                            materialsMap.Add(objectInstanceID, map);
                        }

                        if (!map.Contains(material))
                            map.Add(material);
                    }
                }
            }
        }

        GameObject[] gameObjects = Resources.FindObjectsOfTypeAll(typeof(GameObject)) as GameObject[];
        foreach (GameObject gameObject in gameObjects)
        {
            int gameObjectInstanceID = gameObject.GetInstanceID();
            if (!resourceMap.Contains(gameObjectInstanceID) && !instanceMap.Contains(gameObjectInstanceID))
                continue;

            Component[] cps = gameObject.GetComponentsInChildren<Component>(true);
            foreach (var cp in cps)
            {
                if (null == cp)
                {
                    continue;
                }

                UnityEditor.SerializedObject so = new UnityEditor.SerializedObject(cp);
                var iter = so.GetIterator();
                while (iter.NextVisible(true))
                {
                    if (iter.propertyType == UnityEditor.SerializedPropertyType.ObjectReference)
                    {
                        if (iter.objectReferenceValue != null)
                        {
                            int objectInstanceID = iter.objectReferenceValue.GetInstanceID();
                            HashSet<int> map;
                            if (!gameObjectsMap.TryGetValue(gameObject, out map))
                            {
                                map = new HashSet<int>();
                                gameObjectsMap.Add(gameObject, map);
                            }

                            if (!map.Contains(objectInstanceID))
                                map.Add(objectInstanceID);
                        }
                    }
                }
            }
        }
#endif

        for (int i = 0; i < textureList.Count; ++i)
        {
            MemoryItem item = textureList[i];
            List<ResourceItem> resourcelist = FindTextureResources(item.InstanceID, resourceMap, instanceMap, spriteMap, materialsMap, gameObjectsMap);

            foreach (ResourceItem resource in resourcelist)
            {
                string trackBack = string.Empty;
                if (resource.instanceID != 0)
                {
                    trackBack = GameRoot.Instance.GetResTraceBack(resource.instanceID);
                }

                if (string.IsNullOrEmpty(trackBack))
                {
                    trackBack = "Not Find Lua TraceBack";
                }

                references.Add(string.Format("[{0}] [Texture]{1}{2} \n{3}\n\n", item.InstanceID, item.name, resource.path, trackBack));
            }
        }

        return references;
    }

    private static List<ResourceItem> FindTextureResources(int instanceID, HashSet<int> resourceMap, HashSet<int> instanceMap, Dictionary<int, List<Sprite>> spriteMap,
        Dictionary<int, HashSet<Material>> materialsMap, Dictionary<GameObject, HashSet<int>> gameObjectsMap)
    {
        List<ResourceItem> resourcelist = new List<ResourceItem>();

        // Lua直接加载的
        if (resourceMap.Contains(instanceID))
        {
            ResourceItem resourceItem = new ResourceItem(string.Empty, instanceID);
            resourcelist.Add(resourceItem);
        }

        //被Prefab或者实例化所依赖的
        foreach (var kv in gameObjectsMap)
        {
            GameObject gameObject = kv.Key;
            HashSet<int> map = kv.Value;
            if (map.Contains(instanceID))
            {
                int goInstanceID = gameObject.GetInstanceID();
                ResourceItem resourceItem;
                if (resourceMap.Contains(goInstanceID))
                {
                    resourceItem = new ResourceItem(string.Format(" => [Prefab]{0}", gameObject.name), goInstanceID);
                }
                else
                {
                    resourceItem = new ResourceItem(string.Format(" => [GameObject(Instance)]{0}", gameObject.name), goInstanceID);
                }
                resourcelist.Add(resourceItem);
            }
        }

        HashSet<Material> materialMap;
        if (materialsMap.TryGetValue(instanceID, out materialMap))
        {
            foreach (Material material in materialMap)
            {
                //被Material引用，Material被Lua加载的
                int materialInstanceID = material.GetInstanceID();
                if (resourceMap.Contains(materialInstanceID))
                {
                    ResourceItem resourceItem = new ResourceItem(string.Format(" => [Material]{0}", material.name), materialInstanceID);
                    resourcelist.Add(resourceItem);
                }
                else
                {
                    ResourceItem resourceItem = new ResourceItem(string.Format(" => [Material]{0}", material.name), 0);
                    resourcelist.Add(resourceItem);
                }

                //被Material引用，Material被Prefab或者实例化所依赖的
                foreach (var kv in gameObjectsMap)
                {
                    GameObject gameObject = kv.Key;
                    int goInstanceID = gameObject.GetInstanceID();
                    HashSet<int> map = kv.Value;
                    if (map.Contains(materialInstanceID))
                    {
                        ResourceItem resourceItem;
                        if (resourceMap.Contains(goInstanceID))
                        {
                            resourceItem = new ResourceItem(string.Format(" => [Material]{0} = > [Prefab]{1}", material.name, gameObject.name), goInstanceID);
                        }
                        else
                        {
                            resourceItem = new ResourceItem(string.Format(" => [Material]{0} = > [GameObject(Instance)]{1}", material.name, gameObject.name), goInstanceID);
                        }
                        resourcelist.Add(resourceItem);
                    }
                }
            }
        }

        if (spriteMap.ContainsKey(instanceID))
        {
            List<Sprite> spriteList = spriteMap[instanceID];

            foreach (Sprite sprite in spriteList)
            {
                int spriteInstanceID = sprite.GetInstanceID();

                //被Sprite引用，Sprite被Lua加载的
                if (resourceMap.Contains(spriteInstanceID))
                {
                    ResourceItem resourceItem = new ResourceItem(string.Format(" => [Sprite]{0}", sprite.name), spriteInstanceID);
                    resourcelist.Add(resourceItem);
                }
                else
                {
                    ResourceItem resourceItem = new ResourceItem(string.Format(" => [Sprite]{0}", sprite.name), 0);
                    resourcelist.Add(resourceItem);
                }

                //被Sprite引用，Sprite被Prefab或者实例化所依赖的
                foreach (var kv in gameObjectsMap)
                {
                    GameObject gameObject = kv.Key;
                    int goInstanceID = gameObject.GetInstanceID();
                    HashSet<int> map = kv.Value;
                    if (map.Contains(spriteInstanceID))
                    {
                        ResourceItem resourceItem;
                        if (resourceMap.Contains(goInstanceID))
                        {
                            resourceItem = new ResourceItem(string.Format(" => [Sprite]{0} = > [Prefab]{1}", sprite.name, gameObject.name), goInstanceID);
                        }
                        else
                        {
                            resourceItem = new ResourceItem(string.Format(" => [Sprite]{0} = > [GameObject(Instance)]{1}", sprite.name, gameObject.name), goInstanceID);
                        }
                        resourcelist.Add(resourceItem);
                    }
                }
            }
        }

        return resourcelist;
    }

    private static List<string> FindGameObjectReferences(List<MemoryItem> gameobjList, HashSet<int> resourceMap, HashSet<int> instanceMap)
    {
        List<string> references = new List<string>();
        for (int i = 0; i < gameobjList.Count; ++i)
        {
            MemoryItem item = gameobjList[i];
            bool isPrefab = resourceMap.Contains(item.InstanceID);
            bool isGameobj = instanceMap.Contains(item.InstanceID);
            if (isPrefab || isGameobj)
            {
                string trackBack = string.Empty;
                trackBack = GameRoot.Instance.GetResTraceBack(item.InstanceID);

                if (string.IsNullOrEmpty(trackBack))
                {
                    trackBack = "Not Find Lua TraceBack";
                }

                if (isPrefab)
                {
                    references.Add(string.Format("[{0}] [Prefab]{1} \n{2}\n\n", item.InstanceID, item.name, trackBack));
                }
                else
                {
                    references.Add(string.Format("[{0}] [GameObject(Instance)]{1} \n{2}\n\n", item.InstanceID, item.name, trackBack));
                }
            }
        }

        return references;
    }

    private static List<string> FindMaterialReferences(List<MemoryItem> materialList, HashSet<int> resourceMap, HashSet<int> instanceMap)
    {
        List<string> references = new List<string>();
#if UNITY_EDITOR
        Dictionary<int, MemoryItem> dic = new Dictionary<int, MemoryItem>();
        foreach (var item in materialList)
        {
            dic.Add(item.InstanceID, item);
        }

        GameObject[] gameObjects = Resources.FindObjectsOfTypeAll(typeof(GameObject)) as GameObject[];
        foreach (GameObject gameObject in gameObjects)
        {
            Component[] cps = gameObject.GetComponentsInChildren<Component>(true);
            foreach (var cp in cps)
            {
                if (null == cp)
                {
                    continue;
                }

                UnityEditor.SerializedObject so = new UnityEditor.SerializedObject(cp);
                var iter = so.GetIterator();
                while (iter.NextVisible(true))
                {
                    if (iter.propertyType == UnityEditor.SerializedPropertyType.ObjectReference)
                    {
                        if (iter.objectReferenceValue != null)
                        {
                            int objectInstanceID = iter.objectReferenceValue.GetInstanceID();
                            MemoryItem item;
                            if (dic.TryGetValue(objectInstanceID, out item))
                            {
                                references.Add(string.Format("[{0}][Material]{1}  = > [{2}][GameObject]{3}\n\n", item.InstanceID, item.name, gameObject.GetInstanceID(), gameObject.name));
                            }
                        }
                    }
                }
            }
        }
#endif

        return references;
    }

    private struct MemoryItem
    {
        public string size;
        public string name;
        public long bytes;
        private int instanceID;
        public string log;

        public MemoryItem(string name, string size, long bytes, string log)
        {
            this.name = name;
            this.size = size;
            this.bytes = bytes;
            instanceID = 0;
            this.log = log;
        }

        public int InstanceID
        {
            get
            {
                return instanceID;
            }

            set
            {
                instanceID = value;
            }
        }
    }

    private struct ResourceItem
    {
        public string path;
        public int instanceID;

        public ResourceItem(string path, int instanceID)
        {
            this.path = path;
            this.instanceID = instanceID;
        }
    }

    private static class WindowGUIStyle
    {
        public static GUIStyle ContentStyle { get; private set; }
        public static GUIStyle LogContentStyle { get; private set; }
        public static GUIStyle ButtonTextStyle { get; private set; }
        public static GUIStyle ToggleStyle { get; private set; }

        static WindowGUIStyle()
        {
            ContentStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 20,
                alignment = TextAnchor.UpperLeft,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };

            LogContentStyle = new GUIStyle(GUI.skin.label)
            {
                fontSize = 20,
                alignment = TextAnchor.MiddleLeft,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };

            ButtonTextStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 25,
                alignment = TextAnchor.MiddleCenter,
                padding = new RectOffset(0, 0, 0, 0),
                fontStyle = FontStyle.Bold,
            };
        }
    }
}
