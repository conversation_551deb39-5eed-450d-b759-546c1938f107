﻿using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana.Editor;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System;

namespace Build
{
    public class BuildMaterial
    {
        private static string outputDir = "Assets/Game/Shaders/Materials";

        private static List<string> outputMaterialPathsList = new List<string>();

        public static void GetKeywordIdDic(Dictionary<string, int> dic, HashSet<int> idSet)
        {
            string path = Path.Combine(outputDir, "keyword_id.txt");
            if (!File.Exists(path))
            {
                return;
            }

            string[] lines = File.ReadAllLines(path);
            for (int i = 0; i < lines.Length; i++)
            {
                if (string.Empty != lines[i])
                {
                    string[] ary = lines[i].Split(' ');
                    dic.Add(ary[0], Convert.ToInt32(ary[1]));
                    idSet.Add(Convert.ToInt32(ary[1]));
                }
            }
        }

        public static int GetIncKeywordId(HashSet<int> idSet)
        {
            for (int i = 1; i < 1000; i++)
            {
                if (!idSet.Contains(i))
                {
                    idSet.Add(i);
                    return i;
                }
            }

            return 0;
        }

        public static void SaveKeywordIdDic(Dictionary<string, int> dic)
        {
            string path = Path.Combine(outputDir, "keyword_id.txt");
            List<string> list = new List<string>();
            foreach (var item in dic)
            {
                list.Add(string.Format("{0} {1}", item.Key, item.Value));
            }

            File.WriteAllLines(path, list.ToArray());
        }

        // 当此脚本被改动过时，将不考虑增量打材质球，因为keyword规则则可能已经变了
        private static bool IsIncBuild()
        {
            if (AssetDatabase.FindAssets("t:material", new string[] { outputDir }).Length == 0)
            {
                return false;
            }

            Dictionary<string, string> modifyTime2CheckCSFilePath = new Dictionary<string, string>()
            {
                { "build_material_cs.txt", string.Format("{0}/Game/Scripts/Editor/Tools/Build/BuildMaterial.cs", Application.dataPath)},
                { "shader_lab_optimize_cs.txt", string.Format("{0}/Game/Scripts/Editor/Tools/Build/ShaderLabOptimize.cs", Application.dataPath)},
            };
            foreach (var item in modifyTime2CheckCSFilePath)
            {
                string modifyPath = BuilderConfig.GetBuildLogPath(item.Key);
                if (!File.Exists(modifyPath))
                {
                    return false;
                }

                string oldWriteTime = File.ReadAllText(modifyPath);
                FileInfo scriptFile = new FileInfo(item.Value);
                if (!scriptFile.Exists)
                {
                    return false;
                }

                long newWriteTime = GetTimeStamp(scriptFile.LastWriteTime);
                if (newWriteTime != Convert.ToInt64(oldWriteTime))
                {
                    return false;
                }
            }

            return true;
        }

        public static void WriteBuildMaterialCsTime()
        {
            string modifyPath = BuilderConfig.GetBuildLogPath("build_material_cs.txt");
            FileInfo scriptFile = new FileInfo(string.Format("{0}/Game/Scripts/Editor/Tools/Build/BuildMaterial.cs", Application.dataPath));
            if (scriptFile.Exists)
            {
                long newWriteTime = GetTimeStamp(scriptFile.LastWriteTime);
                File.WriteAllText(modifyPath, newWriteTime.ToString());
            }
        }

        public static void Build()
        {
            var materialKeys = new HashSet<MaterialKey>(MaterialKeyCompare.Default);
            outputMaterialPathsList.Clear();
            string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game"});
            bool isIncBuild = IsIncBuild();
            foreach (var guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                if (isIncBuild && !BuildAssetsMgr.IsAssetDirty(assetPath))
                {
                    continue;
                }

                if (assetPath.StartsWith(outputDir))
                {
                    continue;
                }
               
                var material = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
                if (!IsValidMaterial(material))
                {
                    continue;
                }

                GetMaterialKey(material, materialKeys);
            }

            var keyword_str_id_dic = new Dictionary<string, int>();
            var keyword_id_dic = new HashSet<int>();
            var new_material_full_path_dic = new HashSet<string>();
            GetKeywordIdDic(keyword_str_id_dic, keyword_id_dic);

            foreach (var key in materialKeys)
            {
                var nameBuilder = new StringBuilder();
                nameBuilder.Append(outputDir);
                nameBuilder.Append('/');
                nameBuilder.Append(key.Shader.name.Replace('/', '_').Replace(" ", ""));
                List<int> keyWordIdList = new List<int>();
                foreach (var keyword in key.Keywords)
                {
                    int keyword_id = 0;
                    if (!keyword_str_id_dic.TryGetValue(keyword, out keyword_id))
                    {
                        keyword_id = GetIncKeywordId(keyword_id_dic);
                        if (keyword_id <= 0)
                        {
                            Debug.LogErrorFormat("BuildMateril Fail, keyword_id is error");
                            return;
                        }

                        keyword_str_id_dic.Add(keyword, keyword_id);
                    }

                    keyWordIdList.Add(keyword_id);
                }

                // 对id进行排序
                keyWordIdList.Sort();
                for (int i = 0; i < keyWordIdList.Count; i++)
                {
                    nameBuilder.Append('_');
                    nameBuilder.Append(keyWordIdList[i]);
                }

                nameBuilder.Append(".mat");
                var outputPath = nameBuilder.ToString();

                string absoulute_path = Application.dataPath + "/../" + outputPath;
                if (File.Exists(absoulute_path))
                {
                    if (!outputMaterialPathsList.Contains(outputPath))
                    {
                        outputMaterialPathsList.Add(outputPath);
                    }
                    continue;
                }

                var material = new Material(key.Shader);
                material.shaderKeywords = key.Keywords;
                AssetDatabaseUtil.ReplaceAsset(material, outputPath);
                new_material_full_path_dic.Add(absoulute_path);

                if (!outputMaterialPathsList.Contains(outputPath))
                {
                    outputMaterialPathsList.Add(outputPath);
                }
            }

           // DeleteUselessMaterialAssets(); // 使用增量打包后不能再用了
            SaveKeywordIdDic(keyword_str_id_dic);

            AssetDatabase.Refresh();
            // 刷新文件
            foreach (var absoulute_path in new_material_full_path_dic)
            {
                if (File.Exists(absoulute_path)) {
                    BuildAssetsMgr.RefreshFile(absoulute_path);
                }
                if (File.Exists(absoulute_path + ".meta")) {
                    BuildAssetsMgr.RefreshFile(absoulute_path + ".meta");
                }
            }
        }

        private static void GetMaterialKey(Material material, HashSet<MaterialKey> materialKeys)
        {
            List<string> keywordList = new List<string>(material.shaderKeywords);
            materialKeys.Add(new MaterialKey(material.shader, keywordList.ToArray()));
            if (material.shader.name.StartsWith("YifStandard"))
            {
                GetYifMaterialKey(material, materialKeys, keywordList);
            }
            else if (material.shader.name.IndexOf("YY/") >= 0)
            {
                GetYYMaterialKey(material, materialKeys, keywordList);
            }
            else if (material.shader.name.IndexOf("Srp/") >= 0
                || material.shader.name.IndexOf("Universal Render Pipeline/") >= 0
                || material.shader.name.IndexOf("JYShaders/") >= 0)
            {
                GetSRPMaterialKey(material, materialKeys, keywordList);
            }
        }

        private static void GetYifMaterialKey(Material material, HashSet<MaterialKey> materialKeys, List<string> keywordList)
        {
            // 慎加！非主程且不理解者别动！！！！ 而且加了后，需要把旧的Shader/Materials的全删除，重新打包才能起效果
            if (material.HasProperty("_MaterialStyle"))
            {
                float materialStyle = material.GetFloat("_MaterialStyle");
                if (materialStyle == 1)
                {
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "ENABLE_POST_EFFECT" });
                }
                else if (materialStyle == 2)
                {
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "FOG_LINEAR" });
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTMAP_ON" });
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTMAP_ON", "FOG_LINEAR" });
                }
                else if (materialStyle == 3)
                {
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "ENABLE_POST_EFFECT" });
                    AddExtraKeywords(material, materialKeys, keywordList, new string[] { "ENABLE_CLIP_RECT" });
                }
            }
        }

        private static void GetYYMaterialKey(Material material, HashSet<MaterialKey> materialKeys, List<string> keywordList)
        {
            if (material.shader.name == "YY/YYStandard")  //YY/YYShaderFeatureTest
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTPROBE_SH", "_PROBE_BLEND" });
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTPROBE_SH" });
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "ENABLE_CLIP_RECT" });

                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTPROBE_SH", "ENABLE_CLIP_RECT" });
            }
            else if (material.shader.name == "YY/YYStandardMap" || material.shader.name == "YY/YYStandardGrass" || material.shader.name == "YY/YYStandardT4M")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "LIGHTMAP_ON", "FOG_LINEAR" });
            }
        }

        private static void GetSRPMaterialKey(Material material, HashSet<MaterialKey> materialKeys, List<string> keywordList)
        {
            if (material.shader.name == "Srp/Standard/SrpRole_PbrCf")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "_ADDITIONAL_LIGHTS" });
            }
            else if (material.shader.name == "Srp/Standard/SrpMap_PbrCf")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "_MAIN_LIGHT_SHADOWS" , "ENABLE_HEIGHT_FOG"});
            }
            else if (material.shader.name == "Universal Render Pipeline/Terrain/Lit")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "_MAIN_LIGHT_SHADOWS" , "_MASKMAP", "_ADDITIONAL_LIGHT_SHADOWS" });
            }
            else if ( material.shader.name == "Srp/Terrain/URPTerrainCf"
                || material.shader.name == "Srp/Terrain/URPTerrainAddCf")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "DIRLIGHTMAP_COMBINED", "_ALPHATEST_ON", "_MASKMAP", "_NORMALMAP" });
            }
            else if (material.shader.name == "JYShaders/StylizedScene"
                     || material.shader.name == "JYShaders/StylizedTerrain")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "_MAIN_LIGHT_SHADOWS" , "ENABLE_HEIGHT_FOG"});
            }
            else if (material.shader.name == "JYShaders/StylizedHair"
                || material.shader.name == "JYShaders/StylizedFace"
                || material.shader.name == "JYShaders/StylizedEyes"
                || material.shader.name == "JYShaders/StylizedEyeLash"
                || material.shader.name == "JYShaders/StylizedSkin"
                || material.shader.name == "JYShaders/StylizedGem")
            {
                AddExtraKeywords(material, materialKeys, keywordList, new string[] { "_CLIP_SPACE_REMAP", "_LINEAR_TO_SRGB_CONVERT" });
            }
        }

        private static void AddExtraKeywords(Material material, HashSet<MaterialKey> materialKeys, List<string> keywordList, string[] extraKeywords)
        {
            HashSet<string> newKeywrods = new HashSet<string>(keywordList);
            for (int i = 0; i < extraKeywords.Length; i++)
            {
                newKeywrods.Add(extraKeywords[i]);
            }

            materialKeys.Add(new MaterialKey(material.shader, newKeywrods.ToArray()));
        }

        private static void DeleteUselessMaterialAssets()
        {
            List<string> localMaterialAssetList = new List<string>();
            string[] guids = AssetDatabase.FindAssets("t:material", new string[] { outputDir });
            for (int i = 0; i < guids.Length; i++)
            {
                var assetName = AssetDatabase.GUIDToAssetPath(guids[i]);
                localMaterialAssetList.Add(assetName);
            }

            for (int i = 0; i < outputMaterialPathsList.Count; i++)
            {
                string materialAsset = outputMaterialPathsList[i];
                if (localMaterialAssetList.Contains(materialAsset))
                {
                    localMaterialAssetList.Remove(materialAsset);
                }
            }

            foreach (string asset in localMaterialAssetList)
            {
                bool isDeleted = AssetDatabase.DeleteAsset(asset);
                if (!isDeleted)
                {
                    Debug .LogErrorFormat ("Path: {0} is Useless Materials, delete Fail, please check!", asset);
                }
            }
        }

        public static bool IsValidMaterial(Material material)
        {
            if (null == material)
                return false;

            return ShaderLabOptimize.IsValidShader(material.shader);
        }

        private static long GetTimeStamp(DateTime time)
        {
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            return (long)(time - startTime).TotalMilliseconds;
        }

        private class MaterialKey
        {
            public MaterialKey(Shader shader, string[] keywords)
            {
                this.Shader = shader;
                this.Keywords = keywords;
            }

            public Shader Shader { get; private set; }

            public string[] Keywords { get; private set; }
        }

        private class MaterialKeyCompare : IEqualityComparer<MaterialKey>
        {
            private static volatile MaterialKeyCompare defaultComparer;

            public static MaterialKeyCompare Default
            {
                get
                {
                    if (defaultComparer == null)
                    {
                        defaultComparer = new MaterialKeyCompare();
                    }

                    return defaultComparer;
                }
            }

            public bool Equals(MaterialKey x, MaterialKey y)
            {
                return x.Shader == y.Shader &&
                    x.Keywords.SequenceEqual(y.Keywords);
            }

            public int GetHashCode(MaterialKey obj)
            {
                int hashcode = obj.Shader.GetHashCode();
                foreach (var keyword in obj.Keywords)
                {
                    hashcode ^= keyword.GetHashCode();
                }

                return hashcode;
            }
        }
    }
}

