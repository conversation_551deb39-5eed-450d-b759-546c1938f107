﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System;
using Nirvana;
using System.Text.RegularExpressions;
using System.Text;
using UnityEditor;

namespace Build
{
    internal class AssetItem
    {
        public string fullPath;
        public string path;
        public long newWriteTime;
        public long oldWriteTime;
        public string bundleName = "None";
        public long fileSize;
        public int textureWidth;
        public int textureHeight;
        public DateTime LastWriteTime;
    }

    internal class ABItem
    {
        public string bundleName;
        public long oldWriteTime;
        public long newWriteTime;
        public uint md5;
        public uint crc;
    }

    public class BuildAssetsMgr
    {
        private static string assetDir;
        private static Dictionary<string, AssetItem> assetDic = new Dictionary<string, AssetItem>();
        private static List<string> dirtyAssets = new List<string>();
        private static Dictionary<string, string> guidToPathDic = new Dictionary<string, string>();
        private static Dictionary<string, string[]> dependenciesDic = new Dictionary<string, string[]>();
        private static Dictionary<string, ABItem> abDic = new Dictionary<string, ABItem>();
        private static int buildPlatType;

        public static void Init(BuildPlatType platType)
        {
            assetDir = Application.dataPath.Replace("Assets", "");

            buildPlatType = (int)platType;
            assetDic.Clear();
            abDic.Clear();
            guidToPathDic.Clear();
            dependenciesDic.Clear();

            DeployTool.ReportBuildLog("BuildAssetsMgr, ReadFileModifyLog");
            ReadFileModifyLog();

            DeployTool.ReportBuildLog("BuildAssetsMgr, FetchAsset");
            FetchAsset();

            DeployTool.ReportBuildLog("BuildAssetsMgr, ReadDependenciesLog");
            ReadDependenciesLog();

            DeployTool.ReportBuildLog("BuildAssetsMgr, ReadABModeifLog");
            ReadABModeifLog();
        }

        private static void FetchAsset()
        {
            dirtyAssets.Clear();
            List<string> fileList = new List<string>();
            GetAllFiles(Application.dataPath, fileList);
            for (int i = 0; i < fileList.Count; i++)
            {
                var fullPath = fileList[i];
                RefreshFile(fullPath);
            }

            string modifyFilePath = BuilderConfig.GetBuildLogPath(string.Format("dirty_file_list{0}.txt", buildPlatType));
            File.WriteAllLines(modifyFilePath, dirtyAssets.ToArray());
        }

        public static void RefreshFile(string fullPath)
        {
            FileInfo fileInfo = new FileInfo(fullPath);
            if (!fileInfo.Exists)
            {
                Debug.LogErrorFormat("[BuildAssetsMgr] RefreshFile不存在资源 {0}", fullPath);
                return;
            }

            fullPath = fileInfo.FullName;
            long newWriteTime = GetTimeStamp(fileInfo.LastWriteTime);

            AssetItem assetItem;
            if (string.IsNullOrEmpty(assetDir))
            {
                assetDir = Application.dataPath.Replace("Assets", "");
            }

            string path = fullPath.Replace("\\", "/").Replace(assetDir, "");
            if (!assetDic.TryGetValue(path, out assetItem))
            {
                assetItem = new AssetItem();
                assetItem.fullPath = fullPath;
                assetItem.path = path;
                assetItem.oldWriteTime = 0;
                assetItem.newWriteTime = newWriteTime;
                assetItem.LastWriteTime = fileInfo.LastWriteTime;
                assetItem.fileSize = fileInfo.Length;
                TryParseMetaFile(assetItem);
                assetDic.Add(path, assetItem);
                dirtyAssets.Add(path);
            }
            else
            {
                if (assetItem.oldWriteTime != newWriteTime)
                {
                    dirtyAssets.Add(path);
                }

                assetItem.fullPath = fullPath;
                assetItem.newWriteTime = newWriteTime;
                assetItem.LastWriteTime = fileInfo.LastWriteTime;
                TryParseMetaFile(assetItem);
            }
        }

        // 打包完AB包后拾取AB
        public static void RefreshABDic(string[] allAssetBundles, bool isLuaAB)
        {
            for (int i = 0; i < allAssetBundles.Length; i++)
            {
                string bundleName = allAssetBundles[i];
                string fullPath;
                if (isLuaAB)
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/LuaAssetBundle/" + bundleName);
                }
                else
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/" + bundleName);
                }

                FileInfo fileInfo = new FileInfo(fullPath);
                if (null == fileInfo)
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr RefreshABDic, fileInfo is nil, {0} ", fullPath));
                    break;
                }

                long newWriteTime = GetTimeStamp(fileInfo.LastWriteTime);
                ABItem abItem;
                if (!abDic.TryGetValue(bundleName, out abItem))
                {
                    abItem = new ABItem();
                    abItem.bundleName = bundleName;
                    abItem.oldWriteTime = newWriteTime;
                    abItem.newWriteTime = newWriteTime;
                    abItem.md5 = MD5.GetMD5FromFile(fullPath);
                    BuildPipeline.GetCRCForAssetBundle(fullPath, out abItem.crc);

                    abDic.Add(bundleName, abItem);
                }
                else
                {
                    abItem.newWriteTime = newWriteTime;
                    if (abItem.oldWriteTime != newWriteTime)
                    {
                        abItem.md5 = MD5.GetMD5FromFile(fullPath);
                        BuildPipeline.GetCRCForAssetBundle(fullPath, out abItem.crc);
                        abItem.oldWriteTime = newWriteTime;
                    }
                }
            }
        }

        private static void TryParseMetaFile(AssetItem assetItem)
        {
            // 文件没改动则bundlename没改
            if (assetItem.oldWriteTime == assetItem.newWriteTime
                || Path.GetExtension(assetItem.path) != ".meta")
            {
                return;
            }

            // meta文件设为none
            assetItem.bundleName = "None";

            // 改资源源文件的属性记录
            string fullPath = string.Format("{0}/../{1}", Application.dataPath, assetItem.path);
            string orignalAssetFullPath = assetItem.path.Replace(".meta", "");
            AssetItem orignalAssetItem;
            if (assetDic.TryGetValue(orignalAssetFullPath, out orignalAssetItem))
            {
                if (!File.Exists(fullPath))
                {
                    Debug.LogErrorFormat("[BuildAssetMgr] TryParseMetaFile 文件不存在 {0}", fullPath);
                    return;
                }

                string content = File.ReadAllText(fullPath);

                // 解析出BunelName
                string[] ary = Regex.Match(content, "assetBundleName.*").Value.Split(' ');
                if (ary.Length <= 1)
                {
                    orignalAssetItem.bundleName = "None";
                }
                else
                {
                    string bundleName = ary[1].Replace("\n", "").Replace("\r", "");
                    orignalAssetItem.bundleName = string.IsNullOrEmpty(bundleName) ? "None" : bundleName;
                }
            }
        }

        internal static List<AssetItem> GetBundleAssetItem()
        {
            List<AssetItem> list = new List<AssetItem>();
            foreach (var kv in assetDic)
            {
                if (kv.Value.bundleName != "None")
                {
                    list.Add(kv.Value);
                }
            }

            return list;
        }

        // 获取资源依赖的其他资源
        internal static string[] GetDependencies(string assetPath)
        {
            string[] list;
            if (!dependenciesDic.TryGetValue(assetPath, out list))
            {
                list = AssetDatabase.GetDependencies(assetPath, true);
                List<string> tempList = new List<string>();
                for (int i = 0; i < list.Length; i++)
                {
                    if (list[i].EndsWith(".cs") || list[i].EndsWith(".dll"))
                    {
                        continue;
                    }

                    tempList.Add(list[i]);
                }

                list = tempList.ToArray();
                dependenciesDic.Add(assetPath, list);
            }

            return list;
        }

        // 获取资源大小
        internal static long GetAssetFileSize(string assetPath)
        {
            AssetItem assetItem;
            if (assetDic.TryGetValue(assetPath, out assetItem))
            {
                return assetItem.fileSize;
            }
            else
            {
                FileInfo fileInfo = new FileInfo(string.Format("{0}/../{1}", Application.dataPath, assetPath));
                return fileInfo.Length;
            }
        }

        // 获得纹理大小
        internal static bool GetTextureSize(string assetPath, ref int width, ref int height)
        {
            AssetItem assetItem;
            if (!assetDic.TryGetValue(assetPath, out assetItem))
            {
                return false;
            }

            width = assetItem.textureWidth;
            height = assetItem.textureHeight;
            if (width <= 0 || height <= 0)
            {
                Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                if (null == texture)
                {
                    DeployTool.ReportBuildLog(string.Format("[Error] GetTextureSize, texture is nil, {0}", assetPath));
                    return false;
                }

                width = texture.width;
                height = texture.height;
                assetItem.textureWidth = width;
                assetItem.textureHeight = height;
            }

            return true;
        }

        // 获得AB的MD5， 使用前先RefreshABDic
        public static uint GetABMd5(string abName, bool isLuaAB)
        {
            ABItem abItem;
            if (!abDic.TryGetValue(abName, out abItem) || abItem.md5 == 0)
            {
                string fullPath;
                if (isLuaAB)
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/LuaAssetBundle/" + abName);
                }
                else
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/" + abName);
                }
                return MD5.GetMD5FromFile(fullPath);
            }

            return abItem.md5;
        }


        // 获得AB的CRC， 使用前先RefreshABDic
        public static uint GetABCRC(string abName, bool isLuaAB)
        {
            ABItem abItem;
            if (!abDic.TryGetValue(abName, out abItem) || abItem.crc == 0)
            {
                string fullPath;
                if (isLuaAB)
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/LuaAssetBundle/" + abName);
                }
                else
                {
                    fullPath = BuilderConfig.GetAssetBundlePath((BuildPlatType)buildPlatType, "AssetBundle/" + abName);
                }

                BuildPipeline.GetCRCForAssetBundle(fullPath, out abItem.crc);
                return abItem.crc;
            }

            return abItem.crc;
        }

        // 是否有资源被改动过
        public static bool IsHadAssetDirty()
        {
            return dirtyAssets.Count > 0;
        }

        public static bool IsAssetDirty(string path)
        {
            AssetItem assetItem;
            if (!assetDic.TryGetValue(path, out assetItem))
            {
                return true;
            }

            return assetItem.newWriteTime != assetItem.oldWriteTime;
        }

        private static void ReadFileModifyLog()
        {
            string path = BuilderConfig.GetBuildLogPath(string.Format("asset_write_log{0}.txt", buildPlatType));
            if (!File.Exists(path))
            {
                return;
            }

            string[] lines = File.ReadAllLines(path);
            for (int i = 0; i < lines.Length; i++)
            {
                var ary = lines[i].Split('#');
                if (ary.Length != 6)
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取asset_write_log失败, {0}", lines[i]));
                    break;
                }
                if (!assetDic.ContainsKey(ary[0]))
                {
                    var assetItem = new AssetItem();
                    assetItem.path = ary[0];
                    assetItem.oldWriteTime = Convert.ToInt64(ary[1]);
                    assetItem.newWriteTime = assetItem.oldWriteTime;
                    assetItem.bundleName = ary[2];
                    assetItem.fileSize = Convert.ToInt64(ary[3]);
                    assetItem.textureWidth = Convert.ToInt32(ary[4]);
                    assetItem.textureHeight = Convert.ToInt32(ary[5]);
                    assetDic.Add(assetItem.path, assetItem);
                }
                else
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取asset_write_log失败, 重复资源，{0}", lines[i]));
                    break;
                }
            }
        }

        // 必须打包完后调用
        public static void WriteFileModifyLog()
        {
            string path = BuilderConfig.GetBuildLogPath(string.Format("asset_write_log{0}.txt", buildPlatType));
            var dir = Path.GetDirectoryName(path);
            if (!File.Exists(path))
            {
                Directory.CreateDirectory(dir);
            }

            // 因为在打包过程中，unity的refresh经常会更改文件的修改时间（即使实际上文件没有变化），这会导致增量打包经常失效
            // 在打包完后，强制把修改时间改为打包前
            foreach (var kv in assetDic)
            {
                if (File.Exists(kv.Value.fullPath))
                {
                    if (kv.Value.fullPath.Contains("/.svn/"))
                    {
                        continue;
                    }

                    try
                    {
                        FileInfo fileInfo = new FileInfo(kv.Value.fullPath);
                        fileInfo.LastWriteTime = kv.Value.LastWriteTime;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError(ex.ToString());
                    }
                }
            }

            string[] lines = new string[assetDic.Count];
            var index = 0;
            foreach (var kv in assetDic)
            {
                lines[index++] = string.Format("{0}#{1}#{2}#{3}#{4}#{5}",
                    kv.Value.path,
                    kv.Value.newWriteTime,
                    kv.Value.bundleName,
                    kv.Value.fileSize,
                    kv.Value.textureWidth,
                    kv.Value.textureHeight);
            }

            File.WriteAllLines(path, lines);
        }

        // 读取依赖关系文件
        private static void ReadDependenciesLog()
        {
            string path = BuilderConfig.GetBuildLogPath("asset_dependencies_log.txt");
            if (!File.Exists(path))
            {
                return;
            }

            string[] lines = File.ReadAllLines(path);
            for (int i = 0; i < lines.Length; i++)
            {
                var ary = lines[i].Split('#');
                if (ary.Length <= 0)
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取asset_dependencies_log失败, 重复资源，{0}", lines[i]));
                    break;
                }

                string assetPath = ary[0];
                bool isDity = false;
                if (IsAssetDirty(assetPath))
                {
                    isDity = true;
                }

                string[] list = new string[ary.Length - 1];
                for (int m = 1; m < ary.Length; m++)
                {
                    if (IsAssetDirty(ary[m]))
                    {
                        isDity = true;
                        break;
                    }

                    list[m - 1] = ary[m];
                }

                // 只有文件本身和所依赖的文件都没有改变过，才加入依赖表
                if (!isDity)
                {
                    if (dependenciesDic.ContainsKey(assetPath))
                    {
                        DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取asset_dependencies_log失败, {0}", lines[i]));
                        break;
                    }
                    dependenciesDic.Add(assetPath, list);
                }
            }
        }

        // 写依赖关系文件
        public static void WriteDependenciesLog()
        {
            string path = BuilderConfig.GetBuildLogPath("asset_dependencies_log.txt");
            var dir = Path.GetDirectoryName(path);
            if (!File.Exists(path))
            {
                Directory.CreateDirectory(dir);
            }

            List<string> lines = new List<string>();
            foreach (var kv in dependenciesDic)
            {
                string[] depList = kv.Value;
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.Append(kv.Key);
                for (int i = 0; i < depList.Length; i++)
                {
                    stringBuilder.Append("#");
                    stringBuilder.Append(depList[i]);
                }
                lines.Add(stringBuilder.ToString());
            }

            File.WriteAllLines(path, lines.ToArray());
        }

        // 读AB的修改日志
        public static void ReadABModeifLog()
        {
            string path = BuilderConfig.GetBuildLogPath(string.Format("ab_write_log{0}.txt", buildPlatType));
            if (!File.Exists(path))
            {
                return;
            }

            string[] lines = File.ReadAllLines(path);
            for (int i = 0; i < lines.Length; i++)
            {
                var ary = lines[i].Split('#');
                if (ary.Length != 4)
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取ab_write_log失败, {0}", lines[i]));
                    break;
                }
                if (!abDic.ContainsKey(ary[0]))
                {
                    var abItem = new ABItem();
                    abItem.bundleName = ary[0];
                    abItem.oldWriteTime = Convert.ToInt64(ary[1]);
                    abItem.newWriteTime = abItem.oldWriteTime;
                    abItem.md5 = Convert.ToUInt32(ary[2]);
                    abItem.crc = Convert.ToUInt32(ary[3]);

                    // md5和newWirteTime都
                    abDic.Add(abItem.bundleName, abItem);
                }
                else
                {
                    DeployTool.ReportBuildLog(string.Format("[Fail] BuildAssetsMgr 读取ab_write_log失败, 重复资源，{0}", lines[i]));
                    break;
                }
            }
        }

        // 写AB的修改日志
        public static void WriteABModifyLog()
        {
            string path = BuilderConfig.GetBuildLogPath(string.Format("ab_write_log{0}.txt", buildPlatType));
            var dir = Path.GetDirectoryName(path);
            if (!File.Exists(path))
            {
                Directory.CreateDirectory(dir);
            }

            string[] lines = new string[abDic.Count];
            var index = 0;
            foreach (var kv in abDic)
            {
                lines[index++] = string.Format("{0}#{1}#{2}#{3}",
                    kv.Value.bundleName,
                    kv.Value.newWriteTime,
                    kv.Value.md5,
                    kv.Value.crc);
            }

            File.WriteAllLines(path, lines);
        }

        private static long GetTimeStamp(DateTime time)
        {
            DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            return (long)(time - startTime).TotalMilliseconds;
        }

        public static void GetAllFiles(string dir, List<string> fileList)
        {
            if (dir.IndexOf("Game\\Lua") >= 0
                || dir.IndexOf("Game\\LuaBundle") >= 0
                || dir.IndexOf("Game\\LuaBundleJit") >= 0
                || dir.IndexOf("Game\\Scripts") >= 0
                || dir.IndexOf("StreamingAssets") >= 0
                || dir.IndexOf("ToLua") >= 0)
            {
                return;
            }

            DirectoryInfo d = new DirectoryInfo(dir);
            FileSystemInfo[] infos = d.GetFileSystemInfos();
            foreach (FileSystemInfo fsinfo in infos)
            {
                if (fsinfo is DirectoryInfo)     //判断是否为文件夹
                {
                    GetAllFiles(fsinfo.FullName, fileList);//递归调用
                }
                else
                {
                    fileList.Add(fsinfo.FullName);
                }
            }
        }
    }
}
