﻿using UnityEngine;
using UnityEditor;
using System.Text;
using System.Collections.Generic;

namespace AssetsCheck
{
    public class UnuseKeywordValueCleanChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = {
            "Assets/Game/Actor",
            "Assets/Game/Model",
            "Assets/Game/Effects",
            //"Assets\\Game\\Actors\\Character\\RoleMan\\Body\\1201025",
        };

        static Dictionary<string, Dictionary<string, string>> Shader2KeywordAndPropertyNameDic = new Dictionary<string, Dictionary<string, string>>()
        {
            { "Srp/Standard/SrpRole_PbrCf", new Dictionary<string, string>()
                {
                    { "ENABLE_FLOW_ADD&ENABLE_FLOW_MUL", "_FlowTex" },
                    { "ENABLE_POST_EFFECT", "_PostEffectTex" },
                    { "_EMISSION", "_EmissionMap" },
                    { "_ANISO_ON", "_AnisoSpecNoiseTex" },
                    { "_NORMALMAP", "_NormalMap" },
                }
            },
            { "Srp/Standard/EffectCf", new Dictionary<string, string>()
                {
                    { "ENABLE_UV_NOISE", "_UVNoise" },
                    { "ENABLE_DECAL_ALL&ENABLE_DECAL_ALPHA", "_DecalTex" },
                    { "ENABLE_DISSOLVE_AMOUNT&ENABLE_DISSOLVE_VERTEX", "_DissloveTex" },
                    { "ENABLE_GLOW", "_GlowTex" },
                    { "ENABLE_HEIGHT_MAP", "_HeightMap" },
                    { "ENABLE_NORMAL_MAP", "_NormalMap" },
                    { "_METALLICGLOSSMAP", "_MetallicGlossMap" },
                }
            },
            //{ "JYShaders/StylizedFace", new Dictionary<string, string>()
            //    {
            //        { "ENABLE_UV_NOISE", "_UVNoise" },
            //        { "ENABLE_DECAL_ALL&ENABLE_DECAL_ALPHA", "_DecalTex" },
            //        { "ENABLE_DISSOLVE_AMOUNT", "_DissloveTex" },
            //    }
            //},
        };

        static Dictionary<string, string[]> SrpShaderUnusePropertyName = new Dictionary<string, string[]>()
        {
            {
                "Srp/Standard/SrpRole_PbrCf", new string[]
                {
                    "_BaseMap", "_BumpMap", "_Control", "_Cubemap", "_DecalTex", "_DetailAlbedoMap",
                    "_DetailNormalMap", "_DissloveTex", "_GlowTex", "_MetallicGlossTex", "_MixNoiseTex",
                    "_NormalTex", "_OcclusionTex", "_ParallaxMap", "_SpecGlossMap",
                    "_Splat0", "_Splat1", "_Splat2", "_Splat3", "_UVNoise",
                    "unity_Lightmaps", "unity_LightmapsInd", "unity_ShadowMasks", "_ChannelMask", "_OcclusionMap", "_DetailMask",
                }
            },
            {
                "Srp/Standard/EffectCf", new string[]
                {
                    "_ChannelMask",
                    "_MetallicGlossTex", "_MixNoiseTex", 
                    "_Splat0", "_Splat1", "_Splat2", "_Splat3", "_HSVTex",
                }
            },
            {
                "JYShaders/StylizedFace", new string[]
                {
                    "_AnisoSpecNoiseTex", "_BumpMap", "_ChannelMask", "_MetallicGlossMap",
                    "_DetailAlbedoMap", "_DetailMask", "_DetailNormalMap", "_EmissionMap", "_FlowMaskTex",
                    "_FlowTex", "_MainTex", "_OcclusionMap", "_ParallaxMap", "_PostEffectTex", "_SpecGlossMap", "unity_Lightmaps",
                     "unity_LightmapsInd", "unity_ShadowMasks"
                }
            },
        };

        public override string GetErrorDesc()
        {
            return "检测材质球keyword无用状态下还保留资源引用问题";
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:Material", checkDirs);
            string shaderName = "";
            try
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    bool isMaterialDirty = false;
                    string matPath = AssetDatabase.GUIDToAssetPath(guids[i]);
                    Material material = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
                    if (material == null || material.shader == null)
                        continue;

                    shaderName = material.shader.name;
                    HashSet<string> invalidPropertys = new HashSet<string>();
                    HashSet<string> invalidKeywords = new HashSet<string>();

                    if (SrpShaderUnusePropertyName.ContainsKey(shaderName))
                    {
                        string[] unusePropertyNames = SrpShaderUnusePropertyName[material.shader.name];
                        foreach (string propertyName in unusePropertyNames)
                        {
                            if (material.GetTexture(propertyName) != null)
                            {
                                invalidPropertys.Add(propertyName);
                                isMaterialDirty = true;
                            }
                        }
                    }

                    if (Shader2KeywordAndPropertyNameDic.ContainsKey(shaderName))
                    {
                        Dictionary<string, string> keyword2PropertyName = Shader2KeywordAndPropertyNameDic[shaderName];
                        foreach (var item in keyword2PropertyName)
                        {
                            string[] keywords = item.Key.Split('&');
                            bool isKeywordEnabled = false;
                            foreach (string keyword in keywords)
                            {
                                if (material.HasProperty(item.Value) && material.IsKeywordEnabled(keyword))
                                {
                                    isKeywordEnabled = true;
                                }
                            }
                            if (!isKeywordEnabled && material.GetTexture(item.Value) != null)
                            {
                                isMaterialDirty = true;
                                foreach (string keyword in keywords)
                                {
                                    if (material.HasProperty(item.Value) && !material.IsKeywordEnabled(keyword))
                                    {
                                        invalidKeywords.Add(keyword);
                                    }
                                }
                            }
                        }
                    }

                    if (isMaterialDirty)
                    {
                        CheckItem checkItem = new CheckItem();
                        checkItem.asset = matPath;
                        checkItem.invalidKeywords = invalidKeywords;
                        checkItem.invalidProperty = invalidPropertys;
                        this.outputList.Add(checkItem);
                    }

                    EditorUtility.DisplayProgressBar("正在处理材质球", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError(ex.ToString());
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.ClearProgressBar();
        }

        override protected void OnFix(string[] lines)
        {
            string[] guids = AssetDatabase.FindAssets("t:Material", checkDirs);
            try
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    bool isMaterialDirty = false;
                    string matPath = AssetDatabase.GUIDToAssetPath(guids[i]);
                    CleanMatPropertyByPath(matPath);

                    EditorUtility.DisplayProgressBar("正在处理材质球", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError(ex.ToString());
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                EditorUtility.ClearProgressBar();
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.ClearProgressBar();
        }

        [MenuItem("Assets/策划专用/清理材质球无用属性引用")]
        public static void SelectMatToOperate()
        {
            if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
                {
                    int totalCount = Selection.instanceIDs.Length;
                    for (int i = 0; i < totalCount; i++)
                    {
                        int instanceID = Selection.instanceIDs[i];
                        string path = AssetDatabase.GetAssetPath(instanceID);
                        if (AssetDatabase.IsValidFolder(path))
                        {
                            SelectFolderToOperate(new string[] { path });

                            EditorUtility.DisplayProgressBar("正在处理材质球..."
                            , string.Format("{0} / {1}", i + 1, totalCount)
                            , (float)(i + 1) / (float)totalCount);
                        }
                        else if(path.EndsWith(".mat"))
                        {
                            CleanMatPropertyByPath(path);
                        }
                    }
                }
                else if (null != Selection.activeObject)
                {
                    string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        SelectFolderToOperate(new string[] { path });
                    }
                    else if (path.EndsWith(".mat"))
                    {
                        CleanMatPropertyByPath(path);
                    }
                }
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }

        public static void SelectFolderToOperate(string[] paths)
        {
            string[] guids = AssetDatabase.FindAssets("t:material", paths);
            int index = 0;
            int count = guids.Length;
            foreach (string guid in guids)
            {
                string asset = AssetDatabase.GUIDToAssetPath(guid);
                if (asset.EndsWith(".mat"))
                {
                    CleanMatPropertyByPath(asset);
                }

                EditorUtility.DisplayProgressBar("正在处理材质球..."
                            , string.Format("{0} / {1}", index + 1, count)
                            , (float)(index + 1) / (float)count);
            }
        }

        private static void CleanMatPropertyByPath(string matPath)
        {
            Material material = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
            if (material != null && material.shader != null)
            {
                string shaderName = material.shader.name;
                bool isMatDirty = false;
                if (SrpShaderUnusePropertyName.ContainsKey(shaderName))
                {
                    string[] unusePropertyNames = SrpShaderUnusePropertyName[shaderName];
                    foreach (string propertyName in unusePropertyNames)
                    {

                        if (material.GetTexture(propertyName) != null)
                        {
                            material.SetTexture(propertyName, null);
                        }
                    }
                }

                if (Shader2KeywordAndPropertyNameDic.ContainsKey(shaderName))
                {
                    Dictionary<string, string> keyword2PropertyName = Shader2KeywordAndPropertyNameDic[shaderName];
                    foreach (var item in keyword2PropertyName)
                    {
                        string[] keywords = item.Key.Split('&');
                        bool isKeywordEnabled = false;
                        foreach (string key in keywords)
                        {
                            if (material.HasProperty(item.Value) && material.IsKeywordEnabled(key))
                            {
                                isKeywordEnabled = true;
                            }
                        }

                        if (!isKeywordEnabled && material.GetTexture(item.Value) != null)
                        {
                            isMatDirty = true;
                            material.SetTexture(item.Value, null);
                        }
                    }
                }

                //FLOW_MASK需要ENABLE_FLOW_ADD或ENABLE_FLOW_MUL开启才能用，这里需要特殊判断
                //if (material.IsKeywordEnabled("ENABLE_FLOW_MASK")
                //    && !material.IsKeywordEnabled("ENABLE_FLOW_ADD")
                //     && !material.IsKeywordEnabled("ENABLE_FLOW_MUL"))
                //{
                //    isMatDirty = true;
                //    material.SetTexture("_FlowMaskTex", null);
                //}

                if (isMatDirty)
                {
                    EditorUtility.SetDirty(material);
                }
            }
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public HashSet<string> invalidKeywords;
            public HashSet<string> invalidProperty;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                string keys = "";
                foreach (var key in invalidKeywords)
                {
                    keys += (keys == "") ? key : ("|" + key);

                }
                string propertys = "";
                foreach (var property in invalidProperty)
                {
                    propertys = (propertys == "") ? property : propertys + ("|" + property);

                }
                builder.Append(string.Format("材质球路径:{0},InvalidKeywords:{1},InvalidProperty:{2}", asset, string.IsNullOrEmpty(keys)?"NULL":keys, string.IsNullOrEmpty(propertys) ? "NULL" : propertys));
                return builder;
            }
        }

    }
}
