﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

public class CameraSetEditor : SetModuleEditor
{
    public static void ShowWindow()
    {
        CameraSetEditor window = GetWindow<CameraSetEditor>("Setting");
        window.Show();
    }
    
    private Camera cameraComponent;
    [SerializeField]
    private CameraJsonList jsonDataList;
    [SerializeField]
    private CameraJsonData recordJsonData;
    
    protected override void Awake()
    {
        base.Awake();
        jsonPath = SearchThisCSharpDirectory(typeof(CameraSetEditor).ToString());
        ReadJson();
    }

    void OnGUI()
    {
        selectGo = EditorGUILayout.ObjectField("选中的摄像机", selectGo, typeof(GameObject), true, GUILayout.Width(500)) as GameObject;
        selectGo = Selection.activeGameObject;
        if (selectGo == null)
        {
            return;
        }

        cameraComponent = selectGo.GetComponent<Camera>();
        if (cameraComponent == null)
        {
            //为了使Help的窗口大小如下
            GUILayout.BeginArea(new Rect(0, 20, 480, 40));
            EditorGUILayout.HelpBox("Don't Find Camera Component！！！", MessageType.Error, true);
            GUILayout.EndArea();
        }
        else
        {
            EditorGUILayout.LabelField("Select Mode", guiStyle);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("保存模板", GUILayout.Width(200)))
            {
                if (!string.IsNullOrEmpty(newModuleName))
                {
                    AddModule(newModuleName);
                }
            }
            newModuleName = EditorGUILayout.TextField(newModuleName, GUILayout.Width(270));

            EditorGUILayout.EndHorizontal();
            
            scroll = EditorGUILayout.BeginScrollView(scroll);

            for (int i = 0; i < jsonDataList.inforList.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();

                GUI.skin.button.alignment = TextAnchor.LowerLeft;
                if (GUILayout.Button(jsonDataList.inforList[i].name, GUILayout.Width(250)))
                {
                    LoadModule(jsonDataList.inforList[i].name);
                }
                GUI.skin.button.alignment = TextAnchor.MiddleCenter;

                if (GUILayout.Button("上移", GUILayout.Width(50)))
                {
                    if (i > 0)
                    {
                        CameraJsonData tmpdata = jsonDataList.inforList[i];
                        jsonDataList.inforList[i] = jsonDataList.inforList[i - 1];
                        jsonDataList.inforList[i - 1] = tmpdata;
                        WriteJson();
                    }
                }

                if (GUILayout.Button("下移", GUILayout.Width(50)))
                {
                    if (i < jsonDataList.inforList.Count - 1)
                    {
                        CameraJsonData tmpdata = jsonDataList.inforList[i];
                        jsonDataList.inforList[i] = jsonDataList.inforList[i + 1];
                        jsonDataList.inforList[i + 1] = tmpdata;
                        WriteJson();
                    }
                }

                if (GUILayout.Button("移除", GUILayout.Width(100)))
                {
                    RemoveModule(jsonDataList.inforList[i].name);
                }

                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndScrollView();

            GUILayout.Space(20);

            if (GUILayout.Button("撤销", GUILayout.Width(470)))
            {
                if (recordJsonData != null)
                {
                    SetValueByJsonData(recordJsonData);
                }
            }
        }

    }
    
    protected override void LoadModule(string moduleName)
    {
        recordJsonData = GetJsonDataFromObject("recordJsonData");
        ReadJson();
        foreach (var infor in jsonDataList.inforList)
        {
            if (infor.name == moduleName)
            {
                SetValueByJsonData(infor);
            }
        }
    }

    protected override void AddModule(string moduleName)
    {
        ReadJson();
        CameraJsonData jsonData = GetJsonDataFromObject(moduleName);

        for (int i = 0; i < jsonDataList.inforList.Count; i++)
        {
            if (jsonDataList.inforList[i].name == moduleName)
            {
                jsonDataList.inforList[i] = jsonData;
                WriteJson();
                return;
            }
        }

        jsonDataList.inforList.Add(jsonData);
        WriteJson();
    }

    protected override void RemoveModule(string moduleName)
    {
        ReadJson();
        for (int i = 0; i < jsonDataList.inforList.Count; i++)
        {
            if (jsonDataList.inforList[i].name == moduleName)
            {
                jsonDataList.inforList.RemoveAt(i);
                WriteJson();
                return;
            }
        }
    }

    protected void ReadJson()
    {
        if (!File.Exists(jsonPath))
        {
            File.Create(jsonPath);
        }
        string data = File.ReadAllText(jsonPath, Encoding.UTF8);

        jsonDataList = JsonUtility.FromJson<CameraJsonList>(data);
        if (jsonDataList == null)
        {
            jsonDataList = new CameraJsonList();
        }
        if (jsonDataList.inforList == null)
        {
            jsonDataList.inforList = new List<CameraJsonData>();
        }
    }

    private void WriteJson()
    {
        string data = JsonUtility.ToJson(jsonDataList);
        File.WriteAllText(jsonPath, data, Encoding.UTF8);
    }

    private CameraJsonData GetJsonDataFromObject(string moduleName)
    {
        CameraJsonData infor = new CameraJsonData();
        infor.name = moduleName;

        infor.cameraPos = cameraComponent.transform.localPosition;
        infor.cameraRot = cameraComponent.transform.localEulerAngles;
        infor.cameraScale = cameraComponent.transform.localScale;

        return infor;
    }
    
    private void SetValueByJsonData(CameraJsonData infor)
    {
        var isPrefab = PrefabUtility.GetPrefabType(selectGo);

        if (isPrefab == PrefabType.PrefabInstance)
        {
            PrefabUtility.DisconnectPrefabInstance(selectGo);
        }

        if (infor.cameraPos != Vector3.zero)
        {
            cameraComponent.transform.localPosition = infor.cameraPos;
            cameraComponent.transform.localEulerAngles = infor.cameraRot;
            cameraComponent.transform.localScale = infor.cameraScale;
        }
    }

    
}

#region//定义Json的类
[Serializable]
public class CameraJsonData
{
    public string name;

    public Vector3 cameraPos;
    public Vector3 cameraRot;
    public Vector3 cameraScale;
}

[Serializable]
public class CameraJsonList
{
    public List<CameraJsonData> inforList;
}
#endregion
