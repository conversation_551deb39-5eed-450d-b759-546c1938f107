﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Nirvana;
using UnityEngine;
using System.Collections;

public sealed class ActorAttachEffect : MonoBehaviour
{
    [SerializeField]
    [Tooltip("The effect prefab")]
    [AssetType(typeof(EffectControl))]
    private AssetID effectAsset;

    [SerializeField]
    private Transform attachTransform;

    [SerializeField]
    private Vector3 scale = new Vector3(1.0f, 1.0f, 1.0f);

    public bool playOnAwake = false;

    private bool hasEffect = false;

    private GameObject effect;

    private Vector3 position = new Vector3(0f, 0f, 0f);

    private void OnEnable()
    {
        if (this.playOnAwake)
        {
            this.PlayEffect();
        }
        else
        {
            this.StopEffect();
        }
    }

    public AssetID EffectAsset
    {
        get { return this.effectAsset;  }
        set { this.effectAsset = value;  }
    }

    public Transform AttachTransform
    {
        set { this.attachTransform = value; }
    }

    public Vector3 SetPosition
    {
        set { this.position = value; }
    }

    public Vector3 Scale
    {
        set { this.scale = value; }
    }

    public void PlayEffect()
    {
    }

    public void StopEffect()
    {
    }
}

