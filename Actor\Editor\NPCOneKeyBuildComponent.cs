﻿using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using Nirvana;

/// <summary>
/// 一键生成新NPC模型所需所有组件，材质球及其基础设置
/// </summary>
class NPCOneKeyBuildComponent : Editor
{
    //[MenuItem("Assets/策划专用/一键生成模型/一键生成NPC")]
    public static void Fixed()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            int totalCount = Selection.instanceIDs.Length;
            for (int i = 0; i < totalCount; i++)
            {
                int instanceID = Selection.instanceIDs[i];
                string path = AssetDatabase.GetAssetPath(instanceID);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedNPCInPaths(checkDirs);
                }
                else
                {
                    Debug.LogError("操作提示！！！请选中文件夹进行操作！！！");
                }

                EditorUtility.DisplayProgressBar("正在NPC..."
                    , string.Format("{0} / {1}", i + 1, totalCount)
                    , (float)(i + 1) / (float)totalCount);
            }
        }
        else if (null != Selection.activeObject)
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (AssetDatabase.IsValidFolder(path))
            {
                checkDirs = new string[] { path };
                FixedNPCInPaths(checkDirs);
            }
            else
            {
                Debug.LogError("操作提示！！！请选中文件夹进行操作！！！");
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    static void FixedNPCInPaths(string[] paths)
    {
        foreach (string path in paths)
        {
            string fbxName = path.Replace("Assets/Game/Model/NPC/", "");
            string fbxPath = path + "/" + fbxName + ".FBX";
            string prefabPath = path + "/" + fbxName + "001.prefab";
            string matPath = path + "/" + fbxName + ".mat";

            if (File.Exists(fbxPath))
            {
                GameObject modelFBX = AssetDatabase.LoadAssetAtPath<GameObject>(fbxPath);
                GameObject npcPrefab = PrefabUtility.CreatePrefab(prefabPath, modelFBX);
                if (npcPrefab)
                {
                    GameObject npcModel = AssetDatabase.LoadAssetAtPath(prefabPath, typeof(GameObject)) as GameObject;
                    npcModel = GameObject.Instantiate(npcModel);

                    AnimatorOptimizer ao = npcModel.GetOrAddComponent<AnimatorOptimizer>();
                    ao.Deoptimize();

                    ActorAttachment attachment = npcModel.GetOrAddComponent<ActorAttachment>();
                    attachment.AutoPick();
                    if (attachment.GetAttachPoint(0) == null)
                    {
                        Debug.LogErrorFormat("报错！！！路径：{0} 该模型找不到UI挂点，请检查！！！", prefabPath);
                    }

                    npcModel.GetOrAddComponent<AnimatorEventDispatcher>();
                    ClickableObject clickObj = npcModel.GetOrAddComponent<ClickableObject>();

                    GameObject clickableObj = new GameObject();
                    clickableObj.transform.parent = npcModel.transform;
                    clickableObj.transform.localPosition = Vector3.zero;
                    clickableObj.transform.localRotation = Quaternion.identity;
                    clickableObj.transform.localScale = Vector3.one;
                    clickableObj.gameObject.name = "Clickable";
                    clickableObj.gameObject.layer = GameLayers.Clickable;

                    CapsuleCollider collider = clickableObj.GetOrAddComponent<CapsuleCollider>();
                    collider.transform.localPosition = new Vector3(0, 1.3f, 0);
                    collider.height = 3;
                    collider.radius = 1;

                    Clickable clickable = clickableObj.GetOrAddComponent<Clickable>();
                    clickable.Owner = clickObj;

                    Material mat = new Material(Shader.Find("YifStandard/YifStandardActor"));
                    AssetDatabase.CreateAsset(mat, matPath);
                    Material newMat = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
                    newMat.SetFloat("_MaterialStyle", 1);
                    newMat.EnableKeyword("ENABLE_DIFFUSE");
                    string[] texGUIDs = AssetDatabase.FindAssets("t:texture", new string[] { path });
                    if (texGUIDs != null)
                    {
                        var mainTexPath = AssetDatabase.GUIDToAssetPath(texGUIDs[0]);
                        mat.mainTexture = AssetDatabase.LoadAssetAtPath(mainTexPath, typeof(Texture)) as Texture;
                    }
                    else
                    {
                        Debug.LogErrorFormat("报错！！！路径：{0} 找不到贴图文件，请检查！！！", path);
                    }

                    Renderer renderer = npcModel.GetComponentInChildren<Renderer>();
                    if (renderer)
                    {
                        renderer.sharedMaterial = mat;
                    }
                    else
                    {
                        Debug.LogErrorFormat("报错！！！路径：{0} 没有挂载Mesh Renderer或者Skinned Mesh Renderer组件，请检查！！！", prefabPath);
                    }

                    ActorRender actorRenderer = npcModel.GetOrAddComponent<ActorRender>();
                    actorRenderer.AutoFetch();

                    PrefabUtility.ReplacePrefab(npcModel, npcPrefab, ReplacePrefabOptions.ConnectToPrefab);
                    GameObject.DestroyImmediate(npcModel);
                }
            }
            else
            {
                Debug.LogErrorFormat("报错！！！文件：{0} ，请保证要生成NPC模型的FBX文件与父级文件夹同名（如：6100.FBX）", fbxPath);
            }
        }
    }
}
