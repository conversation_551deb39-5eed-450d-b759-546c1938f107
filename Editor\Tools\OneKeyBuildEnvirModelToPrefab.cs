﻿using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using UnityEditor.SceneManagement;
using Object = UnityEngine.Object;

public class OneKeyBuildEnvirModelToPrefab
{
    private static string prefabRoot = "/Prefabs";
    private static string prefabExtension = ".prefab";

    [MenuItem("自定义工具/美术专用/一键生成场景预制体")]
    public static void Build()
    {
        Object[] selections = Selection.objects;

        Object target_T = Selection.activeObject;

        if (null == selections || selections.Length == 0)
        {
            EditorUtility.DisplayDialog("提示", "请选中要生成的模型", "确定");
            return;
        }

        int curIndex = 0;
        int totalCount = selections.Length;
        for (int i = 0; i < totalCount; i++)
        {
            Object target = PrefabUtility.GetPrefabParent(selections[i]);

            string modelFBXPath = AssetDatabase.GetAssetPath(target);
            if (string.IsNullOrEmpty(modelFBXPath))
            {
                //EditorUtility.DisplayDialog("提示", "请选择正确的目标", "确定");
                continue;
            }

            string parentFolderPath = Directory.GetParent(modelFBXPath).Parent.FullName;
            parentFolderPath = parentFolderPath.Replace("\\", "/");
            string safeFolderPath = string.Concat(parentFolderPath, prefabRoot);

            if (!Directory.Exists(safeFolderPath))
            {
                Directory.CreateDirectory(safeFolderPath);
            }

            string fileName = string.Concat(safeFolderPath, "/", target.name, prefabExtension);
            if (File.Exists(fileName))
            {
                AssetDatabase.DeleteAsset(fileName);
            }

            target = selections[i];
            GameObject targetObj = target as GameObject;
            GameObject cloneObj = GameObject.Instantiate<GameObject>(targetObj);
            cloneObj.name = target.name.Replace("(Clone)", string.Empty);
            string genPrefabFullName = string.Concat(safeFolderPath, "/", cloneObj.name, prefabExtension);

            string temp = genPrefabFullName.Replace("Assets", "&");
            int index = temp.IndexOf("&");
            temp = temp.Substring(index, temp.Length - index);
            string relativePath = temp.Replace("&", "Assets");

            Object prefabObj = PrefabUtility.CreateEmptyPrefab(relativePath);
            GameObject prefab = PrefabUtility.ReplacePrefab(cloneObj, prefabObj, ReplacePrefabOptions.ConnectToPrefab);
            GameObject.DestroyImmediate(targetObj);

            curIndex += 1;
            EditorUtility.DisplayProgressBar("一键生成场景预制体", string.Format("进度: {0} / {1}", curIndex, totalCount), (float)curIndex / (float)totalCount);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
    }


    //[MenuItem("Assets/场景导出专用/场景资源批量改名")]
    //public static void FixedName()
    //{
    //    if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
    //    {
    //        if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
    //        {
    //            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
    //            DirectoryInfo DInfo = new DirectoryInfo(path);
    //            var files = DInfo.GetFiles();

    //            string newNameStr = "A1_YW_XueDi";
    //            List<string> oldNameMatchs = new List<string>() {
    //                "W28_YW_xuedi", "W28_YW_BingXue", "W6_YW_BingXue", "W28_YW_bingxue",
    //            };

    //            foreach (var f in files)
    //            {
    //                bool isMatch = false;
    //                string matchStr = "";
    //                foreach (string s in oldNameMatchs)
    //                {
    //                    if (f.Name.Contains(s))
    //                    {
    //                        isMatch = true;
    //                        matchStr = s;
    //                        break;
    //                    }
    //                }
    //                if (isMatch)
    //                {
    //                    string newName = f.Name.Replace(matchStr, newNameStr);
    //                    f.MoveTo(Path.Combine(path, newName));
    //                }
    //            }
    //        }
    //    }
    //    AssetDatabase.Refresh();
    //    AssetDatabase.SaveAssets();
    //    EditorUtility.ClearProgressBar();
    //}

    [MenuItem("自定义工具/美术专用/场景物件计数")]
    public static void CounterSceneObjects()
    {
        Dictionary<string, int> dict = new Dictionary<string, int>();
        Dictionary<string, string> dict2 = new Dictionary<string, string>();
        Dictionary<string, GameObject> dict3 = new Dictionary<string, GameObject>();
        HashSet<GameObject> set = new HashSet<GameObject>();
        foreach (var t in Transform.FindObjectsByType<Transform>(FindObjectsInactive.Exclude, FindObjectsSortMode.None))
        {
            var root = PrefabUtility.GetNearestPrefabInstanceRoot(t.gameObject);
            if (root && !set.Contains(root))
            {
                Transform parent = root.transform.parent;
                while (parent && parent.parent)
                {
                    if (parent.parent.name == "Models")
                    {
                        break;
                    }

                    parent = parent.parent;
                }

                if (parent == null)
                {
                    continue;
                }

                var path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(root);
                root.name = Path.GetFileNameWithoutExtension(path);
                if (!string.IsNullOrEmpty(path))
                {
                    if (!dict.TryGetValue(path, out int c))
                    {
                        dict.Add(path, 0);
                        dict2.Add(path, parent.name);
                        dict3.Add(path, root);
                    }

                    dict[path]++;
                }

                set.Add(root);
            }

        }
        
        var keys = dict.Keys.ToArray();
        Array.Sort(keys, (a, b) =>
        {
            return dict[a] > dict[b] ? -1 : 1;
        });
        
        foreach (var d in keys)
        {
            Debug.Log($"{dict2[d]} -> {Path.GetFileNameWithoutExtension(d)} ： {dict[d]}", dict3[d]);
        }
    }
}
