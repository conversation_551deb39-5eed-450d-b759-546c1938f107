﻿using UnityEngine;
using UnityEditor;
using AssetsCheck;
using System.Text;
using System;
using System.Collections.Generic;

class ParticleSystemRenderMeshFixed
{
    //用来替换的mesh
    private static string fixMeshPath = "Assets/Game/Effects/FBX/OpenReadWrite/eff_mesh_plane_tongyong_lhl.fbx";

    private Dictionary<int, Mesh> guidToMesh = new Dictionary<int, Mesh>();

    private static int QuadInstanceID = 1376;
    private static int PlaneInstanceID = 1374;

    [MenuItem("Assets/技术专用/【未维护】修复Mesh")]
    public static void Fixed()
    {
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            int totalCount = Selection.instanceIDs.Length;
            int instanceID = Selection.instanceIDs[0];
            string selectPath = AssetDatabase.GetAssetPath(instanceID);
            if (AssetDatabase.IsValidFolder(selectPath))
            {
                string[] checkDirs = new string[] { selectPath };
                try
                {
                    Mesh fixMesh = AssetDatabase.LoadAssetAtPath(fixMeshPath, typeof(Mesh)) as Mesh;
                    string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
                    int index = 0;
                    for (int k = 0; k < guids.Length; k++)
                    {
                        string path = AssetDatabase.GUIDToAssetPath(guids[k]);
                        GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        ParticleSystemRenderer[] particleSystems = gameObject.GetComponentsInChildren<ParticleSystemRenderer>();
                        if (particleSystems != null && particleSystems.Length > 0)
                        {
                            bool isDirty = false;
                            for (int j = 0; j < particleSystems.Length; j++)
                            {
                                ParticleSystemRenderer particleSystemRenderer = particleSystems[j];
                                //这里只处理引用了1394的mesh，有发现其他的需要添加处理
                                if (particleSystemRenderer != null && particleSystemRenderer.mesh != null)
                                {
                                    if (particleSystemRenderer.renderMode == ParticleSystemRenderMode.Mesh)
                                    {
                                        if (particleSystemRenderer.mesh.GetInstanceID() == QuadInstanceID)
                                        {
                                            isDirty = true;
                                            particleSystemRenderer.mesh = fixMesh;
                                            Debug.LogErrorFormat("path:{0}, node name:{1}", path, particleSystemRenderer.gameObject.name);
                                        }
                                    }
                                    else
                                    {
                                        particleSystemRenderer.mesh = null;
                                        isDirty = true;
                                        Debug.LogErrorFormat("清理mesh引用 path:{0}, node name:{1}", path, particleSystemRenderer.gameObject.name);
                                    }
                                }
                            }
                            if (isDirty)
                            {
                                PrefabUtility.SaveAsPrefabAssetAndConnect(gameObject, path, InteractionMode.UserAction);
                            }
                        }
                        GameObject.DestroyImmediate(gameObject);
                        index++;
                        EditorUtility.DisplayProgressBar("", string.Format("进度：{0}/{1}", index, guids.Length), (float)index / (float)guids.Length);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                    EditorUtility.ClearProgressBar();
                }
            }
            else
            {
                Debug.LogError("操作提示！！！请选中文件夹进行操作！！！");
            }
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }
    }

    [MenuItem("Assets/技术专用/一键修改SkinnedMeshRenderer")]
    public static void QuickChangeSkinnedMeshRendererName()
    {
        if (null == Selection.activeObject || null == Selection.instanceIDs || Selection.instanceIDs.Length <= 0)
        {
            return;
        }

        int totalCount = Selection.instanceIDs.Length;
        int instanceID = Selection.instanceIDs[0];
        string selectPath = AssetDatabase.GetAssetPath(instanceID);

        if (!AssetDatabase.IsValidFolder(selectPath))
        {
            return;
        }

        string[] checkDirs = new string[] { selectPath };
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int index = 0;
        for (int k = 0; k < guids.Length; k++)
        {
            string path = AssetDatabase.GUIDToAssetPath(guids[k]);
            string name_first = GetSkinNameByPath(path);

            if (name_first == string.Empty)
                continue;

            GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
            SkinnedMeshRenderer[] newSkinenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);

            if (newSkinenderers == null)
                continue;

            if (newSkinenderers.Length == 1)
                newSkinenderers[0].gameObject.name = name_first;

            if (newSkinenderers.Length == 2 && name_first == "RoleFace")
            {
                newSkinenderers[0].gameObject.name = "RoleEyeball";
                newSkinenderers[1].gameObject.name = name_first;
            }

            index++;
            EditorUtility.SetDirty(prefab);
            EditorUtility.DisplayProgressBar("", string.Format("进度：{0}/{1}", index, guids.Length), (float)index / (float)guids.Length);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }


    public static string GetSkinNameByPath(string path)
    {
        if (path.StartsWith("Assets/Game/Actors/Character/RoleMan/Face") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Face")
            || path.StartsWith("Assets/Game/Actors/Character/RoleMan/Realm/Face") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Realm/Face"))
        {
            return "RoleFace";
        }
        else if (path.StartsWith("Assets/Game/Actors/Character/RoleMan/Body") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Body")
            || path.StartsWith("Assets/Game/Actors/Character/RoleMan/Realm/Body") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Realm/Body"))
        {
            return "RoleBody";
        }
        else if (path.StartsWith("Assets/Game/Actors/Character/RoleMan/Hair") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Hair")
            || path.StartsWith("Assets/Game/Actors/Character/RoleMan/Realm/Hair") || path.StartsWith("Assets/Game/Actors/Character/RoleWoman/Realm/Hair"))
        {
            return "RoleHair";
        }

        return string.Empty;
    }
}