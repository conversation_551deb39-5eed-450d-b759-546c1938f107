﻿using System;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Collections;
using LitJson;
using System.IO;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

class ImageCNIdentifyTool
{
    private static string API_KEY = "lNvPKRcToSPIlV7VhYbldWdW";
    private static string SECRET_KEY = "319LXlbpogQIdVD3g7c70xcFkAiRHzpz";

    private static string[] checkDirs = new string[] { //"Assets/Game/UIs/Image",
                                                    //"Assets/Game/UIs/RawImages",
                                                    "Assets/Game/UIs/View",
                                                    //"Assets/Game/UIs/Icon",
    };
    private static List<string> checkWhiteDirs = new List<string>() {
        //"Assets/Game/UIs/View/achievement_ui/Images",
        //"Assets/Game/UIs/View/act_qxzl/Images",
        //"Assets/Game/UIs/View/appearance_other_ui/Images",
        //"Assets/Game/UIs/View/bizuo_ui/Images",
        //"Assets/Game/UIs/View/boss_ui/Images",
        //"Assets/Game/UIs/View/calendar/Images",
        //"Assets/Game/UIs/View/chat_ui/Images",
        //"Assets/Game/UIs/View/chenghao/Images",
        //"Assets/Game/UIs/View/common_effect/Images",
        //"Assets/Game/UIs/View/country_map_ui/Images",
        //"Assets/Game/UIs/View/equip_target/Images",
        //"Assets/Game/UIs/View/equipfumo_ui/Images",
        //"Assets/Game/UIs/View/equipment_mark_ui/Images",
        //"Assets/Game/UIs/View/equipment_ui/Images",
        //"Assets/Game/UIs/View/eternal_night_ui/Images",
        //"Assets/Game/UIs/View/field1v1_ui/Images",
        //"Assets/Game/UIs/View/fortunecat_ui/Images",
        //"Assets/Game/UIs/View/fubenpanel/Images",
        //"Assets/Game/UIs/View/FunTrailer/Images",
        //"Assets/Game/UIs/View/guide_ui/Images",
        //"Assets/Game/UIs/View/guild_answer_ui/Images",
        //"Assets/Game/UIs/View/guild_battle_new_ui/Images",
        //"Assets/Game/UIs/View/guild_ui/Images",
        //"Assets/Game/UIs/View/gurad_invalid_time/Images",
        //"Assets/Game/UIs/View/itemtip_ui/Images",
        //"Assets/Game/UIs/View/jingjie_ui/Images",
        //"Assets/Game/UIs/View/kuafu_honorhalls_ui/Images",
        //"Assets/Game/UIs/View/kuafu_pvp_ui/Images",
        //"Assets/Game/UIs/View/limit_time_gift/Images",
        //"Assets/Game/UIs/View/lingzhi/Images",
        //"Assets/Game/UIs/View/login_ui/Images",
        //"Assets/Game/UIs/View/long_hun_ui/Images",
        //"Assets/Game/UIs/View/long_zhu_ui/Images",
        //"Assets/Game/UIs/View/main_ui/Images/icon/normal_icon",
        //"Assets/Game/UIs/View/main_ui/Images/icon/activity_icon",
        //"Assets/Game/UIs/View/main_ui/Images/icon",
        //"Assets/Game/UIs/View/map_ui/Images",
        //"Assets/Game/UIs/View/shj_ui/Images/nopack",
        //"Assets/Game/UIs/View/shj_ui/Images",
        //"Assets/Game/UIs/View/miscpre_load/Floating",
        //"Assets/Game/UIs/View/miscpre_load/Images",
        //"Assets/Game/UIs/View/mount_lingchong_ui/Images",
        //"Assets/Game/UIs/View/new_team_ui/Images",
        //"Assets/Game/UIs/View/open_server_activity_ui/Images",
        //"Assets/Game/UIs/View/recharge_ui/Images",
        //"",
        //"",
        //"",
        //"",
        //"",
        //"",
        //"Assets/Game/UIs/View/",
        //"Assets/Game/UIs/View/",
        //"Assets/Game/UIs/View/",
    };

    private static List<string> checkBlackDirs = new List<string>()
    {
        "Assets/Game/UIs/View/main_ui/Images",
        "Assets/Game/UIs/View/marry_ui/Images",
        "Assets/Game/UIs/View/tianshen/Images",
        "Assets/Game/UIs/View/zhanling_ui/Images",
    };

    private static Dictionary<string, ImgWordItem> ImgWordItemList = new Dictionary<string, ImgWordItem>();
    private static Dictionary<string, ImgWordItem> ImgWordItemErrorList = new Dictionary<string, ImgWordItem>();

    [MenuItem("自定义工具/策划专用/图片中文识别", priority = 1)]
    public static void ImageCNIdentify()
    {
        ImageCNIdentifyInternal();
    }

    public static void ImageCNIdentifyInternal(string _checkDirs = "", bool _isIgnoreA1 = false)
    {
        if (EditorUtility.DisplayDialog("提示", "是否开始识别工程内图片中文", "开始识别", "取消"))
        {
            string errorPath = "";
            string[] __checkDirs = string.IsNullOrEmpty(_checkDirs) ? checkDirs : new string[] { _checkDirs };
            try
            {
                Debug.LogError("开始识别图片中的文字");
                InitWordItemList();

                Dictionary<string, string> imageDir = new Dictionary<string, string>();
                List<string> pathList = new List<string>();
                int count = 0;
                string[] GUIDs = AssetDatabase.FindAssets("*", __checkDirs);
                int total = GUIDs.Length;
                foreach (string guid in GUIDs)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid).Replace("\\", "/");
                    if (!path.EndsWith(".png") && !path.EndsWith(".jpg"))
                    {
                        count++;
                        EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                        continue;
                    }
                    int LIndexOf = path.LastIndexOf("/");
                    string forwardPath = path.Substring(0, LIndexOf);
                    if (checkWhiteDirs.Contains(forwardPath) || checkBlackDirs.Contains(forwardPath))
                    {
                        count++;
                        EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                        continue;
                    }

                    if (!_isIgnoreA1)
                    {
                        string imgName = path.Substring(LIndexOf + 1, path.Length - LIndexOf - 1).Replace(".png", "");
                        if (!imgName.StartsWith("a1_"))
                        {
                            count++;
                            EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                            continue;
                        }
                    }

                    if (!ImgWordItemList.ContainsKey(path))
                    {
                        pathList.Add(path);
                    }
                    count++;
                    EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                }

                count = 0;
                total = pathList.Count;
                foreach (string p in pathList)
                {
                    if (ImgWordItemList.ContainsKey(p))
                        continue;
                    errorPath = p;
                    string imgName = p.Substring(p.LastIndexOf("/") + 1, p.Length - p.LastIndexOf("/") - 1).Replace(".png", "");
                    string filePath = string.Format("{0}/../{1}", Application.dataPath, p);
                    string imgWords = SendImgRequest(filePath);
                    if (!string.IsNullOrEmpty(imgWords))
                    {
                        ImgWordItem imgWordItem = new ImgWordItem(0, imgName, p, imgWords, "");
                        ImgWordItemList.Add(p, imgWordItem);
                        Debug.LogErrorFormat("路径：{0} 新增，文字内容：{1}", p, imgWords);
                    }
                    Thread.Sleep(100);
                    count++;
                    EditorUtility.DisplayProgressBar("识别图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                }

                SaveRecord();
                EditorUtility.ClearProgressBar();
                Debug.LogError("识别图片中的文字完毕");
            }
            catch (Exception ex)
            {
                Debug.LogErrorFormat("Error Path:{0}, Exception:{1}", errorPath, ex);
                EditorUtility.ClearProgressBar();
                SaveRecord();
                ImgWordItemList.Clear();
            }
        }
    }

    private static void SaveRecord(string csvPath = "")
    {
        if (ImgWordItemList.Count == 0) return;
        try
        {
            string excelPath = string.IsNullOrEmpty(csvPath) ? GetImgWordRecordPath() : csvPath;
            Debug.LogError("开始SaveRecord, path:" + excelPath);
            using (var file = File.Open(excelPath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read))
            {
                using (var writeStream = new StreamWriter(file, Encoding.UTF8))
                {
                    writeStream.WriteLine("序号,png图片名字,png图片位置,文本内容");
                    int id = 1;
                    int total = ImgWordItemList.Count;
                    foreach (KeyValuePair<string, ImgWordItem> item in ImgWordItemList)
                    {
                        ImgWordItem imgWordItem = item.Value;
                        writeStream.WriteLine(string.Format("{0},{1},{2},{3}", id, imgWordItem.IMG_Name, imgWordItem.IMG_Path, imgWordItem.IMG_Words));
                        EditorUtility.DisplayProgressBar("保存记录中", string.Format("{0} / {1}", id, total), (float)id / (float)total);
                        id++;
                    }
                    writeStream.Close();
                    writeStream.Dispose();
                }
                file.Close();
                file.Dispose();
            }

            if (ImgWordItemErrorList.Count != 0)
            {
                string errorExcelPath = GetImgWordErrorRecordPath();
                using (var file = File.Open(errorExcelPath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read))
                {
                    using (var writeStream = new StreamWriter(file, Encoding.UTF8))
                    {
                        writeStream.WriteLine("序号,png图片名字,png图片位置,报错内容");
                        int id = 1;
                        int total = ImgWordItemErrorList.Count;
                        foreach (KeyValuePair<string, ImgWordItem> item in ImgWordItemErrorList)
                        {
                            ImgWordItem imgWordItem = item.Value;
                            writeStream.WriteLine(string.Format("{0},{1},{2},{3}", id, "", imgWordItem.IMG_Path, imgWordItem.IMG_ERROR));
                            EditorUtility.DisplayProgressBar("保存ERROR记录中", string.Format("{0} / {1}", id, total), (float)id / (float)total);
                            id++;
                        }
                        writeStream.Close();
                        writeStream.Dispose();
                    }
                    file.Close();
                    file.Dispose();
                }
            }

            EditorUtility.ClearProgressBar();
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
        }
    }

    private static string SendImgRequest(string imageURL)
    {
        string ImgCHNWords = "";
        if (File.Exists(imageURL))
        {
            byte[] imgByte = File.ReadAllBytes(imageURL);
            var client = new Baidu.Aip.Ocr.Ocr(API_KEY, SECRET_KEY);
            JObject jObject = client.GeneralBasic(imgByte);
            foreach (JProperty jp in jObject.Children())
            {
                if (jp.Name == "words_result")
                {
                    JArray ja = JArray.Parse(jp.Value.ToString());
                    List<ImgWordJson> jList = ja.ToObject<List<ImgWordJson>>();
                    for (int i = 0; i < jList.Count; i++)
                    {
                        string words = jList[i].words.ToString();
                        ImgCHNWords += IsAllWordENG(words) ? "" : words;
                    }
                }
                else if (jp.Name == "error_msg")
                {
                    if (!ImgWordItemErrorList.ContainsKey(imageURL))
                    {
                        ImgWordItemErrorList.Add(imageURL, new ImgWordItem(0, "", imageURL, "", jp.Value.ToString()));
                    }
                    Debug.LogErrorFormat("imageURL:{0}, Error Msg:{1}", imageURL, jp.Value);
                }
            }
        }
        return ImgCHNWords;
    }

    static bool IsAllWordENG(string words)
    {
        words = Regex.Replace(words, @"[^a-zA-Z0-9\u4e00-\u9fa5\s]", "");
        Regex regEnglish = new Regex("^[a-zA-Z]");
        if (regEnglish.IsMatch(words))
        {
            return true;
        }
        return false;
    }

    static void InitWordItemList()
    {
        Debug.LogError("开始InitWordItemList");
        ImgWordItemList.Clear();
        ImgWordItemErrorList.Clear();
        checkWhiteDirs.Clear();
        string excelPath = GetImgWordRecordPath();
        if (!File.Exists(excelPath))
        {
            File.Create(excelPath);
        }
        else
        {
            using (var file = File.Open(excelPath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None))
            {
                using (var readStream = new StreamReader(file))
                {
                    int count = 0;
                    string line;
                    while (!readStream.EndOfStream)
                    {
                        line = readStream.ReadLine();
                        if (count > 0 && !string.IsNullOrEmpty(line))
                        {
                            string[] s = line.Split(',');
                            string filePath = string.Format("{0}/../{1}", Application.dataPath, s[2]);
                            if (!ImgWordItemList.ContainsKey(s[2]) && File.Exists(filePath))
                            {
                                ImgWordItemList.Add(s[2], new ImgWordItem(int.Parse(s[0]), s[1], s[2], s[3], ""));
                            }

                            string str = s[2].Substring(0, s[2].LastIndexOf("/"));
                            checkWhiteDirs.Add(str);
                        }
                        count += 1;
                    }
                    readStream.Close();
                    readStream.Dispose();
                }
                file.Close();
                file.Dispose();
            }
            SaveRecord(Application.dataPath + "/../AssetsCheck/ImgWordRecord_Init.csv");
        }
    }

    private static string GetImgWordRecordPath()
    {
        return Application.dataPath + "/../AssetsCheck/ImgWordRecord.csv";
    }

    private static string GetImgWordErrorRecordPath()
    {
        return Application.dataPath + "/../AssetsCheck/ImgWordErrorRecord.csv";
    }

    //[MenuItem("自定义工具/策划专用/图片中文识别(测试)", priority = 2)]
    public static void Test()
    {
        string filePath = "G:/word.png";
        Debug.LogError(SendImgRequest(filePath));
    }

    //[MenuItem("自定义工具/策划专用/图片中文识别(高精度接口测试)", priority = 3)]
    public static void GJDTest()
    {
        string filePath = "G:/word.png";

        string ImgCHNWords = "";
        if (File.Exists(filePath))
        {
            byte[] imgByte = File.ReadAllBytes(filePath);
            var client = new Baidu.Aip.Ocr.Ocr(API_KEY, SECRET_KEY);
            JObject jObject = client.AccurateBasic(imgByte);
            foreach (JProperty jp in jObject.Children())
            {
                if (jp.Name == "words_result")
                {
                    JArray ja = JArray.Parse(jp.Value.ToString());
                    List<ImgWordJson> jList = ja.ToObject<List<ImgWordJson>>();
                    for (int i = 0; i < jList.Count; i++)
                    {
                        string words = jList[i].words.ToString();
                        ImgCHNWords += IsAllWordENG(words) ? "" : words;
                    }
                    break;
                }
            }
        }

        Debug.LogError(ImgCHNWords);
    }

    [MenuItem("自定义工具/策划专用/导出图片", priority = 4)]
    public static void OutImg()
    {
        string excelPath = GetImgWordRecordPath();
        string outputPath = Application.dataPath + "/../ImgWord/";
        if (!Directory.Exists(outputPath))
        {
            Directory.CreateDirectory(outputPath);
        }

        try
        {
            using (var file = File.Open(excelPath, FileMode.OpenOrCreate, FileAccess.Read, FileShare.Read))
            {
                using (var readStream = new StreamReader(file))
                {
                    int count = 0;
                    string line;
                    while (!readStream.EndOfStream)
                    {
                        line = readStream.ReadLine();
                        if (count > 0)
                        {
                            string[] lineStr = line.Split(',');
                            if (File.Exists(outputPath + lineStr[2]))
                            {
                                count++;
                            }
                            else
                            {
                                string newDir = outputPath + lineStr[2].Substring(0, lineStr[2].LastIndexOf("/"));
                                if (!Directory.Exists(newDir))
                                {
                                    Directory.CreateDirectory(newDir);
                                }
                                File.Copy(Application.dataPath + "/../" + lineStr[2], outputPath + lineStr[2]);
                            }
                        }
                        count++;
                    }
                    readStream.Close();
                    readStream.Dispose();
                }
                file.Close();
                file.Dispose();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
        }
        Debug.LogError("导出图片完毕!");
        System.Diagnostics.Process.Start("explorer.exe", outputPath);
    }

    [MenuItem("自定义工具/策划专用/选择目录进行图片中文识别")]
    static void SelectPathToImageCNIdentify()
    {
        string Path = "G:/WordImg/";
        if (Directory.Exists(Path))
        {
            string errorPath = "";
            InitWordItemList();
            List<string> pathList = new List<string>();
            try
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(Path);
                FileInfo[] fileInfos = directoryInfo.GetFiles("*.png", SearchOption.AllDirectories);
                int count = 0;
                int total = fileInfos.Length;
                foreach (FileInfo fileInfo in fileInfos)
                {
                    string path = fileInfo.FullName.Replace("\\", "/");
                    int AIndexOf = path.IndexOf("Assets/");
                    path = path.Substring(AIndexOf, path.Length - AIndexOf);
                    int LIndexOf = path.LastIndexOf("/");
                    string forwardPath = path.Substring(0, LIndexOf);
                    if (checkWhiteDirs.Contains(forwardPath) || checkBlackDirs.Contains(forwardPath))
                    {
                        count++;
                        EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                        continue;
                    }

                    if (!ImgWordItemList.ContainsKey(path))
                    {
                        pathList.Add(path);
                    }
                    count++;
                    EditorUtility.DisplayProgressBar("扫描图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                }

                count = 0;
                total = pathList.Count;
                foreach (string p in pathList)
                {
                    if (ImgWordItemList.ContainsKey(p))
                        continue;
                    errorPath = p;
                    string imgName = p.Substring(p.LastIndexOf("/") + 1, p.Length - p.LastIndexOf("/") - 1).Replace(".png", "");
                    string filePath = string.Format("{0}/../{1}", Application.dataPath, p);
                    //string imgWords = SendImgRequest(filePath);
                    //if (!string.IsNullOrEmpty(imgWords))
                    {
                        ImgWordItem imgWordItem = new ImgWordItem(0, imgName, p, "", "");
                        ImgWordItemList.Add(p, imgWordItem);
                        Debug.LogErrorFormat("路径：{0} 新增，文字内容：{1}", p, "");
                    }
                    Thread.Sleep(100);
                    count++;
                    EditorUtility.DisplayProgressBar("识别图片中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
                }

                SaveRecord();
                EditorUtility.ClearProgressBar();
                Debug.LogError("识别图片中的文字完毕");
            }
            catch (Exception ex)
            {
                SaveRecord();
                EditorUtility.ClearProgressBar();
                Debug.LogError(ex);
                Debug.LogErrorFormat("Error path:{0}", errorPath);
            }
        }
    }

    [MenuItem("自定义工具/策划专用/遍历UI文件夹进行图片中文识别")]
    static void ImageCNIdentifyByTxt()
    {
        List<ImgWordItem2> imgWordList = new List<ImgWordItem2>();
        string[] GUIDs = AssetDatabase.FindAssets("*", new string[] { "Assets/Game/Uis" });
        int count = 0;
        int total = GUIDs.Length;
        string errorpath = "";
        try
        {
            foreach (string guid in GUIDs)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                path = path.Replace("\\", "/");
                errorpath = path;
                if (path.EndsWith("word.txt") && !path.EndsWith(".meta"))
                {
                    int lastIndexOf = path.LastIndexOf("/");
                    string parentPath = path.Substring(0, lastIndexOf);
                    TextAsset textAsset = AssetDatabase.LoadAssetAtPath(path, typeof(TextAsset)) as TextAsset;
                    //Debug.LogErrorFormat("{0},  {1},  {2},  {3}", textAsset.text, pngName, parentPath, path.LastIndexOf("/"));
                    string[] lines = textAsset.text.Split(new string[] { "\r\n" }, StringSplitOptions.None);
                    foreach (string line in lines)
                    {
                        if (!string.IsNullOrEmpty(line))
                        {
                            string[] strs = line.Split('#');
                            if (strs.Length == 3)
                            {
                                ImgWordItem2 item = new ImgWordItem2(0, "", parentPath + strs[0], strs[2], strs[1]);
                                imgWordList.Add(item);
                            }
                            else
                            {
                                Debug.LogErrorFormat("path:{0}, 文本 :{1} 参数个数有误，请检查", path, line);
                            }
                        }
                    }
                }
                count++;
                EditorUtility.DisplayProgressBar("识别图片word中", string.Format("{0} / {1}", count, total), (float)count / (float)total);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            Debug.LogError("errorpath = " + errorpath);
        }

        if (imgWordList.Count > 0)
        {
            string excelPath = Application.dataPath + "/../AssetsCheck/ImgWordRecord2.csv";
            try
            {
                using (var file = File.Open(excelPath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read))
                {
                    using (var writeStream = new StreamWriter(file, Encoding.UTF8))
                    {
                        writeStream.WriteLine("图片路径,样式,文本内容");
                        int id = 1;
                        total = imgWordList.Count;
                        foreach (ImgWordItem2 item in imgWordList)
                        {
                            writeStream.WriteLine(string.Format("{0},{1},{2}", item.IMG_Path, item.IMG_Words, item.Word_Type));
                            EditorUtility.DisplayProgressBar("保存记录中", string.Format("{0} / {1}", id, total), (float)id / (float)total);
                            id++;
                        }
                        writeStream.Close();
                        writeStream.Dispose();
                    }
                    file.Close();
                    file.Dispose();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
                EditorUtility.ClearProgressBar();
            }
        }
        EditorUtility.ClearProgressBar();
    }
}

class ImgWordItem
{
    public int ID = 0;
    public string IMG_Name = "";
    public string IMG_Path = "";
    public string IMG_Words = "";
    public string IMG_ERROR = "";
    public ImgWordItem(int _ID, string _IMG_Name, string _IMG_Path, string _IMG_Words, string _IMG_ERROR)
    {
        this.ID = _ID;
        this.IMG_Name = _IMG_Name;
        this.IMG_Path = _IMG_Path;
        this.IMG_Words = _IMG_Words;
        this.IMG_ERROR = _IMG_ERROR;
    }
}

class ImgWordJson
{
    public string words { get; set; }
}

class ImgWordItem2
{
    public int ID = 0;
    public string IMG_Name = "";
    public string IMG_Path = "";
    public string IMG_Words = "";
    public string Word_Type = "";
    public ImgWordItem2(int _ID, string _IMG_Name, string _IMG_Path, string _IMG_Words, string _Word_Type)
    {
        this.ID = _ID;
        this.IMG_Name = _IMG_Name;
        this.IMG_Path = _IMG_Path;
        this.IMG_Words = _IMG_Words;
        this.Word_Type = _Word_Type;
    }
}