using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using MobileCompatibility;

/// <summary>
/// 移动端SkinnedMeshRenderer兼容性检测工具
/// 基于Odin Inspector的可视化检测和修复工具
/// </summary>
public class MobileSkinnedMeshDetector : OdinEditorWindow
{
    [MenuItem("Tools/移动端工具/SkinnedMesh兼容性检测器", priority = 1)]
    private static void OpenWindow()
    {
        GetWindow<MobileSkinnedMeshDetector>("移动端SkinnedMesh检测器").Show();
    }

    #region 检测设置

    [BoxGroup("检测设置"), LabelText("包含非活动对象")]
    [InfoBox("启用此选项将检测场景中所有的SkinnedMeshRenderer，包括被禁用的GameObject")]
    public bool includeInactiveObjects = false;

    [BoxGroup("检测设置"), LabelText("自动修复问题")]
    [InfoBox("启用后将在检测完成后自动尝试修复部分问题")]
    public bool autoFixIssues = false;

    [BoxGroup("检测设置"), LabelText("详细日志输出")]
    public bool enableDetailedLogging = true;

    #endregion

    #region 检测操作

    [BoxGroup("检测操作"), Button("开始检测当前场景", ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1f)]
    private void StartSceneDetection()
    {
        if (EditorApplication.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请在非播放模式下进行检测", "确定");
            return;
        }

        EditorUtility.DisplayProgressBar("移动端兼容性检测", "正在扫描场景...", 0f);
        
        try
        {
            currentReport = MobileCompatibilityChecker.RunFullCompatibilityCheck(includeInactiveObjects);
            
            if (enableDetailedLogging)
            {
                LogDetectionResults();
            }

            if (autoFixIssues && currentReport.Statistics.ErrorCount > 0)
            {
                ShowAutoFixDialog();
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[MobileSkinnedMeshDetector] 检测过程中发生错误: {e.Message}");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
    }

    [BoxGroup("检测操作"), Button("检测所有场景", ButtonSizes.Medium), GUIColor(0.8f, 0.6f, 0.4f)]
    private void StartAllScenesDetection()
    {
        if (EditorApplication.isPlaying)
        {
            EditorUtility.DisplayDialog("警告", "请在非播放模式下进行检测", "确定");
            return;
        }

        if (!EditorUtility.DisplayDialog("确认", "这将检测所有场景，可能需要较长时间。是否继续？", "是", "否"))
            return;

        allScenesReports = new List<SceneDetectionResult>();
        var scenePaths = EditorBuildSettings.scenes.Where(s => s.enabled).Select(s => s.path).ToArray();
        
        var currentScene = EditorSceneManager.GetActiveScene().path;
        
        try
        {
            for (int i = 0; i < scenePaths.Length; i++)
            {
                var scenePath = scenePaths[i];
                if (EditorUtility.DisplayCancelableProgressBar($"检测场景 ({i + 1}/{scenePaths.Length})", 
                    $"正在检测: {Path.GetFileNameWithoutExtension(scenePath)}", (float)i / scenePaths.Length))
                {
                    break;
                }

                EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);
                var report = MobileCompatibilityChecker.RunFullCompatibilityCheck(includeInactiveObjects);
                
                allScenesReports.Add(new SceneDetectionResult
                {
                    SceneName = Path.GetFileNameWithoutExtension(scenePath),
                    ScenePath = scenePath,
                    Report = report
                });
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            
            // 恢复原场景
            if (!string.IsNullOrEmpty(currentScene))
            {
                EditorSceneManager.OpenScene(currentScene, OpenSceneMode.Single);
            }
        }
    }

    [BoxGroup("检测操作"), Button("清除检测结果", ButtonSizes.Medium)]
    private void ClearResults()
    {
        currentReport = null;
        allScenesReports?.Clear();
    }

    #endregion

    #region 检测结果显示

    [ShowInInspector, ReadOnly, PropertyOrder(100)]
    [ShowIf("HasCurrentReport")]
    [BoxGroup("当前场景检测结果"), LabelText("检测报告")]
    private MobileCompatibilityReport currentReport;

    [ShowInInspector, ReadOnly, PropertyOrder(101)]
    [ShowIf("HasAllScenesReports")]
    [BoxGroup("所有场景检测结果"), LabelText("场景检测结果")]
    private List<SceneDetectionResult> allScenesReports;

    private bool HasCurrentReport => currentReport != null;
    private bool HasAllScenesReports => allScenesReports != null && allScenesReports.Count > 0;

    #endregion

    #region 问题修复功能

    [BoxGroup("修复功能"), Button("修复选中的问题", ButtonSizes.Medium), GUIColor(0.8f, 1f, 0.4f)]
    [EnableIf("HasFixableIssues")]
    private void FixSelectedIssues()
    {
        if (currentReport == null) return;

        var fixableIssues = currentReport.ErrorIssues.Where(CanAutoFix).ToList();
        if (fixableIssues.Count == 0)
        {
            EditorUtility.DisplayDialog("信息", "当前没有可自动修复的问题", "确定");
            return;
        }

        if (EditorUtility.DisplayDialog("确认修复", 
            $"将尝试自动修复 {fixableIssues.Count} 个问题。是否继续？", "是", "否"))
        {
            ApplyAutoFixes(fixableIssues);
        }
    }

    [BoxGroup("修复功能"), Button("打开原始修复工具", ButtonSizes.Medium)]
    private void OpenOriginalFixerTool()
    {
        var fixer = FindObjectOfType<MobileSkinnedMeshFixer>();
        if (fixer == null)
        {
            var go = new GameObject("MobileSkinnedMeshFixer");
            fixer = go.AddComponent<MobileSkinnedMeshFixer>();
            Undo.RegisterCreatedObjectUndo(go, "Create MobileSkinnedMeshFixer");
        }
        
        Selection.activeObject = fixer;
        EditorGUIUtility.PingObject(fixer);
    }

    private bool HasFixableIssues => currentReport?.ErrorIssues?.Any(CanAutoFix) == true;

    #endregion

    #region 报告导出功能

    [BoxGroup("报告导出"), Button("导出详细报告", ButtonSizes.Medium), GUIColor(1f, 0.8f, 0.4f)]
    [EnableIf("HasCurrentReport")]
    private void ExportDetailedReport()
    {
        if (currentReport == null) return;

        var path = EditorUtility.SaveFilePanel("导出检测报告", "", 
            $"移动端兼容性报告_{DateTime.Now:yyyyMMdd_HHmmss}", "txt");
        
        if (!string.IsNullOrEmpty(path))
        {
            ExportReportToFile(currentReport, path);
            EditorUtility.DisplayDialog("导出完成", $"报告已导出到: {path}", "确定");
        }
    }

    [BoxGroup("报告导出"), Button("导出CSV格式", ButtonSizes.Medium)]
    [EnableIf("HasCurrentReport")]
    private void ExportCSVReport()
    {
        if (currentReport == null) return;

        var path = EditorUtility.SaveFilePanel("导出CSV报告", "", 
            $"移动端兼容性报告_{DateTime.Now:yyyyMMdd_HHmmss}", "csv");
        
        if (!string.IsNullOrEmpty(path))
        {
            ExportReportToCSV(currentReport, path);
            EditorUtility.DisplayDialog("导出完成", $"CSV报告已导出到: {path}", "确定");
        }
    }

    #endregion

    #region 辅助数据结构

    [Serializable]
    public class SceneDetectionResult
    {
        [LabelText("场景名称"), ReadOnly]
        public string SceneName;
        
        [LabelText("场景路径"), ReadOnly]
        public string ScenePath;
        
        [LabelText("检测报告"), ReadOnly]
        public MobileCompatibilityReport Report;
        
        [LabelText("问题摘要"), ReadOnly]
        public string IssueSummary => Report?.GetSummaryText() ?? "未检测";
    }

    #endregion

    #region 内部方法

    private void LogDetectionResults()
    {
        if (currentReport == null) return;

        Debug.Log($"[MobileSkinnedMeshDetector] {currentReport.GetSummaryText()}");
        
        foreach (var issue in currentReport.AllIssues.OrderByDescending(i => i.Severity))
        {
            var message = $"[{issue.Level}] {issue.Description}\n修复建议: {issue.FixSuggestion}";
            
            switch (issue.Level)
            {
                case IssueLevel.Error:
                    Debug.LogError(message, issue.AffectedObject);
                    break;
                case IssueLevel.Warning:
                    Debug.LogWarning(message, issue.AffectedObject);
                    break;
                case IssueLevel.Info:
                default:
                    Debug.Log(message, issue.AffectedObject);
                    break;
            }
        }
    }

    private void ShowAutoFixDialog()
    {
        var fixableCount = currentReport.ErrorIssues.Count(CanAutoFix);
        if (fixableCount > 0 && EditorUtility.DisplayDialog("自动修复", 
            $"检测到 {fixableCount} 个可自动修复的错误。是否现在修复？", "是", "否"))
        {
            ApplyAutoFixes(currentReport.ErrorIssues.Where(CanAutoFix));
        }
    }

    private bool CanAutoFix(CompatibilityIssue issue)
    {
        // 判断哪些问题可以自动修复
        switch (issue.Type)
        {
            case IssueType.BoneSystem:
                return issue.Description.Contains("Quality Settings") || 
                       issue.Description.Contains("updateWhenOffscreen");
            case IssueType.RenderSettings:
                return issue.Description.Contains("Motion Vectors") || 
                       issue.Description.Contains("TwoSided");
            case IssueType.MaterialShader:
                return issue.Description.Contains("GPU Skinning");
            default:
                return false;
        }
    }

    private void ApplyAutoFixes(IEnumerable<CompatibilityIssue> issues)
    {
        int fixedCount = 0;
        
        foreach (var issue in issues)
        {
            try
            {
                if (ApplySingleFix(issue))
                {
                    fixedCount++;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"修复问题时发生错误: {e.Message}", issue.AffectedObject);
            }
        }

        EditorUtility.DisplayDialog("修复完成", $"成功修复了 {fixedCount} 个问题", "确定");
        
        // 重新检测以更新结果
        if (fixedCount > 0)
        {
            currentReport = MobileCompatibilityChecker.RunFullCompatibilityCheck(includeInactiveObjects);
        }
    }

    private bool ApplySingleFix(CompatibilityIssue issue)
    {
        var renderer = issue.AffectedComponent as SkinnedMeshRenderer;
        
        switch (issue.Type)
        {
            case IssueType.BoneSystem:
                if (issue.Description.Contains("Quality Settings"))
                {
                    QualitySettings.skinWeights = SkinWeights.FourBones;
                    return true;
                }
                if (issue.Description.Contains("updateWhenOffscreen") && renderer != null)
                {
                    Undo.RecordObject(renderer, "Fix updateWhenOffscreen");
                    renderer.updateWhenOffscreen = false;
                    return true;
                }
                break;
                
            case IssueType.RenderSettings:
                if (renderer != null)
                {
                    Undo.RecordObject(renderer, "Fix Render Settings");
                    if (issue.Description.Contains("Motion Vectors"))
                    {
                        renderer.skinnedMotionVectors = false;
                        return true;
                    }
                    if (issue.Description.Contains("TwoSided"))
                    {
                        renderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.On;
                        return true;
                    }
                }
                break;
                
            case IssueType.MaterialShader:
                if (renderer != null && issue.Description.Contains("GPU Skinning"))
                {
                    var materials = renderer.materials;
                    for (int i = 0; i < materials.Length; i++)
                    {
                        if (materials[i] != null)
                        {
                            Undo.RecordObject(materials[i], "Disable GPU Skinning");
                            materials[i].DisableKeyword("_ENABLE_GPU_SKINNING");
                        }
                    }
                    return true;
                }
                break;
        }
        
        return false;
    }

    private void ExportReportToFile(MobileCompatibilityReport report, string filePath)
    {
        using (var writer = new StreamWriter(filePath))
        {
            writer.WriteLine("移动端SkinnedMeshRenderer兼容性检测报告");
            writer.WriteLine("=" + new string('=', 50));
            writer.WriteLine($"检测时间: {report.Statistics.ScanTime}");
            writer.WriteLine($"扫描对象数量: {report.Statistics.TotalScannedObjects}");
            writer.WriteLine($"SkinnedMeshRenderer数量: {report.Statistics.SkinnedMeshRendererCount}");
            writer.WriteLine($"检测耗时: {report.Statistics.ScanDuration}ms");
            writer.WriteLine();
            
            writer.WriteLine(report.GetSummaryText());
            writer.WriteLine();
            
            if (report.ErrorIssues.Count > 0)
            {
                writer.WriteLine("错误问题:");
                writer.WriteLine("-" + new string('-', 30));
                WriteIssueDetails(writer, report.ErrorIssues);
                writer.WriteLine();
            }
            
            if (report.WarningIssues.Count > 0)
            {
                writer.WriteLine("警告问题:");
                writer.WriteLine("-" + new string('-', 30));
                WriteIssueDetails(writer, report.WarningIssues);
                writer.WriteLine();
            }
            
            if (report.InfoIssues.Count > 0)
            {
                writer.WriteLine("信息提示:");
                writer.WriteLine("-" + new string('-', 30));
                WriteIssueDetails(writer, report.InfoIssues);
            }
        }
    }

    private void WriteIssueDetails(StreamWriter writer, List<CompatibilityIssue> issues)
    {
        foreach (var issue in issues.OrderByDescending(i => i.Severity))
        {
            writer.WriteLine($"对象: {GetGameObjectPath(issue.AffectedObject)}");
            writer.WriteLine($"类型: {issue.Type}");
            writer.WriteLine($"描述: {issue.Description}");
            writer.WriteLine($"修复建议: {issue.FixSuggestion}");
            writer.WriteLine($"技术详情: {issue.TechnicalDetails}");
            writer.WriteLine($"严重程度: {issue.Severity}/10");
            writer.WriteLine();
        }
    }

    private void ExportReportToCSV(MobileCompatibilityReport report, string filePath)
    {
        using (var writer = new StreamWriter(filePath))
        {
            // CSV 头部
            writer.WriteLine("对象路径,问题等级,问题类型,问题描述,修复建议,技术详情,严重程度");
            
            foreach (var issue in report.AllIssues.OrderByDescending(i => i.Severity))
            {
                var objectPath = GetGameObjectPath(issue.AffectedObject);
                writer.WriteLine($"\"{objectPath}\",\"{issue.Level}\",\"{issue.Type}\",\"{issue.Description}\",\"{issue.FixSuggestion}\",\"{issue.TechnicalDetails}\",{issue.Severity}");
            }
        }
    }

    private string GetGameObjectPath(GameObject obj)
    {
        if (obj == null) return "null";
        
        var path = obj.name;
        var parent = obj.transform.parent;
        
        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }
        
        return path;
    }

    #endregion

    #region Unity 回调

    protected override void OnEnable()
    {
        base.OnEnable();
        titleContent = new GUIContent("移动端SkinnedMesh检测器");
    }

    #endregion
} 