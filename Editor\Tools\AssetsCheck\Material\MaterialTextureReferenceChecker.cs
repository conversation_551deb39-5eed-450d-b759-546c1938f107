﻿using UnityEngine;
using UnityEditor;
using System.Text;
using System.Collections.Generic;
using AssetsCheck;
using System.IO;

class MaterialTextureReferenceChecker : BaseChecker
{

    public override string GetErrorDesc()
    {
        return "";
    }

    private static string[] MaterialTextureKeywords = new string[] { "_MainTex", "_NormalMap", "_MetallicGlossMap" };

    [MenuItem("Assets/策划专用/修复材质球跨引用问题(查找选中材质球同目录下的贴图进行引用)")]
    public static void SelectMatToOperate()
    {
        if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID).Replace("\\", "/");

                    if (AssetDatabase.IsValidFolder(path))
                    {
                        SelectFolderToOperate(new string[] { path });

                        EditorUtility.DisplayProgressBar("正在处理材质球..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                    }
                    else if (path.EndsWith(".mat"))
                    {
                        CheckAndResetMaterialTextureReference(path);
                    }
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject).Replace("\\", "/");
                if (AssetDatabase.IsValidFolder(path))
                {
                    SelectFolderToOperate(new string[] { path });
                }
                else if (path.EndsWith(".mat"))
                {
                    CheckAndResetMaterialTextureReference(path);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void SelectFolderToOperate(string[] paths)
    {
        string[] guids = AssetDatabase.FindAssets("t:material", paths);
        int index = 0;
        int count = guids.Length;
        foreach (string guid in guids)
        {
            string asset = AssetDatabase.GUIDToAssetPath(guid);
            if (asset.EndsWith(".mat"))
            {
                CheckAndResetMaterialTextureReference(asset);
            }

            EditorUtility.DisplayProgressBar("正在处理材质球..."
                        , string.Format("{0} / {1}", index + 1, count)
                        , (float)(index + 1) / (float)count);
        }
    }

    private static void CheckAndResetMaterialTextureReference(string matPath)
    {
        Material material = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
        if (material != null && material.shader != null && material.shader.name == "Srp/Standard/SrpRole_PbrCf")
        {
            bool isMatDirty = false;

            int lastIndexOf = matPath.LastIndexOf("/");
            string root = matPath.Substring(0, lastIndexOf);
            string matName = matPath.Substring(lastIndexOf + 1, matPath.Length - lastIndexOf - 1).Replace(".mat", "");
            string[] guids = AssetDatabase.FindAssets("t:texture", new string[] { root });

            for (int i = 0; i < MaterialTextureKeywords.Length; i++)
            {
                Texture originMainTex = material.GetTexture(MaterialTextureKeywords[i]);
                if (originMainTex == null)
                {
                    if (i == 0)
                    {
                        Debug.LogErrorFormat("修复失败，材质球路径：{0} 找不到该材质球的主贴图，请检查", matPath);
                    }
                    continue;
                }
                Texture newMainTex = AssetDatabase.LoadAssetAtPath<Texture>(string.Format("{0}/{1}.tga", root, originMainTex.name));
                if (newMainTex == null)
                {
                    foreach (string guid in guids)
                    {
                        string asset = AssetDatabase.GUIDToAssetPath(guid);
                        //_MainTex
                        if (i == 0
                            && TextureAssetImporter.HasKeyWordInName(asset, "n")
                            && TextureAssetImporter.HasKeyWordInName(asset, "nor")
                            && TextureAssetImporter.HasKeyWordInName(asset, "normal")
                            && TextureAssetImporter.HasKeyWordInName(asset, "t")
                            && TextureAssetImporter.HasKeyWordInName(asset, "pbr")
                            && TextureAssetImporter.HasKeyWordInName(asset, "g"))
                        {
                            continue;
                        }
                        //_NormalMap
                        else if (i == 1 && !TextureAssetImporter.HasKeyWordInName(asset, "n")
                            && !TextureAssetImporter.HasKeyWordInName(asset, "nor")
                            && !TextureAssetImporter.HasKeyWordInName(asset, "normal"))
                        {
                            continue;
                        }
                        //_MetallicGlossMap
                        else if (i == 2 && !TextureAssetImporter.HasKeyWordInName(asset, "pbr") && !TextureAssetImporter.HasKeyWordInName(asset, "g"))
                        {
                            continue;
                        }

                        newMainTex = AssetDatabase.LoadAssetAtPath<Texture>(asset);
                        if (null != newMainTex)
                        {
                            break;
                        }
                    }
                    if (i == 0 && newMainTex == null)
                    {
                        Debug.LogErrorFormat("修复失败，材质球路径：{0} 找不到该材质球的主贴图，请检查", matPath);
                    }
                }

                if (newMainTex != null && newMainTex != originMainTex)
                {
                    material.SetTexture(MaterialTextureKeywords[i], newMainTex);
                    isMatDirty = true;
                }
            }

            if (isMatDirty)
            {
                EditorUtility.SetDirty(material);
            }
        }
    }
}