﻿using UnityEngine;
using UnityEditor;

class GenericAssetImporter : AssetPostprocessor
{
    static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
    {
        foreach (var importedAsset in importedAssets)
        {
            if (importedAsset.Contains(ABShaderMarkRule.fix))
                continue;

            RenameImporter(importedAsset);
            AssetBundleMarkRule.MarkAssetBundle(importedAsset);
        }

        foreach (var movedAsset in movedAssets)
        {
            if (movedAsset.Contains(ABShaderMarkRule.fix))
                continue;

            RenameImporter(movedAsset);
            AssetBundleMarkRule.MarkAssetBundle(movedAsset);
        }

        SpriteAtlasAssetImporter.OnPostprocessAllAssets(importedAssets, deletedAssets, movedAssets, movedFromAssetPaths);
    }

    //检测导入资源是否带有"--","———"
    static void RenameImporter(string importedAsset)
    {
        if (!importedAsset.StartsWith(AssetBundleMarkRule.BaseDir)
            || importedAsset.StartsWith(AssetBundleMarkRule.ScenesDir)
            || importedAsset.StartsWith(AssetBundleMarkRule.ArtFontDir)
            || importedAsset.EndsWith(".cs"))    //脚本不用添加abName
        {
            return;
        }

        string name = importedAsset.Substring(importedAsset.LastIndexOf("/") + 1);

        name = name.Replace("-", "_");
        name = name.Replace("——", "_");
        name = name.Replace("—", "_");
        name = name.Replace("+", "_");
        name = name.Replace("*", "_");
        name = name.Replace(" ", "");

        AssetDatabase.RenameAsset(importedAsset, name);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
}
