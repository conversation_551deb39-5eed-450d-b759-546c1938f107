﻿using UnityEngine;
using UnityEditor;
using Nirvana;
using System.Text;
using System.IO;
using Game;
using System.Collections.Generic;
using System.Linq;

namespace AssetsCheck
{
    class ActorRenderChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/Model/Weapon", };

        override public string GetErrorDesc()
        {
            return string.Format("Actor上的渲染效率有问题");
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                bool flag = false;
                CheckItem checkItem = new CheckItem();
                checkItem.asset = path;

                // this.CheckUnuseComponent(gameobj, ref checkItem);
                // this.CheckQualityActive(gameobj, ref checkItem);
                // this.CheckRenderPassInMainRender(gameobj, ref checkItem);
                this.CheckNoActorRender(gameobj, ref checkItem);
                // if (checkItem.hasUnUseComponent || checkItem.invalidQualityControlActive || checkItem.invalidRenderPass)
                // {
                //     this.outputList.Add(checkItem);
                // }
                if (checkItem.noActorRender)
                {
                    this.outputList.Add(checkItem);
                }
            }
        }

        private void CheckNoActorRender(GameObject gameobj, ref CheckItem checkItem)
        {
            // 检查预制体名字是否全为数字
            string prefabName = gameobj.name;
            bool isAllDigits = !string.IsNullOrEmpty(prefabName) && prefabName.All(char.IsDigit);
            
            // 只有当预制体名字全为数字时，才执行检查逻辑
            if (isAllDigits)
            {
                if(null == gameobj.GetComponent<ActorRender>())
                {
                    checkItem.noActorRender = true;
                }

                if(null == gameobj.GetComponent<Animator>())
                {
                    checkItem.noActorRender = true;
                }
            }
        }

        private void CheckUnuseComponent(GameObject gameobj, ref CheckItem checkItem)
        {
            if (null != gameobj.GetComponentInChildren<NirvanaRenderer>())
            {
                checkItem.hasUnUseComponent = true;
            }

            if (null != gameobj.GetComponentInChildren<Camera>())
            {
                checkItem.hasUnUseComponent = true;
            }

            if (null != gameobj.GetComponentInChildren<LimitSceneEffects>())
            {
                checkItem.hasUnUseComponent = true;
            }

            if (null != gameobj.GetComponentInChildren<ActorFadeout>())
            {
                checkItem.hasUnUseComponent = true;
            }

            if (null != gameobj.GetComponentInChildren<SimpleShadow>())
            {
                checkItem.hasUnUseComponent = true;
            }
        }

        private void CheckQualityActive(GameObject gameobj, ref CheckItem checkItem)
        {
            if (null != gameobj.GetComponentInChildren<GameObjectAttach>())
            {
                QualityControlActive qualityControl = gameobj.GetComponent<QualityControlActive>();
                if (null == qualityControl || qualityControl.HasInvalidControl())
                {
                    checkItem.invalidQualityControlActive = true;
                }
            }
        }

        private void CheckRenderPassInMainRender(GameObject gameobj, ref CheckItem checkItem)
        {
            if (!this.IsActorGameObj(gameobj))
            {
                return;
            }

            SkinnedMeshRenderer[] renderers = gameobj.GetComponentsInChildren<SkinnedMeshRenderer>();
            foreach (var render in renderers)
            {
                if (render.sharedMaterials.Length > 2)
                {
                    checkItem.invalidRenderPass = true;
                    return;
                }

                foreach (var material in render.sharedMaterials)
                {
                    if (null == material || !material.HasProperty("_MaterialStyle"))
                    {
                        checkItem.invalidRenderPass = true;
                        return;
                    }

                    // 主mainRender只需有一个pass
                    if (material.GetFloat("_MaterialStyle") == 1 && render.sharedMaterials.Length > 1)
                    {
                        checkItem.invalidRenderPass = true;
                        return;
                    }
                }
            }
        }

        override protected void OnFix(string[] lines)
        {
            if (lines.Length <= 0)
            {
                Debug.LogError("当前没有需要处理的 ActorRender, 请先检查！");
                return;
            }

            Debug.LogError("当前处理的 ActorRender, 不能一键修复！");
            return;

            int count = 0;
            int total = lines.Length;
            foreach (string line in lines)
            {
                string[] str = line.Split(',');
                string path = str[0].Replace("asset:", "");
                GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                GameObject gameobj = GameObject.Instantiate(prefab);

                if (null == gameobj)
                {
                    continue;
                }

                this.DestroyUnuseComponent(gameobj);
                this.FixActorFadeOut(path, gameobj);
                this.FixActorRender(path, gameobj);

                PrefabUtility.SaveAsPrefabAsset(gameobj, path);
                GameObject.DestroyImmediate(gameobj, true);
                count++;
                EditorUtility.DisplayProgressBar("正在修复ActorRender 物体...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        private void DestroyUnuseComponent(GameObject gameobj)
        {
            NirvanaRenderer[] components = gameobj.GetComponentsInChildren<NirvanaRenderer>(true);
            for (int j = 0; j < components.Length; j++)
            {
                GameObject.DestroyImmediate(components[j], true);
            }

            LimitSceneEffects[] limitSceneEffects = gameobj.GetComponentsInChildren<LimitSceneEffects>(true);
            for (int j = 0; j < limitSceneEffects.Length; j++)
            {
                GameObject.DestroyImmediate(limitSceneEffects[j], true);
            }

            Camera[] cameras = gameobj.GetComponentsInChildren<Camera>(true);
            for (int j = 0; j < cameras.Length; j++)
            {
                GameObject.DestroyImmediate(cameras[j].gameObject, true);
            }

            ActorFadeout[] actorFadeouts = gameobj.GetComponentsInChildren<ActorFadeout>(true);
            for (int j = 0; j < actorFadeouts.Length; j++)
            {
                GameObject.DestroyImmediate(actorFadeouts[j], true);
            }

            SimpleShadow[] simpleShadows = gameobj.GetComponentsInChildren<SimpleShadow>(true);
            for (int j = 0; j < simpleShadows.Length; j++)
            {
                GameObject.DestroyImmediate(simpleShadows[j], true);
            }
        }

        private void FixActorFadeOut(string path, GameObject gameobj)
        {
            if (path.IndexOf("/Mount/") <= 0)
            {
                return;
            }

            if (!this.IsActorGameObj(gameobj))
            {
                return;
            }

            ActorFadeout actorFadeOut = gameobj.GetOrAddComponent<ActorFadeout>();
            actorFadeOut.AutoFetch();
        }

        private void FixActorRender(string path, GameObject gameobj)
        {
            if (!this.IsActorGameObj(gameobj))
            {
                return;
            }

            if (null == gameobj.GetComponent<ActorRender>())
            {
                Debug.LogFormat("not found actorrender, so create, {0}", path);
            }
            ActorRender actorRender = gameobj.GetOrAddComponent<ActorRender>();
            actorRender.AutoFetch();
        }

        private bool IsActorGameObj(GameObject gameobj)
        {
            if (null == gameobj.GetComponent<ActorAttachment>()
              && null == gameobj.GetComponent<AttachObject>()
               && null == gameobj.GetComponent<AttachSkinObject>()
               && null == gameobj.GetComponent<AnimatorOptimizer>()
                  && null == gameobj.GetComponent<Animator>())
            {
                return false;
            }

            return true;
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public bool hasUnUseComponent;
            public bool invalidQualityControlActive;
            public bool invalidRenderPass;
            public bool noActorRender;
            public string MainKey
            {
                // get { return string.Format("{0}, hasUnUseComponent = {1}, invalidQualityControlActive = {2}, invalidRenderPass = {3}", 
                //     asset, hasUnUseComponent, invalidQualityControlActive, invalidRenderPass); }
                get { return string.Format("{0}, noActorRender = {1}", asset, noActorRender); }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(this.asset);
                return builder;
            }
        }

        ///------------------------- 检测Actor是否用了MeshRenderer ---------------------------
        ///路径Assets/Game/Actor下所有模型都要用SkinnedMeshRenderer

        static List<string> MeshRendererCheckOutPut = new List<string>();
        [MenuItem("Assets/策划专用/检测MeshRenderer")]
        public static void CheckActorUseMeshRenderer()
        {
            MeshRendererCheckOutPut.Clear();
            string[] checkDirs = null;
            if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
                {
                    int totalCount = Selection.instanceIDs.Length;
                    for (int i = 0; i < totalCount; i++)
                    {
                        int instanceID = Selection.instanceIDs[i];
                        string path = AssetDatabase.GetAssetPath(instanceID);

                        if (!path.StartsWith("Assets/Game/Actor"))
                        {
                            continue;
                        }
                        if (AssetDatabase.IsValidFolder(path))
                        {
                            checkDirs = new string[] { path };
                            CheckActorUseMeshRendererInPaths(checkDirs);
                        }
                        else
                        {
                            CheckActorRenderer(Selection.gameObjects[i]);
                        }

                        EditorUtility.DisplayProgressBar("正在扫描..."
                            , string.Format("{0} / {1}", i + 1, totalCount)
                            , (float)(i + 1) / (float)totalCount);
                    }
                }
                else if (null != Selection.activeObject)
                {
                    string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        CheckActorUseMeshRendererInPaths(checkDirs);
                    }
                    else
                    {
                        CheckActorRenderer(Selection.activeGameObject);
                    }
                }

                EditorUtility.ClearProgressBar();
                AssetDatabase.Refresh();
                AssetDatabase.SaveAssets();
            }


            //输出log
            if (MeshRendererCheckOutPut.Count > 0)
            {
                StringBuilder builder = new StringBuilder();
                builder.Append("--------- 以下是挂载了MeshRenderer的模型，请检查 \n");
                foreach (string log in MeshRendererCheckOutPut)
                {
                    builder.AppendLine(log);
                }
                MeshRendererCheckOutPut.Clear();
                File.WriteAllText(Path.Combine(AssetsCheckConfig.OutputDir, "ActorMeshRendererCheck.txt"), builder.ToString());
            }
            else
            {
                Debug.LogError("检测Actor是否用了MeshRenderer，检测结果无发现异常");
            }
        }

        private static void CheckActorUseMeshRendererInPaths(string[] checkDirs)
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            int totalCount = guids.Length;
            int cutIndex = 0;
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (null == gameobj)
                {
                    continue;
                }

                ActorRender renderers = gameobj.GetComponentInChildren<ActorRender>();
                if (renderers != null)
                {
                    MeshRenderer[] meshRender = renderers.GetComponentsInChildren<MeshRenderer>();
                    if (meshRender != null && meshRender.Length > 0)
                    {
                        MeshRendererCheckOutPut.Add(path);
                    }
                }

                cutIndex++;
                EditorUtility.DisplayProgressBar("正在扫描..."
                            , string.Format("{0} / {1}", cutIndex, totalCount)
                            , (float)(cutIndex) / totalCount);
            }
        }

        private static void CheckActorRenderer(Object asset)
        {
            GameObject gameobj = asset as GameObject;
            ActorRender renderers = gameobj.GetComponentInChildren<ActorRender>();
            if (renderers != null)
            {
                MeshRenderer[] meshRender = renderers.GetComponentsInChildren<MeshRenderer>();
                if (meshRender != null && meshRender.Length > 0)
                {
                    string path = AssetDatabase.GetAssetPath(asset);
                    MeshRendererCheckOutPut.Add(path);
                }
            }
        }
    }
}
