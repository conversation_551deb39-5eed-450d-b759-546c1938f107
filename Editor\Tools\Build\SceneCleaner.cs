﻿using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public class SceneCleaner
{
    public static void ClearScene()
    {
        string[] checkDirs = { "Assets/Game/Scenes/Map" };
        string[] guids = AssetDatabase.FindAssets("t:scene", checkDirs);

        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.EndsWith("_Main.unity") || path.EndsWith("_Detail.unity"))
            {
                Scene scene = EditorSceneManager.OpenScene(path);
                GameObject[] root_objs = scene.GetRootGameObjects();
                GameObject rootObj = null;
                for (int i = 0; i < root_objs.Length; i++)
                {
                    if (root_objs[i].name == "Main")
                    {
                        rootObj = root_objs[i];
                    }
                }

                if (null == rootObj)
                {
                    Debug.LogErrorFormat("不存在Main节点, {0}", path);
                    continue;
                }

                if (!rootObj.activeInHierarchy)
                {
                    continue;
                }

                SceneOptimize optimize = rootObj.GetComponent<SceneOptimize>();
                if (null == optimize)
                {
                    Debug.LogErrorFormat("不存在SceneOptimize {0}", scene.name);
                    continue;
                }

                optimize.QuickOptimize();
                EditorSceneManager.MarkSceneDirty(scene);
                EditorSceneManager.SaveScene(scene);
                EditorSceneManager.CloseScene(scene, true);
            }
        }

        AssetDatabase.SaveAssets();
    }
}
