﻿using Nirvana.Editor;
using System;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

[CustomEditor(typeof(BuildConfigNew))]
internal sealed class BuildConfigNewEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.serializedObject.Update();
        GUILayout.Label("Basic", GUITheme.ListItemHeaderStyle, Array.Empty<GUILayoutOption>());
        GUILayoutEx.BeginContents();
        this.DrawBasicConfigure();
        GUILayoutEx.EndContents();
        EditorGUILayout.Space();
        GUILayout.Label("Device", GUITheme.ListItemHeaderStyle, Array.Empty<GUILayoutOption>());
        GUILayoutEx.BeginContents();
        this.DrawDeviceConfigure();
        GUILayoutEx.EndContents();
        base.serializedObject.ApplyModifiedProperties();
    }

    private void DrawBasicConfigure()
    {
        EditorGUILayout.PropertyField(this.companyName, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.productName, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.productNameI18N, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.version, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.defineSymbols, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.scenes, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.splashScreen, Array.Empty<GUILayoutOption>());
    }

    private void DrawDeviceConfigure()
    {
        EditorGUILayout.PropertyField(this.targetDevice, Array.Empty<GUILayoutOption>());
        switch (this.targetDevice.enumValueIndex)
        {
            case 0:
                GUILayoutEx.BeginContents();
                this.DrawIOSConfigure();
                GUILayoutEx.EndContents();
                break;
            case 1:
                GUILayoutEx.BeginContents();
                this.DrawAndroidConfig();
                GUILayoutEx.EndContents();
                break;
            case 2:
            case 3:
                GUILayoutEx.BeginContents();
                this.DrawDesktopConfig();
                GUILayoutEx.EndContents();
                break;
        }
    }

    private void DrawIOSConfigure()
    {
        EditorGUILayout.PropertyField(this.iOSApplicationIdentifier, new GUIContent("Application Identifier"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSProvisioningProfile, new GUIContent("Provisioning Profile"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSCodeSignIdentity, new GUIContent("Code Sign Identity"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSBuildNumber, new GUIContent("Build Number"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSApplicationDisplayName, new GUIContent("Application Display Name"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSEnableAutomaticSigning, new GUIContent("Enable Automatic Signing"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSDeveloperTeamID, new GUIContent("Developer Team ID"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSEnableBitCode, new GUIContent("Enable BitCode"), Array.Empty<GUILayoutOption>());
        this.iOSFrameworkList.DoLayoutList();
        EditorGUILayout.PropertyField(this.iOSIcon180, new GUIContent("Icon 180x180"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon167, new GUIContent("Icon 167x167"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon152, new GUIContent("Icon 152x152"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon144, new GUIContent("Icon 144x144"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon120, new GUIContent("Icon 120x120"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon114, new GUIContent("Icon 114x114"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon76, new GUIContent("Icon 76x76"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon72, new GUIContent("Icon 72x72"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSIcon57, new GUIContent("Icon 57x57"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSpotlightIcon120, new GUIContent("Spotlight Icon 120x120"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSpotlightIcon80, new GUIContent("Spotlight Icon 80x80"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSpotlightIcon40, new GUIContent("Spotlight Icon 40x40"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSettingsIcon87, new GUIContent("Settings Icon 87x87"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSettingsIcon58, new GUIContent("Settings Icon 58x58"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSSettingsIcon29, new GUIContent("Settings Icon 29x29"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSNotificationIcon60, new GUIContent("Notification Icon 60x60"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSNotificationIcon40, new GUIContent("Notification Icon 40x40"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSNotificationIcon20, new GUIContent("Notification Icon 20x20"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.iOSAppstoreIcon1024, new GUIContent("Appstore Icon 1024x1024"), Array.Empty<GUILayoutOption>());
    }

    private void DrawAndroidConfig()
    {
        EditorGUILayout.PropertyField(this.androidApplicationIdentifier, new GUIContent("Application Identifier"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidBundleVersionCode, new GUIContent("Bundle Version Code"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidKeystoreName, new GUIContent("Keystore Name"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidKeystorePass, new GUIContent("Keystore Password"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidKeyaliasName, new GUIContent("Keyalias Name"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidKeyaliasPass, new GUIContent("Keyalias Password"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon192, new GUIContent("Icon 192x192"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon144, new GUIContent("Icon 144x144"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon96, new GUIContent("Icon 96x96"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon72, new GUIContent("Icon 72x72"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon48, new GUIContent("Icon 48x48"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.androidIcon36, new GUIContent("Icon 36x36"), Array.Empty<GUILayoutOption>());
    }

    private void DrawDesktopConfig()
    {
        EditorGUILayout.PropertyField(this.desktopDefaultIsFullScreen, new GUIContent("Default Is FullScreen"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopDefaultIsNativeResolution, new GUIContent("Default Is Native Resolution"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopDefaultScreenWidth, new GUIContent("Default Screen Width"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopDefaultScreenHeight, new GUIContent("Default Screen Height"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopResizableWindow, new GUIContent("Resizable Window"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon1024, new GUIContent("Icon 1024x1024"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon512, new GUIContent("Icon 512x512"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon256, new GUIContent("Icon 256x256"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon128, new GUIContent("Icon 128x128"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon48, new GUIContent("Icon 48x48"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon32, new GUIContent("Icon 32x32"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.desktopIcon16, new GUIContent("Icon 16x16"), Array.Empty<GUILayoutOption>());
    }

    private void OnEnable()
    {
        SerializedObject serializedObject = base.serializedObject;
        this.companyName = serializedObject.FindProperty("companyName");
        this.productName = serializedObject.FindProperty("productName");
        this.productNameI18N = serializedObject.FindProperty("productNameI18N");
        this.version = serializedObject.FindProperty("version");
        this.defineSymbols = serializedObject.FindProperty("defineSymbols");
        this.scenes = serializedObject.FindProperty("scenes");
        this.splashScreen = serializedObject.FindProperty("splashScreen");
        this.targetDevice = serializedObject.FindProperty("targetDevice");
        this.iOSApplicationIdentifier = serializedObject.FindProperty("iOSApplicationIdentifier");
        this.iOSProvisioningProfile = serializedObject.FindProperty("iOSProvisioningProfile");
        this.iOSCodeSignIdentity = serializedObject.FindProperty("iOSCodeSignIdentity");
        this.iOSBuildNumber = serializedObject.FindProperty("iOSBuildNumber");
        this.iOSApplicationDisplayName = serializedObject.FindProperty("iOSApplicationDisplayName");
        this.iOSEnableAutomaticSigning = serializedObject.FindProperty("iOSEnableAutomaticSigning");
        this.iOSDeveloperTeamID = serializedObject.FindProperty("iOSDeveloperTeamID");
        this.iOSEnableBitCode = serializedObject.FindProperty("iOSEnableBitCode");
        this.iOSFrameworks = serializedObject.FindProperty("iOSFrameworks");
        this.iOSIcon180 = serializedObject.FindProperty("iOSIcon180");
        this.iOSIcon167 = serializedObject.FindProperty("iOSIcon167");
        this.iOSIcon152 = serializedObject.FindProperty("iOSIcon152");
        this.iOSIcon144 = serializedObject.FindProperty("iOSIcon144");
        this.iOSIcon120 = serializedObject.FindProperty("iOSIcon120");
        this.iOSIcon114 = serializedObject.FindProperty("iOSIcon114");
        this.iOSIcon76 = serializedObject.FindProperty("iOSIcon76");
        this.iOSIcon72 = serializedObject.FindProperty("iOSIcon72");
        this.iOSIcon57 = serializedObject.FindProperty("iOSIcon57");
        this.iOSSpotlightIcon120 = serializedObject.FindProperty("iOSSpotlightIcon120");
        this.iOSSpotlightIcon80 = serializedObject.FindProperty("iOSSpotlightIcon80");
        this.iOSSpotlightIcon40 = serializedObject.FindProperty("iOSSpotlightIcon40");
        this.iOSSettingsIcon87 = serializedObject.FindProperty("iOSSettingsIcon87");
        this.iOSSettingsIcon58 = serializedObject.FindProperty("iOSSettingsIcon58");
        this.iOSSettingsIcon29 = serializedObject.FindProperty("iOSSettingsIcon29");
        this.iOSNotificationIcon60 = serializedObject.FindProperty("iOSNotificationIcon60");
        this.iOSNotificationIcon40 = serializedObject.FindProperty("iOSNotificationIcon40");
        this.iOSNotificationIcon20 = serializedObject.FindProperty("iOSNotificationIcon20");
        this.iOSAppstoreIcon1024 = serializedObject.FindProperty("iOSAppstoreIcon1024");
        this.androidApplicationIdentifier = serializedObject.FindProperty("androidApplicationIdentifier");
        this.androidBundleVersionCode = serializedObject.FindProperty("androidBundleVersionCode");
        this.androidKeystoreName = serializedObject.FindProperty("androidKeystoreName");
        this.androidKeystorePass = serializedObject.FindProperty("androidKeystorePass");
        this.androidKeyaliasName = serializedObject.FindProperty("androidKeyaliasName");
        this.androidKeyaliasPass = serializedObject.FindProperty("androidKeyaliasPass");
        this.androidIcon192 = serializedObject.FindProperty("androidIcon192");
        this.androidIcon144 = serializedObject.FindProperty("androidIcon144");
        this.androidIcon96 = serializedObject.FindProperty("androidIcon96");
        this.androidIcon72 = serializedObject.FindProperty("androidIcon72");
        this.androidIcon48 = serializedObject.FindProperty("androidIcon48");
        this.androidIcon36 = serializedObject.FindProperty("androidIcon36");
        this.desktopDefaultIsFullScreen = serializedObject.FindProperty("desktopDefaultIsFullScreen");
        this.desktopDefaultIsNativeResolution = serializedObject.FindProperty("desktopDefaultIsNativeResolution");
        this.desktopDefaultScreenWidth = serializedObject.FindProperty("desktopDefaultScreenWidth");
        this.desktopDefaultScreenHeight = serializedObject.FindProperty("desktopDefaultScreenHeight");
        this.desktopResizableWindow = serializedObject.FindProperty("desktopResizableWindow");
        this.desktopIcon1024 = serializedObject.FindProperty("desktopIcon1024");
        this.desktopIcon512 = serializedObject.FindProperty("desktopIcon512");
        this.desktopIcon256 = serializedObject.FindProperty("desktopIcon256");
        this.desktopIcon128 = serializedObject.FindProperty("desktopIcon128");
        this.desktopIcon48 = serializedObject.FindProperty("desktopIcon48");
        this.desktopIcon32 = serializedObject.FindProperty("desktopIcon32");
        this.desktopIcon16 = serializedObject.FindProperty("desktopIcon16");
        this.iOSFrameworkList = new ReorderableList(serializedObject, this.iOSFrameworks);
        this.iOSFrameworkList.drawHeaderCallback = delegate (Rect rect)
        {
            GUI.Label(rect, "Frameworks:");
        };
        this.iOSFrameworkList.elementHeight = 1f * EditorGUIUtility.singleLineHeight;
        this.iOSFrameworkList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            SerializedProperty arrayElementAtIndex = this.iOSFrameworks.GetArrayElementAtIndex(index);
            EditorGUI.PropertyField(rect, arrayElementAtIndex, GUIContent.none);
        };
    }

    private SerializedProperty companyName;

    private SerializedProperty productName;

    private SerializedProperty productNameI18N;

    private SerializedProperty version;

    private SerializedProperty defineSymbols;

    private SerializedProperty scenes;

    private SerializedProperty splashScreen;

    private SerializedProperty targetDevice;

    private SerializedProperty iOSApplicationIdentifier;

    private SerializedProperty iOSProvisioningProfile;

    private SerializedProperty iOSCodeSignIdentity;

    private SerializedProperty iOSBuildNumber;

    private SerializedProperty iOSApplicationDisplayName;

    private SerializedProperty iOSEnableAutomaticSigning;

    private SerializedProperty iOSDeveloperTeamID;

    private SerializedProperty iOSEnableBitCode;

    private SerializedProperty iOSFrameworks;

    private SerializedProperty iOSIcon180;

    private SerializedProperty iOSIcon167;

    private SerializedProperty iOSIcon152;

    private SerializedProperty iOSIcon144;

    private SerializedProperty iOSIcon120;

    private SerializedProperty iOSIcon114;

    private SerializedProperty iOSIcon76;

    private SerializedProperty iOSIcon72;

    private SerializedProperty iOSIcon57;

    private SerializedProperty iOSSpotlightIcon120;

    private SerializedProperty iOSSpotlightIcon80;

    private SerializedProperty iOSSpotlightIcon40;

    private SerializedProperty iOSSettingsIcon87;

    private SerializedProperty iOSSettingsIcon58;

    private SerializedProperty iOSSettingsIcon29;

    private SerializedProperty iOSNotificationIcon60;

    private SerializedProperty iOSNotificationIcon40;

    private SerializedProperty iOSNotificationIcon20;

    private SerializedProperty iOSAppstoreIcon1024;

    private SerializedProperty androidApplicationIdentifier;

    private SerializedProperty androidBundleVersionCode;

    private SerializedProperty androidKeystoreName;

    private SerializedProperty androidKeystorePass;

    private SerializedProperty androidKeyaliasName;

    private SerializedProperty androidKeyaliasPass;

    private SerializedProperty androidIcon192;

    private SerializedProperty androidIcon144;

    private SerializedProperty androidIcon96;

    private SerializedProperty androidIcon72;

    private SerializedProperty androidIcon48;

    private SerializedProperty androidIcon36;

    private SerializedProperty desktopDefaultIsFullScreen;

    private SerializedProperty desktopDefaultIsNativeResolution;

    private SerializedProperty desktopDefaultScreenWidth;

    private SerializedProperty desktopDefaultScreenHeight;

    private SerializedProperty desktopResizableWindow;

    private SerializedProperty desktopIcon1024;

    private SerializedProperty desktopIcon512;

    private SerializedProperty desktopIcon256;

    private SerializedProperty desktopIcon128;

    private SerializedProperty desktopIcon48;

    private SerializedProperty desktopIcon32;

    private SerializedProperty desktopIcon16;

    private ReorderableList iOSFrameworkList;
}
