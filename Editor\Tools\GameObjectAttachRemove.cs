﻿using Game;
using Nirvana;
using System;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

//清理资源用，移除prefab上所有挂载的GameObjectAttach组件
class GameObjectAttachRemove
{
    [MenuItem("Assets/ProjectFixtor/GameObjectAttach一键移除")]
    public static void Fixed()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedInPaths(checkDirs);
                    }
                    else
                    {
                        GameObject[] objs = Selection.gameObjects[i].GetComponentsInChildren<GameObject>(true);
                        for (int j = 0; j < objs.Length; j++)
                        {
                            GameObject gameObject = GameObject.Instantiate(objs[j]);
                            GameObjectAttach[] gameObjectAttaches = gameObject.GetComponentsInChildren<GameObjectAttach>(true);
                            if (gameObjectAttaches != null && gameObjectAttaches.Length > 0)
                            {
                                FixedPrefab(gameObject, path);
                            }
                            GameObject.DestroyImmediate(gameObject);
                        }
                    }

                    EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedInPaths(checkDirs);
                }
                else
                {
                    GameObject[] objs = Selection.activeGameObject.GetComponentsInChildren<GameObject>(true);
                    for (int i = 0; i < objs.Length; i++)
                    {
                        GameObject gameObject = GameObject.Instantiate(objs[i]);
                        GameObjectAttach[] gameObjectAttaches = gameObject.GetComponentsInChildren<GameObjectAttach>(true);
                        if (gameObjectAttaches != null && gameObjectAttaches.Length > 0)
                        {
                            FixedPrefab(gameObject, path);
                        }
                        GameObject.DestroyImmediate(gameObject);
                    }
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedInPaths(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == prefab)
            {
                continue;
            }
            GameObject gameObject = GameObject.Instantiate(prefab);
            GameObjectAttach[] gameObjectAttaches = gameObject.GetComponentsInChildren<GameObjectAttach>(true);
            if (gameObjectAttaches != null && gameObjectAttaches.Length > 0)
            {
                FixedPrefab(gameObject, path);
            }
            GameObject.DestroyImmediate(gameObject);

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }
    }


    public static void FixedPrefab(GameObject obj, string filePath)
    {
        try
        {
            GameObjectAttach[] gameObjectAttaches = obj.GetComponentsInChildren<GameObjectAttach>(true);
            for (int i = gameObjectAttaches.Length - 1; i >= 0; i--)
            {
                if (!string.IsNullOrEmpty(gameObjectAttaches[i].AssetGuid))
                {
                    string asset = AssetDatabase.GUIDToAssetPath(gameObjectAttaches[i].AssetGuid);
                    GameObject gameObject = string.IsNullOrEmpty(asset) ? null : AssetDatabase.LoadAssetAtPath(asset, typeof(GameObject)) as GameObject;
                    if (null != gameObject)
                    {
                        gameObject = null;
                        AssetDatabase.DeleteAsset(asset);
                    }
                }
                PreviewObject previewObject = obj.GetComponent<PreviewObject>();
                if (previewObject != null)
                {
                    GameObject.DestroyImmediate(previewObject);
                }
                GameObject.DestroyImmediate(gameObjectAttaches[i]);
            }
            PrefabUtility.SaveAsPrefabAsset(obj, filePath);
            GameObject.DestroyImmediate(obj);

        }
        catch (Exception ex)
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError("Error filePath:" + filePath);
            Debug.LogError(ex.ToString());
        }
    }

    [MenuItem("Assets/ProjectFixtor/场景GameObjectAttach一键移除")]
    public static void FixedScene()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedSceneInPaths(checkDirs);
                    }

                    EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedSceneInPaths(checkDirs);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedSceneInPaths(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:scene", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (!path.EndsWith("_Main.unity"))
            {
                continue;
            }

            bool isSceneDirty = false;
            Scene scene = EditorSceneManager.OpenScene(path);
            GameObject[] root_objs = scene.GetRootGameObjects();
            GameObject mainObj = root_objs[0];
            if (mainObj.name == "Main")
            {
                Transform effectTrans = mainObj.transform.Find("Effects");
                if (effectTrans == null) 
                {
                    effectTrans = mainObj.transform.Find("Effect");
                }

                if (effectTrans != null)
                {
                    GameObjectAttach[] gameObjectAttaches = effectTrans.gameObject.GetComponentsInChildren<GameObjectAttach>(true);
                    for (int i = gameObjectAttaches.Length - 1; i >= 0; i--)
                    {
                        GameObject.DestroyImmediate(gameObjectAttaches[i].gameObject);
                        isSceneDirty = true;
                    }

                    QualityControlActive qualityControlActive = effectTrans.gameObject.GetComponent<QualityControlActive>();
                    if (qualityControlActive != null) {
                        qualityControlActive.AutoFetch(false);
                    }
                }
            }

            if (isSceneDirty)
            {
                EditorSceneManager.MarkSceneDirty(scene);
                EditorSceneManager.SaveScene(scene);
            }
            EditorSceneManager.CloseScene(scene, true);

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/ProjectFixtor/ParticlePrefab一键移除")]
    public static void FindAndDeleteParticlePrefab()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FindAndDeleteParticlePrefabInPaths(checkDirs);
                    }

                    EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FindAndDeleteParticlePrefabInPaths(checkDirs);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    static void FindAndDeleteParticlePrefabInPaths(string[] checkDirs)
    {
        string filePath = "";
        try {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            int totalCount = guids.Length;
            int cutIndex = 0;
            foreach (var guid in guids)
            {
                filePath = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(filePath);
                ParticleSystem particleSystem = prefab.GetComponentInChildren<ParticleSystem>();
                if (particleSystem != null && prefab.GetComponentInChildren<ActorRender>() == null)
                {
                    AssetDatabase.DeleteAsset(filePath);
                }

                cutIndex++;
                EditorUtility.DisplayProgressBar("正在处理"
                            , string.Format("{0} / {1}", cutIndex, totalCount)
                            , (float)(cutIndex) / totalCount);
            }
            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
        }
        catch (Exception ex)
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError("Error filePath:" + filePath);
            Debug.LogError(ex.ToString());
        }
    }
}