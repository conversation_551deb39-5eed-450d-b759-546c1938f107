﻿using UnityEngine;
using System.Collections;
using UnityEditor;
using System;
using UnityEditor.SceneManagement;

public class SceneSwitch : Editor
{
    [MenuItem("Window/场景切换/Main")]
    static void EngineSceneEditorMainScene()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/main.unity");
    }

    [MenuItem("Window/场景切换/SkillEditor")]
    static void EngineSceneT1()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/SkillEditor/SkillEditor.unity");
    }

    [MenuItem("Window/场景切换/ViewEditor")]
    static void EngineSceneT2()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/vieweditor.unity");
    }

    [MenuItem("Window/场景切换/HUDEditor")]
    static void EngineHUDEditor()
    {
        EditorSceneManager.OpenScene("Assets/ThirdParty/HUDPackage/Example/HUDEditor.unity");
    }

    //[MenuItem("Window/场景切换/ToolsEditor")]
    //static void EngineToolsEditor()
    //{
    //    EditorSceneManager.OpenScene("Assets/Game/Scenes/toolseditor.unity");
    //}


    //[MenuItem("Window/场景切换/ParticScene")]
    //static void EngineParticEditor()
    //{
    //    EditorSceneManager.OpenScene("Assets/Game/Scenes/particscene.unity");
    //}
    
    [MenuItem("Window/场景切换/美术示例")]
    static void EngineSceneT3()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/ArtExample/ArtExample.unity");
    }
    
    [MenuItem("Window/场景切换/昼夜示例")]
    static void EngineSceneT4()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/ArtExample/DayNightExample.unity");
    }
    
    [MenuItem("Window/场景切换/角色示例")]
    static void EngineSceneT5()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/ArtExample/RoleExample.unity");
    }

    [MenuItem("Window/场景切换/配置UI场景")]
    static void EngineSceneT6()
    {
        EditorSceneManager.OpenScene("Assets/Game/Scenes/Map/A3_UI/a3_ui_default_main.unity");
    }
}