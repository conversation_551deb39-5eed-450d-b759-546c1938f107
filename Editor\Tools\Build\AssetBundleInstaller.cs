﻿using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana.Editor;
using System.Text;
using System.Collections.Generic;
using System;
using Nirvana;
using System.Text.RegularExpressions;

namespace Build
{
    /// <summary>
    /// AssetBundle 安装和管理工具类，负责生成不同规格的资源包、处理依赖关系、预下载列表等
    /// </summary>
    public static class AssetBundleInstaller
    {
        /// <summary>
        /// 生成各种规格的安装包文件（小包/中包）
        /// </summary>
        /// <param name="buildPlatType">目标构建平台类型</param>
        public static void BuildBundleInstallFile(BuildPlatType buildPlatType)
        {
            // 构建小包
            BuildSsizeBundleFile(buildPlatType);
            // 构建中包
            BuildMsizeBundleFile(buildPlatType);
        }

        // 生成小包入包列表(小小包+新手资源)
        public static void BuildSsizeBundleFile(BuildPlatType buildPlatType)
        {
            // 基础包配置处理
            string xs_file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(InstallBundleSize.sizeXS);
            string xs_path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", xs_file_name));

            // 初始化构建器并处理基础配置
            StringBuilder builder = new StringBuilder();
            builder.Append("//小包资源(150-200M)是通过小小包+新手资源合成，请勿手动修改此文件！\n\n");

            // 读取并过滤基础配置文件
            string[] lines = File.ReadAllLines(xs_path);
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i];
                // 跳过空行
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }
                // 跳过注释行
                if (line.IndexOf("//") >= 0)
                {
                    continue;
                }

                builder.Append(line + "\n");
            }

            // 获取构建平台的清单信息 并处理依赖关系
            var manifest = BuilderConfig.GetManifestInfo(buildPlatType);
            HashSet<string> filters = GetFilterBundleSet(buildPlatType, manifest, InstallBundleSize.sizeXS);
            Dictionary<string, List<string>> dependList;
            HashSet<string> depend_bundle_set = GetDependAssetBundles(manifest, filters, out dependList);
            filters.UnionWith(depend_bundle_set);

            string[] allbundles = manifest.GetAllAssetBundles();
            HashSet<string> allBundlesHashSet = new HashSet<string>();

            for (int i = 0; i < allbundles.Length; ++i)
            {
                allBundlesHashSet.Add(allbundles[i]);
            }

            // 处理资源记录文件
            string recordingPath = Path.Combine(Application.dataPath, "Resource Recordings.csv");
            string[] recordings = File.ReadAllLines(recordingPath);

            for (int i = 1; i < recordings.Length; i++)
            {
                string line = recordings[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                string[] splits = line.Split(',');
                if (splits.Length < 2)
                {
                    continue;
                }

                string bundleName = splits[0];
                //剔除掉已经入包的资源
                if (filters.Contains(bundleName))
                {
                    continue;
                }

                //剔除不存在的资源
                if (!allBundlesHashSet.Contains(bundleName))
                {
                    continue;
                }

                int level = Int32.Parse(splits[1]);
                if (level <= 0)
                {
                    filters.Add(bundleName);
                    builder.Append(string.Format("{0}\n", bundleName));
                }
                else if (level <= 1)
                {
                    if (bundleName.StartsWith("uis/view") || bundleName.StartsWith("scenes/map/") || bundleName.StartsWith("cg"))
                    {
                        filters.Add(bundleName);
                        builder.Append(string.Format("{0}\n", bundleName));
                    }
                }
                else if (level <= 5)
                {
                    if (bundleName.StartsWith("cg"))
                    {
                        filters.Add(bundleName);
                        builder.Append(string.Format("{0}\n", bundleName));
                    }
                }
            }
            // 处理预下载列表文件
            string filePath = Path.Combine(Application.dataPath, "Game/Deploy/Install/PreDownLoadBundles.txt");
            if (File.Exists(filePath))
            {
                recordings = File.ReadAllLines(filePath);
                for (int i = 0; i < recordings.Length; i++)
                {
                    string line = recordings[i];
                    if (string.IsNullOrEmpty(line))
                    {
                        continue;
                    }

                    string[] splits = line.Split(' ');
                    if (splits.Length < 2)
                    {
                        continue;
                    }

                    string bundleName = splits[1];
                    //剔除掉已经入包的资源
                    if (filters.Contains(bundleName))
                    {
                        continue;
                    }

                    //剔除不存在的资源
                    if (!allBundlesHashSet.Contains(bundleName))
                    {
                        continue;
                    }

                    int level = Int32.Parse(splits[0]);
                    if (level <= 0)
                    {
                        filters.Add(bundleName);
                        builder.Append(string.Format("{0}\n", bundleName));
                    }
                    else if (level <= 1)
                    {
                        if (bundleName.StartsWith("uis/view") || bundleName.StartsWith("scenes/map/") || bundleName.StartsWith("cg"))
                        {
                            filters.Add(bundleName);
                            builder.Append(string.Format("{0}\n", bundleName));
                        }
                    }
                    else if (level <= 5)
                    {
                        if (bundleName.StartsWith("cg"))
                        {
                            filters.Add(bundleName);
                            builder.Append(string.Format("{0}\n", bundleName));
                        }
                    }
                }
            }

            // 写入最终的小包配置文件
            string s_file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(InstallBundleSize.sizeS);
            string s_path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", s_file_name));
            File.WriteAllText(s_path, builder.ToString());
        }

        // 生成中包入包列表(小包+新手资源)
        public static void BuildMsizeBundleFile(BuildPlatType buildPlatType)
        {
            string s_file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(InstallBundleSize.sizeS);
            string s_path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", s_file_name));

            StringBuilder builder = new StringBuilder();
            builder.Append("//中包资源(大概500M)是通过小包+新手资源合成，请勿手动修改此文件！\n\n");
            string[] lines = File.ReadAllLines(s_path);
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                if (line.IndexOf("//") >= 0)
                {
                    continue;
                }

                builder.Append(line + "\n");
            }

            var manifest = BuilderConfig.GetManifestInfo(buildPlatType);
            HashSet<string> filters = GetFilterBundleSet(buildPlatType, manifest, InstallBundleSize.sizeXS);
            Dictionary<string, List<string>> dependList;
            HashSet<string> depend_bundle_set = GetDependAssetBundles(manifest, filters, out dependList);
            filters.UnionWith(depend_bundle_set);

            string[] allbundles = manifest.GetAllAssetBundles();
            HashSet<string> allBundlesHashSet = new HashSet<string>();

            for (int i = 0; i < allbundles.Length; ++i)
            {
                allBundlesHashSet.Add(allbundles[i]);
            }

            string recordingPath = Path.Combine(Application.dataPath, "Resource Recordings.csv");
            string[] recordings = File.ReadAllLines(recordingPath);

            for (int i = 1; i < recordings.Length; i++)
            {
                string line = recordings[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                string[] splits = line.Split(',');
                if (splits.Length < 2)
                {
                    continue;
                }

                string bundleName = splits[0];
                //剔除掉已经入包的资源
                if (filters.Contains(bundleName))
                {
                    continue;
                }

                //剔除不存在的资源
                if (!allBundlesHashSet.Contains(bundleName))
                {
                    continue;
                }

                int level = Int32.Parse(splits[1]);
                if (level <= 100)
                {
                    if (bundleName.StartsWith("uis/view")
                        || bundleName.StartsWith("scenes/map/") 
                        || bundleName.StartsWith("cg") 
                        || bundleName.StartsWith("actors/")
                         || bundleName.StartsWith("model/"))
                    {
                        filters.Add(bundleName);
                        builder.Append(string.Format("{0}\n", bundleName));     
                    }
                }
            }

            string m_file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(InstallBundleSize.sizeM);
            string m_path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", m_file_name));
            File.WriteAllText(m_path, builder.ToString());
        }

        // 构建强更列表文件
        public static void BuildStrongUpdateLuaFile(InstallBundleSize sizeType)
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("return {");

            string file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(sizeType);
            string path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", file_name));
            string[] lines = File.ReadAllLines(path);
            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                if (line.IndexOf("//") >= 0)
                {
                    continue;
                }

                if (line.EndsWith("/"))
                {
                    builder.Append(string.Format("\n    \"^{0}*\",", line));
                }
                else
                {
                    builder.Append(string.Format("\n    \"^{0}\",", line));
                }
            }
            builder.Append("\n}");

            File.WriteAllText(Path.Combine(Application.dataPath, "Game/Lua/config/config_strong_update.lua"), builder.ToString());
            AssetDatabase.Refresh();
        }

        // 构建预下载列表文件
        public static void BuildPreDownLoadLuaFile(InstallBundleSize sizeType, BuildPlatType buildPlatType)
        {
            var manifest = BuilderConfig.GetManifestInfo(buildPlatType);
            HashSet<string> filters = GetFilterBundleSet(buildPlatType, manifest, sizeType);
            Dictionary<string, List<string>> dependList;
            HashSet<string> depend_bundle_set = GetDependAssetBundles(manifest, filters, out dependList);
            filters.UnionWith(depend_bundle_set);

            string[] allbundles = manifest.GetAllAssetBundles();
            HashSet<string> allBundlesHashSet = new HashSet<string>();

            List<PreloadItem> preloadItems = new List<PreloadItem>();

            for (int i = 0; i < allbundles.Length; ++i)
            {
                allBundlesHashSet.Add(allbundles[i]);
            }

            // 添加新手资源
            CalcResourceRecording(preloadItems);
            // 添加手动设置的预下载列表
            CalcPredownLoadList(preloadItems, allbundles);
            // 添加场景列表
            CalcPredownLoadSceneList(preloadItems);

            preloadItems.Sort(ComparePreloadItem);

            List<int> removeList = new List<int>();
            HashSet<string> installBundles = new HashSet<string>();
            for (int i = 0; i < preloadItems.Count; ++i)
            {
                string bundleName = preloadItems[i].bundleName;
                // 剔除不存在的资源
                if (!allBundlesHashSet.Contains(bundleName))
                {
                    removeList.Add(i);
                    continue;
                }

                // 剔除已经进包的资源
                if (filters.Contains(bundleName))
                {
                    removeList.Add(i);
                    continue;
                }

                // 剔除重复资源(保留等级最低的那个)
                if (installBundles.Contains(bundleName))
                {
                    removeList.Add(i);
                }
                else
                {
                    installBundles.Add(bundleName);
                }
            }

            for (int i = removeList.Count - 1; i >= 0; --i)
            {
                int index = removeList[i];
                preloadItems.RemoveAt(index);
            }

            StringBuilder builder = new StringBuilder();
            builder.Append("return {");

            for (int i = 0; i < preloadItems.Count; ++i)
            {
                builder.Append(string.Format("\n    {{bundle=\"{0}\", level={1}}},", preloadItems[i].bundleName, preloadItems[i].level));
            }

            builder.Append("\n}");

            File.WriteAllText(Path.Combine(Application.dataPath, "Game/Lua/config/config_predownload.lua"), builder.ToString());
        }

        // 新手资源（录制下来的资源）
        private static void CalcResourceRecording(List<PreloadItem> preloadItems)
        {
            string recordingPath = Path.Combine(Application.dataPath, "Resource Recordings.csv");
            string[] recordings = File.ReadAllLines(recordingPath);

            for (int i = 1; i < recordings.Length; i++)
            {
                string line = recordings[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                string[] splits = line.Split(',');
                if (splits.Length < 2)
                {
                    continue;
                }

                string bundleName = splits[0];
                int level = Int32.Parse(splits[1]);

                //防止新手玩家2次加載
                if (level <= 1)
                {
                    level = 2;
                }

                preloadItems.Add(new PreloadItem(bundleName, level));
            }
        }

        // 手动写入的预下载资源
        private static void CalcPredownLoadList(List<PreloadItem> preloadItems, string[] allbundles)
        {
            string filePath = Path.Combine(Application.dataPath, "Game/Deploy/Install/PreDownLoadBundles.txt");
            if (File.Exists(filePath))
            {
                string[] lines = File.ReadAllLines(filePath);
                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i];
                    if (string.IsNullOrEmpty(line))
                    {
                        continue;
                    }

                    if (line.IndexOf("//") >= 0)
                    {
                        continue;
                    }

                    string[] splits = line.Split(' ');
                    if (splits.Length < 2)
                    {
                        continue;
                    }

                    int level = Int32.Parse(splits[0]);
                    string path = splits[1];

                    for (int j = 0; j < allbundles.Length; j++)
                    {
                        string bundleName = allbundles[j];
                        bool isMatch = false;
                        if (path.EndsWith("/"))
                        {
                            if (bundleName.StartsWith(path))
                            {
                                isMatch = true;
                            }
                        }
                        else
                        {
                            if (bundleName == path)
                            {
                                isMatch = true;
                            }
                        }

                        if (isMatch)
                        {
                            preloadItems.Add(new PreloadItem(bundleName, level));
                        }
                    }
                }
            }
        }

        // 所有野外场景，活动场景，副本场景（策划提供开启等级和对应SceneID）
        private static void CalcPredownLoadSceneList(List<PreloadItem> preloadItems)
        {
            string filePath = Path.Combine(Application.dataPath, "Game/Deploy/Install/PreDownLoadSceneList.txt");
            string configPath = Path.Combine(Application.dataPath, "Game/Lua/config/scenes");
            if (File.Exists(filePath))
            {
                string[] lines = File.ReadAllLines(filePath);
                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i];
                    if (string.IsNullOrEmpty(line))
                    {
                        continue;
                    }

                    if (line.IndexOf("//") >= 0)
                    {
                        continue;
                    }

                    string[] splits = line.Split(' ');
                    if (splits.Length < 2)
                    {
                        continue;
                    }

                    string sceneMap = splits[1];
                    string[] sceneList = sceneMap.Split('|');
                    for (int j = 0; j < sceneList.Length; ++j)
                    {
                        int level = Int32.Parse(splits[0]);
                        int sceneID = Int32.Parse(sceneList[j]);
                        string luaPath = Path.Combine(configPath, string.Format("scene_{0}.lua", sceneID));
                        if (File.Exists(luaPath))
                        {
                            string content = File.ReadAllText(luaPath);
                            Match match = Regex.Match(content, "bundle_name = \"(.*?)\"");
                            string bundleName = match.Groups[1].Value;
                            
                            // 野外场景开启等级读config
                            if (level <= 0)
                            {
                                match = Regex.Match(content, "levellimit = ([0-9]+)");
                                level = Int32.Parse(match.Groups[1].Value);
                            }

                            preloadItems.Add(new PreloadItem(bundleName, level));
                        }
                    }
                }
            }
        }

        // 生成package_0
        public static void BuildSubPackage0()
        {
            StringBuilder builder = new StringBuilder();
            builder.Append("//package_0是通过新手资源动态生成的，手动修改无效\n");

            var config_predownload_path = Path.Combine(Application.dataPath, "Game/Lua/config/config_predownload.lua");
            if (File.Exists(config_predownload_path))
            {
                string[] lines = File.ReadAllLines(config_predownload_path);
                for (int i = 1; i < lines.Length - 1; i++)
                {
                    string line = lines[i];
                    if (string.IsNullOrEmpty(line))
                    {
                        continue;
                    }

                    var matcehs = Regex.Match(line, "{bundle=\"(.*?)\", level=([0-9]+)},");
                    if (matcehs.Success)
                    {
                        string bundleNmae = matcehs.Groups[1].Value;
                        int level = int.Parse(matcehs.Groups[2].Value);
                        // 统计前150级的资源
                        if (level > 150)
                        {
                            break;
                        }

                        builder.Append(bundleNmae + "\n");
                    }
                }
            }

            string savePath = Path.Combine(Application.dataPath, "Game/Deploy/Install/SubPackage_0.txt");
            File.WriteAllText(savePath, builder.ToString());

            AssetDatabase.Refresh();
        }

        // 构建分包
        public static bool BuildSubPackage(BuildPlatType buildPlatType)
        {
            BuildSubPackage0();
            string packageDir = Path.Combine(Application.dataPath, "Game/Lua/config/packages");
            if(!DeleteDirectory(packageDir))
            {
                return false;
            }
            Directory.CreateDirectory(packageDir);

            var manifest = BuilderConfig.GetManifestInfo(buildPlatType);
            string[] allbundles = manifest.GetAllAssetBundles();
            HashSet<string> allBundlesHashSet = new HashSet<string>();
            for (int i = 0; i < allbundles.Length; ++i)
            {
                allBundlesHashSet.Add(allbundles[i]);
            }

            HashSet<string> filters = GetFilterBundleSet(buildPlatType, manifest, InstallBundleSize.sizeS);
            int index = 0;
            foreach (string d in Directory.GetFileSystemEntries(Path.Combine(Application.dataPath, "Game/Deploy/Install")))
            {
                if (File.Exists(d))
                {
                    string name = Path.GetFileNameWithoutExtension(d);
                    if (name.StartsWith("SubPackage_") && Path.GetExtension(d) != ".meta")
                    {
                        StringBuilder builder = new StringBuilder();
                        builder.Append("return {");

                        string[] splits = name.Split('_');
                        index = int.Parse(splits[1]);

                        // 插入记录下来的分包资源
                        InsertPackageRecorder(index, allBundlesHashSet, filters, builder);

                        string[] lines = File.ReadAllLines(d);
                        // 这里倒序插入
                        for (int i = lines.Length - 1; i >= 0; --i)
                        {
                            string line = lines[i];
                            if (string.IsNullOrEmpty(line))
                            {
                                continue;
                            }

                            if (line.IndexOf("//") >= 0)
                            {
                                continue;
                            }

                            if (line.EndsWith("/"))
                            {
                                foreach (var bundle in allbundles)
                                {
                                    if (bundle.StartsWith(line) && !filters.Contains(bundle))
                                    {
                                        filters.Add(bundle);
                                        builder.Append(string.Format("\n    {{bundle=\"{0}\"}},", bundle));
                                    }
                                }
                            }
                            else
                            {
                                //剔除不存在的资源
                                if (!allBundlesHashSet.Contains(line))
                                {
                                    continue;
                                }

                                // 剔除重复资源
                                if (filters.Contains(line))
                                    continue;

                                filters.Add(line);
                                builder.Append(string.Format("\n    {{bundle=\"{0}\"}},", line));
                            }
                        }

                        builder.Append("\n}");
                        File.WriteAllText(Path.Combine(packageDir, string.Format("package_{0}.lua", index)), builder.ToString());
                    }
                }
            }

            File.WriteAllText(Path.Combine(Application.dataPath, "Game/Lua/config/packages/package_count.lua"), string.Format("return {0}", index));
            return true;
        }

        private static void InsertPackageRecorder(int packageIndex, HashSet<string> allBundlesHashSet, HashSet<string> filter, StringBuilder builder)
        {
            // 目前只支持分包1
            if (1 != packageIndex)
            {
                return;
            }

            string recordingPath = Path.Combine(Application.dataPath, "Package Recordings.csv");
            string[] recordings = File.ReadAllLines(recordingPath);

            List<PackageItem> packageItems = new List<PackageItem>();
            for (int i = 1; i < recordings.Length; i++)
            {
                string line = recordings[i];
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                string[] splits = line.Split(',');
                if (splits.Length < 2)
                {
                    continue;
                }

                string bundleName = splits[0];
                //剔除掉已经入包的资源
                if (filter.Contains(bundleName))
                {
                    continue;
                }

                //剔除不存在的资源
                if (!allBundlesHashSet.Contains(bundleName))
                {
                    continue;
                }

                filter.Add(bundleName);

                int level = Int32.Parse(splits[1]);
                int timeStamp = Int32.Parse(splits[2]);
                int useTimes = Int32.Parse(splits[3]);
                packageItems.Add(new PackageItem(bundleName, level, timeStamp, useTimes));
            }

            packageItems.Sort(ComparePackageItem);

            for (int i = 0; i < packageItems.Count; ++i)
            {
                var item = packageItems[i];
                builder.Append(string.Format("\n    {{bundle=\"{0}\"}},", item.bundleName));
            }
        }

        private static bool DeleteDirectory(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Debug.LogErrorFormat("Error, {0} not exists!!!", directoryPath);
                return false;
            }
            try
            {
                foreach (string d in Directory.GetFileSystemEntries(directoryPath))
                {
                    if (File.Exists(d))
                    {
                        FileInfo fi = new FileInfo(d);
                        if (fi.Attributes.ToString().IndexOf("ReadOnly") != -1)
                            fi.Attributes = FileAttributes.Normal;
                        File.Delete(d);
                    }
                    else
                    {
                        DeleteDirectory(d);
                    }
                }

                Directory.Delete(directoryPath);
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
                return false;
            }
            return true;
        }

        public static void CalcTotalSize(BuildPlatType buildPlatType)
        {
            CalcTotalSize(buildPlatType, InstallBundleSize.sizeL);
            CalcTotalSize(buildPlatType, InstallBundleSize.sizeM);
            CalcTotalSize(buildPlatType, InstallBundleSize.sizeS);
            CalcTotalSize(buildPlatType, InstallBundleSize.sizeXS);
        }

        // 计算入包大小
        public static void CalcTotalSize(BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            Dictionary<string, string> install_list = GetInstallBundlesList(buildPlatType, sizeType);

            long total_size = 0;
            string bundle_dir = BuilderConfig.GetAssetBundlePath(buildPlatType);
            foreach (var kv in install_list)
            {
                string path = Path.Combine(bundle_dir, kv.Key);

                if (Directory.Exists(path))
                {
                    total_size += DirectoryUtil.GetDirectoryLength(path);
                }

                if (File.Exists(path))
                {
                    FileInfo file_info = new FileInfo(path);
                    total_size += file_info.Length;
                }
            }

            int size = Convert.ToInt32(total_size * 1.0f / 1024 / 1024);
            Debug.LogFormat("{0} : {1}M", sizeType, size);

            CheckInstallTxtList(buildPlatType, sizeType);
        }

        // 检查配置
        private static void CheckInstallTxtList(BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            HashSet<string> un_match_set = new HashSet<string>();
            GetFilterBundleSet(buildPlatType, BuilderConfig.GetManifestInfo(buildPlatType), sizeType, un_match_set);
            GetFilterBundleSet(buildPlatType, BuilderConfig.GetLuaManifestInfo(buildPlatType), sizeType);
            Resources.UnloadUnusedAssets();

            foreach (var item in un_match_set)
            {
                Debug.LogErrorFormat("没找到对应资源：{0}", item);
            }  
        }

        // 建生成各种规格的AssetBundle列表，给sdk用
        public static void WriteInstallBundlesList(BuildPlatType buildPlatType, bool outPutLog = false)
        {
            InstallBundleSize[] size_list = 
            {
                InstallBundleSize.sizeL,
                InstallBundleSize.sizeM,
                InstallBundleSize.sizeS,
                InstallBundleSize.sizeXS,
               // InstallBundleSize.sizeAll,
            };

            for (int i = 0; i < size_list.Length; i++)
            {
                Dictionary<string, string> install_list = GetInstallBundlesList(buildPlatType, size_list[i], outPutLog);
                var builder = new StringBuilder();
                foreach (var kv in install_list)
                {
                    if (!String.IsNullOrEmpty(kv.Key))
                    {
                        builder.Append(kv.Key);
                        builder.Append(' ');
                        builder.Append(kv.Value);
                        builder.Append('\n');
                    }
                }

                string assetbundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
                string file_name = BuilderConfig.GetAssetBundleInstallListTxtName(size_list[i]);
                var path = Path.Combine(assetbundle_path, string.Format("../{0}.txt", file_name));
                File.WriteAllText(path, builder.ToString());
            }
        }

        // 拷要安装的bundle文件进行StreamAsssets（生成apk时用的是这个目录）
        public static void CopyInstallBundlesToStreamAsssets(BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            // 先删除旧目录
            var target_path = "Assets/StreamingAssets/AssetBundle";
            if (Directory.Exists(target_path))
            {
                Directory.Delete(target_path, true);
            }
            Directory.CreateDirectory(target_path);

            // 先删除旧lua目录
            var lua_target_path = Path.Combine(target_path, "LuaAssetBundle");
            Directory.CreateDirectory(lua_target_path);

            // 拷版本号文件
            string bundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
            var versionPath = Path.GetFullPath(Path.Combine(bundle_path, "version.txt"));
            File.Copy(versionPath, Path.Combine(target_path, "version.txt"));

            // 拷mainfest文件
            var manifestPath = Path.GetFullPath(Path.Combine(bundle_path, "AssetBundle"));
            File.Copy(manifestPath, Path.Combine(target_path, "AssetBundle"));
            var assetBundleLuaPath = Path.GetFullPath(Path.Combine(bundle_path, "AssetBundle.lua"));
            File.Copy(assetBundleLuaPath, Path.Combine(target_path, "AssetBundle.lua"));

            // 拷Lua mainfest文件
            var luaManifestPath = Path.GetFullPath(Path.Combine(bundle_path, "LuaAssetBundle/LuaAssetBundle"));
            File.Copy(luaManifestPath, Path.Combine(lua_target_path, "LuaAssetBundle"));
            var path = Path.GetFullPath(Path.Combine(bundle_path, "LuaAssetBundle/LuaAssetBundle.lua"));
            File.Copy(path, Path.Combine(lua_target_path, "LuaAssetBundle.lua"));

            // 拷进包AssetBundle文件
            CopyInstallBundles(buildPlatType, sizeType, bundle_path, target_path);

            // 写file_txt文件
            WriteFileListTxt();

            Resources.UnloadUnusedAssets();
        }

        public static void CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            EncryptMgr.ClearEncryptKey();
            EncryptMgr.InitABEncryptKey();

            // 先删除旧目录
            var target_path = "Assets/StreamingAssets/AssetBundle";
            if (Directory.Exists(target_path))
            {
                Directory.Delete(target_path, true);
            }
            Directory.CreateDirectory(target_path);

            // 先删除旧lua目录
            var lua_target_path = Path.Combine(target_path, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle"));
            Directory.CreateDirectory(lua_target_path);

            // 拷版本号文件
            string bundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
            var versionPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetEncryptPath2Base64Editor("version.txt")));
            File.Copy(versionPath, Path.Combine(target_path, EncryptMgr.GetEncryptPath2Base64Editor("version.txt")));

            // 拷mainfest文件
            var manifestPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetEncryptPath2Base64Editor("AssetBundle")));
            File.Copy(manifestPath, Path.Combine(target_path, EncryptMgr.GetEncryptPath2Base64Editor("AssetBundle")));
            var assetBundleLuaPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetEncryptPath2Base64Editor("AssetBundle.lua")));
            File.Copy(assetBundleLuaPath, Path.Combine(target_path, EncryptMgr.GetEncryptPath2Base64Editor("AssetBundle.lua")));

            // 拷Lua mainfest文件
            var luaManifestPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle")));
            File.Copy(luaManifestPath, Path.Combine(lua_target_path, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle")));
            var path = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle.lua")));
            File.Copy(path, Path.Combine(lua_target_path, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle.lua")));

            // 拷进包AssetBundle文件
            CopyInstallEncryptBundles(buildPlatType, sizeType, bundle_path, target_path);

            // 写file_txt文件
            WriteEncryptFileListTxt(buildPlatType);

            Resources.UnloadUnusedAssets();
        }

        public static void AddManifestBundlesToList(ManifestInfo manifest, List<string> list)
        {
            string[] all_assets = manifest.GetAllAssetBundles();
            for (int i = 0; i < all_assets.Length; i++)
            {
                list.Add(string.Format("{0} {1}", all_assets[i], manifest.GetAssetBundleHash(all_assets[i])));
            }
        }

        private static void CopyInstallBundles(BuildPlatType buildPlatType, InstallBundleSize sizeType, string bundlePath, string targetPath)
        {
            Debug.LogFormat("Start Copy Install Bundles:{0}, {1}", sizeType, bundlePath);

            List<string> errorList = new List<string>();
            string[] lines = new string[] { };
            if (InstallBundleSize.sizeAll == sizeType)
            {
                List<string> list = new List<string>();
                AddManifestBundlesToList(BuilderConfig.GetManifestInfo(buildPlatType), list);
                AddManifestBundlesToList(BuilderConfig.GetLuaManifestInfo(buildPlatType), list);
                lines = list.ToArray();
            }
            else
            {
                string file_name = BuilderConfig.GetAssetBundleInstallListTxtName(sizeType);
                var install_list_txt_path = Path.Combine(bundlePath, string.Format("../{0}.txt", file_name));
                lines = File.ReadAllLines(install_list_txt_path);
            }
          
            string lua_str = "lua/";
            for (int i = 0; i < lines.Length; i++)
            {
                if (string.IsNullOrEmpty(lines[i]))
                {
                    continue;
                }

                string[] ary = lines[i].Split(' ');
                if (2 != ary.Length)
                {
                    errorList.Add(lines[i]);
                    Debug.LogErrorFormat("CopyInstallBundlesToStreamAsssets Error! {0}", lines[i]);
                    continue;
                }

                string bundle_name = ary[0];
                string hash = ary[1];

                var src = "";
                var path = "";

                if (bundle_name.StartsWith(lua_str))
                {
                    src = Path.Combine(bundlePath, "LuaAssetBundle/" + bundle_name);
                    path = Path.Combine(targetPath, "LuaAssetBundle/" + bundle_name);
                }
                else
                {
                    src = Path.Combine(bundlePath, bundle_name);
                    path = Path.Combine(targetPath, bundle_name);
                }

                src = src.Replace("\\", "/");
                path = path.Replace("\\", "/");

                var pathDir = Path.GetDirectoryName(path);
                if (!Directory.Exists(pathDir))
                {
                    Directory.CreateDirectory(pathDir);
                }

                if (File.Exists(src))
                {
                    File.Copy(src, path + "-" + hash, true);
                }
                else
                {
                    Debug.LogErrorFormat("File Copy Error {0}, file is not exist", src);
                }
            }

            if (0 == errorList.Count)
            {
                Debug.Log("拷贝成功!");
            }
            else
            {
                File.WriteAllLines(Path.Combine(Application.streamingAssetsPath, "error.txt"), errorList.ToArray());
            }
        }

        public static void CopyInstallEncryptBundles(BuildPlatType buildPlatType, InstallBundleSize sizeType, string bundlePath, string targetPath)
        {
            Debug.LogFormat("Start Copy Install Bundles:{0}, {1}", sizeType, bundlePath);

            List<string> errorList = new List<string>();
            string[] lines = new string[] { };
            if (InstallBundleSize.sizeAll == sizeType)
            {
                List<string> list = new List<string>();
                AddManifestBundlesToList(BuilderConfig.GetManifestInfo(buildPlatType), list);
                AddManifestBundlesToList(BuilderConfig.GetLuaManifestInfo(buildPlatType), list);
                lines = list.ToArray();
            }
            else
            {
                string file_name = BuilderConfig.GetAssetBundleInstallListTxtName(sizeType);
                var install_list_txt_path = Path.Combine(bundlePath, string.Format("../{0}.txt", file_name));
                lines = File.ReadAllLines(install_list_txt_path);
            }

            string lua_str = "lua/";
            for (int i = 0; i < lines.Length; i++)
            {
                if (string.IsNullOrEmpty(lines[i]))
                {
                    continue;
                }

                string[] ary = lines[i].Split(' ');
                if (2 != ary.Length)
                {
                    errorList.Add(lines[i]);
                    Debug.LogErrorFormat("CopyInstallBundlesToStreamAsssets Error! {0}", lines[i]);
                    continue;
                }

                string bundle_name = ary[0];
                string hash = ary[1];

                var src = "";
                var path = "";

                if (bundle_name.StartsWith(lua_str))
                {
                    src = Path.Combine(bundlePath, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle/" + bundle_name));
                    path = Path.Combine(targetPath, EncryptMgr.GetEncryptPath2Base64Editor("LuaAssetBundle/" + bundle_name));
                }
                else
                {
                    src = Path.Combine(bundlePath, EncryptMgr.GetEncryptPath2Base64Editor(bundle_name));
                    path = Path.Combine(targetPath, EncryptMgr.GetEncryptPath2Base64Editor(bundle_name));
                }

                src = src.Replace("\\", "/");
                path = path.Replace("\\", "/");

                var pathDir = Path.GetDirectoryName(path);
                if (!Directory.Exists(pathDir))
                {
                    Directory.CreateDirectory(pathDir);
                }

                if (File.Exists(src))
                {
                    File.Copy(src, path + "-" + hash, true);
                }
                else
                {
                    Debug.LogErrorFormat("File Copy Error {0}, file is not exist", src);
                }
            }

            if (0 == errorList.Count)
            {
                Debug.Log("拷贝成功!");
            }
            else
            {
                File.WriteAllLines(Path.Combine(Application.streamingAssetsPath, "error.txt"), errorList.ToArray());
            }
        }

        public static void WriteFileListTxt()
        {
            var fileList = new StringBuilder();
            AssetDatabase.Refresh();
            var guids = AssetDatabase.FindAssets("*", new string[] { "Assets/StreamingAssets" });
            foreach (var guid in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                if (AssetDatabase.IsValidFolder(path))
                {
                    continue;
                }

                if (Path.GetFileName(path) == "file_list.txt")
                {
                    continue;
                }

                var uri1 = new Uri(Application.streamingAssetsPath + "/");
                var uri2 = new Uri(Path.GetFullPath(path));
                var relPath = uri1.MakeRelativeUri(uri2).ToString();

                fileList.Append(relPath);
                fileList.Append('\n');
            }

            var fileListPath = Path.Combine(Application.streamingAssetsPath, "file_list.txt");
            File.WriteAllText(fileListPath, fileList.ToString());
        }

        public static void WriteEncryptFileListTxt(BuildPlatType buildPlatType)
        {
            var fileList = new StringBuilder();
            AssetDatabase.Refresh();
            var guids = AssetDatabase.FindAssets("*", new string[] { "Assets/StreamingAssets" });
            foreach (var guid in guids)
            {
                var path = AssetDatabase.GUIDToAssetPath(guid);
                if (AssetDatabase.IsValidFolder(path))
                {
                    continue;
                }

                if (Path.GetFileName(path) == EncryptMgr.GetEncryptPath2Base64Editor("file_list.txt"))
                {
                    continue;
                }

                var uri1 = new Uri(Application.streamingAssetsPath + "/");
                var uri2 = new Uri(Path.GetFullPath(path));
                var relPath = uri1.MakeRelativeUri(uri2).ToString();

                fileList.Append("AssetBundle/" + EncryptMgr.GetDecryptPath2Base64(relPath.Replace("AssetBundle/", "")));
                fileList.Append('\n');
            }

            var fileListPath = Path.Combine(Application.streamingAssetsPath, EncryptMgr.GetEncryptPath2Base64Editor("file_list.txt"));
            File.WriteAllText(fileListPath, fileList.ToString());
        }

        // 获得进包列表
        private static Dictionary<string, string> GetInstallBundlesList(BuildPlatType buildPlatType, InstallBundleSize sizeType, bool outPutLog = false)
        {
            if (InstallBundleSize.sizeAll == sizeType)
            {
                return GetAllInstallBundlesList(buildPlatType, sizeType);
            }
            else
            {
                return GetInstallBundlesSizeList(buildPlatType, sizeType, outPutLog);
            }
        }

        // 获得进包列表(所有进包)
        private static Dictionary<string, string> GetAllInstallBundlesList(BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            var install_list = new Dictionary<string, string>();

            ManifestInfo manifest = BuilderConfig.GetManifestInfo(buildPlatType);
            string[] bundles = manifest.GetAllAssetBundles();
            for (int i = 0; i < bundles.Length; i++)
            {
                install_list.Add(bundles[i], manifest.GetAssetBundleHash(bundles[i]));
            }

            // Lua的AssetBundleManifest
            ManifestInfo luaManifest = BuilderConfig.GetLuaManifestInfo(buildPlatType);
            bundles = luaManifest.GetAllAssetBundles();
            foreach (var bundle in bundles)
            {
                install_list.Add(bundle, luaManifest.GetAssetBundleHash(bundle));
            }

            return install_list;
        }

        // 获得进包列表（从配置中过滤出，并计算出依赖列表）
        public static Dictionary<string, string> GetInstallBundlesSizeList(BuildPlatType buildPlatType, InstallBundleSize sizeType, bool outPutLog = false)
        {
            var install_list = new Dictionary<string, string>();

            ManifestInfo manifest = BuilderConfig.GetManifestInfo(buildPlatType);

            HashSet<string> bundle_set = GetFilterBundleSet(buildPlatType, manifest, sizeType);
            Dictionary<string, List<string>> dependList;
            HashSet<string> depend_bundle_set = GetDependAssetBundles(manifest, bundle_set, out dependList, outPutLog);
            
            if (outPutLog)
            {
                OutPutDepend(dependList, buildPlatType, sizeType);
            }

            ManifestInfo luaManifest = BuilderConfig.GetLuaManifestInfo(buildPlatType);
            HashSet<string> lua_bundle_set = GetLuaAssetBundles(buildPlatType, luaManifest);

            bundle_set.UnionWith(depend_bundle_set);
            bundle_set.UnionWith(lua_bundle_set);

            foreach (var bundle_name in bundle_set)
            {
                if (lua_bundle_set.Contains(bundle_name))
                {
                    install_list.Add("LuaAssetBundle/" + bundle_name, luaManifest.GetAssetBundleHash(bundle_name));
                }
                else
                {
                    install_list.Add(bundle_name, manifest.GetAssetBundleHash(bundle_name));
                }
                
            }

            Resources.UnloadUnusedAssets();
            return install_list;
        }

        // 根据进包配置过滤出AssetBundle集合
        public static HashSet<string> GetFilterBundleSet(BuildPlatType buildPlatType, ManifestInfo manifest, InstallBundleSize sizeType, HashSet<string> unMatchLineSet = null)
        {
            string file_name = BuilderConfig.GetAssetBundleIntallFilterTxtName(sizeType);
            string path = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", file_name));
            string[] lines = File.ReadAllLines(path);
            string bundleRootDir = Path.GetFullPath(BuilderConfig.GetAssetBundlePath(buildPlatType)).Replace("\\", "/");

            HashSet<string> bundle_set = new HashSet<string>();
            HashSet<string> invalidLines = unMatchLineSet ?? new HashSet<string>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line)) continue;
                string trimmedLine = line.Trim();

                if (!trimmedLine.EndsWith("/"))
                {
                    if (!manifest.IsExistsAssetBundle(trimmedLine))
                        invalidLines.Add(trimmedLine);
                    else
                        bundle_set.Add(trimmedLine);
                    continue;
                }

                string searchDir = Path.Combine(bundleRootDir, trimmedLine).Replace("\\", "/");
                if (!Directory.Exists(searchDir))
                {
                    invalidLines.Add(trimmedLine);
                    continue;
                }

                List<string> files = new List<string>();
                DirectoryUtil.GetAllFiles(searchDir, files);
                foreach (var file in files)
                {
                    if (Path.GetExtension(file) == ".manifest") continue;
                    string bundleName = file.Replace("\\", "/").Substring(bundleRootDir.Length + 1);
                    bundle_set.Add(bundleName);
                }
            }

            return bundle_set;
        }

        // 获得依赖集合
        private static HashSet<string> GetDependAssetBundles(ManifestInfo manifest, HashSet<string> bundelsSet, out Dictionary<string, List<string>> dependList, bool outPutLog = false)
        {
            dependList = new Dictionary<string, List<string>>();
            HashSet<string> depend_set = new HashSet<string>();
            foreach (var item in bundelsSet)
            {
                List<string> depends = manifest.GetAllDependencies(item);
                for (int i = 0; i < depends.Count; i++)
                {
                    string depend = depends[i];
                    depend_set.Add(depend);

                    if (outPutLog)
                    {
                        List<string> list;
                        if (!dependList.TryGetValue(depend, out list))
                        {
                            list = new List<string>();
                            dependList.Add(depend, list);
                        }
                        list.Add(item);
                    }
                }
            }

            return depend_set;
        }

        private static HashSet<string> GetLuaAssetBundles(BuildPlatType buildPlatType, ManifestInfo manifest)
        {
            string lua_str = "lua/";
            string[] allbundles = manifest.GetAllAssetBundles();

            HashSet<string> bundle_set = new HashSet<string>();
            for (int i = 0; i < allbundles.Length; i++)
            {
                if (allbundles[i].StartsWith(lua_str))
                {
                    bundle_set.Add(allbundles[i]);
                }
            }

            return bundle_set;
        }

        public static void WriteModifyMaterialList(string[] modifyMaterialList)
        {
            var path = Path.Combine(Application.dataPath, "../MaterialList.txt");
            for (int i = 0; i < modifyMaterialList.Length; i++)
            {
                modifyMaterialList[i] = Path.Combine(Application.dataPath, string.Format("../{0}", modifyMaterialList[i]));
            }
            File.WriteAllLines(path, modifyMaterialList);
        }

        private struct PreloadItem
        {
            public string bundleName;
            public int level;
            public PreloadItem(string bundleName, int level)
            {
                this.bundleName = bundleName;
                this.level = level;
            }
        }

        private static int ComparePreloadItem(PreloadItem a, PreloadItem b)
        {
            return a.level.CompareTo(b.level);

        }

        private struct PackageItem
        {
            public string bundleName;
            public int level;
            public int timeStamp;
            public int useTimes;
            public PackageItem(string bundleName, int level, int timeStamp, int useTimes)
            {
                this.bundleName = bundleName;
                this.level = level;
                this.timeStamp = timeStamp;
                this.useTimes = useTimes;
            }
        }

        // 用于比较预加载项优先级的比较器
        private static int ComparePackageItem(PackageItem a, PackageItem b)
        {
            if (0 != a.level.CompareTo(b.level))
                return a.level.CompareTo(b.level);
            else
            {
                if (0 != a.timeStamp.CompareTo(b.timeStamp))
                    return a.timeStamp.CompareTo(b.timeStamp);
                else
                {
                    return a.useTimes.CompareTo(b.useTimes);
                }
            }
        }

        private static void OutPutDepend(Dictionary<string, List<string>> dependList, BuildPlatType buildPlatType, InstallBundleSize sizeType)
        {
            if (sizeType != InstallBundleSize.sizeS)
                return;

            string assetbundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
            string file_name = BuilderConfig.GetAssetBundleInstallListTxtName(sizeType) + "_depend";
            var path = Path.Combine(assetbundle_path, string.Format("../{0}.txt", file_name));

            var builder = new StringBuilder();
            foreach (var kv in dependList)
            {
                if (!String.IsNullOrEmpty(kv.Key))
                {
                    builder.Append(kv.Key);
                    builder.Append("\n");
                    foreach (var item in kv.Value)
                    {
                        builder.Append("\t" + item);
                        builder.Append("\n");
                    }
                    builder.Append("\n");
                }
            }

            File.WriteAllText(path, builder.ToString());
        }
    }
}
