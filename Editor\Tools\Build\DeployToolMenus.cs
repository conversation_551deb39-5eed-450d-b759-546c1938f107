﻿using System;
using UnityEditor;
using UnityEngine;
using Build;
using System.IO;

class DeployToolMenus : DeployTool
{
    #region 打包
    [MenuItem("自定义工具/发布版本/安卓/打包Android本地测试专用")]
    public static void BuildAndroidForTest()
    {
        string testApkPath = Path.Combine(Application.dataPath, "../Build/android/a1_dev_test.apk");
        BuildOptions buildOptions = BuildOptions.AllowDebugging | BuildOptions.ConnectToHost | BuildOptions.ConnectWithProfiler | BuildOptions.Development;
        BuildPipeline.BuildPlayer(new string[] { "Assets/Game/Scenes/main.unity" }, testApkPath, BuildTarget.Android, buildOptions);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android资源/Release")]
    public static void BuildReleaseAndroid()
    {
        BuildAssets(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android资源/Dev")]
    public static void BuildDevAndroid()
    {
        BuildAssets(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android资源 + 清理所有资源/Release")]
    public static void BuildClearAndAndroid()
    {
        ClearAll();
        BuildAssets(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android资源 + 清理所有资源/Dev")]
    public static void BuildClearAndDevAndroid()
    {
        ClearAll();
        BuildAssets(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包iOS资源 + 清理所有资源/Release")]
    public static void BuildClearAndiOS()
    {
        ClearAll();
        BuildAssets(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包IOS资源/Release")]
    public static void BuildReleaseIOS()
    {
        BuildAssets(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包IOS资源/Dev")]
    public static void BuildDevIOS()
    {
        BuildAssets(BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/Windows/打包Windows资源/Release")]
    public static void BuildReleaseWindows()
    {
        BuildAssets(BuildPlatType.Windows);
    }

    [MenuItem("自定义工具/发布版本/Windows/打包Windows资源/Dev")]
    public static void BuildDevWindows()
    {
        BuildAssets(BuildPlatType.WindowsDev);
    }

    [MenuItem("自定义工具/发布版本/Windows/打包Lua资源/Release")]
    public static void BuildWindowLua()
    {
        BuildLuaAssets(BuildPlatType.Windows);
    }

    [MenuItem("自定义工具/发布版本/Windows/打包Lua资源/Dev")]
    public static void BuildWindowDevLua()
    {
        BuildLuaAssets(BuildPlatType.WindowsDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Lua资源/Release")]
    public static void BuildAndroidLua()
    {
        BuildLuaAssets(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Lua资源/Dev")]
    public static void BuildAndroidDevLua()
    {
        BuildLuaAssets(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包Lua资源/Release")]
    public static void BuildIosLua()
    {
        BuildLuaAssets(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包Lua资源/Dev")]
    public static void BuildIosDevLua()
    {
        BuildLuaAssets(BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/生成Android.apk/Release")]
    public static void BuildReleaseAndroidApk()
    {
        BuildPlayer(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/生成Android.apk/Dev")]
    public static void BuildDevAndroidApk()
    {
        BuildPlayer(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/Windows/生成Windows.exe/Release")]
    public static void BuildReleaseWindowsExe()
    {
        BuildPlayer(BuildPlatType.Windows);
    }

    [MenuItem("自定义工具/发布版本/Windows/生成Windows.exe/Dev")]
    public static void BuildDevWindowsExe()
    {
        BuildPlayer(BuildPlatType.WindowsDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android库/Android")]
    public static void BuildAndroidLib()
    {
        ExportProject(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android库/AndroidDev")]
    public static void BuildAndroidDevLib()
    {
        ExportProject(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/安卓/打包Android库/AndroidGM")]
    public static void BuildAndroidGMLib()
    {
		RuntimeGUIMgr.SetIsAndroidGM(true);
        ExportProject(BuildPlatType.AndroidGM);
        RuntimeGUIMgr.SetIsAndroidGM(false);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包IOS库/IOS")]
    public static void BuildIOSLib()
    {
        ExportProject(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包IOS库/IOSDev")]
    public static void BuildIOSDevLib()
    {
        ExportProject(BuildPlatType.IOSDev);
    }

    [MenuItem("自定义工具/发布版本/iOS/打包IOS审核包-IOS库")]
    public static void BuildAuditIOSLib()
    {
        ExportProject(BuildPlatType.Audit);
    }

    [MenuItem("自定义工具/发布版本/打包材质球")]
    public static void RebuildMaterial()
    {
        // ClearMaterial();
        BuildMaterial.Build();
    }

    [MenuItem("自定义工具/发布版本/审核包/打包审核包资源")]
    public static void BuildAuditAssetBundle()
    {
        BuildAssets(BuildPlatType.Audit);
    }
    #endregion

    #region 资源拷贝
    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/整包/Release")]
    public static void InstallAndroidAssetBundlesAll()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.Android, InstallBundleSize.sizeAll);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/整包/Dev")]
    public static void InstallAndroidDevAssetBundlesAll()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.AndroidDev, InstallBundleSize.sizeAll);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/大包/Release")]
    public static void InstallAndroidAssetBundlesL()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.Android, InstallBundleSize.sizeL);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/大包/Dev")]
    public static void InstallAndroidDevAssetBundlesL()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.AndroidDev, InstallBundleSize.sizeL);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/中包/Release")]
    public static void InstallAndroidAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.Android, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/中包/Dev")]
    public static void InstallAndroidDevAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.AndroidDev, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/小包/Release")]
    public static void InstallAndroidAssetBundlesS()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.Android, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/小包/Dev")]
    public static void InstallAndroidDevAssetBundlesS()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.AndroidDev, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/小小包/Release")]
    public static void InstallAndroidAssetBundlesXS()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.Android, InstallBundleSize.sizeXS);
    }

    [MenuItem("自定义工具/发布版本/安卓/拷资源进打包目录/小小包/Dev")]
    public static void InstallAndroidDevAssetBundlesXS()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.AndroidDev, InstallBundleSize.sizeXS);
    }

    [MenuItem("自定义工具/发布版本/iOS/拷资源进打包目录/中包/Release")]
    public static void InstalliOSAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.IOS, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/iOS/拷资源进打包目录/中包/Dev")]
    public static void InstalliOSDevAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.IOSDev, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/Windows/拷资源进打包目录/中包/Release")]
    public static void InstallWindowsAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.Windows, InstallBundleSize.sizeM); // 拷贝进包列表（中包）
    }

    [MenuItem("自定义工具/发布版本/Windows/拷资源进打包目录/中包/Dev")]
    public static void InstallWindowsDevAssetBundlesM()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.WindowsDev, InstallBundleSize.sizeM); // 拷贝进包列表（中包）
    }

    [MenuItem("自定义工具/发布版本/Windows/拷资源进打包目录/小包/Release")]
    public static void InstallWindowsAssetBundlesS()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.Windows, InstallBundleSize.sizeS); // 拷贝进包列表（小包）
    }

    [MenuItem("自定义工具/发布版本/Windows/拷资源进打包目录/小包/Dev")]
    public static void InstallWindowsDevAssetBundlesS()
    {
        AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(BuildPlatType.WindowsDev, InstallBundleSize.sizeS); // 拷贝进包列表（小包）
    }

    [MenuItem("自定义工具/发布版本/审核包/拷审核资源进Streaming目录（小包）")]
    public static void InstallAuditAssetBundlesS()
    {
        AssetBundleInstaller.CopyInstallEncryptBundlesToStreamAsssets(BuildPlatType.Audit, InstallBundleSize.sizeS); // 拷贝进包列表（小包）
    }
    #endregion

    [MenuItem("自定义工具/发布版本/Windows/计算入包大小/中包/Release")]
    public static void CalcInstallMSizeWindowsRelease()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.Windows, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/Windows/计算入包大小/中包/Dev")]
    public static void CalcInstallMSizeWindowsDev()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.WindowsDev, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/大包/Release")]
    public static void CalcInstallLSizeAndroidRelease()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.Android, InstallBundleSize.sizeL);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/大包/Dev")]
    public static void CalcInstallLSizeAndroidDev()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.AndroidDev, InstallBundleSize.sizeL);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/中包/Release")]
    public static void CalcInstallMSizeAndroidRelease()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.Android, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/中包/Dev")]
    public static void CalcInstallMSizeAndroidDev()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.AndroidDev, InstallBundleSize.sizeM);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/小包/Release")]
    public static void CalcInstallSSizeAndroidRelease()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.Android, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/小包/Dev")]
    public static void CalcInstallSSizeAndroidDev()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.AndroidDev, InstallBundleSize.sizeS);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/小小包/Release")]
    public static void CalcInstallXSSizeAndroidRelease()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.Android, InstallBundleSize.sizeXS);
    }

    [MenuItem("自定义工具/发布版本/安卓/计算入包大小/小小包/Dev")]
    public static void CalcInstallXSSizeAndroidDev()
    {
        AssetBundleInstaller.CalcTotalSize(BuildPlatType.AndroidDev, InstallBundleSize.sizeXS);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/安卓/Release")]
    public static void WriteAndroidReleaseInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.Android, true);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/安卓/Dev")]
    public static void WriteAndroidDevInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.AndroidDev, true);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/Windows/Release")]
    public static void WriteWindowsReleaseInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.Windows, true);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/Windows/Dev")]
    public static void WriteWindowsDevInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.WindowsDev, true);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/iOS/Release")]
    public static void WriteiOSReleaseInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.IOS, true);
    }

    [MenuItem("自定义工具/发布版本/生成入包列表/iOS/Dev")]
    public static void WriteiOSDevInstallBundlesList()
    {
        AssetBundleInstaller.WriteInstallBundlesList(BuildPlatType.IOSDev, true);
    }

    [MenuItem("自定义工具/发布版本/生成怪物包围盒配置")]
    public static void BuildBossAndMonsterBound()
    {
        BuildActorBounds.Build();
    }

    [MenuItem("自定义工具/发布版本/编译Lua代码")]
    public static void BuildLua()
    {
        LuaTool.BuildLuaAll();
    }

    [MenuItem("自定义工具/发布版本/生成强更列表/Android/Release")]
    public static void BuildStrongUpdateLuaFileAndroidRelease()
    {
        BuildStrongUpdateLuaFile(BuildPlatType.Android);
    }

    [MenuItem("自定义工具/发布版本/生成强更列表/Android/Dev")]
    public static void BuildStrongUpdateLuaFileAndroidDev()
    {
        BuildStrongUpdateLuaFile(BuildPlatType.AndroidDev);
    }

    [MenuItem("自定义工具/发布版本/生成强更列表/iOS/Release")]
    public static void BuildStrongUpdateLuaFileIOSRelease()
    {
        BuildStrongUpdateLuaFile(BuildPlatType.IOS);
    }

    [MenuItem("自定义工具/发布版本/生成强更列表/iOS/Dev")]
    public static void BuildStrongUpdateLuaFileIOSDev()
    {
        BuildStrongUpdateLuaFile(BuildPlatType.IOSDev);
    }

    static void BuildStrongUpdateLuaFile(BuildPlatType buildPlatType)
    {
        BuildText.Build();
        // 构建Actor体积大小配置表
        BuildActorBounds.Build();
        // 生成各种入包文件
        AssetBundleInstaller.BuildBundleInstallFile(buildPlatType);
        //构建预下载列表文件(以小包来做剔除）
        AssetBundleInstaller.BuildPreDownLoadLuaFile(InstallBundleSize.sizeS, buildPlatType);
        AssetBundleInstaller.BuildStrongUpdateLuaFile(InstallBundleSize.sizeS);
        // 构建分包
        AssetBundleInstaller.BuildSubPackage(buildPlatType);
    }

    #region 清理
    [MenuItem("自定义工具/发布版本/清理/一键清理所有")]
    public static void ClearAll()
    {
        ClearUnuseAsset();
        ClearMaterial();
        ClearScene();
        ClearActor();
        ClearEffect();
        ClearCG();
    }

    [MenuItem("自定义工具/发布版本/清理/清理无用资源")]
    public static void ClearUnuseAsset()
    {
        UnuseAssetCleaner.DelAllUnUseAsset();
    }

    [MenuItem("自定义工具/发布版本/清理/清理Environments目录下废弃资源(fbx,贴图)")]
    public static void ClearUnUseEnvironments()
    {
        UnuseAssetCleaner.DelAllUnUseEnvironments();
    }

    [MenuItem("自定义工具/发布版本/清理/清理Actors目录下废弃资源(fbx,贴图)")]
    public static void ClearUnUseActors()
    {
        UnuseAssetCleaner.DelAllUnUseActors();
    }

    [MenuItem("自定义工具/发布版本/清理/清理材质球")]
    public static void ClearMaterial()
    {
        MaterialCleaner.ClearUnityDefaultShader();
        MaterialCleaner.ClearlDaultStyleMaterial();
        MaterialCleaner.ClearUnExistsKeyword();
        MaterialCleaner.ClearInvalieProperties();
    }

    [MenuItem("自定义工具/发布版本/清理/清理场景")]
    public static void ClearScene()
    {
        SceneCleaner.ClearScene();
    }

    [MenuItem("自定义工具/发布版本/清理/清理角色")]
    public static void ClearActor()
    {
        ActorCleaner.ClearActor();
    }

    [MenuItem("自定义工具/发布版本/清理/清理特效")]
    public static void ClearEffect()
    {
        EffectCleaner.ClearEffect();
    }

    [MenuItem("自定义工具/发布版本/清理/清理CG")]
    public static void ClearCG()
    {
        CGCleaner.ClearCG();
    }

    [MenuItem("自定义工具/发布版本/清理/清理Audio")]
    public static void ClearAudio()
    {
        AudioCleaner.ClearAudio();
    }

    [MenuItem("自定义工具/发布版本/清理/清理组件")]
    public static void ClearComponent()
    {
        ComponentCleaner.ClearComponent();
    }

    [MenuItem("自定义工具/发布版本/清理/输出清理报告")]
    public static void CleanReport()
    {
        CleanReporter.OutputCleanReport();
    }
    #endregion

    [MenuItem("自定义工具/发布版本/检查预加载配置")]
    public static void BuildCheckPreDownloadConfig()
    {
        CheckPreDownloadConfig();
    }

    [MenuItem("自定义工具/发布版本/整包资源加密/加密")]
    public static void EncryptStreammingAsset()
    {
        EncryptMgr.EncryptSteamFiles();
    }

    [MenuItem("自定义工具/发布版本/整包资源加密/解密")]
    public static void DecryptStreammingAsset()
    {
        EncryptMgr.DecryptSteamFiles();
    }

    [MenuItem("自定义工具/发布版本/WriteFileList")]
    public static void WriteFileList()
    {
        AssetBundleInstaller.WriteFileListTxt();
    }

    public static void BuildReleaseWindowsByPy()
    {
        BuildReleaseWindows();
    }

    public static void BuildDevWindowsByPy()
    {
        BuildDevWindows();
    }

    public static void BuildDevWindowsExeByPy() 
    {
        BuildPlayer(BuildPlatType.WindowsDev, true);
    }

    public static void BuildAndroidLibByPy()
    {
        ReportBuildLog("ExportProject Start!!!");
        ExportProject(BuildPlatType.Android, true);
    }

    public static void BuildAndroidDevLibByPy()
    {
        ReportBuildLog("ExportProject Start!!!");
        ExportProject(BuildPlatType.AndroidDev, true);
    }

    public static void BuildAndroidGMLibByPy()
    {
        ReportBuildLog("ExportProject Start!!!");
        RuntimeGUIMgr.SetIsAndroidGM(true);
        ExportProject(BuildPlatType.AndroidGM, true);
        RuntimeGUIMgr.SetIsAndroidGM(false);
    }

    public static void BuildIOSLibByPy()
    {
        ReportBuildLog("ExportProject Start!!!");
        ExportProject(BuildPlatType.IOS, true);
    }

    public static void BuildIOSDevLibByPy()
    {
        ReportBuildLog("ExportProject Start!!!");
        ExportProject(BuildPlatType.IOSDev, true);
    }
}