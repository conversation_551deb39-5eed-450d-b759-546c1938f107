﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;

public class ActorRenderEffectPlayer
{
    #region Object Pool

    private static ObjectPool<ActorRenderEffectPlayer> s_PlayerPool = new ObjectPool<ActorRenderEffectPlayer>(null);
    
    public static ActorRenderEffectPlayer Get(ActorRender actorRender)
    {
        var player = s_PlayerPool.Get();
        player.Setup(actorRender);
        return player;
    }
    
    public static void Release(ActorRenderEffectPlayer player)
    {
        s_PlayerPool.Release(player);
    }
    
    #endregion

    private static readonly string ENABLE_POST_EFFECT = "ENABLE_POST_EFFECT";
    private static readonly int _PostEffectType = Shader.PropertyToID("_PostEffectType");
    
    public  ActorRender actorRender;

    private List<ActorRenderEffect> m_Effects;
    private MaterialPropertyBlock m_MaterialPropertyBlock;
    private Dictionary<ActorRenderEffectType, ActorRenderEffect> m_Type2EffectDict;
    private int m_BitMask;

    public int BitMask
    {
        get => m_BitMask;
        set
        {
            m_BitMask = value;
            m_MaterialPropertyBlock.SetFloat(_PostEffectType, m_BitMask);
        }
    }

    public ActorRenderEffectPlayer()
    {
        this.m_MaterialPropertyBlock = new MaterialPropertyBlock();
        this.m_Effects = new List<ActorRenderEffect>();
        this.m_Type2EffectDict = new Dictionary<ActorRenderEffectType, ActorRenderEffect>();
        this.m_BitMask = 0;
    }

    public void Setup(ActorRender actorRender)
    {
        this.actorRender = actorRender;
        this.m_BitMask = 0;
    }

    public void Execute(ActorRenderEffectType type, bool state = true)
    { 
        ActorRenderEffect e = GetEffect(type);
        if (state)
        {
            int mask = BitMask;
            BitMask |= 1 << (int)e.type;
        
            e.startedTime = Time.time;

            if (e.Life > 0 && !m_Effects.Contains(e))
                m_Effects.Add(e);
        
            e.OnEnter(m_MaterialPropertyBlock);
        
            var list = this.actorRender.RenderList;
            for (int i = 0; i < list.Count; i++)
            {
                var item = list[i];
                if (item.renderer != null)
                {
                    item.renderer.SetPropertyBlock(m_MaterialPropertyBlock);
                    if (BitMask != mask)
                    {
                        Material[] materials = MaterialMgr.Instance.GetClonedMaterials(item.renderer);
                        foreach (Material material in materials)
                        {
                            if (material != null)
                                material.EnableKeyword(ENABLE_POST_EFFECT);
                        }
                    }
                }
            }
        }
        else
        {
            Stop(e);
        }
        
    }


    public void Update()
    {
        int mask = BitMask;
        for (int i = 0; i < m_Effects.Count; i++)
        {
            var e = m_Effects[i];
            if (!e.OnUpdate(m_MaterialPropertyBlock))
            {
                Stop(e);
            }
        }

        bool needUpdateKeyword = BitMask != mask && BitMask == 0;
        var list = this.actorRender.RenderList;
        for (int i = 0; i < list.Count; i++)
        {
            var item = list[i];
            if (item.renderer != null)
            {
                item.renderer.SetPropertyBlock(m_MaterialPropertyBlock);
                if (needUpdateKeyword)
                {
                    Material[] materials = MaterialMgr.Instance.GetClonedMaterials(item.renderer);
                    foreach (Material material in materials)
                    {
                        if (material != null)
                            material.DisableKeyword(ENABLE_POST_EFFECT);
                    }
                }
            }
        }
    }
    
    public void SetMultiColor(bool state, Color color)
    { 
        ActorRenderMultiColorEffect e = GetEffect(ActorRenderEffectType.MultiColor) as ActorRenderMultiColorEffect;
        if (state)
            e.Color = color;
        Execute(ActorRenderEffectType.MultiColor, state);
    }
    
    public void StopAll()
    {
        BitMask = 0;
        m_Effects.Clear();
        var list = this.actorRender.RenderList;
        for (int i = 0; i < list.Count; i++)
        {
            var item = list[i];
            if (item.renderer != null)
            {
                Material[] materials = MaterialMgr.Instance.GetClonedMaterials(item.renderer);
                item.renderer.SetPropertyBlock(m_MaterialPropertyBlock);
                foreach (Material material in materials)
                {
                    if (material != null)
                        material.DisableKeyword(ENABLE_POST_EFFECT);
                }
            }
        }
    }
    
    public void Stop(ActorRenderEffect e)
    {
        e.OnExit(m_MaterialPropertyBlock);
        BitMask &= ~(1 << (int)e.type);
        if (m_Effects.Contains(e))
            m_Effects.Remove(e);
    }

    private ActorRenderEffect GetEffect(ActorRenderEffectType type)
    {
        if (!m_Type2EffectDict.TryGetValue(type, out ActorRenderEffect e))
        {
            if (type == ActorRenderEffectType.Blink)
                e = new ActorRenderBlinkEffect();
            else if (type == ActorRenderEffectType.Dissolve)
                e = new ActorRenderDissolveEffect();
            else if (type == ActorRenderEffectType.MultiColor)
                e = new ActorRenderMultiColorEffect();
            else if (type == ActorRenderEffectType.Gray)
                e = new ActorRenderGrayEffect();
            
            e.type = type;
            m_Type2EffectDict.Add(type, e);
        }

        return e;
    }
}

public enum ActorRenderEffectType
{
    Blink,
    Dissolve,
    MultiColor,
    Gray,
}

public abstract class ActorRenderEffect
{
    public float startedTime;
    public ActorRenderEffectType type;
    public virtual float Life { get; }
    public float Progress => Mathf.Min((Time.time - startedTime) / Life, 1.0f);

    protected ActorRenderEffectData Data => ActorRenderEffectData.Instance;
    
    public virtual void OnEnter(MaterialPropertyBlock block)
    {

    }
    public virtual void OnExit(MaterialPropertyBlock block)
    {

    }
    public virtual bool OnUpdate(MaterialPropertyBlock block)
    {
        return false;
    }
}

public class ActorRenderBlinkEffect : ActorRenderEffect
{
    private int _BlinkIntensity = Shader.PropertyToID("_BlinkIntensity");  
    public override float Life => Data.blinkLife;
    public override bool OnUpdate(MaterialPropertyBlock block)
    {
        float prog = Progress;
        float v = Data.blinkCurve.Evaluate(prog);
        block.SetFloat(_BlinkIntensity, v);
        return prog < 1;
    }
}

public class ActorRenderDissolveEffect : ActorRenderEffect
{
    private int _DissolveIntensity = Shader.PropertyToID("_DissolveIntensity");  
    public override float Life => Data.dissolveLife;
    public override bool OnUpdate(MaterialPropertyBlock block)
    {
        float prog = Progress;
        float v = Data.dissolveCurve.Evaluate(prog);
        block.SetFloat(_DissolveIntensity, v);
        return prog < 1;
    }
}

public class ActorRenderMultiColorEffect : ActorRenderEffect
{
    private int _ColorGradingEffect = Shader.PropertyToID("_EffectMultiColor");  
    public Color Color;
    public override void OnEnter(MaterialPropertyBlock block)
    {
        block.SetColor(_ColorGradingEffect, Color);
    }
}

public class ActorRenderGrayEffect : ActorRenderEffect
{

}