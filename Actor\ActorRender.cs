﻿using Game;
using LuaInterface;
using System;
using System.Collections.Generic;
using UnityEngine;
using Nirvana;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class ActorRender : MonoBehaviour
{
    [SerializeField]
    private List<RenderItem> renderList = new List<RenderItem>();
    private List<uint> defaultRenderLayerMaskList = new List<uint>();       // 默认渲染层掩码备份
    private List<int> defaultRendergameObjectLayerList = new List<int>();

    private int materialQuality = -1;
    private bool isDisableAllAttachEffect = false;
    private int effectQualityLevel = -1;
    private bool isGray = false;
    private bool isHdTexture = false;
    private bool isMultiColor = false;
    private bool isChangeLaryMask = false;
    private bool isCulled = false;
    private Color multiColor;
    private bool isChangeMainTex = false;
    private bool isChangeRenderGameObjectLary = false;
    private Dictionary<Renderer, Texture> mainDir = new Dictionary<Renderer, Texture>();

    private GameObjectAttach[] allAttachEffects;
    private MeshRenderer[] meshRenderers = null;
    private AnimatorCull animatorCull;
    private JYCharacterShadow characterShadow;
    private ActorRenderEffectPlayer effectPlayer;
    private bool isLerpProbe = false;

    // 特效数据结构和管理
    [System.Serializable]
    public class EffectAttachmentData
    {
        public string nodePath;           // 节点路径（相对于根节点）
        public string nodeName;           // 节点名称
        public AssetID asset;             // 特效资源ID
        public float delayTime;           // 延迟时间
        public Vector3 localPosition;     // 本地位置
        public Vector3 localRotation;     // 本地旋转
        public Vector3 localScale;        // 本地缩放
        public ChangeSkin.SkinType skinType; // 所属部位类型
    }

    [SerializeField]
    private List<EffectAttachmentData> effectAttachments = new List<EffectAttachmentData>();
    
    // 动态创建的特效节点记录（用于清理）
    private HashSet<Transform> dynamicEffectNodes = new HashSet<Transform>();

    // 特效节点名称到部位类型的映射
    private static readonly Dictionary<string, ChangeSkin.SkinType> EffectNodeSkinMap = new Dictionary<string, ChangeSkin.SkinType>
    {
        // 根据实际的特效节点命名规则进行映射
        { "RoleBody", ChangeSkin.SkinType.Body },
        { "RoleFace", ChangeSkin.SkinType.Face },
        { "RoleHair", ChangeSkin.SkinType.Hair },
        { "RoleEyeball", ChangeSkin.SkinType.Eyeball },
    };

    // 节点查找缓存和对象池
    private Dictionary<string, Transform> nodePathCache = new Dictionary<string, Transform>();
    private Dictionary<string, Transform> nodeNameCache = new Dictionary<string, Transform>();
    private bool isCacheBuilt = false;
    private readonly object cacheLock = new object();
    
    // 临时集合对象池
    // 特效数据
    private static readonly UnityEngine.Pool.ObjectPool<List<EffectAttachmentData>> EffectListPool = 
        new UnityEngine.Pool.ObjectPool<List<EffectAttachmentData>>(
            createFunc: () => new List<EffectAttachmentData>(),
            actionOnGet: list => list.Clear(),
            actionOnRelease: list => list.Clear(),
            collectionCheck: false,
            defaultCapacity: 3
        );
    
    // GameObjectAttach
    private static readonly UnityEngine.Pool.ObjectPool<List<GameObjectAttach>> AttachListPool = 
        new UnityEngine.Pool.ObjectPool<List<GameObjectAttach>>(
            createFunc: () => new List<GameObjectAttach>(),
            actionOnGet: list => list.Clear(),
            actionOnRelease: list => list.Clear(),
            collectionCheck: false,
            defaultCapacity: 3
        );
    
    // 特效节点
    private static readonly UnityEngine.Pool.ObjectPool<List<Transform>> TransformListPool = 
        new UnityEngine.Pool.ObjectPool<List<Transform>>(
            createFunc: () => new List<Transform>(),
            actionOnGet: list => list.Clear(),
            actionOnRelease: list => list.Clear(),
            collectionCheck: false,
            defaultCapacity: 3
        );
    
    private static readonly UnityEngine.Pool.ObjectPool<Dictionary<string, Transform>> DictPool = 
        new UnityEngine.Pool.ObjectPool<Dictionary<string, Transform>>(
            createFunc: () => new Dictionary<string, Transform>(),
            actionOnGet: dict => dict.Clear(),
            actionOnRelease: dict => dict.Clear(),
            collectionCheck: false,
            defaultCapacity: 2
        );

    [NoToLua]
    public List<RenderItem> RenderList
    {
        get
        {
            return renderList;
        }
    }

    [NoToLua]
    public List<EffectAttachmentData> EffectAttachments
    {
        get
        {
            return effectAttachments;
        }
    }

    private void Awake()
    {
        multiColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
        allAttachEffects = GetAllAttachEffects();
        meshRenderers = this.GetMeshRenderers();
        this.SetDefaultRenderingLayerMask();
        this.SetDefaultRendererGameObjectLayer();
    }

    private void OnEnable()
    {
        GetCharacterShadow()?.Validate();
        effectPlayer = ActorRenderEffectPlayer.Get(this);
    }

    private void OnDisable()
    {
        GetCharacterShadow()?.Validate();
        if (effectPlayer != null)
        {
            ActorRenderEffectPlayer.Release(effectPlayer);
            effectPlayer = null;
        }
    }

    private void Update()
    {
        this.effectPlayer?.Update();
    }

    public List<RenderItem> GetRenderList()
    {
        return renderList;
    }

    public void SetRenderList(List<RenderItem> renderList)
    {
        this.renderList = renderList;
        this.SetDefaultRenderingLayerMask();
    }

    public void UpdateRender(Renderer rd, Material[] pbrs)
    {
        if (rd == null)
        {
            return;
        }

        for (int i = 0; i < this.renderList.Count; i++)
        {
            Renderer renderer = renderList[i].renderer;
            if (null == renderer) continue;

            if (renderer == rd)
            {
                RenderItem item = renderList[i];
                item.pbrMaterials = pbrs;
                renderList[i] = item;
            }
        }
    }

    // 提供外部直接获取Renderers，避免用GetComponentInChildren
    public Renderer[] GetRenderers()
    {
        int count = renderList.Count;
        Renderer[] renderers = new Renderer[count];
        for (int i = 0; i < count; i++)
        {
            renderers[i] = renderList[i].renderer;
        }

        return renderers;
    }

    // 提供外部直接获取MeshRenderers，避免用GetComponentInChildren
    public MeshRenderer[] GetMeshRenderers()
    {
        if (null != meshRenderers)
        {
            return meshRenderers;
        }

        int count = renderList.Count;
        int skinneMeshRendererCount = 0;
        MeshRenderer renderer = null;
        for (int i = 0; i < count; i++)
        {
            renderer = renderList[i].renderer as MeshRenderer;
            if (null != renderer)
            {
                ++skinneMeshRendererCount;
            }
        }

        meshRenderers = new MeshRenderer[skinneMeshRendererCount];
        skinneMeshRendererCount = 0;
        for (int i = 0; i < count; i++)
        {
            renderer = renderList[i].renderer as MeshRenderer;
            if (null != renderer)
            {
                meshRenderers[skinneMeshRendererCount++] = renderer;
            }
        }

        return meshRenderers;
    }

    private GameObjectAttach[] GetAllAttachEffects()
    {
        if (null != allAttachEffects) return allAttachEffects;

        allAttachEffects = this.GetComponentsInChildren<GameObjectAttach>();
        return allAttachEffects;
    }

    public Animator GetAnimator()
    {
        return GetAnimatorCull().TryInitAnimator();
    }

    public AnimatorCull GetAnimatorCull()
    {
        if (null != animatorCull) return animatorCull;
        animatorCull = this.GetOrAddComponent<AnimatorCull>();
        return animatorCull;
    }

    public JYCharacterShadow GetCharacterShadow()
    {
        if (characterShadow == null)
        {
            characterShadow = this.GetComponentInParent<JYCharacterShadow>();
        }
        return characterShadow;
    }

    // 提供外部直接获取SkinnedMeshRenderers，避免用GetComponentInChildren
    public SkinnedMeshRenderer[] GetSkinnedMeshRenderers()
    {
        int count = renderList.Count;
        int skinneMeshRendererCount = 0;
        SkinnedMeshRenderer renderer = null;
        for (int i = 0; i < count; i++)
        {
            renderer = renderList[i].renderer as SkinnedMeshRenderer;
            if (null != renderer)
            {
                ++skinneMeshRendererCount;
            }
        }

        SkinnedMeshRenderer[] renderers = new SkinnedMeshRenderer[skinneMeshRendererCount];
        skinneMeshRendererCount = 0;
        for (int i = 0; i < count; i++)
        {
            renderer = renderList[i].renderer as SkinnedMeshRenderer;
            if (null != renderer)
            {
                renderers[skinneMeshRendererCount++] = renderer;
            }
        }

        return renderers;
    }

    public int GetMaterialQuality()
    {
        return this.materialQuality;
    }

    // 品质切换
    public void SetMaterialQuality(int materialQuality)
    {
        if (this.materialQuality != materialQuality)
        {
            this.materialQuality = materialQuality;
            //this.UpdateSharedMaterial();
        }
    }

    // 开关材质球 keyword
    public void SetMaterialKeyword(string keyword, bool isEnable)
    {
        RenderItem item;
        Material[] cloneMaterials = null;
        Material cloneMaterial = null;
        for (int i = 0; i < renderList.Count; i++)
        {
            item = renderList[i];
            if (null != item.renderer)
            {
                cloneMaterials = MaterialMgr.Instance.GetClonedMaterials(item.renderer);
                if (null == cloneMaterials) continue;

                for (int j = 0; j < cloneMaterials.Length; j++)
                {
                    cloneMaterial = cloneMaterials[j];
                    if (isEnable) cloneMaterial.EnableKeyword(keyword);
                    else cloneMaterial.DisableKeyword(keyword);
                }
            }
        }
    }

    // 材质球 pbr和ui切换（ui材质已无）
    private void UpdateSharedMaterial()
    {
        for (int i = 0; i < renderList.Count; i++)
        {
            var item = renderList[i];
            if (null == item.renderer)
            {
                continue;
            }

            var pbrMaterials = item.pbrMaterials;
            foreach (Material mat in pbrMaterials)
            {
                this.TryEnableGPUInstancing(mat);
            }
            MaterialMgr.Instance.SetSharedMaterial(item.renderer, item.pbrMaterials);

            item.renderer.enabled = true;

            if (isGray)
            {
                UpdateGray();
            }

            if (isMultiColor)
            {
                UpdateMultiColor();
            }
        }
    }

    // 尝试开启GPUInstancing。注意GPUInstancing只有在OpenGL 3.1以上才全部支持。不支持手机将关掉
    private void TryEnableGPUInstancing(Material material)
    {
        if (null == material) return;

        bool isEanbleGPUInstancing = SceneOptimizeMgr.GetIsEnableGPUInstancing();
        if (!isEanbleGPUInstancing)
        {
            material.enableInstancing = false;
        }
        else
        {
            if (material.enableInstancing)
            {
                GPUInstancingOrderGroup.RefreshRenderOrder(this.gameObject, this.GetMeshRenderers());
            }
        }
    }

    // 是否设置render的object的layer为CastShadow层
    public void SetIsCastShadow(bool isCastShadow)
    {
        // for (int i = 0; i < renderList.Count; i++)
        // {
        //     Renderer renderer = renderList[i].renderer;
        //     if (null == renderer) continue;
        //
        //     renderer.gameObject.layer = isCastShadow & !renderList[i].notCastShadow ? GameLayers.CastShadow : GameLayers.Default;
        // }
    }

    #region 渲染层掩码设置
    private void SetDefaultRenderingLayerMask()
    {
        this.defaultRenderLayerMaskList.Clear();
        if (this.renderList == null || this.renderList.Count == 0)
        {
            return;
        }

        for (int i = 0; i < this.renderList.Count; i++)
        {
            if (this.renderList[i].renderer == null)
            {
                Debug.LogErrorFormat("模型{0}挂载的ActorRender组件丢失render绑定，请策划和程序检查", this.gameObject.name.Replace("(Clone)", ""));
                continue;
            }
            this.defaultRenderLayerMaskList.Add(this.renderList[i].renderer.renderingLayerMask);
        }
    }

    public void SetRendererRenderingLayerMask(int layer, bool isOpen)
    {
        if (renderList == null || renderList.Count == 0)
        {
            return;
        }

        this.isChangeLaryMask = true;
        for (int i = 0; i < renderList.Count; i++)
        {
            if (isOpen)
            {
                renderList[i].renderer.renderingLayerMask |= (uint)(1 << layer);
            }
            else
            {
                renderList[i].renderer.renderingLayerMask &= (uint)~(1 << layer);
            }
        }
    }

    public void ResetRendererRenderingLayerMask()
    {
        if (!this.isChangeLaryMask) return;
        if (renderList == null || renderList.Count == 0)
        {
            return;
        }

        for (int i = 0; i < renderList.Count; i++)
        {
            renderList[i].renderer.renderingLayerMask = this.defaultRenderLayerMaskList[i];
        }
    }

    // 设置水面反射效果
    public void SetIsWaterSurfaceReflection(bool isOpen)
    {
        SetRendererRenderingLayerMask(1, isOpen);
    }
    #endregion

    #region 遍历处理object下的绑定GameObjectAttach的特效
    public void SetIsDisableAllAttachEffects(bool isDisableAllAttachEffect, int effectQualityLevel = -1)
    {
        if (this.isDisableAllAttachEffect == isDisableAllAttachEffect && this.effectQualityLevel == effectQualityLevel)
        {
            return;
        }

        this.isDisableAllAttachEffect = isDisableAllAttachEffect;
        this.effectQualityLevel = effectQualityLevel;

        if (null == this.allAttachEffects)
        {
            this.allAttachEffects = this.GetComponentsInChildren<GameObjectAttach>();
        }

        for (int i = 0; i < this.allAttachEffects.Length; i++)
        {
            if (null != this.allAttachEffects[i])
            {
                this.allAttachEffects[i].SetIsSceneOptimize(isDisableAllAttachEffect, effectQualityLevel);
            }
        }
    }
    #endregion

    #region 表现效果修改、材质修改
    #region 改变材质主贴图
    public void SetMainTexture(Texture tex)
    {
        for (int i = 0; i < renderList.Count; i++)
        {
            var item = renderList[i];
            if (null != item.renderer)
            {
                var cloneMaterial = MaterialMgr.Instance.GetClonedMaterial(item.renderer);
                if (null == cloneMaterial) continue;

                if (!this.mainDir.ContainsKey(item.renderer))
                {
                    this.mainDir.Add(item.renderer, cloneMaterial.mainTexture);
                }

                this.isChangeMainTex = true;

                cloneMaterial.mainTexture = tex;
            }
        }
    }

    public void ResetMainTexture()
    {
        if (this.isChangeMainTex)
        {
            foreach (var v in this.mainDir)
            {
                if (v.Key != null && v.Key.material.mainTexture != v.Value)
                {
                    v.Key.material.mainTexture = v.Value;
                }
            }
        }

        this.isChangeMainTex = false;
        this.mainDir.Clear();
    }
    #endregion

    // 是否是高清材质
    public void SetIsHdTexture(bool isHdTexture)
    {
        if (this.isHdTexture != isHdTexture)
        {
            this.isHdTexture = isHdTexture;
            this.SetMaterialKeyword("ENABLE_HD_TEX", isHdTexture);
        }
    }

    public void StopAllRenderEffects()
    {
        this.isMultiColor = false;
        this.isGray = false;
        this.effectPlayer?.StopAll();
    }

    // 变灰
    public void SetIsGray(bool isGray)
    {
        if (this.isGray != isGray)
        {
            this.isGray = isGray;
            UpdateGray();
        }
    }

    private void UpdateGray()
    {
        this.effectPlayer?.Execute(ActorRenderEffectType.Gray, isGray);
    }

    #region 混合颜色
    public void SetIsMultiColor(bool isMultiColor, Color color)
    {
        if (this.isMultiColor != isMultiColor || this.multiColor != color)
        {
            this.isMultiColor = isMultiColor;
            this.multiColor = color;
            UpdateMultiColor();
        }
    }

    private void UpdateMultiColor()
    {
        this.effectPlayer?.SetMultiColor(isMultiColor, this.multiColor);
    }
    #endregion

    // 确保effectPlayer已初始化
    private void EnsureEffectPlayer()
    {
        if (effectPlayer == null)
        {
            effectPlayer = ActorRenderEffectPlayer.Get(this);
        }
    }

    // 溶解
    public void PlayDissolveEffect()
    {
        EnsureEffectPlayer();
        this.effectPlayer?.Execute(ActorRenderEffectType.Dissolve);
    }

    // 过渡
    public void PlayFadeEffect(bool isFadeIn, float time, Action callback)
    {
        EnsureEffectPlayer();
        this.effectPlayer?.Execute(ActorRenderEffectType.Blink);
    }

    // 闪烁
    public void PlayBlinkEffect()
    {
        EnsureEffectPlayer();
        this.effectPlayer?.Execute(ActorRenderEffectType.Blink);
    }

    // 模型染色（预留接口）
    public void SetDyeingColor(bool isDyeing, Color color, string json = "")
    {

    }

    // 探针混合
    public void SetIsLerpProbe(bool isLerpProbe, bool isForce = false)
    {
        if (this.isLerpProbe != isLerpProbe || isForce)
        {
            this.isLerpProbe = isLerpProbe;
            this.SetMaterialKeyword("_PROBE_BLEND", isLerpProbe);
        }
    }
    #endregion

    // 设置是否剔除
    public void SetIsCulled(bool isCulled)
    {
        if (this.isCulled == isCulled) return;

        this.isCulled = isCulled;

        this.GetAnimatorCull().SetIsCulled(isCulled);

        // var layer = isCulled ? GameLayers.Invisible : GameLayers.CastShadow;
        // for (int i = 0; i < renderList.Count; i++)
        // {
        //     var renderer = renderList[i].renderer;
        //     if (null != renderer)
        //     {
        //         renderer.gameObject.layer = layer;
        //     }
        // }

        var attachEffects = this.GetAllAttachEffects();
        for (int i = 0; i < attachEffects.Length; i++)
        {
            attachEffects[i].SetIsDisableEffect(isCulled);
        }
    }

    #region 渲染物体层级展示修改
    public void SetDefaultRendererGameObjectLayer()
    {
        this.defaultRendergameObjectLayerList.Clear();
        if (this.renderList == null || this.renderList.Count == 0)
        {
            return;
        }

        for (int i = 0; i < this.renderList.Count; i++)
        {
            if (this.renderList[i].renderer == null)
            {
                Debug.LogErrorFormat("模型{0}挂载的ActorRender组件丢失render绑定，请策划和程序检查", this.gameObject.name.Replace("(Clone)", ""));
                continue;
            }
            this.defaultRendergameObjectLayerList.Add(this.renderList[i].renderer.gameObject.layer);
        }
    }

    public void SetRendererGameObjectLayer(int layer)
    {
        if (renderList == null || renderList.Count == 0)
        {
            return;
        }

        this.isChangeRenderGameObjectLary = true;
        for (int i = 0; i < renderList.Count; i++)
        {
            renderList[i].renderer.gameObject.layer = layer;
        }
    }

    public void ResetRendererGameObjectLayer()
    {
        if (!this.isChangeRenderGameObjectLary) return;
        if (renderList == null || renderList.Count == 0)
        {
            return;
        }

        this.isChangeRenderGameObjectLary = false;
        for (int i = 0; i < renderList.Count; i++)
        {
            renderList[i].renderer.gameObject.layer = this.defaultRendergameObjectLayerList[i];
        }
    }
    #endregion




#if UNITY_EDITOR
    [NoToLua]
    public void AutoFetch()
    {
        Renderer[] renderers = this.GetComponentsInChildren<Renderer>();
        List<RenderItem> list = new List<RenderItem>();
        foreach (var render in renderers)
        {
            if (render.GetComponent<ParticleSystem>() || render.GetComponentInParent<GameObjectAttach>())
            {
                continue;
            }

            if (render.GetComponent<MeshRenderer>())
            {
                Debug.LogError(this.gameObject.name + "，该资源有节点使用了MeshRendener，请检查，并根据情况改为SkinnedMeshRenderer");
            }

            bool notCastShadow = false;
            foreach (var renderItem in renderList)
            {
                if (renderItem.renderer == render)
                {
                    notCastShadow = renderItem.notCastShadow;
                    break;
                }
            }

            var sharedMaterials = render.sharedMaterials;
            RenderItem item = new RenderItem();
            item.renderer = render;
            item.notCastShadow = notCastShadow;
            item.pbrMaterials = sharedMaterials;
            list.Add(item);
        }

        SetRenderList(list);
        
        // 自动收集特效数据
        CollectEffectAttachments();
        
        EditorUtility.SetDirty(this.gameObject);
        AssetDatabase.SaveAssets();
    }

    /// <summary>
    /// 收集当前对象下的所有GameObjectAttach组件数据
    /// </summary>
    [NoToLua]
    public void CollectEffectAttachments()
    {
        effectAttachments.Clear();
        
        // 检查是否为Assets/Game/Actors路径下的预制体
        string prefabPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(this.gameObject);
        if (string.IsNullOrEmpty(prefabPath) || !prefabPath.StartsWith("Assets/Game/Actors"))
        {
            return;
        }
        
        ChangeSkin.SkinType skinType = GetActorRenderSkinType();
        
        var attachments = GetComponentsInChildren<GameObjectAttach>(true);
        
        // 预分配容量避免扩容
        if (effectAttachments.Capacity < attachments.Length)
        {
            effectAttachments.Capacity = attachments.Length;
        }
        
        foreach (var attachment in attachments)
        {
            if (attachment == null) continue;
            
            string nodePath = GetNodePath(attachment.transform);
            
            var effectData = new EffectAttachmentData
            {
                nodePath = nodePath,
                nodeName = attachment.transform.name,
                asset = attachment.Asset,
                delayTime = attachment.delayTime,
                localPosition = attachment.transform.localPosition,
                localRotation = attachment.transform.localEulerAngles,
                localScale = attachment.transform.localScale,
                skinType = skinType
            };
            
            effectAttachments.Add(effectData);
        }
        
        // 重建缓存以包含新收集的特效节点
        ClearNodeCache();
    }

    /// <summary>
    /// 获取当前ActorRender的部位类型
    /// </summary>
    private ChangeSkin.SkinType GetActorRenderSkinType()
    {
        var skinnedMeshRenderers = GetSkinnedMeshRenderers();
        foreach (var renderer in skinnedMeshRenderers)
        {
            if (renderer != null && EffectNodeSkinMap.TryGetValue(renderer.name, out ChangeSkin.SkinType skinType))
            {
                return skinType;
            }
        }
        
        return ChangeSkin.SkinType.Body;
    }

    /// <summary>
    /// 获取节点的相对路径
    /// </summary>
    private string GetNodePath(Transform target)
    {
        if (target == this.transform) return "";
        
        List<string> pathParts = new List<string>();
        Transform current = target;
        
        while (current != null && current != this.transform)
        {
            pathParts.Insert(0, current.name);
            current = current.parent;
        }
        
        return string.Join("/", pathParts.ToArray());
    }
#endif

#region 特效数据管理    
    /// <summary>
    /// 按部位应用特效数据（换装时调用）
    /// </summary>
    public void ApplyEffectAttachmentsBySkinType(ActorRender sourceActorRender, ChangeSkin.SkinType skinType, Dictionary<string, Transform> boneDic)
    {
        if (sourceActorRender == null || sourceActorRender.effectAttachments == null) return;
        
        // 清除该部位的现有特效
        ClearEffectAttachmentsBySkinType(skinType);

        // 应用新特效
        ApplyEffectAttachmentsOnly(sourceActorRender, skinType, boneDic);
    }

    /// <summary>
    /// 只应用特效数据，不清理现有特效（用于换装流程中已经预先清理的情况）
    /// </summary>
    public void ApplyEffectAttachmentsOnly(ActorRender sourceActorRender, ChangeSkin.SkinType skinType, Dictionary<string, Transform> boneDic)
    {
        if (sourceActorRender == null || sourceActorRender.effectAttachments == null) return;

        if (sourceActorRender.effectAttachments.Count == 0)
        {
            return;
        }

        // 筛选出属于指定部位的特效数据
        var targetEffects = new List<EffectAttachmentData>();
        foreach (var effectData in sourceActorRender.effectAttachments)
        {
            if (effectData.skinType == skinType)
            {
                targetEffects.Add(effectData);
            }
        }
        
        if (targetEffects.Count == 0)
        {
            return;
        }
        
        // 批量应用特效数据
        ApplyEffectAttachmentsBatch(targetEffects, boneDic);
        
        // 将新的特效数据添加到本地记录中
        effectAttachments.AddRange(targetEffects);
    }

    /// <summary>
    /// 批量应用特效数据
    /// </summary>
    private void ApplyEffectAttachmentsBatch(List<EffectAttachmentData> effects, Dictionary<string, Transform> boneDic)
    {
        if (effects == null || effects.Count == 0) return;
        
        var nodeCache = DictPool.Get();
        var nodesToProcess = EffectListPool.Get();
        
        try
        {
            // 批量收集和缓存所有目标节点
            foreach (var effectData in effects)
            {
                string cacheKey = GetEffectCacheKey(effectData);
                
                if (!nodeCache.ContainsKey(cacheKey))
                {
                    Transform targetNode = FindOrCreateTargetNode(effectData, boneDic);
                    if (targetNode != null)
                    {
                        nodeCache[cacheKey] = targetNode;
                        nodesToProcess.Add(effectData);
                    }
                }
                else
                {
                    nodesToProcess.Add(effectData);
                }
            }
            
            // 批量处理特效应用
            for (int i = 0; i < nodesToProcess.Count; i++)
            {
                var effectData = nodesToProcess[i];
                string cacheKey = GetEffectCacheKey(effectData);
                
                if (nodeCache.TryGetValue(cacheKey, out Transform targetNode) && targetNode != null)
                {
                    ApplySingleEffectAttachmentOptimized(effectData, targetNode);
                }
            }
        }
        finally
        {
            DictPool.Release(nodeCache);
            EffectListPool.Release(nodesToProcess);
        }
    }
    
    /// <summary>
    /// 生成特效缓存键
    /// </summary>
    private string GetEffectCacheKey(EffectAttachmentData effectData)
    {
        // 使用StringBuilder避免多次字符串分配
        var keyBuilder = new System.Text.StringBuilder(32);
        if (!string.IsNullOrEmpty(effectData.nodeName))
        {
            keyBuilder.Append(effectData.nodeName);
        }
        keyBuilder.Append('_');
        if (!string.IsNullOrEmpty(effectData.nodePath))
        {
            keyBuilder.Append(effectData.nodePath);
        }
        return keyBuilder.ToString();
    }
    
    /// <summary>
    /// 单个特效应用
    /// </summary>
    private void ApplySingleEffectAttachmentOptimized(EffectAttachmentData effectData, Transform targetNode)
    {
        if (targetNode == null) return;

        // 批量处理组件操作
        var existingAttach = targetNode.GetComponent<GameObjectAttach>();
        bool hasExisting = existingAttach != null;
        bool needsRefresh = false;

        GameObjectAttach attachComponent;
        if (hasExisting)
        {
            attachComponent = existingAttach;
            // 检查Asset是否发生变化，如果变化则需要触发刷新
            needsRefresh = !attachComponent.Asset.Equals(effectData.asset);
        }
        else
        {
            attachComponent = targetNode.gameObject.AddComponent<GameObjectAttach>();
        }
        
        // 更新组件属性
        attachComponent.Asset = effectData.asset;
        attachComponent.delayTime = effectData.delayTime;

        // 如果Asset发生了变化，需要手动触发特效更新
        if (hasExisting && needsRefresh)
        {
            attachComponent.enabled = false;
            attachComponent.enabled = true;
        }

        // 批量设置Transform属性
        targetNode.localPosition = effectData.localPosition;
        targetNode.localEulerAngles = effectData.localRotation;
        targetNode.localScale = effectData.localScale;
    }

    /// <summary>
    /// 清除指定部位的特效绑定
    /// </summary>
    public void ClearEffectAttachmentsBySkinType(ChangeSkin.SkinType skinType)
    {
        var nodesToDestroy = TransformListPool.Get();
        var attachesToDestroy = AttachListPool.Get();
        var effectsToRemove = EffectListPool.Get();
        
        try
        {
            // 收集需要处理的特效数据
            for (int i = 0; i < effectAttachments.Count; i++)
            {
                if (effectAttachments[i].skinType == skinType)
                {
                    effectsToRemove.Add(effectAttachments[i]);
                }
            }
            
            // 批量查找和收集需要销毁的对象
            foreach (var effectData in effectsToRemove)
            {
                Transform targetNode = FindExistingNode(effectData);
                if (targetNode != null)
                {
                    var existingAttach = targetNode.GetComponent<GameObjectAttach>();
                    if (existingAttach != null)
                    {
                        attachesToDestroy.Add(existingAttach);
                    }
                    
                    // 如果是动态创建的节点，也删除节点
                    if (dynamicEffectNodes.Contains(targetNode))
                    {
                        nodesToDestroy.Add(targetNode);
                    }
                }
            }
            
            // 批量移除特效数据记录
            for (int i = effectAttachments.Count - 1; i >= 0; i--)
            {
                if (effectAttachments[i].skinType == skinType)
                {
                    effectAttachments.RemoveAt(i);
                }
            }
            
            // 批量清理动态节点记录
            foreach (var node in nodesToDestroy)
            {
                dynamicEffectNodes.Remove(node);
            }
            
            // 执行批量销毁
            DestroyBatchGameObjectAttachComponents(attachesToDestroy);
            DestroyBatchNodes(nodesToDestroy);
            
            // 清理相关缓存
            ClearRelatedCache(skinType);
        }
        finally
        {
            TransformListPool.Release(nodesToDestroy);
            AttachListPool.Release(attachesToDestroy);
            EffectListPool.Release(effectsToRemove);
        }
    }
    
    /// <summary>
    /// 清理相关缓存（避免缓存失效）
    /// </summary>
    private void ClearRelatedCache(ChangeSkin.SkinType skinType)
    {
        // 如果有大量清理操作，重建缓存可能更高效
        if (nodePathCache.Count > 20 || nodeNameCache.Count > 20)
        {
            ClearNodeCache();
        }
    }

    /// <summary>
    /// 查找现有节点
    /// </summary>
    private Transform FindExistingNode(EffectAttachmentData effectData)
    {
        // 确保缓存已构建
        EnsureNodeCacheBuilt();
        
        // 优先通过路径查找
        if (!string.IsNullOrEmpty(effectData.nodePath) && 
            nodePathCache.TryGetValue(effectData.nodePath, out Transform pathResult))
        {
            // 验证缓存的有效性
            if (pathResult != null)
            {
                return pathResult;
            }
            else
            {
                // 缓存失效，移除无效条目
                nodePathCache.Remove(effectData.nodePath);
            }
        }
        
        // 通过节点名称查找
        if (!string.IsNullOrEmpty(effectData.nodeName) && 
            nodeNameCache.TryGetValue(effectData.nodeName, out Transform nameResult))
        {
            // 验证缓存的有效性
            if (nameResult != null)
            {
                return nameResult;
            }
            else
            {
                // 缓存失效，移除无效条目
                nodeNameCache.Remove(effectData.nodeName);
            }
        }
        
        // 缓存未命中，直接查找并更新缓存
        return FindAndCacheNode(effectData);
    }
    
    /// <summary>
    /// 查找节点并更新缓存
    /// </summary>
    private Transform FindAndCacheNode(EffectAttachmentData effectData)
    {
        Transform found = null;
        
        // 尝试通过路径查找
        if (!string.IsNullOrEmpty(effectData.nodePath))
        {
            found = transform.Find(effectData.nodePath);
            if (found != null)
            {
                nodePathCache[effectData.nodePath] = found;
                return found;
            }
        }
        
        // 遍历查找
        if (!string.IsNullOrEmpty(effectData.nodeName))
        {
            Transform[] allChildren = GetComponentsInChildren<Transform>(true);
            foreach (Transform child in allChildren)
            {
                if (child.name == effectData.nodeName)
                {
                    nodeNameCache[effectData.nodeName] = child;
                    return child;
                }
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// 确保节点缓存已构建
    /// </summary>
    private void EnsureNodeCacheBuilt()
    {
        if (isCacheBuilt) return;
        
        lock (cacheLock)
        {
            if (isCacheBuilt) return;
            
            BuildNodeCache();
            isCacheBuilt = true;
        }
    }
    
    /// <summary>
    /// 构建节点缓存
    /// </summary>
    private void BuildNodeCache()
    {
        nodePathCache.Clear();
        nodeNameCache.Clear();
        
        Transform[] allChildren = GetComponentsInChildren<Transform>(true);
        foreach (Transform child in allChildren)
        {
            if (child == this.transform) continue;
            
            // 缓存节点名称
            if (!nodeNameCache.ContainsKey(child.name))
            {
                nodeNameCache[child.name] = child;
            }
            
            // 缓存节点路径
            string path = GetCachedNodePath(child);
            if (!string.IsNullOrEmpty(path))
            {
                nodePathCache[path] = child;
            }
        }
    }
    
    /// <summary>
    /// 获取节点路径
    /// </summary>
    private string GetCachedNodePath(Transform target)
    {
        if (target == this.transform) return "";
        
        var pathParts = TransformListPool.Get();
        try
        {
            Transform current = target;
            while (current != null && current != this.transform)
            {
                pathParts.Add(current);
                current = current.parent;
            }
            
            if (pathParts.Count == 0) return "";
            
            // 反向构建路径
            var pathBuilder = new System.Text.StringBuilder();
            for (int i = pathParts.Count - 1; i >= 0; i--)
            {
                if (pathBuilder.Length > 0) pathBuilder.Append('/');
                pathBuilder.Append(pathParts[i].name);
            }
            
            return pathBuilder.ToString();
        }
        finally
        {
            TransformListPool.Release(pathParts);
        }
    }
    
    /// <summary>
    /// 清理节点缓存
    /// </summary>
    private void ClearNodeCache()
    {
        lock (cacheLock)
        {
            nodePathCache.Clear();
            nodeNameCache.Clear();
            isCacheBuilt = false;
        }
    }

    /// <summary>
    /// 批量销毁组件
    /// </summary>
    private void DestroyBatchGameObjectAttachComponents(List<GameObjectAttach> components)
    {
        foreach (var component in components)
        {
            if (component != null)
            {
                DestroyImmediate(component);
            }
        }
    }

    /// <summary>
    /// 批量销毁节点
    /// </summary>
    private void DestroyBatchNodes(List<Transform> nodes)
    {
        foreach (var node in nodes)
        {
            if (node != null)
            {
                DestroyImmediate(node.gameObject);
            }
        }
    }

    /// <summary>
    /// 应用单个特效数据（重载：使用预查找的节点）
    /// </summary>
    private void ApplySingleEffectAttachment(EffectAttachmentData effectData, Transform targetNode)
    {
        if (targetNode == null)
        {
            return;
        }

        // 清理目标节点上现有的GameObjectAttach组件
        var existingAttach = targetNode.GetComponent<GameObjectAttach>();
        if (existingAttach != null)
        {
            DestroyImmediate(existingAttach);
        }

        // 添加新的GameObjectAttach组件
        var newAttach = targetNode.gameObject.AddComponent<GameObjectAttach>();
        newAttach.Asset = effectData.asset;
        newAttach.delayTime = effectData.delayTime;

        // 设置本地变换
        targetNode.localPosition = effectData.localPosition;
        targetNode.localEulerAngles = effectData.localRotation;
        targetNode.localScale = effectData.localScale;
    }

    /// <summary>
    /// 应用单个特效数据（需要骨骼字典查找）
    /// </summary>
    private void ApplySingleEffectAttachment(EffectAttachmentData effectData, Dictionary<string, Transform> boneDic)
    {
        Transform targetNode = FindOrCreateTargetNode(effectData, boneDic);
        ApplySingleEffectAttachment(effectData, targetNode);
    }

    /// <summary>
    /// 查找或创建目标节点
    /// </summary>
    private Transform FindOrCreateTargetNode(EffectAttachmentData effectData, Dictionary<string, Transform> boneDic)
    {
        // 尝试通过节点名称在骨骼字典中查找
        if (boneDic != null && boneDic.TryGetValue(effectData.nodeName, out Transform foundBone))
        {
            return foundBone;
        }
        
        // 尝试通过路径查找
        if (!string.IsNullOrEmpty(effectData.nodePath))
        {
            Transform found = transform.Find(effectData.nodePath);
            if (found != null)
            {
                return found;
            }
        }
        
        // 原始骨骼节点找不到时，尝试在父节点下创建
        return CreateDynamicNode(effectData, boneDic);
    }

    /// <summary>
    /// 创建动态节点
    /// </summary>
    private Transform CreateDynamicNode(EffectAttachmentData effectData, Dictionary<string, Transform> boneDic)
    {
        Transform parentNode = this.transform;
        
        // 尝试从路径解析父节点
        if (!string.IsNullOrEmpty(effectData.nodePath))
        {
            int lastSlashIndex = effectData.nodePath.LastIndexOf('/');
            if (lastSlashIndex > 0)
            {
                string parentPath = effectData.nodePath.Substring(0, lastSlashIndex);
                string parentName = effectData.nodePath.Substring(lastSlashIndex + 1);
                
                // 优先使用骨骼字典查找
                if (boneDic != null && boneDic.TryGetValue(parentName, out Transform foundParent))
                {
                    parentNode = foundParent;
                }
                else
                {
                    // 使用缓存查找父路径
                    if (nodePathCache.TryGetValue(parentPath, out Transform cachedParent) && cachedParent != null)
                    {
                        parentNode = cachedParent;
                    }
                    else
                    {
                        Transform foundByPath = transform.Find(parentPath);
                        if (foundByPath != null)
                        {
                            parentNode = foundByPath;
                            // 更新缓存
                            nodePathCache[parentPath] = foundByPath;
                        }
                    }
                }
            }
        }

        // 创建新节点
        GameObject newNode = new GameObject(effectData.nodeName);
        newNode.transform.SetParent(parentNode, false);
        
        // 记录动态创建的节点以便清理
        dynamicEffectNodes.Add(newNode.transform);
        
        // 将新节点加入缓存
        if (!string.IsNullOrEmpty(effectData.nodeName))
        {
            nodeNameCache[effectData.nodeName] = newNode.transform;
        }
        if (!string.IsNullOrEmpty(effectData.nodePath))
        {
            nodePathCache[effectData.nodePath] = newNode.transform;
        }
        
        return newNode.transform;
    }

    /// <summary>
    /// 清理特效绑定
    /// </summary>
    public void ClearAllEffectGameObjectAttachs()
    {
        var attachesToDestroy = AttachListPool.Get();
        var nodesToDestroy = TransformListPool.Get();
        
        try
        {
            // 收集所有需要清理的组件和节点
            var attachments = GetComponentsInChildren<GameObjectAttach>(true);
            attachesToDestroy.AddRange(attachments);
            nodesToDestroy.AddRange(dynamicEffectNodes);
            
            // 批量销毁
            DestroyBatchGameObjectAttachComponents(attachesToDestroy);
            DestroyBatchNodes(nodesToDestroy);
            
            // 清理记录
            dynamicEffectNodes.Clear();
            effectAttachments.Clear();
            
            // 重新获取allAttachEffects
            allAttachEffects = null;
            
            // 清理所有缓存
            ClearNodeCache();
        }
        finally
        {
            AttachListPool.Release(attachesToDestroy);
            TransformListPool.Release(nodesToDestroy);
        }
    }
    
    /// <summary>
    /// 在对象销毁时清理缓存和对象池
    /// </summary>
    private void OnDestroy()
    {
        ClearNodeCache();
        
        // 清理对象池（静态对象池在应用结束时会自动清理，这里主要是清理实例相关的缓存）
        if (Application.isPlaying)
        {
            // 在运行时不清理静态池，只清理实例缓存
            dynamicEffectNodes.Clear();
            effectAttachments.Clear();
        }
    }
#endregion

    [Serializable]
    public struct RenderItem
    {
        public Renderer renderer;
        public bool notCastShadow;
        public Material[] pbrMaterials;
    }
}