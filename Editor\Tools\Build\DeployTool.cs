﻿using Nirvana.Editor;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using Build;
using System.Runtime.InteropServices;
using UnityEngine.Networking;
using System.Collections;

class DeployTool
{
    // 打包资源
    internal static bool BuildAssets(BuildPlatType buildPlatType)
    {
        if (buildPlatType != BuildPlatType.Audit && !BuilderConfig.GetBuildDevice(buildPlatType).IsMatchCurrentTarget())
        {
            EditorUtility.DisplayDialog("Error", "平台不一致，请切换到相关平台再执行", "Yes");
            return false;
        }

        EncryptMgr.ClearEncryptKey();
        buildLogList.Clear();
        BuildAssetsMgr.Init(buildPlatType);
        // 如果没有游戏资源的变化，则只打包lua
        if (!BuildAssetsMgr.IsHadAssetDirty())
        {
            ReportBuildLog("BuildAssets, No Game Resource Changes, Start To Build Lua File!!!");
            return BuildLuaAssets(buildPlatType);
        }

        // 为了生成最新shader文件
        ReportBuildLog("BuildAssets, BuildMaterial.Build");
        BuildMaterial.Build();

        ReportBuildLog("BuildAssets, ShaderLabOptimize.CreateVariantsShaders");
        ShaderLabOptimize.CreateVariantsShaders();
        ReportBuildLog("BuildAssets, ShaderLabOptimize.UseVariantShaders");
        ShaderLabOptimize.UseVariantShaders();

        ReportBuildLog("BuildAssets, BuildAssetBundle.Build");
        if (!BuildAssetBundle.Build(buildPlatType))
        {
            ShaderLabOptimize.ResumeOrignalShaders();
            ReportBuildLog("[ABBuild Fail] BuildAssets, BuildAssetBundle.Build Fail");
            return false;
        }
        ReportBuildLog("BuildAssets, ShaderLabOptimize.ResumeOrignalShaders");
        ShaderLabOptimize.ResumeOrignalShaders();
        // 构建常用字字典
        ReportBuildLog("BuildAssets, BuildText.Build");
        BuildText.Build();

        // 构建Actor体积大小配置表
        ReportBuildLog("BuildAssets, BuildActorBounds.Build");
        if (!BuildActorBounds.Build())
        {
            return false;
        }

        // 生成各种入包文件
        ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildSsizeBundleFile");
        AssetBundleInstaller.BuildBundleInstallFile(buildPlatType);

        // 构建预下载列表文件(以小包来做剔除）
        ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildPreDownLoadLuaFile");
        AssetBundleInstaller.BuildPreDownLoadLuaFile(InstallBundleSize.sizeS, buildPlatType);

        // 构建强更列表(以小包来做强更新列表）
        ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildStrongUpdateLuaFile");
        AssetBundleInstaller.BuildStrongUpdateLuaFile(InstallBundleSize.sizeS);

        // 构建分包
        ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildSubPackage");
        if (!AssetBundleInstaller.BuildSubPackage(buildPlatType))
        {
            return false;
        }

        AssetDatabase.Refresh();

        // 编译所有lua文件
        ReportBuildLog("BuildAssets, LuaTool.BuildLuaAll");
        LuaTool.BuildLuaAll();

        // 打包LuaAssetBundle
        ReportBuildLog("BuildAssets, BuildAssetBundle.BuildLua");
        BuildAssetBundle.BuildLua(buildPlatType);
        AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

        // 写版本号
        ReportBuildLog("BuildAssets, BuilderVersion.WriteVersion");
        if (!BuilderVersion.WriteVersion(buildPlatType))
        {
            return false;
        }

        // 保存各种规格的进包列表（给sdk）
        ReportBuildLog("BuildAssets, AssetBundleInstaller.WriteInstallBundlesList");
        AssetBundleInstaller.WriteInstallBundlesList(buildPlatType);

        AssetDatabase.Refresh();

        // 写日志缓存
        ReportBuildLog("BuildAssets, BuildAssetsMgr.WriteBuildMaterialCsTime");
        BuildMaterial.WriteBuildMaterialCsTime();
        ReportBuildLog("BuildAssets, BuildAssetsMgr.WriteDependenciesLog");
        BuildAssetsMgr.WriteDependenciesLog();
        ReportBuildLog("BuildAssets, BuildAssetsMgr.WriteFileModifyLog");
        BuildAssetsMgr.WriteFileModifyLog();
        ReportBuildLog("BuildAssets, BuildAssetsMgr.WriteABModifyLog");
        BuildAssetsMgr.WriteABModifyLog();

        ReportBuildLog("[ABBuild Success] BuildAssets, Build Success!");
        EncryptMgr.ClearEncryptKey();

        return true;
    }

    internal static bool BuildLuaAssets(BuildPlatType buildPlatType)
    {
        try
        {
            if (buildPlatType != BuildPlatType.Audit && !BuilderConfig.GetBuildDevice(buildPlatType).IsMatchCurrentTarget())
            {
                EditorUtility.DisplayDialog("Error", "平台不一致，请切换到相关平台再执行", "Yes");
                return false;
            }

            // 构建预下载列表文件(以小包来做剔除）
            ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildPreDownLoadLuaFile");
            AssetBundleInstaller.BuildPreDownLoadLuaFile(InstallBundleSize.sizeS, buildPlatType);

            // 构建强更列表(以小包来做强更新列表）
            ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildStrongUpdateLuaFile");
            AssetBundleInstaller.BuildStrongUpdateLuaFile(InstallBundleSize.sizeS);

            // 构建分包
            ReportBuildLog("BuildAssets, AssetBundleInstaller.BuildSubPackage");
            if (!AssetBundleInstaller.BuildSubPackage(buildPlatType))
            {
                ReportBuildLog("[Fail] BuildAssets, AssetBundleInstaller.BuildSubPackage");
                return false;
            }

            AssetDatabase.Refresh();

            // 编译所有lua文件
            ReportBuildLog("BuildLuaAssets, LuaTool.BuildLuaAll");
            LuaTool.BuildLuaAll();

            // 打包LuaAssetBundle
            ReportBuildLog("BuildLuaAssets, BuildAssetBundle.BuildLua");
            BuildAssetBundle.BuildLua(buildPlatType);
            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

            // 保存各种规格的进包列表（给sdk）
            ReportBuildLog("BuildLuaAssets, AssetBundleInstaller.WriteInstallBundlesList");
            AssetBundleInstaller.WriteInstallBundlesList(buildPlatType);

            // 写版本号
            ReportBuildLog("BuildLuaAssets, BuilderVersion.WriteVersion");
            if (!BuilderVersion.WriteVersion(buildPlatType))
            {
                ReportBuildLog("[Fail] BuildLuaAssets, BuilderVersion.WriteVersion");
                return false;
            }

            // 写日志缓存
            ReportBuildLog("BuildLuaAssets, BuildAssetsMgr.WriteDependenciesLog");
            BuildAssetsMgr.WriteDependenciesLog();
            ReportBuildLog("BuildLuaAssets, BuildAssetsMgr.WriteFileModifyLog");
            BuildAssetsMgr.WriteFileModifyLog();
            ReportBuildLog("BuildAssets, BuildAssetsMgr.WriteABModifyLog");
            BuildAssetsMgr.WriteABModifyLog();
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            return false;
        }

        ReportBuildLog("[ABBuild Success]BuildLuaAssets, Build Success!");
        return true;
    }

    private static List<string> buildLogList = new List<string>();
    internal static void ClearReportLog()
    {
        buildLogList.Clear();
        ReportBuildLog(string.Empty);
    }

    internal static void ReportBuildLog(string log)
    {
        if (!string.IsNullOrEmpty(log))
        {
            log = string.Format("【{0}】{1}", DateTime.Now.ToString("yyyy-MM-dd hh:mm:ss"), log);
            buildLogList.Add(log);
            Debug.Log(log);
        }

        string path = string.Format("{0}/../Build/log/build_log.txt", Application.dataPath);
        var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }

        File.WriteAllLines(path, buildLogList.ToArray());
    }

    // 打包APP
    internal static void BuildPlayer(BuildPlatType buildPlatType, bool isCallByPy = false)
    {
        if (!BuilderConfig.GetBuildDevice(buildPlatType).IsMatchCurrentTarget())
        {
            if (!isCallByPy)
                EditorUtility.DisplayDialog("Error", "平台不一致，请切换到相关平台再执行", "Yes");
            ReportBuildLog("BuildWindowsExe Failed, Platform not match!!!");
            return;
        }

        try
        {
            AssetBundleInstaller.CopyInstallBundlesToStreamAsssets(buildPlatType, InstallBundleSize.sizeS); // 拷贝进包列表（小包）
        }
        catch (Exception ex)
        {
            ReportBuildLog("BuildWindowsExe Failed, CopyInstallBundlesToStreamAsssets Error!!!" + ex.ToString());
            return;
        }


        BuildSettingNew build_settting = BuilderConfig.GetBuildSetting(buildPlatType);
        string asset_bundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
        string output_path = BuilderConfig.GetOutputPlayerPath(buildPlatType);
        build_settting.BuildPlayer(asset_bundle_path, output_path);                    // 构建player
        ReportBuildLog("BuildWindowsExe Success!!!");
    }

    // 打包工程给sdk
    internal static void ExportProject(BuildPlatType buildPlatType, bool isCallByPy = false)
    {
        if (buildPlatType != BuildPlatType.Audit && !BuilderConfig.GetBuildDevice(buildPlatType).IsMatchCurrentTarget())
        {
            if(!isCallByPy)
                EditorUtility.DisplayDialog("Error", "平台不一致，请切换到相关平台再执行", "Yes");
            ReportBuildLog("ExportProject Failed, Platform not match!!!");
            return;
        }

        BuildSettingNew build_settting = BuilderConfig.GetBuildSetting(buildPlatType);
        string asset_bundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType);
        string output_path = BuilderConfig.GetOutputProjectPath(buildPlatType);
        if (string.IsNullOrEmpty(output_path))
        {
            if (!isCallByPy)
                EditorUtility.DisplayDialog("Error", "output_path为空,请检查", "Yes");
            ReportBuildLog("ExportProject Failed, output_path为空,请检查!!!");
            return;
        }

        if (Directory.Exists(output_path))
        {
            Directory.Delete(output_path, true);
        }
        Directory.CreateDirectory(output_path);

        build_settting.BuildPlayer(BuilderConfig.GetAssetBundlePath(buildPlatType), output_path, true);
        ReportBuildLog("ExportProject Success!!!");
    }

    // 检查预加载文件
    internal static void CheckPreDownloadConfig()
    {
        string path = string.Format("{0}/Game/Lua/game/scene/loading/predownload.lua", Application.dataPath);
        string[] lines = File.ReadAllLines(path);

        var manifest = BuilderConfig.GetManifestInfo(BuildPlatType.Android);
        for (int i = 0; i < lines.Length; i++)
        {
            string line = lines[i];
            if (line.IndexOf("bundle = \"") >= 0)
            {
                var s = line.IndexOf("\"");
                var e = line.LastIndexOf("\"");
                var bundleName = line.Substring(s + 1, e - s - 1);
                if (!manifest.IsExistsAssetBundle(bundleName))
                {
                    Debug.LogErrorFormat("预下载配置错误：{0}", bundleName);
                }
            }
        }
    }
}


