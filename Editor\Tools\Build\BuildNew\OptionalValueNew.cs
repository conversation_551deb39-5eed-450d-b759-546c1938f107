﻿using System;
using UnityEngine;

public class OptionalValueNew
{
}

[Serializable]
public class OptionalValueNew<T> : OptionalValueNew
{
    [SerializeField]
    private bool hasValue = false;

    [SerializeField]
    private T value;

    internal T Value
    {
        get
        {
            return value;
        }
        set
        {
            this.value = value;
            hasValue = true;
        }
    }

    internal bool HasValue => hasValue;

    internal OptionalValueNew()
    {
    }

    internal OptionalValueNew(T value, bool hasValue)
    {
        this.value = value;
        this.hasValue = hasValue;
    }

    internal void ResetValue()
    {
        hasValue = false;
    }
}

