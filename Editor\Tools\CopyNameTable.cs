﻿using EnhancedUI.EnhancedScroller;
using Nirvana;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using UnityEditor;
using UnityEngine;

public class CopyNameTable {
    const string scroller_str = "if not self.{name}_list then\r\tself.{name}_list = AsyncListView.New(XXXRender, self.node_list[\"{name}\"])" +
        "\r\tself.{name}_list:SetSelectCallBack(BindTool.Bind(self.OnselectChangeXXXclick,self)) " +
        "\rend";
    const string button_str = "XUI.AddClickEventListener(self.node_list[\"{name}\"],BindTool.Bind(self.OnClickXXX, self))";
    const string text_str = "self.node_list[\"{name}\"].text.text = \"\"";
    const string raw_str = "local bundle, asset = ResPath.GetXXXImages(res_id)" +
        "\rself.node_list[\"{name}\"].image:LoadSprite(bundle, asset, function()" +
        "\r\tself.node_list[\"{name}\"].image:SetNativeSize()" +
        "\rend)";
    const string img_str = "local bundle, asset = ResPath.GetXXXImages(res_id)" +
        "\rself.node_list[\"{name}\"].image:LoadSprite(bundle, asset, function()" +
        "\r\tself.node_list[\"{name}\"].image:SetNativeSize()" +
        "\rend)";

    static Dictionary<Type, string> copm_dic = new Dictionary<Type, string>()
    {
        {typeof(EnhancedScroller) ,scroller_str},
        {typeof(UnityEngine.UI.Button), button_str},
        {typeof(UnityEngine.UI.Text), text_str},
        {typeof(UnityEngine.UI.RawImage), raw_str},
        {typeof(UnityEngine.UI.Image), img_str},
    };

    [MenuItem("CONTEXT/UINameTable/CopyNameTableNode")]
    static void CopyNameTableNode(MenuCommand cmd)
    {
        UINameTable uINameTable = (UINameTable)cmd.context;
        GUIUtility.systemCopyBuffer = "";

        StringBuilder stringBuilder = new StringBuilder();
        foreach (var item in uINameTable.Lookup)
        {
            foreach (var cell in item.Value.GetComponents<Component>())
            {
                if (copm_dic.ContainsKey(cell.GetType()))
                {
                    string str = copm_dic[cell.GetType()];
                    str = str.Replace("{name}", item.Key);
                    stringBuilder.Append(str);
                    stringBuilder.Append("\n\n");
                    break;
                }
            }
        }

        GUIUtility.systemCopyBuffer = stringBuilder.ToString();

        if (!string.IsNullOrEmpty(stringBuilder.ToString()))
        {
            EditorUtility.DisplayDialog("节点获取成功", "节点信息已经复制到粘贴板", "Ok");
        }
    }

    [MenuItem("CONTEXT/UINameTable/拾取选中的节点")]
    static void AddSelectionsNodeToNodeList(MenuCommand cmd)
    {
        UINameTable uINameTable = (UINameTable)cmd.context;
        UnityEngine.Object[] objs = Selection.objects;
        if (null == uINameTable || objs.Length <= 0) return;

        foreach (var item in objs)
        {
            string key = item.name;
            GameObject obj = item as GameObject;
            uINameTable.Add(key, obj);
        }
    }

    public static List<string> start_with_str = new List<string>() { "btn_", "desc_", "rawimg_", "img_", "flag_", "slider_", "list_", "remind_", "model_" };
    public static List<string> end_with_str = new List<string>() { "_btn", "_remind", "_list", "_img", "_flag", };
    public const string auto_get_node_str = "\"btn_\", \"desc_\", \"rawimg_\", \"img_\", \"flag_\",\"slider_\", \"list_\", \"remind_\", \"_btn\", \"_remind\", \"_list\", \"_img\", \"_flag\"";

    [MenuItem("CONTEXT/UINameTable/自动拾取节点"+ auto_get_node_str)]
    static void AutoAddNodeToNodeList(MenuCommand cmd)
    {
        UINameTable uINameTable = (UINameTable)cmd.context;
        Transform[] gameObjects = uINameTable.transform.GetComponentsInChildren<Transform>(true);
        if (gameObjects.Length <= 0) return;

        foreach (var item in gameObjects)
        {
            string key = item.gameObject.name;
            foreach (var aoc in start_with_str)
            {
                if (key.StartsWith(aoc))
                {
                    uINameTable.Add(key, item.gameObject);
                }
            }

            foreach (var aoc in end_with_str)
            {
                if (key.EndsWith(aoc))
                {
                    uINameTable.Add(key, item.gameObject);
                }
            }
        }
    }

    static Dictionary<string, GameObject> copy_dic = new Dictionary<string, GameObject>();
    [MenuItem("CONTEXT/UINameTable/复制所有节点",  false)]
    static void CopyAllNode(MenuCommand cmd) {
        UINameTable uINameTable = (UINameTable)cmd.context;
        copy_dic = uINameTable.Lookup;
    }

    [MenuItem("CONTEXT/UINameTable/复制所有节点", true)]
    static bool IsCanCopyAllNode(MenuCommand cmd)
    {
        UINameTable uINameTable = (UINameTable)cmd.context;

        if (uINameTable.Lookup.Count <= 0)
        {
            return false;
        }

        return true;
    }

    [MenuItem("CONTEXT/UINameTable/粘贴所有节点", false)]
    static void PassAllNode(MenuCommand cmd)
    {
        UINameTable uINameTable = (UINameTable)cmd.context;
        foreach (var item in copy_dic)
        {
            uINameTable.Add(item.Key, item.Value);
        }
    }

    [MenuItem("CONTEXT/UINameTable/粘贴所有节点", true)]
    static bool IsCanPassAllNode(MenuCommand cmd) {
        if (copy_dic.Count <= 0)
        {
            return false;
        }
         
        return true;
    }
}
