﻿using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using System.Runtime.InteropServices;
using System.IO;
using System.Text;
using System.Linq;
using System.Xml;
using System.Text.RegularExpressions;
using System.Collections.ObjectModel;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Text = UnityEngine.UI.Text;
//using SharpSvn; 

/// <summary>
/// 外服翻译专用
/// </summary>
class TranslationTool
{
    static string[] translateDirs = new string[] {
        //"Assets/Game/Lua/",
        "Assets/Game/UIs/View",
    };
    static string serverPath = Application.dataPath + "/../../../server";
    static string configXMLPath = Application.dataPath + "/../../../server/server/src/config/";

    static Dictionary<string, string> syntaxDir = new Dictionary<string, string>();

    [MenuItem("自定义工具/翻译工具/一键翻译预制体-简体转繁体", priority = 1)]
    public static void OneKeyTranslate()
    {
        InitSyntaxDir();
        ChineseUtils(false, "Assets/Game/UIs/View", true);
    }

    [MenuItem("自定义工具/翻译工具/自选目录翻译-简体转繁体", priority = 2)]
    public static void TransToTraditional()
    {
        InitSyntaxDir();
        ChineseUtils(false, null, true);
    }

    //[MenuItem("自定义工具/翻译工具/繁体转简体（项目内lua、配置文件、预制体文本）", priority = 3)]
    //public static void TransToSimplified()
    //{
    //    ChineseUtils(true, null, true);
    //}

    private static bool CheckIsRoleFashionModel(string bundleName)
    {
        if (Regex.IsMatch(bundleName, "model/character") &&
            (Regex.IsMatch(bundleName, "/body") || Regex.IsMatch(bundleName, "hair") || Regex.IsMatch(bundleName, "/face")))
        {
            return true;
        }
        return false;
    }

    [MenuItem("自定义工具/翻译工具/语法替换", priority = 3)]
    public static void TransToGrammar()
    {
        InitSyntaxDir();
        TransGrammar();
    }

    //[MenuItem("自定义工具/翻译工具/翻译客户端SVN导出目录", priority = 4)]
    //public static void TranslateClientSvnExploreFile()
    //{
    //    InitSyntaxDir();
    //    string clientPath = "C:/repo/a1_tw_pack/client/u3d_proj/";
    //    string findPath = EditorUtility.OpenFolderPanel("Find Folder", "Resources", "");
    //    if (string.IsNullOrEmpty(findPath))
    //    {
    //        return;
    //    }

    //    findPath = findPath.Replace("\\", "/");
    //    if (!findPath.EndsWith("/Assets"))
    //    {
    //        Debug.LogError("请选择Assets目录");
    //        return;
    //    }

    //    List<string> translatePath = new List<string>();
    //    List<string> withoutExtensions = new List<string>() { ".prefab", ".txt", ".lua", ".xml" };
    //    string[] files = Directory.GetFiles(findPath, "*.*", SearchOption.AllDirectories).Where(s => withoutExtensions.Contains(Path.GetExtension(s).ToLower())).ToArray();
    //    foreach (string path in files)
    //    {
    //        string tPath = path.Replace("\\", "/");
    //        bool isCheck = false;
    //        foreach (string checkPath in translateDirs)
    //        {
    //            if (tPath.Contains(checkPath))
    //            {
    //                isCheck = true;
    //                break;
    //            }
    //        }
    //        if (!isCheck) continue;

    //        int indexOf = tPath.IndexOf("Assets");
    //        string newPath = tPath.Substring(indexOf, tPath.Length - indexOf);
    //        Debug.LogError(newPath);
    //        translatePath.Add(clientPath + newPath);
    //    }

    //    DoTranslate(translatePath.ToArray(), findPath, true, false);
    //}

    //[MenuItem("自定义工具/翻译工具/翻译服务器SVN导出目录", priority = 4)]
    //public static void TranslateServerSvnExploreFile()
    //{
    //    InitSyntaxDir();
    //    string serverPath = "C:/repo/a1_tw_pack/server/server/src/";
    //    string findPath = EditorUtility.OpenFolderPanel("Find Folder", "Resources", "");
    //    if (string.IsNullOrEmpty(findPath))
    //    {
    //        return;
    //    }

    //    findPath = findPath.Replace("\\", "/");
    //    if (!findPath.EndsWith("/server/server/src/config"))
    //    {
    //        Debug.LogError("请选择config目录");
    //        return;
    //    }

    //    List<string> translatePath = new List<string>();
    //    List<string> withoutExtensions = new List<string>() { ".xml" };
    //    string[] files = Directory.GetFiles(findPath, "*.*", SearchOption.AllDirectories).Where(s => withoutExtensions.Contains(Path.GetExtension(s).ToLower())).ToArray();
    //    foreach (string path in files)
    //    {
    //        string tPath = path.Replace("\\", "/");
    //        int indexOf = tPath.IndexOf("config");
    //        string newPath = tPath.Substring(indexOf, tPath.Length - indexOf);
    //        Debug.LogError(newPath);
    //        translatePath.Add(serverPath + newPath);
    //    }

    //    DoTranslate(translatePath.ToArray(), findPath, true, false);
    //}

    private static void DoTranslate(string[] filePaths, string rootDir, bool isNeedSyntax, bool isSimpified)
    {
        System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
        stopwatch.Start();
        int count = 0;
        int total = filePaths.Length;
        try
        {
            for (int m = 0; m < total; m++)
            {
                string path = filePaths[m].Replace("\\", "/");
                if (Path.GetExtension(path) == ".meta")
                {
                    continue;
                }

                if (Path.GetExtension(path) == ".txt" || Path.GetExtension(path) == ".lua")
                {
                    string text = isNeedSyntax ? SyntaxSubString(File.ReadAllText(path)) : File.ReadAllText(path);
                    string transStr = isSimpified ? ChineseStringUtility.ToSimplified(text) : ChineseStringUtility.ToTraditional(text);
                    File.WriteAllText(path, transStr);
                    count++;
                }
                else if (Path.GetExtension(path) == ".xml")
                {
                    XmlDocument doc = new XmlDocument();
                    doc.Load(path);
                    bool isDirth = false;
                    XmlNode node = RecursiveTranslateXMLNode(doc.DocumentElement, isSimpified, isNeedSyntax, ref isDirth);
                    if (isDirth)
                    {
                        doc.Save(path);
                    }
                    count++;
                }
                else if (Path.GetExtension(path) == ".prefab")
                {
                    path = GetRelativeAssetsPath(path);
                    GameObject tmp = (GameObject)AssetDatabase.LoadAssetAtPath(path, typeof(GameObject));
                    Text[] labels = (tmp.GetComponentsInChildren<Text>(true));
                    bool isDirty = false;
                    for (int j = 0; j < labels.Length; j++)
                    {
                        if (!string.IsNullOrEmpty(labels[j].text))
                        {
                            string transText = isSimpified ? ChineseStringUtility.ToSimplified(labels[j].text) : ChineseStringUtility.ToTraditional(labels[j].text);
                            labels[j].text = isNeedSyntax ? SyntaxSubString(transText) : transText;
                            isDirty = true;
                            count++;
                        }
                    }
                    if (isDirty)
                    {
                        EditorUtility.SetDirty(tmp);
                    }
                }
                EditorUtility.DisplayProgressBar(string.Format("正在转化{0}体字", isSimpified ? "简" : "繁"), string.Format("{0} / {1}", count, total), (float)count / (float)total);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
        }
        EditorUtility.ClearProgressBar();
        TimeSpan timespan = stopwatch.Elapsed;
        stopwatch.Stop();
        Debug.LogErrorFormat("扫描路劲:{0}, 总共修改文件:{1}个, 总共用时:{2}分{3}秒", rootDir, count, timespan.Minutes, timespan.TotalSeconds);
    }

    //[MenuItem("自定义工具/翻译工具/svn测试", priority = 1)]
    //public static void CheckSvnLog()
    //{
    //    //int SvnLogIDStart = 16400;
    //    //int SvnLogIdEnd = 16500;

    //    //string applicationPath = Application.dataPath;
    //    //SharpSvn.SvnClient svnClient = new SharpSvn.SvnClient();
    //    //SharpSvn.SvnLogArgs svnLogArgs = new SharpSvn.SvnLogArgs();
    //    //svnLogArgs.RetrieveAllProperties = false;
    //    //Collection<SharpSvn.SvnLogEventArgs> status;
    //    //svnClient.GetLog(applicationPath, svnLogArgs, out status);
    //    //int messgNum = 0;
    //    //string logText = "";
    //    //string lastLog = "";
    //    //foreach (var item in status)
    //    //{
    //    //    if (messgNum > 10)
    //    //        break;
    //    //    messgNum += 1;
    //    //    if (string.IsNullOrEmpty(item.LogMessage) || item.LogMessage == " " || lastLog == item.LogMessage)
    //    //    {
    //    //        continue;
    //    //    }
    //    //    logText = item.Author + "：" + item.LogMessage + "\n" + logText;
    //    //    lastLog = item.LogMessage;
    //    //}
    //}

    private void GetSvnLog()
    {
        FileInfo fileInfo = new FileInfo(Application.dataPath);
        //SvnClient svnClient = new SvnClient();

    }

    private static bool InitSyntaxDir()
    {
        string stPath = Application.dataPath + "/../AssetsCheck/SyntaxTranslate.txt";
        if (!File.Exists(stPath))
        {
            Debug.LogErrorFormat("{0} not exist!", stPath);
            return false;
        }
        syntaxDir.Clear();
        string[] lines = File.ReadAllLines(stPath);
        for (int i = 1; i < lines.Length; i++)
        {
            string[] lineStr = lines[i].Split(',');
            string[] regTexs = lineStr[0].Split('|');
            string regTex = "";
            foreach (string reg in regTexs)
            {
                regTex += string.Format("[\\u{0}]", reg);
            }
            syntaxDir.Add(regTex, lineStr[2]);
        }
        return true;
    }

    private static string SyntaxSubString(string txt)
    {
        if (syntaxDir.Count <= 0)
            return txt;

        foreach (KeyValuePair<string, string> keyValuePair in syntaxDir)
        {
            string keyAscii = keyValuePair.Key;
            if (Regex.IsMatch(txt, @keyAscii))
            {
                Regex reg = new Regex(@keyAscii, RegexOptions.None);
                txt = reg.Replace(txt, keyValuePair.Value);
                break;
            }
        }
        return txt;
    }

    public static void ChineseUtils(bool isSimpified, string translateDir, bool isNeedSyntax)
    {
        string findPath = translateDir;
        if (string.IsNullOrEmpty(findPath))
        {
            findPath = EditorUtility.OpenFolderPanel("Find Folder", "Resources", "");
            if (string.IsNullOrEmpty(findPath))
            {
                return;
            }
        }

        System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
        stopwatch.Start();
        if (isNeedSyntax && (syntaxDir == null || syntaxDir.Count == 0))
        {
            InitSyntaxDir();
        }

        List<string> withoutExtensions = new List<string>() { ".prefab", ".txt", ".lua", ".xml" };
        string[] files = Directory.GetFiles(findPath, "*.*", SearchOption.AllDirectories).Where(s => withoutExtensions.Contains(Path.GetExtension(s).ToLower())).ToArray();
        DoTranslate(files, translateDir, isNeedSyntax, isSimpified);
    }

    public static void TransGrammar(string translatePath = "")
    {
        try
        {
            string findPath = translatePath;
            if (string.IsNullOrEmpty(findPath))
            {
                findPath = EditorUtility.OpenFolderPanel("Find Folder", "Resources", "");
                if (string.IsNullOrEmpty(findPath))
                {
                    return;
                }
            }

            StringBuilder builder = new StringBuilder();
            List<string> withoutExtensions = new List<string>() { ".prefab", ".txt", ".lua", ".xml" };
            builder.Append("修改：-----------------------\n");
            int count = 0;
            string[] files = Directory.GetFiles(findPath, "*.*", SearchOption.AllDirectories).Where(s => withoutExtensions.Contains(Path.GetExtension(s).ToLower())).ToArray();
            int total = files.Length;
            try
            {
                for (int m = 0; m < files.Length; m++)
                {
                    string path = files[m].Replace("\\", "/");
                    if (Path.GetExtension(path) == ".meta")
                    {
                        continue;
                    }

                    if (Path.GetExtension(path) == ".txt" || Path.GetExtension(path) == ".lua")
                    {
                        string txt = File.ReadAllText(path);
                        string newTex = SyntaxSubString(txt);
                        File.WriteAllText(path, newTex);
                        count++;
                    }
                    else if (Path.GetExtension(path) == ".xml")
                    {
                        XmlDocument doc = new XmlDocument();
                        doc.Load(path);
                        bool isDirth = false;
                        XmlNode node = RecursiveTranslateGrammarXMLNode(doc.DocumentElement, ref isDirth);
                        if (isDirth)
                        {
                            doc.Save(path);
                        }
                        count++;
                    }
                    else if (Path.GetExtension(path) == ".prefab")
                    {
                        path = GetRelativeAssetsPath(path);
                        GameObject tmp = (GameObject)AssetDatabase.LoadAssetAtPath(path, typeof(GameObject));
                        Text[] labels = (tmp.GetComponentsInChildren<Text>(true));
                        bool isDirty = false;
                        for (int j = 0; j < labels.Length; j++)
                        {
                            if (!string.IsNullOrEmpty(labels[j].text))
                            {
                                labels[j].text = SyntaxSubString(labels[j].text);
                                isDirty = true;
                                count++;
                            }
                        }
                        if (isDirty)
                        {
                            EditorUtility.SetDirty(tmp);
                        }
                    }
                    EditorUtility.DisplayProgressBar(string.Format("正在进行语法替换"), string.Format("{0} / {1}", count, total), (float)count / (float)total);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex.ToString());
                EditorUtility.ClearProgressBar();
            }

        }
        catch (Exception ex)
        {
            Debug.LogError(ex);
            EditorUtility.ClearProgressBar();
        }
        syntaxDir.Clear();
        EditorUtility.ClearProgressBar();
    }

    private static XmlNode RecursiveTranslateXMLNode(XmlNode node, bool isSimpified, bool isNeedSyntax, ref bool isDirty)
    {
        for (int i = 0; i < node.ChildNodes.Count; i++)
        {
            XmlNode node1 = node.ChildNodes[i];
            if (!node1.HasChildNodes)
            {
                if (!string.IsNullOrEmpty(node1.Value))
                {
                    string text = isNeedSyntax ? SyntaxSubString(node1.InnerText) : node1.InnerText;
                    if (ContainChinese(text))
                    {
                        node1.InnerText = isSimpified ? ChineseStringUtility.ToSimplified(text) : ChineseStringUtility.ToTraditional(text);
                        isDirty = true;
                    }
                }
            }
            else
            {
                RecursiveTranslateXMLNode(node1, isSimpified, isNeedSyntax, ref isDirty);
            }
        }
        return node;
    }

    private static XmlNode RecursiveTranslateGrammarXMLNode(XmlNode node, ref bool isDirty)
    {
        for (int i = 0; i < node.ChildNodes.Count; i++)
        {
            XmlNode node1 = node.ChildNodes[i];
            if (!node1.HasChildNodes)
            {
                if (!string.IsNullOrEmpty(node1.Value))
                {
                    string text = node1.InnerText;
                    if (ContainChinese(text))
                    {
                        node1.InnerText = SyntaxSubString(text);
                        isDirty = true;
                    }
                }
            }
            else
            {
                RecursiveTranslateGrammarXMLNode(node1, ref isDirty);
            }
        }
        return node;
    }

    static private string GetRelativeAssetsPath(string path)
    {
        return "Assets" + Path.GetFullPath(path).Replace(Path.GetFullPath(Application.dataPath), "").Replace('\\', '/');
    }

    private static bool ContainChinese(string input)
    {
        string pattern = "[\u4e00-\u9fbb]";
        return Regex.IsMatch(input, pattern);
    }
}

/// <summary>
/// 中文字符工具类
/// </summary>
public static class ChineseStringUtility
{
    private const int LOCALE_SYSTEM_DEFAULT = 0x0800;
    private const int LCMAP_SIMPLIFIED_CHINESE = 0x02000000;
    private const int LCMAP_TRADITIONAL_CHINESE = 0x04000000;

    [DllImport("kernel32", CharSet = CharSet.Auto, SetLastError = true)]
    private static extern int LCMapString(int Locale, int dwMapFlags, string lpSrcStr, int cchSrc, [Out] string lpDestStr, int cchDest);

    /// <summary>
    /// 讲字符转换为繁体中文
    /// </summary>
    /// <param name="source">输入要转换的字符串</param>
    /// <returns>转换完成后的字符串</returns>
    public static string ToTraditional(string source)
    {
        String target = new String(' ', source.Length);
        int ret = LCMapString(LOCALE_SYSTEM_DEFAULT, LCMAP_TRADITIONAL_CHINESE, source, source.Length, target, source.Length);
        return target;
    }

    /// <summary>
    /// 讲字符转换为简体中文
    /// </summary>
    /// <param name="source">输入要转换的字符串</param>
    /// <returns>转换完成后的字符串</returns>
    public static string ToSimplified(string source)
    {
        String target = new String(' ', source.Length);
        int ret = LCMapString(LOCALE_SYSTEM_DEFAULT, LCMAP_SIMPLIFIED_CHINESE, source, source.Length, target, source.Length);
        return target;
    }
}