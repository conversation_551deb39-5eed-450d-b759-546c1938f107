﻿using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.IO;
using Object = UnityEngine.Object;
using System.Text;
using System.Text.RegularExpressions;

public class ResourceCheckTool : EditorWindow
{
    #region Member variables
    //-------------------------------------------------------------------------
    private List<string> m_SelectPathList;
    private List<string> m_SelectGUIDList;
    private List<string> M_ResNotBeRefs;
    private Dictionary<string,List<string>> m_DicResPrefabs;
    private Dictionary<string,List<string>> m_DicResPrefabsGuid;
    private string m_strUIPerfabPath;
    private string m_strCurrentPerfabPath;
    private Vector2 m_scrollPos;
    //-------------------------------------------------------------------------
    #endregion

    #region Public Method
    //-------------------------------------------------------------------------
    [MenuItem("Tools/换UI工具/资源引用检测")]
    public static void Open()
    {

        GetWindowWithRect(typeof(ResourceCheckTool),new Rect(0, 0, 700, 800), true);

    }
    //-------------------------------------------------------------------------
    void OnGUI()
    {
        if (m_SelectPathList!=null && m_SelectPathList.Count!=0)
        {

            m_scrollPos = GUILayout.BeginScrollView(m_scrollPos, true, true, GUILayout.Height(800));
            GUILayout.Space(10);
            GUI.backgroundColor = Color.green;
            if (GUILayout.Button("重新分析 - UI", GUILayout.Height(50)))
            {
                __Init();
                m_SelectPathList.Clear();
                __GetSelectItem();
                //__GetAllPrefabs(m_strCurrentPerfabPath);
                __CheckEveryPrefab();
                m_strCurrentPerfabPath = m_strUIPerfabPath;
            }
            GUILayout.Space(10);

            List<string> lists = null;
            GUILayout.Label("当前预设资源路径为：");
            GUILayout.Label(m_strCurrentPerfabPath);

            if (0 == m_SelectPathList.Count)
            {
                GUILayout.Label("-------------------------------------------------------------------------");
                GUILayout.Label("当前分析的资源为：");
                GUILayout.Label("此资源尚未被任何预设引用过，可以考虑删除！");
            }

            GUIStyle LowLevelButton = new GUIStyle(GUI.skin.button);
            //LowLevelButton.normal.background = TextureMaker.Gray(0.27f);
            LowLevelButton.normal.textColor = new Color(0.95f, 0.95f, 0.95f, 1);
            //LowLevelButton.hover.background = TextureMaker.Gray(0.35f);
            LowLevelButton.hover.textColor = new Color(0.95f, 0.95f, 0.95f, 1);
            //LowLevelButton.active.background = TextureMaker.Gray(0.55f);
            LowLevelButton.active.textColor = new Color(0.95f, 0.95f, 0.95f, 1);

            for (int nInedx = 0; nInedx != m_SelectPathList.Count; ++nInedx)
            {
                if (m_DicResPrefabs.TryGetValue(m_SelectGUIDList[nInedx], out lists))
                {
                    if (null != lists && 0 != lists.Count)
                    {
                        GUILayout.Label("-------------------------------------------------------------------------");
                        GUILayout.Label("当前分析的资源为：");
                        GUILayout.Label(m_SelectPathList[nInedx]);
                        GUILayout.Label("当前分析的资源引用信息为：");
                    
                        foreach (string str in lists)
                        {
                            if(GUILayout.Button(str, LowLevelButton))
                            {
                                string[] arr = str.Split(' ');
                                string path = arr[0];
                                GameObject go = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                                Debug.Log(path+go.name);
                                EditorGUIUtility.PingObject(go);
                            }
                        }
                    }
                    else
                    {
                        M_ResNotBeRefs.Add(m_SelectPathList[nInedx]);
                    }
                }

            }

            if (0 != M_ResNotBeRefs.Count)
            {
                GUILayout.Label("-------------------------------------------------------------------------");
                GUILayout.Label("以下为完全没被引用过的资源：");
                foreach (string str in M_ResNotBeRefs)
                {
                    GUILayout.Label(str);
                }
                M_ResNotBeRefs.Clear();
            }

            GUILayout.Label("-------------------------------------------------------------------------");

            GUILayout.EndScrollView();


        }
        else
        {
            GUILayout.Space(10);
            GUILayout.Label("当前没有资源被选中，选择你需要分析的资源然后点【分析】");
            GUILayout.Space(100);

            GUI.backgroundColor = Color.red;
            if (GUILayout.Button("分析 - UI", GUILayout.Height(50)))
            {
                __Init();
                __GetSelectItem();
                __GetAllPrefabs(m_strUIPerfabPath);
                __CheckEveryPrefab();
                m_strCurrentPerfabPath = m_strUIPerfabPath;
            }
            
            GUILayout.Space(10);
        }

    }
    //-------------------------------------------------------------------------
    #endregion

    #region private Method
    //-------------------------------------------------------------------------
    private void __Init()
    {
        if (null == m_SelectPathList)
        {
            m_SelectPathList = new List<string>();
        }
        m_SelectPathList.Clear();

        if (null == m_SelectGUIDList)
        {
            m_SelectGUIDList = new List<string>();
        }
        m_SelectGUIDList.Clear();

        if (null == m_DicResPrefabs)
        {
            m_DicResPrefabs = new Dictionary<string,List<string>>();
        }
        m_DicResPrefabs.Clear();

        if (null == m_DicResPrefabsGuid)
        {
            m_DicResPrefabsGuid = new Dictionary<string,List<string>>();
        }

        if (null == M_ResNotBeRefs)
        {
            M_ResNotBeRefs = new List<string>();
        }
        M_ResNotBeRefs.Clear();

        m_strUIPerfabPath = "Assets/Game/UIs/View/";
    }

    //-------------------------------------------------------------------------
    private void __GetSelectItem()
    {
        foreach (Object o in Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets))
        {
            var path = AssetDatabase.GetAssetPath(o);

            // 过滤掉meta文件和文件夹
            if (path.Contains(".meta") || path.Contains(".") == false)
            {
                continue;
            }

            m_SelectPathList.Add(path);
            m_SelectGUIDList.Add(AssetDatabase.AssetPathToGUID(path));

            // Debug.Log("path = " + path);
            // Debug.Log("GUID = " + AssetDatabase.AssetPathToGUID(path));
        }
    }
    //-------------------------------------------------------------------------
    private void __GetAllPrefabs(string strPrefabsPath)
    {
        if (null == m_SelectPathList || 0 == m_SelectPathList.Count)
        {
            Debug.Log("请在Project中选择需要分析的资源！");
            return;
        }
        __GetFiles(strPrefabsPath);
    }
    //-------------------------------------------------------------------------
    private void __CheckEveryPrefab()
    {
        if (null == m_SelectGUIDList || 0 == m_SelectGUIDList.Count)
        {
            return;
        }

        if (null == m_SelectPathList || 0 == m_SelectPathList.Count)
        {
            return;
        }

        if (null == m_DicResPrefabsGuid || 0 == m_DicResPrefabsGuid.Count)
        {
            return;
        }

        m_DicResPrefabs.Clear();
        int nLen = m_SelectPathList.Count;
        for (int nInedx = 0; nInedx != nLen; ++nInedx)
        {
            //EditorUtility.DisplayCancelableProgressBar("搜索预设引用关系", "搜索中..." + m_SelectPathList[nInedx], nInedx / nLen);
            string strFilePath = m_SelectPathList[nInedx];
            string strFileGUID = m_SelectGUIDList[nInedx];
            List<string> list = null;
            if (!m_DicResPrefabs.TryGetValue(strFileGUID, out list))
            {
                list = new List<string>();
                list.Clear();
                m_DicResPrefabs.Add(strFileGUID, list);
            }

            if (null != list)
            {
                __CheckByGUID(strFileGUID, ref list);
            }

        }

        //EditorUtility.ClearProgressBar();
    }
    //-------------------------------------------------------------------------
    private void __CheckByGUID(string strGUID, ref List<string> list)
    {
        if (null == strGUID || null == list)
        {
            return;
        }

        if (null == m_DicResPrefabsGuid || 0 == m_DicResPrefabsGuid.Count)
        {
            return;
        }

        foreach(string key in m_DicResPrefabsGuid.Keys)
        {
            int nCount = 0;
            List<string> strs = m_DicResPrefabsGuid[key];
            for(int i = 0; i < strs.Count; i++)
            {
                if(strs[i] == strGUID)
                {
                    nCount++;
                }
            }
            if(nCount > 0)
            {
                list.Add(key + " 在此资源中被引用 " + nCount + " 次");
            }
        }
    }

    //-------------------------------------------------------------------------
    /// 
    /// 获取目录下的所有对象路径，去掉了.meta
    /// 
    /// 目录路径
    /// 是否递归获取
    /// 

    private  void __GetFiles(string path, bool recursive = true)
    {
        int index = 0;
        string pattern = @"guid: [0-9a-z]+";
        var resultList = new List<string>();
        var dirArr = Directory.GetFiles(path, "*.prefab", recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly);
        string path_key = "";
        for (int i = 0; i < dirArr.Length; i++)
        {
            // if (Path.GetExtension(dirArr[i]) == ".prefab")
            // {
                path_key = dirArr[i].Replace('\\', '/');
                resultList.Add(path_key);

                string strText = File.ReadAllText(path_key);
                List<string> strs = new List<string>();
                foreach(Match m in Regex.Matches(strText, pattern))
                {
                    string guid = m.Value.Replace("guid: ", "");
                    strs.Add(guid);
                }
                m_DicResPrefabsGuid.Add(path_key, strs);
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("收集所有预制体中的Guid进度","搜索中..." + index + "/" + dirArr.Length, index / dirArr.Length);
                if(isCancel)break;
            // }
            index++;
        }
        EditorUtility.ClearProgressBar();
    }
    //-------------------------------------------------------------------------
    #endregion

}
