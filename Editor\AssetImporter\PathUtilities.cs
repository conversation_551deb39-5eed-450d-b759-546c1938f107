using System.IO;
using System.Linq;
using UnityEngine;

public static class PathUtilities
{
    public const string nonInteralHeader = "Assets/";
    public static readonly string[] textureExtensions = {
        ".tga", ".png", ".psd", ".tga", ".psd", ".exr", ".jpg", ".tif", ".jpg", ".bmp"
    };
    public static string AssetToFilePath(string path)
    {
        path = path.ToUrl();
        if (path.StartsWith(nonInteralHeader))
        {
            path = path.Substring(nonInteralHeader.Length);
            path = Application.dataPath.Open(path);
        }
        path = path.Replace('/', '\\');
        return path;
    }

    public static string FileToAssetPath(string path)
    {
        path = path.ToUrl();
        var dataPath = Application.dataPath;
        if (path.StartsWith(dataPath))
            path = nonInteralHeader + path.Substring(dataPath.Length + 1);
        return path;
    }

    public static string Open(this string first, string second)
    {
        first = first.RemoveEndSeparators();
        second = second.RemoveStartSeparators();
        if (string.IsNullOrEmpty(first))
            return second;
        if (string.IsNullOrEmpty(second))
            return first;
        return first + '/' + second;
    }

    public static string RemoveStartSeparators(this string path)
    {
        return path.TrimStart(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
    }

    public static string RemoveEndSeparators(this string path)
    {
        return path.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
    }

    public static string ToUrl(this string path)
    {
        return path.Replace('\\', '/');
    }

    public static bool IsTexturePath(string assetPath)
    {
        return textureExtensions.Contains(Path.GetExtension(assetPath).ToLower());
    }
}


