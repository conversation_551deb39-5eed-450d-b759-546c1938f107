using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

namespace MobileCompatibility
{
    /// <summary>
    /// 兼容性问题等级
    /// </summary>
    public enum IssueLevel
    {
        [LabelText("错误 - 必须修复")]
        Error,
        [LabelText("警告 - 建议修复")]
        Warning,
        [LabelText("信息 - 优化建议")]
        Info
    }

    /// <summary>
    /// 兼容性问题类型
    /// </summary>
    public enum IssueType
    {
        [LabelText("Mesh兼容性")]
        MeshCompatibility,
        [LabelText("骨骼系统")]
        BoneSystem,
        [LabelText("材质Shader")]
        MaterialShader,
        [LabelText("渲染设置")]
        RenderSettings,
        [LabelText("性能优化")]
        Performance
    }

    /// <summary>
    /// 单个兼容性问题项
    /// </summary>
    [Serializable]
    public class CompatibilityIssue
    {
        [LabelText("问题等级")]
        [ShowInInspector, ReadOnly]
        public IssueLevel Level { get; set; }

        [LabelText("问题类型")]
        [ShowInInspector, ReadOnly]
        public IssueType Type { get; set; }

        [LabelText("问题描述")]
        [ShowInInspector, ReadOnly, MultiLineProperty(3)]
        public string Description { get; set; }

        [LabelText("影响对象")]
        [ShowInInspector, ReadOnly]
        public GameObject AffectedObject { get; set; }

        [LabelText("具体组件")]
        [ShowInInspector, ReadOnly]
        public Component AffectedComponent { get; set; }

        [LabelText("修复建议")]
        [ShowInInspector, ReadOnly, MultiLineProperty(2)]
        public string FixSuggestion { get; set; }

        [LabelText("技术详情")]
        [ShowInInspector, ReadOnly, MultiLineProperty(2)]
        public string TechnicalDetails { get; set; }

        [LabelText("严重程度 (1-10)")]
        [ShowInInspector, ReadOnly, PropertyRange(1, 10)]
        public int Severity { get; set; }

        public CompatibilityIssue(IssueLevel level, IssueType type, string description, 
            GameObject affectedObject, Component affectedComponent = null, 
            string fixSuggestion = "", string technicalDetails = "", int severity = 5)
        {
            Level = level;
            Type = type;
            Description = description;
            AffectedObject = affectedObject;
            AffectedComponent = affectedComponent;
            FixSuggestion = fixSuggestion;
            TechnicalDetails = technicalDetails;
            Severity = severity;
        }
    }

    /// <summary>
    /// 检测统计信息
    /// </summary>
    [Serializable]
    public class DetectionStatistics
    {
        [LabelText("扫描对象总数")]
        [ShowInInspector, ReadOnly]
        public int TotalScannedObjects { get; set; }

        [LabelText("SkinnedMeshRenderer数量")]
        [ShowInInspector, ReadOnly]
        public int SkinnedMeshRendererCount { get; set; }

        [LabelText("检测到的问题总数")]
        [ShowInInspector, ReadOnly]
        public int TotalIssues { get; set; }

        [LabelText("错误数量")]
        [ShowInInspector, ReadOnly]
        public int ErrorCount { get; set; }

        [LabelText("警告数量")]
        [ShowInInspector, ReadOnly]
        public int WarningCount { get; set; }

        [LabelText("信息数量")]
        [ShowInInspector, ReadOnly]
        public int InfoCount { get; set; }

        [LabelText("扫描耗时 (毫秒)")]
        [ShowInInspector, ReadOnly]
        public long ScanDuration { get; set; }

        [LabelText("扫描时间")]
        [ShowInInspector, ReadOnly]
        public DateTime ScanTime { get; set; }

        public DetectionStatistics()
        {
            ScanTime = DateTime.Now;
        }

        public void CalculateIssueCounts(List<CompatibilityIssue> issues)
        {
            TotalIssues = issues.Count;
            ErrorCount = 0;
            WarningCount = 0;
            InfoCount = 0;

            foreach (var issue in issues)
            {
                switch (issue.Level)
                {
                    case IssueLevel.Error:
                        ErrorCount++;
                        break;
                    case IssueLevel.Warning:
                        WarningCount++;
                        break;
                    case IssueLevel.Info:
                        InfoCount++;
                        break;
                }
            }
        }
    }

    /// <summary>
    /// 完整的兼容性检测报告
    /// </summary>
    [Serializable]
    public class MobileCompatibilityReport
    {
        [LabelText("检测统计")]
        [ShowInInspector, ReadOnly, BoxGroup("统计信息")]
        public DetectionStatistics Statistics { get; set; }

        [LabelText("所有问题")]
        [ShowInInspector, ReadOnly]
        public List<CompatibilityIssue> AllIssues { get; set; }

        [LabelText("错误问题")]
        [ShowInInspector, ReadOnly, ShowIf("HasErrors")]
        public List<CompatibilityIssue> ErrorIssues => AllIssues?.FindAll(i => i.Level == IssueLevel.Error) ?? new List<CompatibilityIssue>();

        [LabelText("警告问题")]
        [ShowInInspector, ReadOnly, ShowIf("HasWarnings")]
        public List<CompatibilityIssue> WarningIssues => AllIssues?.FindAll(i => i.Level == IssueLevel.Warning) ?? new List<CompatibilityIssue>();

        [LabelText("信息提示")]
        [ShowInInspector, ReadOnly, ShowIf("HasInfos")]
        public List<CompatibilityIssue> InfoIssues => AllIssues?.FindAll(i => i.Level == IssueLevel.Info) ?? new List<CompatibilityIssue>();

        private bool HasErrors => Statistics?.ErrorCount > 0;
        private bool HasWarnings => Statistics?.WarningCount > 0;
        private bool HasInfos => Statistics?.InfoCount > 0;

        public MobileCompatibilityReport()
        {
            Statistics = new DetectionStatistics();
            AllIssues = new List<CompatibilityIssue>();
        }

        public void AddIssue(CompatibilityIssue issue)
        {
            AllIssues.Add(issue);
            Statistics.CalculateIssueCounts(AllIssues);
        }

        public void ClearIssues()
        {
            AllIssues.Clear();
            Statistics.CalculateIssueCounts(AllIssues);
        }

        /// <summary>
        /// 按类型获取问题
        /// </summary>
        public List<CompatibilityIssue> GetIssuesByType(IssueType type)
        {
            return AllIssues?.FindAll(i => i.Type == type) ?? new List<CompatibilityIssue>();
        }

        /// <summary>
        /// 按等级获取问题
        /// </summary>
        public List<CompatibilityIssue> GetIssuesByLevel(IssueLevel level)
        {
            return AllIssues?.FindAll(i => i.Level == level) ?? new List<CompatibilityIssue>();
        }

        /// <summary>
        /// 获取问题摘要文本
        /// </summary>
        public string GetSummaryText()
        {
            if (Statistics.TotalIssues == 0)
            {
                return "恭喜！未发现兼容性问题。";
            }

            return $"检测完成：发现 {Statistics.TotalIssues} 个问题 " +
                   $"(错误: {Statistics.ErrorCount}, 警告: {Statistics.WarningCount}, 信息: {Statistics.InfoCount})";
        }
    }
} 