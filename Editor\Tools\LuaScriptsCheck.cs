﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;
using System;
using System.Threading;

struct attrInfo {
    public int refNum;
    public List<string> refList;
    public List<int> refLine;
}

struct funInfo {
    public Dictionary<string, attrInfo> dic;
}

public class LuaScriptsCheck : EditorWindow
{
    public UnityEngine.Object file;
    private Dictionary<string, funInfo> luaDic = new Dictionary<string, funInfo>();
    private bool logFlag = false;
    private string thisFunStr = "";
    private int line = 0;

    [MenuItem("自定义工具/Lua变量重命名检查")]
    static void Init()
    {
        LuaScriptsCheck window = GetWindow(typeof(LuaScriptsCheck), false, "LuaScriptsCheck") as LuaScriptsCheck;
        window.Show();
		window.Test();
    }

    void OnGUI()
    {
        EditorGUILayout.BeginHorizontal();
        file = EditorGUILayout.ObjectField("Lua 代码检查目录: ", file, typeof(UnityEngine.Object), false);
        if (GUILayout.Button("Check"))
        {
            CheckLua();
           //Test();
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("OpenLog"))
        {
            OpenLog();
            //Test();
        }

        if (GUILayout.Button("ChangeLogFlag"))
        {
            logFlag = !logFlag;
        }
        EditorGUILayout.EndHorizontal();
    }

    void OpenLog()
    {
        string logPath = System.IO.Directory.GetCurrentDirectory() + "//CheckLuaLog.txt";
        if (File.Exists(logPath))
        {
            System.Diagnostics.Process.Start("notepad.exe", logPath);
        }
        else
        {
            Debug.LogError("日志不存在");
        }
    }

    void CheckLua()
    {
        Debug.LogError("开始构建查询字典");

        if (file == null)
        {
            Debug.LogError("请设置检查目录");
            return;
        }
        luaDic.Clear();

        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        for (int i = 0; i < assetsPath.Length; i++)
        {
            if (assetsPath[i].EndsWith(".lua"))
            {
                //var real_path = assetsPath[i].Replace('\\', '/');
                var real_path = assetsPath[i].Replace("\\", "\\\\");
                if (!File.Exists(real_path))
                {
                    continue;
                }

                FileStream fs = new FileStream(real_path, FileMode.Open, FileAccess.Read);
                StreamReader sr = new StreamReader(fs);

                thisFunStr = "";
                while (!sr.EndOfStream)
                {
                    GetFunByRead(sr, assetsPath[i]);
                }

                sr.Close();
                fs.Close();
                line = 0;
            }
        }

        CheckForFun();
    }

    void CheckForFun()
    {
        Debug.LogError("类字典构建完毕，开始检查");
        List<string> logText = new List<string>();
        foreach (var v in luaDic)
        {
            var value = v.Value;
            foreach (var attr in value.dic)
            {
                var attrValue = attr.Value;
                if (attrValue.refList.Count > 1)
                {
                    var lineIndex = 0;
                    foreach (string path in attrValue.refList)
                    {
                        var lineValue = attrValue.refLine[lineIndex];
                        //Debug.LogFormat("有同个类的变量被多个lua文件赋值, 变量名：{0}， 路径：{1}", attr.Key, path);
                        logText.Add(string.Format("有同个类的变量被多个lua文件赋值, 类名：{0}, 变量名：{1}，第{2}行， 路径：{3}", v.Key, attr.Key, lineValue, path));
                        lineIndex += 1;
                    }

                    logText.Add(" ");
                    logText.Add(" ");
                }
            }
        }

        if (logText.Count > 0)
        {
            Debug.LogError("检查到异常，开始写入Log");
            string logPath = System.IO.Directory.GetCurrentDirectory() + "//CheckLuaLog.txt";
            if (File.Exists(logPath))
            {
                File.Delete(logPath);
            }

            FileStream fs = new FileStream(logPath.Replace("\\", "\\\\"), FileMode.Create, FileAccess.ReadWrite);
            StreamWriter sw = new StreamWriter(fs);
            foreach (var k in logText)
            {
                sw.WriteLine(k);
            }
            sw.Close();
            fs.Close();
        }
        else
        {
            string logPath = System.IO.Directory.GetCurrentDirectory() + "//CheckLuaLog.txt";
            if (File.Exists(logPath))
            {
                File.Delete(logPath);
            }

            Debug.LogError("检测不到异常代码，删除本地日志");
        }

        Debug.LogError("lua 检查结束");
    }


    void GetFunByRead(StreamReader sr, string path)
    {
        if(logFlag)
            Debug.LogError("Check Next Function");

        List<String> list = new List<string>();
        int ignoreEnd = 0;
        int endNum = 0;
        bool isGetFun = false;
        bool funHead = false;
        bool funEnd = false;
        bool moreNotes = false;
        string funKey = "";
        funInfo info;
        info.dic = new Dictionary<string, attrInfo>();
        bool strFlag = false;

        while (!isGetFun)
        {
            if (sr.EndOfStream)
                return;

            string str = sr.ReadLine();
            line += 1;

            //检查是否多行注释结束
            if (Regex.IsMatch(str, @"\s*--\]\]"))
            {
                moreNotes = false;
                var findValue = Regex.Match(str, @"\s*--\]\].*").ToString();
                str = str.Replace(findValue, "");
                //continue;

            }

            //检查是否是多行注释内容
            if (moreNotes)
            {
                continue;
            }

            //检查是否是多行注释
            if (Regex.IsMatch(str, @"\s*--\[\["))
            {
                moreNotes = true;
                var findValue = Regex.Match(str, @"\s*--\[\[.*").ToString();
                str = str.Replace(findValue, "");
                //continue;
            }

            //去掉字符串里面的引号内容
            if (Regex.IsMatch(str, @"[\\]{1}"""))
            {
                var operaList = Regex.Matches(str, @"[\\]{1}""");
                for (int i = 0; i < operaList.Count; i++)
                {
                    foreach (var v in operaList[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            str = str.Replace(v.ToString(), "");
                        }
                    }

                }
            }

            // 处理string文本跨行
            if (Regex.IsMatch(str, @"""") && strFlag)
            {
                var findValue = Regex.Match(str, @".*""").ToString();
                str = str.Replace(findValue, "");
                strFlag = false;
            }

            // 属于跨行字符串内容，忽略
            if (strFlag)
            {
                continue;
            }

            //去掉string内容，避免里面的内容影响检查逻辑
            if (Regex.IsMatch(str, @""""))
            {
                var findValue = Regex.Matches(str, @"""");

                //检查string是否在同一行
                if (findValue.Count % 2 > 0)
                {
                    strFlag = true;
                    int index = str.IndexOf(@"""");
                    str = str.Remove(index);
                }
                else
                {
                    //把这一行里所有的字符串内容去掉
                    var operaList = Regex.Matches(str, @""".*""");
                    for (int i = 0; i < operaList.Count; i ++)
                    {
                        foreach (var v in operaList[i].Groups)
                        {
                            if (v != null && v.ToString() != null)
                            {
                                str = str.Replace(v.ToString(), "");
                            }
                        }
                    }
                }
            }

            //检查是否是注释
            if (Regex.IsMatch(str, @"\s*--"))
            {
                var findValue = Regex.Match(str, @"\s*--.*").ToString();
                str = str.Replace(findValue, "");
                //continue;
            }

            //判断是不是用this语句
            if (Regex.IsMatch(str, @"\s*local\s+this\s*=\s*(\w+)"))
            {
                var findValue = Regex.Match(str, @"\s*local\s+this\s*=\s*(\w+)").Groups[1];
                thisFunStr = findValue.ToString();
            }

            //检查是否是函数开始
            if (Regex.IsMatch(str, @"\s*function\s+(\w*)\s*:\s*\w*(?:.*)"))
            {
                if (str.Contains("UpgradeViewAnimation"))
                {
                    int b = 0;
                }
                if (funHead)
                {
                    var logFun = Regex.Match(str, @"\s*function\s*\w*\s*:\s*(\w*)(?:.*)").Groups[1];
                    Debug.LogFormat("有函数end数量不匹配，类名：{0},函数名：{1}, lua文件：{2}", funKey, logFun, path);
                    return;
                }

                funHead = true;
                var findValue = Regex.Match(str, @"\s*function\s*(\w*)\s*:\s*\w*(?:.*)").Groups[1];
                funKey = findValue.ToString();
                if (funKey.Contains("this") && !string.IsNullOrEmpty(thisFunStr))
                {
                    funKey = thisFunStr;
                }

                if (!luaDic.TryGetValue(funKey, out info))
                {
                    info.dic = new Dictionary<string, attrInfo>();
                    luaDic.Add(funKey, info);
                }
                continue;
            }

            //函数内容
            if (funHead)
            {
                //检查回调
                var funList = Regex.Matches(str, @"[^\w]{1}function[^\w]{1}");
                for (int i = 0; i < funList.Count; i++)
                {
                    foreach (var v in funList[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"[^_]{1}function[^_]{1}");

                            if (logFlag)
                                Debug.LogError("Function flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += funList.Count;

                var funListFirst = Regex.Matches(str, @"^function[^\w]{1}");
                for (int i = 0; i < funListFirst.Count; i++)
                {
                    foreach (var v in funListFirst[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"^function[^_]{1}");

                            if (logFlag)
                                Debug.LogError("Function First flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += funListFirst.Count;

                //检查if
                var ifList = Regex.Matches(str, @"[^\w]{1}if[^\w]{1}");
                for (int i = 0; i < ifList.Count; i++)
                {
                    foreach (var v in ifList[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"[^_]{1}if[^_]{1}");

                            if (logFlag)
                                Debug.LogError("If flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += ifList.Count;

                var ifListFirst = Regex.Matches(str, @"^if[^\w]{1}");
                for (int i = 0; i < ifListFirst.Count; i++)
                {
                    foreach (var v in ifListFirst[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"^if[^_]{1}");

                            if (logFlag)
                                Debug.LogError("If First flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += ifListFirst.Count;

                //检查elseif
                //var eifList = Regex.Matches(str, @"[^\w]+elseif");
                //ignoreEnd += eifList.Count;

                //var eifListFirst = Regex.Matches(str, @"^elseif[^\w]+");
                //ignoreEnd += eifListFirst.Count;

                //检查for
                var forList = Regex.Matches(str, @"[^\w]{1}for[^\w]{1}");
                for (int i = 0; i < forList.Count; i++)
                {
                    foreach (var v in forList[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"[^_]{1}for[^_]{1}");

                            if (logFlag)
                                Debug.LogError("For flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += forList.Count;

                var forListFirst = Regex.Matches(str, @"^for[^\w]{1}");
                for (int i = 0; i < forListFirst.Count; i++)
                {
                    foreach (var v in forListFirst[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"^for[^_]{1}");

                            if (logFlag)
                                Debug.LogError("For First flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            ignoreEnd += realList.Count;
                        }
                    }
                }
                //ignoreEnd += forListFirst.Count;

                var endList = Regex.Matches(str, @"[^\w]{1}end[^\w]{1}");
                for (int i = 0; i < endList.Count; i++)
                {
                    foreach (var v in endList[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"[\s|\(|\)|,|\{|\}]{1}end[\s|\(|\)|,|\{|\}]{1}");

                            if (logFlag)
                                Debug.LogError("End flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            endNum += realList.Count;
                        }
                    }
                }
                //endNum += endList.Count;

                var endListFirst = Regex.Matches(str, @"^end[^\w]{1}");
                for (int i = 0; i < endListFirst.Count; i++)
                {
                    foreach (var v in endListFirst[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"^end[\s|\(|\)|,|\{|\}]{1}");

                            if (logFlag)
                                Debug.LogError("End First flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            endNum += realList.Count;
                        }
                    }
                }

                var endListLast = Regex.Matches(str, @"[^\w]{1}end$");
                for (int i = 0; i < endListLast.Count; i++)
                {
                    foreach (var v in endListLast[i].Groups)
                    {
                        if (v != null && v.ToString() != null)
                        {
                            var realList = Regex.Matches(v.ToString(), @"[\s|\(|\)|,|\{|\}]{1}end$");

                            if (logFlag)
                                Debug.LogError("End Last flag:" + str + "|" + realList.Count + "|" + v.ToString());

                            endNum += realList.Count;
                        }
                    }
                }

                if (str.Equals("end"))
                {
                    if (logFlag)
                        Debug.LogError("Equals End flag:" + str);

                    endNum += 1;
                }
                //endNum += endListFirst.Count;
                //if (Regex.IsMatch(str, @"[^\w+]function"))
                //{
                //    ignoreEnd++;
                //}

                //if (Regex.IsMatch(str, @"[^\w+]end[^\w+]"))
                //{
                //    endNum++;
                //}

                //var findValue = Regex.Matches(str, @"(self\.\w+)\s*=");
                var findValue = Regex.Matches(str, @"((self\.\w+)(,\s*self\.\w+)*)\s*=");
                for (int i = 0; i < findValue.Count; i++)
                {
                    string attrKey = findValue[i].Groups[1].ToString();
                    attrInfo aInfo;
                    var keyList = attrKey.Split(',');
                    foreach (var key in keyList)
                    {
                        string addStr = path;
                        if (info.dic.TryGetValue(key, out aInfo))
                        {
                            if (!aInfo.refList.Contains(addStr))
                            {
                                aInfo.refList.Add(addStr);
                                aInfo.refLine.Add(line);
                            }
                        }
                        else
                        {
                            aInfo.refList = new List<string>();
                            aInfo.refLine = new List<int>();
                            aInfo.refList.Add(addStr);
                            aInfo.refLine.Add(line);
                            info.dic.Add(key, aInfo);
                        }
                    }

                    //if (info.dic.TryGetValue(attrKey, out aInfo))
                    //{
                    //    if (!aInfo.refList.Contains(path))
                    //    {
                    //        aInfo.refList.Add(path);
                    //    }
                    //}
                    //else
                    //{
                    //    aInfo.refList = new List<string>();
                    //    aInfo.refList.Add(path);
                    //    info.dic.Add(attrKey, aInfo);
                    //}
                }

                //end的数量减去if之类的忽略掉的刚好等于1时，函数结束
                if (endNum - ignoreEnd == 1)
                {
                    funEnd = true;
                }
            }

            isGetFun = funHead && funEnd;
        }
    }

    void Test()
    {
        string str = "self.node_list.a= 1,2";
        Debug.LogError("end".Equals(" end"));
        Debug.LogError(Regex.IsMatch(str, @"[\s|\(|\)]+end[\s|\(|\)|""]+"));
        //var findValue = Regex.Matches(str, @"[\\]{1}"""); ;
        //for (int i = 0; i < findValue.Count; i++)
        //{
        //    foreach (var v in findValue[i].Groups)
        //    {
        //        Debug.LogError(v.ToString());
        //    }
        //}

        var operaList = Regex.Matches(str, @"((self\.\w+)(,\s*self\.\w+)*)\s*=");
        for (int i = 0; i < operaList.Count; i++)
        {
            //foreach (var v in operaList[i].Groups[1])
            //{
            //    Debug.LogError(v.ToString());
            //}
            string attrKey = operaList[i].Groups[1].ToString();
            Debug.LogError(attrKey);
            var keyList = attrKey.Split(',');
            foreach (var key in keyList)
            {
                Debug.LogError(key);
            }
        }

        //Debug.LogError(Regex.Match(str, @"[^\\]""").ToString());
    }
}
