﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;

public class YYABTool : Editor {

    [MenuItem("Window/YYMatShader")]
	public static void MatShader()
    {
        

        ABShaderMarkRule.Build();

    }

    

    [MenuItem("Window/YYOnlyCreateShader")]
    public static void OnlyCreateShader()
    {
        OnOnlyCreateShader();

    }

    [MenuItem("Window/YYOnlyCreateShaderABName")]
    public static void OnlyCreateShaderABName()
    {
        OnOnlyCreateShaderABName();

    }



    [MenuItem("Window/YYOnlyCreateMat")]
    public static void OnlyCreateMat()
    {
        OnOnlyCreateMat();
    }

    [MenuItem("Window/YYOnlyCreateMatABName")]
    public static void OnlyCreateMatABName()
    {
        OnOnlyCreateMatABName();
    }

    [MenuItem("Window/YYOnlyChargeMatShader")]
    public static void OnlyChargeMatShader()
    {

        OnOnlyChargeMatShader();
    }


    [MenuItem("Window/YYMatShaderBack")]
    public static void MatShaderBack()
    {
       
        ABShaderMarkRule.BuildBack();
    }

   

    private static void OnOnlyCreateShader()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            Shader newShader;
            string newShaderName;
            bool bb = ABShaderMarkRule.BuildOneShader(mat, out newShader, out newShaderName, true);

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    private static void OnOnlyCreateShaderABName()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            Shader newShader;
            string newShaderName;
            bool bb = ABShaderMarkRule.BuildOneShader(mat, out newShader, out newShaderName, false,true);

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    public static void OnOnlyCreateMat()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        AssetDatabase.StartAssetEditing();
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            Shader newShader;
            string newShaderName;
            bool bb = ABShaderMarkRule.BuildOneShader(mat, out newShader, out newShaderName, true);
            if (bb)
            {
                //mat.shader = newShader;

                ABShaderMarkRule.CheckMat(mat, newShaderName, newShader, true);
            }

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }

        EditorUtility.ClearProgressBar();

        AssetDatabase.StopAssetEditing();
    }

    public static void OnOnlyCreateMatABName()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        AssetDatabase.StartAssetEditing();
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            Shader newShader;
            string newShaderName;
            bool bb = ABShaderMarkRule.BuildOneShader(mat, out newShader, out newShaderName, false);
            if (bb)
            {
                //mat.shader = newShader;

                ABShaderMarkRule.CheckMat(mat, newShaderName, newShader, false,true);
            }

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }

        EditorUtility.ClearProgressBar();

        AssetDatabase.StopAssetEditing();
    }


    public static void OnOnlyChargeMatShader()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        AssetDatabase.StartAssetEditing();
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            Shader newShader;
            string newShaderName;
            bool bb = ABShaderMarkRule.BuildOneShader(mat, out newShader, out newShaderName, false, false, true);
            if (bb)
            {
                var oldRenderQueue = mat.renderQueue;
                if (mat.shader.name != newShaderName)
                {
                    mat.shader = newShader;
                    mat.renderQueue = oldRenderQueue;
                }
            }

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }

        EditorUtility.ClearProgressBar();

        AssetDatabase.StopAssetEditing();
    }



}
