﻿#class_name# = #class_name# or BaseClass()

function #class_name#:__init()
	if #class_name#.Instance then
		print_error("[#class_name#] Attempt to create singleton twice!")
		return
	end

	#class_name#.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.XXX, BindTool.Bind(self.GetXXXRemind, self))
end

function #class_name#:__delete()
    RemindManager.Instance:UnRegister(RemindName.XXX)
    #class_name#.Instance = nil
end

----------------------------init_start-----------------------------
function #class_name#:InitParam()

end

function #class_name#:InitConfig()
    -- local cfg = ConfigManager.Instance:GetAutoConfig("XXX_auto")
    -- self.xxx_map_cfg = ListToMap(cfg.xxx, "param_1", "param_2")
    -- self.xxx_map_list_cfg = ListToMapList(cfg.xxx, "param_1", "param_2")
end
-----------------------------init_end------------------------------

--------------------------protocol_start---------------------------
function #class_name#:Set#class_name#Info(protocol)

end
----------------------------protocol_end---------------------------

----------------------------remind_start---------------------------
function #class_name#:GetXXXRemind()
    return 0
end
-----------------------------remind_end----------------------------
--------------------------common_get_start------------------------

--------------------------common_get_end-------------------------