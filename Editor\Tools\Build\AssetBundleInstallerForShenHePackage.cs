﻿using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace Build
{
    public static class AssetBundleInstallerForShenHePackage
    {
        [MenuItem("自定义工具/发布版本/审核包/拷资源进安卓工程审核包目录(Android)")]
        public static void CopyInstallEncryptBundlesToShenHePackage()
        {
            EncryptMgr.RefreshEncryptKey();

            BuildPlatType buildPlatType = BuildPlatType.AndroidDev;
            InstallBundleSize sizeType = InstallBundleSize.sizeM;
            // 先删除旧目录
            string assetbundlePath = BuilderConfig.GetAssetBundlePath(BuildPlatType.AndroidDev).Replace("Android", "Audit");
            string target_path = (assetbundlePath + "/../../../sdk/android/dev/src/main/assets/AssetBundle").Replace("\\", "/");
            if (Directory.Exists(target_path))
            {
                Directory.Delete(target_path, true);
            }
            Directory.CreateDirectory(target_path);

            // 先删除旧lua目录
            var lua_target_path = Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle"));
            Directory.CreateDirectory(lua_target_path);

            // 拷版本号文件
            string bundle_path = BuilderConfig.GetAssetBundlePath(buildPlatType).Replace("Android", "Audit");
            var versionPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("version.txt")));
            File.Copy(versionPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("version.txt")));

            // 拷mainfest文件
            var manifestPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle")));
            File.Copy(manifestPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle")));
            var assetBundleLuaPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle.lua")));
            File.Copy(assetBundleLuaPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle.lua")));

            // 拷Lua mainfest文件
            var luaManifestPath = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle")));
            File.Copy(luaManifestPath, Path.Combine(lua_target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle")));
            var path = Path.GetFullPath(Path.Combine(bundle_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle.lua")));
            File.Copy(path, Path.Combine(lua_target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle.lua")));

            // 拷进包AssetBundle文件
            AssetBundleInstaller.CopyInstallEncryptBundles(buildPlatType, sizeType, bundle_path, target_path);

            // 写file_txt文件
            WriteEncryptFileListTxt();

            Resources.UnloadUnusedAssets();
        }

        [MenuItem("自定义工具/发布版本/审核包/拷资源进安卓工程审核包目录(iOS)")]
        public static void CopyInstallEncryptBundlesToiOSShenHePackage()
        {
            EncryptMgr.RefreshEncryptKey();
            BuildPlatType buildPlatType = BuildPlatType.Audit;
            InstallBundleSize sizeType = InstallBundleSize.sizeS;
            // 先删除旧目录
            string assetbundlePath = BuilderConfig.GetAssetBundlePath(buildPlatType);
            string target_path = (assetbundlePath + "/../../../sdk/ios/its/Data/Raw/AssetBundle").Replace("\\", "/");
            if (Directory.Exists(target_path))
            {
                Directory.Delete(target_path, true);
            }
            Directory.CreateDirectory(target_path);

            // 先删除旧lua目录
            var lua_target_path = Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle"));
            Directory.CreateDirectory(lua_target_path);

            // 拷版本号文件
            var versionPath = Path.GetFullPath(Path.Combine(assetbundlePath, EncryptMgr.GetStreamingEncryptPath2Base64Editor("version.txt")));
            File.Copy(versionPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("version.txt")));

            // 拷mainfest文件
            var manifestPath = Path.GetFullPath(Path.Combine(assetbundlePath, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle")));
            File.Copy(manifestPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle")));
            var assetBundleLuaPath = Path.GetFullPath(Path.Combine(assetbundlePath, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle.lua")));
            File.Copy(assetBundleLuaPath, Path.Combine(target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle.lua")));

            // 拷Lua mainfest文件
            var luaManifestPath = Path.GetFullPath(Path.Combine(assetbundlePath, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle")));
            File.Copy(luaManifestPath, Path.Combine(lua_target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle")));
            var path = Path.GetFullPath(Path.Combine(assetbundlePath, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle/LuaAssetBundle.lua")));
            File.Copy(path, Path.Combine(lua_target_path, EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle.lua")));

            // 拷进包AssetBundle文件
            AssetBundleInstaller.CopyInstallEncryptBundles(buildPlatType, sizeType, assetbundlePath, target_path);

            // 写file_txt文件
            WriteEncryptFileListTxtForiOS();

            Resources.UnloadUnusedAssets();
        }

        //[MenuItem("自定义工具/发布版本/testtest")]
        public static void WriteEncryptFileListTxt()
        {
            string targetPath = Application.dataPath + "/../../sdk/android/dev/src/main/assets/AssetBundle";
            string file_list_path = Path.Combine(targetPath.Replace("AssetBundle", ""), EncryptMgr.GetStreamingEncryptPath2Base64Editor("file_list.txt"));

            string[] files = Directory.GetFiles(targetPath, "*.*", SearchOption.AllDirectories);
            var fileList = new StringBuilder();
            foreach (var path in files)
            {
                if (Directory.Exists(path) || path.EndsWith("dmVyc2lvbg==.txt") || path.EndsWith("QXNzZXRCdW5kbGU=.lua") || path.EndsWith("THVhQXNzZXRCdW5kbGU=.lua"))
                {
                    continue;
                }
                
                string _path = path.Replace("\\", "/");
                int indexOf = _path.IndexOf("AssetBundle/");
                string relPath = _path.Substring(indexOf + 12, _path.Length - indexOf - 12);

                fileList.Append("AssetBundle/" + EncryptMgr.GetDecryptPath2Base64(relPath));
                fileList.Append('\n');
            }
            File.WriteAllText(file_list_path, fileList.ToString());
            DeployToolForEncrypt.XOREncryptAB(file_list_path);
        }

        //[MenuItem("自定义工具/发布版本/testtest2")]
        public static void WriteEncryptFileListTxtForiOS()
        {
            string targetPath = Application.dataPath + "/../../sdk/ios/its/Data/Raw/AssetBundle";
            string file_list_path = Path.Combine(targetPath.Replace("AssetBundle", ""), EncryptMgr.GetStreamingEncryptPath2Base64Editor("file_list.txt"));

            string[] files = Directory.GetFiles(targetPath, "*.*", SearchOption.AllDirectories);
            var fileList = new StringBuilder();
            foreach (var path in files)
            {
                if (Directory.Exists(path)
                    || path.EndsWith(EncryptMgr.GetStreamingEncryptPath2Base64Editor("version.txt"))
                    || path.EndsWith(EncryptMgr.GetStreamingEncryptPath2Base64Editor("AssetBundle.lua"))
                    || path.EndsWith(EncryptMgr.GetStreamingEncryptPath2Base64Editor("LuaAssetBundle.lua"))
                    || path.Contains(".DS_Store"))
                {
                    continue;
                }

                string _path = path.Replace("\\", "/");
                int indexOf = _path.IndexOf("AssetBundle/");
                string relPath = _path.Substring(indexOf + 12, _path.Length - indexOf - 12);

                fileList.AppendLine("AssetBundle/" + EncryptMgr.GetStreamingDecryptPath2Base64(relPath));
            }

            string sdkResPath = Application.dataPath + "/../../sdk/ios/its/Data/Raw/sdk_res";
            if (Directory.Exists(sdkResPath))
            {
                string[] sdkResFiles = Directory.GetFiles(sdkResPath, "*.*", SearchOption.AllDirectories);
                foreach (string s in sdkResFiles)
                {
                    int indexOf = s.IndexOf("sdk_res");
                    string ss = s.Substring(indexOf, s.Length - indexOf);
                    fileList.AppendLine(ss);
                }
            }

            File.WriteAllText(file_list_path, fileList.ToString());
            DeployToolForEncrypt.XOREncryptAB(file_list_path);
        }
    }
}