﻿using Nirvana;
using Nirvana.Editor;
using System;
using System.IO;
using UnityEditor;
using UnityEngine;

[CreateAssetMenu(fileName = "BuildSettingNew", menuName = "Nirvana/Build/BuildSettingNew")]
public sealed class BuildSettingNew : ScriptableObject
{
    [SerializeField]
    private string buildName;

    [SerializeField]
    [EnumLabel]
    private BuildDevice buildDevice;

    [SerializeField]
    private BuildConfigNew[] configs;

    [SerializeField]
    private BuildCustomProcess[] customProcesses;

    [SerializeField]
    private bool development;

    [SerializeField]
    private bool allowDebugging;

    [SerializeField]
    private bool connectToHost;

    [SerializeField]
    private bool connectWithProfiler;

    private BuildConfigDataNew configData;

    private bool refreshConfigData = true;

    public string BuildName => buildName;

    public BuildConfigDataNew ConfigData
    {
        get
        {
            if (refreshConfigData)
            {
                RefreshBuildConfig();
            }

            return configData;
        }
    }

    public void ActiveSetting(bool isExportProject)
    {
        AssetDatabase.Refresh();
        BuildConfigDataNew buildConfigData = ConfigData;
        PlayerSettings.companyName = buildConfigData.CompanyName;
        if (isExportProject)
        {
            PlayerSettings.productName = ((buildDevice == BuildDevice.iOS) ? "ios" : "base");
        }
        else
        {
            PlayerSettings.productName = buildConfigData.ProductName;
        }

        PlayerSettings.bundleVersion = buildConfigData.Version;
        if (buildConfigData.SplashScreen != null)
        {
            buildConfigData.SplashScreen.ActiveSetting();
        }
        else
        {
            SplashScreenConfigNew.ResetSettins();
        }

        if (buildConfigData.DefineSymbols != null && buildConfigData.DefineSymbols.Length != 0)
        {
            string text = string.Empty;
            string[] defineSymbols = buildConfigData.DefineSymbols;
            foreach (string text2 in defineSymbols)
            {
                text = text + text2 + " ";
            }

            BuildTargetGroup buildTargetGroup = buildDevice.GetBuildTargetGroup();
            PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, text);
        }

        switch (buildDevice)
        {
            case BuildDevice.iOS:
                ActiveIOSSetting();
                break;
            case BuildDevice.Android:
                ActiveAndroidSetting();
                break;
            case BuildDevice.Desktop:
            case BuildDevice.Desktop32:
                ActiveDesktopSetting();
                break;
        }

        if (customProcesses == null)
        {
            return;
        }

        BuildTarget buildTarget = buildDevice.GetBuildTarget();
        BuildCustomProcess[] array = customProcesses;
        foreach (BuildCustomProcess buildCustomProcess in array)
        {
            if (buildCustomProcess != null)
            {
                buildCustomProcess.ActiveSetting(buildTarget);
            }
        }
    }

    public void BuildPlayer(string assetBundlePath, string outputPath, bool isExportProject = false)
    {
        ActiveSetting(isExportProject);
        string text = string.Empty;
        if (!isExportProject)
        {
            text = Path.Combine(assetBundlePath, "AssetBundle.manifest");
            if (!File.Exists(text))
            {
                string message = $"The {text} is not existed.\nDo you still want to continue?";
                if (!EditorUtility.DisplayDialog("Build Error", message, "Continue", "Cancel"))
                {
                    return;
                }

                text = null;
            }
        }

        if (isExportProject)
        {
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }
        }
        else
        {
            string directoryName = Path.GetDirectoryName(outputPath);
            if (!Directory.Exists(directoryName))
            {
                Directory.CreateDirectory(directoryName);
            }
        }

        BuildPlayerOptions buildPlayerOptions = default(BuildPlayerOptions);
        buildPlayerOptions.scenes = ConfigData.Scenes;
        buildPlayerOptions.locationPathName = outputPath;
        buildPlayerOptions.target = buildDevice.GetBuildTarget();
        buildPlayerOptions.targetGroup = buildDevice.GetBuildTargetGroup();
        buildPlayerOptions.options = GetBuildOptions(isExportProject);
        buildPlayerOptions.assetBundleManifestPath = text;
        BuildPipeline.BuildPlayer(buildPlayerOptions);
    }

    internal static BuildSettingNew[] FindSettings()
    {
        string[] array = AssetDatabase.FindAssets("t:BuildSetting");
        BuildSettingNew[] array2 = new BuildSettingNew[array.Length];
        for (int i = 0; i < array.Length; i++)
        {
            string guid = array[i];
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            array2[i] = AssetDatabase.LoadAssetAtPath<BuildSettingNew>(assetPath);
        }

        return array2;
    }

    internal void RefreshBuildConfig()
    {
        if (configs == null)
        {
            return;
        }

        configData = default(BuildConfigDataNew);
        BuildConfigNew[] array = configs;
        foreach (BuildConfigNew buildConfig in array)
        {
            if (buildConfig != null)
            {
                buildConfig.SetupConfig(ref configData);
            }
        }

        refreshConfigData = false;
    }

    private void ActiveIOSSetting()
    {
        BuildConfigDataNew buildConfigData = ConfigData;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, buildConfigData.IOSApplicationIdentifier);
        PlayerSettings.iOS.buildNumber = MacroTextNew.ProcessSystemMacro(buildConfigData.IOSBuildNumber);
        PlayerSettings.iOS.applicationDisplayName = buildConfigData.IOSApplicationDisplayName;
        PlayerSettings.iOS.appleEnableAutomaticSigning = buildConfigData.IOSEnableAutomaticSigning;
        PlayerSettings.iOS.appleDeveloperTeamID = buildConfigData.IOSDeveloperTeamID;
        PlayerSettings.iOS.iOSManualProvisioningProfileID = buildConfigData.IOSProvisioningProfile;

        //XCodePostProcess.ProductNameI18N = buildConfigData.ProductNameI18N;
        //XCodePostProcess.EnableBitCode = buildConfigData.IOSEnableBitCode;
        //XCodePostProcess.CodeSignIdentity = buildConfigData.IOSCodeSignIdentity;
        //XCodePostProcess.ClearPlistPair();
        //XCodePostProcess.ClearFrameworks();
        if (buildConfigData.IOSFrameworks != null)
        {
            //string[] iOSFrameworks = buildConfigData.IOSFrameworks;
            //foreach (string framework in iOSFrameworks)
            //{
            //    XCodePostProcess.AddFramework(framework, weak: false);
            //}
        }

        Texture2D[] icons = new Texture2D[19]
        {
                buildConfigData.IOSIcon180, buildConfigData.IOSIcon167, buildConfigData.IOSIcon152, buildConfigData.IOSIcon144, buildConfigData.IOSIcon120, buildConfigData.IOSIcon114, buildConfigData.IOSIcon76, buildConfigData.IOSIcon72, buildConfigData.IOSIcon57, buildConfigData.IOSSpotlightIcon120,
                buildConfigData.IOSSpotlightIcon80, buildConfigData.IOSSpotlightIcon40, buildConfigData.IOSSettingsIcon87, buildConfigData.IOSSettingsIcon58, buildConfigData.IOSSettingsIcon29, buildConfigData.IOSNotificationIcon60, buildConfigData.IOSNotificationIcon40, buildConfigData.IOSNotificationIcon20, buildConfigData.IOSAppstoreIcon1024
        };
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.iOS, icons);
    }

    private void ActiveAndroidSetting()
    {
        BuildConfigDataNew buildConfigData = ConfigData;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, buildConfigData.AndroidApplicationIdentifier);
        PlayerSettings.Android.bundleVersionCode = buildConfigData.AndroidBundleVersionCode;
        PlayerSettings.Android.keystoreName = buildConfigData.AndroidKeystoreName;
        PlayerSettings.Android.keystorePass = buildConfigData.AndroidKeystorePass;
        PlayerSettings.Android.keyaliasName = buildConfigData.AndroidKeyaliasName;
        PlayerSettings.Android.keyaliasPass = buildConfigData.AndroidKeyaliasPass;
        Texture2D[] icons = new Texture2D[6] { buildConfigData.AndroidIcon192, buildConfigData.AndroidIcon144, buildConfigData.AndroidIcon96, buildConfigData.AndroidIcon72, buildConfigData.AndroidIcon48, buildConfigData.AndroidIcon36 };
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Android, icons);
        if (buildConfigData.ProductNameI18N != null)
        {
            buildConfigData.ProductNameI18N.ActiveAndroidSetting();
        }
    }

    private void ActiveDesktopSetting()
    {
        BuildConfigDataNew buildConfigData = ConfigData;
        PlayerSettings.fullScreenMode = (buildConfigData.DesktopDefaultIsFullScreen ? FullScreenMode.FullScreenWindow : FullScreenMode.ExclusiveFullScreen);
        PlayerSettings.defaultIsNativeResolution = buildConfigData.DesktopDefaultIsNativeResolution;
        PlayerSettings.defaultScreenWidth = buildConfigData.DesktopDefaultScreenWidth;
        PlayerSettings.defaultScreenHeight = buildConfigData.DesktopDefaultScreenHeight;
        PlayerSettings.resizableWindow = buildConfigData.DesktopResizableWindow;
        Texture2D[] icons = new Texture2D[8] { buildConfigData.DesktopIcon1024, buildConfigData.DesktopIcon512, buildConfigData.DesktopIcon256, buildConfigData.DesktopIcon128, buildConfigData.DesktopIcon128, buildConfigData.DesktopIcon48, buildConfigData.DesktopIcon32, buildConfigData.DesktopIcon16 };
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Standalone, icons);
    }

    private BuildOptions GetBuildOptions(bool isExportProject)
    {
        BuildOptions buildOptions = BuildOptions.CompressWithLz4;
        PlatformID platform = Environment.OSVersion.Platform;
        PlatformID platformID = platform;
        if (platformID == PlatformID.Unix || platformID == PlatformID.MacOSX)
        {
            buildOptions |= BuildOptions.SymlinkSources;
        }

        if (development)
        {
            buildOptions |= BuildOptions.Development;
        }

        if (allowDebugging)
        {
            buildOptions |= BuildOptions.AllowDebugging;
        }

        if (connectToHost)
        {
            buildOptions |= BuildOptions.ConnectToHost;
        }

        if (connectWithProfiler)
        {
            buildOptions |= BuildOptions.ConnectWithProfiler;
        }

        if (buildDevice == BuildDevice.Android)
        {
            if (isExportProject)
            {
                EditorUserBuildSettings.androidBuildSystem = AndroidBuildSystem.Gradle;
                EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
                EditorUserBuildSettings.il2CppCodeGeneration = UnityEditor.Build.Il2CppCodeGeneration.OptimizeSpeed;
                buildOptions |= BuildOptions.AcceptExternalModificationsToPlayer;
            }
            else
            {
                EditorUserBuildSettings.androidBuildSystem = AndroidBuildSystem.Gradle;
            }
        }

        return buildOptions;
    }
}
