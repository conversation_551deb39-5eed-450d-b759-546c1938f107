﻿using LuaInterface;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[ExecuteInEditMode]
public class ChangeSkin : MonoBehaviour
{
    public enum SkinType
    {
        Body,
        Face,
        Hair,
        Eyeball,
    }

    [SerializeField] private GameObject bodyObj;
    [SerializeField] private GameObject faceObj;
    [SerializeField] private GameObject hairObj;
    [SerializeField] private GameObject eyeballObj;

    private SkinnedMeshRenderer bodyLODMesh;
    private SkinnedMeshRenderer hairLODMesh;
    private SkinnedMeshRenderer faceLODMesh;
    private SkinnedMeshRenderer eyeballLODMesh;
    private ActorRender cachedActorRender;

    private Dictionary<int, SkinnedMeshRenderer> skinDic = new Dictionary<int, SkinnedMeshRenderer>();
    private static readonly Dictionary<string, SkinType> SkinNameMap = new Dictionary<string, SkinType>
    {
        { "RoleBody", SkinType.Body }, { "RoleFace", SkinType.Face },
        { "RoleHair", SkinType.Hair }, { "RoleEyeball", SkinType.Eyeball }
    };

    private Color[] PartIndexDefultColor = new Color[32];
    private Color PartHairDefultColor;

    [System.Serializable]
    private class SkinDictionaryEntry
    {
        public SkinType type;
        public SkinnedMeshRenderer renderer;
    }
    [SerializeField] private List<SkinDictionaryEntry> editorSkinList = new List<SkinDictionaryEntry>();   // 编辑器用

    private Dictionary<string, Transform> boneDic = new Dictionary<string, Transform>();    // 骨骼字典
    private static List<Transform[]> useBonesPool = new List<Transform[]>();    // 骨骼数组对象池

    #region 头部细节调整参数
    public enum HeadCustomizationType
    {
        // 眼部模块
        EyeSize,            // 眼眶大小 - BlendShape
        EyePosition,        // 眼眶上下 - BlendShape
        EyeAngle,           // 眼眶角度 - BlendShape
        EyeClose,           // 眼眶开合 - BlendShape
        EyebrowAngle,       // 眉毛角度 - BlendShape
        EyeShadowColor,     // 眼影颜色 - 材质属性
        EyeballPosition,    // 眼球上下 - BlendShape

        // 瞳孔模块
        PupilType_Left,     // 瞳孔款式 - 材质属性（贴图）
        IrisSize_Left,      // 虹膜大小 - 材质属性
        PupilSize_Left,     // 瞳孔大小 - 材质属性
        PupilColor_Left,    // 瞳孔颜色 - 材质属性

        PupilType_Right,    // 瞳孔款式 - 材质属性（贴图）
        IrisSize_Right,     // 虹膜大小 - 材质属性
        PupilSize_Right,    // 瞳孔大小 - 材质属性
        PupilColor_Right,   // 瞳孔颜色 - 材质属性

        // 鼻子模块
        NoseSize,           // 鼻子大小 - BlendShape
        NoseAngle,          // 鼻头角度 - BlendShape

        // 嘴巴模块
        MouthSize,          // 嘴巴大小 - BlendShape
        MouthPosition,      // 嘴巴上下 - BlendShape
        MouthAngle,         // 嘴角角度 - BlendShape
        MouthColor,         // 嘴巴颜色 - 材质属性

        // 脸颊
        CheekSize,          // 脸颊大小 - BlendShape

        // 下巴
        ChinLength,         // 下巴长度 - BlendShape

        // 脸部贴花
        FaceDecalTex,           // 脸部贴花贴图 - 材质属性（贴图）
        FaceDecalTexST,         // 脸部贴花缩放偏移 - 材质属性
        FaceDecalFade,          // 脸部贴花褪色 - 材质属性
        FaceDecalMirror,        // 脸部贴花镜像 - 材质属性

        // 头发模块
        HairColor,          // 头发颜色 - 材质属性
        FaceDecalColor,         // 脸部贴花颜色 - 材质属性
        FaceBaseTex,            // 脸部贴图 - 材质属性
        BodyBaseColor,            // 身体颜色 - 材质属性
    }

    [System.Serializable]
    private class BlendShapeConfig
    {
        public HeadCustomizationType type;
        public string positiveShape;
        public string negativeShape;
        [Range(-100, 100)] public float weight;
        public SkinType partType;
    }

    [System.Serializable]
    private class DyeColorItemList
    {
        public List<DyeColorItem> dye_list;
    }

    [System.Serializable]
    private class DyeColorItem
    {
        public int dye_index;
        public float r;
        public float g;
        public float b;
        public float a;
    }

    [SerializeField]
    private BlendShapeConfig[] blendShapeConfigs =
    {
        new BlendShapeConfig{ type = HeadCustomizationType.EyeSize, positiveShape = "eye+", negativeShape = "eye-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.EyePosition, positiveShape = "eyepos+", negativeShape = "eyepos-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.EyeAngle, positiveShape = "eyeangle+", negativeShape = "eyeangle-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.EyeClose, positiveShape = "eyeclose+", negativeShape = "eyeclose-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.EyebrowAngle, positiveShape = "eyebrowangle+", negativeShape = "eyebrowangle-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.NoseSize, positiveShape = "nosesize+", negativeShape = "nosesize-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.NoseAngle, positiveShape = "noseangle+", negativeShape = "noseangle-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.MouthAngle, positiveShape = "mouthangle+", negativeShape = "mouthangle-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.MouthSize, positiveShape = "mouthsize+", negativeShape = "mouthsize-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.MouthPosition, positiveShape = "mouthpos+", negativeShape = "mouthpos-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.CheekSize, positiveShape = "cheek+", negativeShape = "cheek-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.ChinLength, positiveShape = "chin+", negativeShape = "chin-", weight = 0, partType = SkinType.Face, },
        new BlendShapeConfig{ type = HeadCustomizationType.EyeballPosition, positiveShape = "eyepos+", negativeShape = "eyepos-", weight = 0, partType = SkinType.Eyeball, },
    };
    private Dictionary<HeadCustomizationType, BlendShapeConfig> blendShapeDict;

    [SerializeField] private Color eyeShadowColor = Color.white;
    [SerializeField] private Color mouthColor = Color.white;
    [SerializeField] private Texture2D pupilTypeRight;
    [SerializeField, Range(0.5f, 3)] private float irisSizeRight = 0.5f;
    [SerializeField, Range(0.5f, 10)] private float pupilSizeRight = 0.5f;
    [SerializeField] private Color pupilColorRight = Color.white;
    [SerializeField] private Texture2D pupilTypeLeft;
    [SerializeField, Range(0.5f, 3)] private float irisSizeLeft = 0.5f;
    [SerializeField, Range(0.5f, 10)] private float pupilSizeLeft = 0.5f;
    [SerializeField] private Color pupilColorLeft = Color.white;
    [SerializeField] private Texture2D faceDecalTex;
    [SerializeField] private Vector4 faceDecalTexST;
    [SerializeField, Range(0, 1)] private float faceDecalFade;
    [SerializeField] private float faceDecalMirror;
    [SerializeField] private Color faceDecalColor = Color.white;
    [SerializeField] private Color hairColor = Color.white;
    [SerializeField] private Texture2D faceBaseTex;
    [SerializeField] private Color bodyBaseColor = Color.white;

    #region MaterialPropertyBlock 对象池
    private static readonly UnityEngine.Pool.ObjectPool<MaterialPropertyBlock> BlockPool = new UnityEngine.Pool.ObjectPool<MaterialPropertyBlock>(
        createFunc: () => new MaterialPropertyBlock(), // 创建新实例
        actionOnGet: block => block.Clear(),           // 取出时重置
        actionOnRelease: block => block.Clear(),       // 放回时重置
        collectionCheck: false,                        // 关闭集合检查
        defaultCapacity: 5                             // 默认容量
    );

    private void SafeReleaseBlock(MaterialPropertyBlock block)
    {
        if (block != null)
        {
            BlockPool.Release(block);
        }
    }
    #endregion

    [System.Serializable]
    public struct MaterialPropertyConfig
    {
        public string propertyName;
        public SkinType partType;
        public int[] materialIndices; // 影响的材质索引数组
    }

    // 材质属性映射
    private static readonly Dictionary<HeadCustomizationType, MaterialPropertyConfig> materialPropertyMap = new()
    {
        // 面部材质属性
        [HeadCustomizationType.EyeShadowColor] = new MaterialPropertyConfig
        {
            propertyName = "_EyeShadowColor",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.MouthColor] = new MaterialPropertyConfig
        {
            propertyName = "_LipColor",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceDecalTex] = new MaterialPropertyConfig
        {
            propertyName = "_DecalTex",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceDecalTexST] = new MaterialPropertyConfig
        {
            propertyName = "_DecalTex_ST",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceDecalFade] = new MaterialPropertyConfig
        {
            propertyName = "_DecalFade",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceDecalMirror] = new MaterialPropertyConfig
        {
            propertyName = "_DecalIsMirror",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceDecalColor] = new MaterialPropertyConfig
        {
            propertyName = "_DecalColor",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.FaceBaseTex] = new MaterialPropertyConfig
        {
            propertyName = "_BaseMap",
            partType = SkinType.Face,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.BodyBaseColor] = new MaterialPropertyConfig
        {
            propertyName = "_Color",
            partType = SkinType.Body,
            materialIndices = new[] { 0 }
        },

        // 眼球材质属性
        [HeadCustomizationType.PupilType_Right] = new MaterialPropertyConfig
        {
            propertyName = "_MainTex",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.IrisSize_Right] = new MaterialPropertyConfig
        {
            propertyName = "_EyesSize",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.PupilSize_Right] = new MaterialPropertyConfig
        {
            propertyName = "_PupilSize",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 0 }
        },
        [HeadCustomizationType.PupilColor_Right] = new MaterialPropertyConfig
        {
            propertyName = "_EyesColor",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 0 }
        },

        // 眼球材质属性
        [HeadCustomizationType.PupilType_Left] = new MaterialPropertyConfig
        {
            propertyName = "_MainTex",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 1 }
        },
        [HeadCustomizationType.IrisSize_Left] = new MaterialPropertyConfig
        {
            propertyName = "_EyesSize",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 1 }
        },
        [HeadCustomizationType.PupilSize_Left] = new MaterialPropertyConfig
        {
            propertyName = "_PupilSize",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 1 }
        },
        [HeadCustomizationType.PupilColor_Left] = new MaterialPropertyConfig
        {
            propertyName = "_EyesColor",
            partType = SkinType.Eyeball,
            materialIndices = new[] { 1 }
        },

        // 头发材质属性
        [HeadCustomizationType.HairColor] = new MaterialPropertyConfig
        {
            propertyName = "_HairColor",
            partType = SkinType.Hair,
            materialIndices = new[] { 1 }
        },
    };

    private Dictionary<SkinType, List<(HeadCustomizationType, float)>> _batchBlendShapesList = new();
    private Dictionary<SkinType, List<(HeadCustomizationType, object)>> _batchMaterialsList = new();
    #endregion








    private void Awake()
    {
        cachedActorRender = GetComponent<ActorRender>();
        blendShapeDict = blendShapeConfigs.ToDictionary(c => c.type);
        bodyLODMesh = FindLODRenderer("Body_LOD");
        hairLODMesh = FindLODRenderer("Hair_LOD");
        faceLODMesh = FindLODRenderer("Face_LOD");
        eyeballLODMesh = FindLODRenderer("Eyeball_LOD");

        BuildBonesMap();
        BuildSkinMap();
        RefreshEditorSkinList();

        foreach (var part in skinDic)
        {
            if (part.Value != null)
            {
                UpdateMaterialSerializeField(part.Key);
            }
        }
    }

    private void Destroy()
    {
        foreach (var part in skinDic)
        {
            if (part.Value != null)
            {
                MaterialMgr.Instance.ResumeSharedMaterials(part.Value);
            }
        }
        editorSkinList.Clear();
        BlockPool.Clear();
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (blendShapeDict == null) return;

        UpdateFaceCustomizations();
    }
#endif

    [NoToLua]
    public void EditorChangeActorRender()
    {
        UpdateReferences(bodyObj, SkinType.Body, ChangeSkinPart);
        UpdateReferences(faceObj, SkinType.Face, ChangeSkinPart);
        UpdateReferences(hairObj, SkinType.Hair, ChangeSkinPart);
        UpdateReferences(eyeballObj, SkinType.Eyeball, ChangeSkinPart);
    }

    private void UpdateReferences(GameObject obj, SkinType tyep, Action<SkinType, ActorRender> changeAction)
    {
        if (obj == null) return;
        var render = obj.GetComponentInChildren<ActorRender>();
        if (render != null) changeAction(tyep, render);
    }

    private SkinnedMeshRenderer FindLODRenderer(string path)
    {
        var lod = transform.Find(path);
        return lod?.GetComponent<SkinnedMeshRenderer>();
    }

    private void BuildBonesMap()
    {
        boneDic.Clear();
        foreach (Transform bone in transform.GetComponentsInChildren<Transform>(true))
        {
            if (!boneDic.ContainsKey(bone.name)) boneDic.Add(bone.name, bone);
        }

        foreach (var bone in GetComponentsInChildren<SkinnedMeshRenderer>(true)
            .SelectMany(r => r.bones)
            .Where(b => !boneDic.ContainsKey(b.name)))
        {
            boneDic.Add(bone.name, bone);
        }
    }

    [NoToLua]
    public void BuildSkinMap()
    {
        skinDic.Clear();
        var renders = GetComponentsInChildren<SkinnedMeshRenderer>(true);
        foreach (var render in renders)
        {
            if (SkinNameMap.TryGetValue(render.name, out var type))
            {
                skinDic[(int)type] = render;
            }
        }
    }

    [NoToLua]
    public void RefreshEditorSkinList()
    {
#if UNITY_EDITOR
        editorSkinList.Clear();
        foreach (var pair in skinDic)
        {
            editorSkinList.Add(new SkinDictionaryEntry
            {
                type = (SkinType)pair.Key,
                renderer = pair.Value
            });
        }
#endif
    }

    public void ChangeBody(ActorRender newPartAR) => ChangeSkinPart(SkinType.Body, newPartAR);
    public void ChangeFace(ActorRender newPartAR) => ChangeSkinPart(SkinType.Face, newPartAR);
    public void ChangeEyeball(ActorRender newPartAR) => ChangeSkinPart(SkinType.Eyeball, newPartAR);
    public void ChangeHair(ActorRender newPartAR) => ChangeSkinPart(SkinType.Hair, newPartAR);
    public void ChangeFaceAndEyeball(ActorRender newPartAR)
    {
        ChangeSkinPart(SkinType.Face, newPartAR);
        ChangeSkinPart(SkinType.Eyeball, newPartAR);
    }

    public void ChangeSkinPart(SkinType type, ActorRender newActorRender)
    {
        if (!skinDic.TryGetValue((int)type, out var oldSMRenderer))
        {
            return;
        }

        if (null == oldSMRenderer || null == newActorRender)
            return;

        // 先清除该部位的旧特效，避免影响后续的骨骼映射和渲染器更新
        if (cachedActorRender != null)
        {
            cachedActorRender.ClearEffectAttachmentsBySkinType(type);
        }

        // 先恢复
        MaterialMgr.Instance.ResumeSharedMaterials(oldSMRenderer);

        SkinnedMeshRenderer newSMRenderer = null;
        var newSMRenderers = newActorRender.GetComponentsInChildren<SkinnedMeshRenderer>(true);
        foreach (var render in newSMRenderers)
        {
            if (SkinNameMap.TryGetValue(render.name, out var outType) && outType == type)
            {
                newSMRenderer = render;
                break;
            }
        }

        if (cachedActorRender == null || newSMRenderer == null)
            return;
        
        var newBones = ProcessBones(newSMRenderer, newActorRender);
        UpdateMainRenderer(oldSMRenderer, newSMRenderer, newActorRender, newBones);
        UpdateLODRenderer(type, newActorRender, newBones);
        UpdateMaterialSerializeField((int)type);
        
        // 应用新的特效数据
        cachedActorRender.ApplyEffectAttachmentsOnly(newActorRender, type, boneDic);
        
        ChangeSkin.ReleaseUseBonesArray(newBones);

        for (int i = 0; i < PartIndexDefultColor.Length; i++)
        {
            PartIndexDefultColor[i] = Color.clear;
        }

        if (type == SkinType.Body)
        {
            UpdateDyeDefultColor();
            ResetPartDyeColor();
        }

        if (type == SkinType.Hair)
        {
            UpdateDyeHairDefultColor();
            ResetPartHairDyeColor();
        }
    }

    private Transform[] ProcessBones(SkinnedMeshRenderer newRenderer, ActorRender actorRender)
    {
        if (newRenderer == null || actorRender == null)
        {
            Debug.LogError("ProcessBones: 无效的输入参数");
            return new Transform[0];
        }

        var newSMRBones = newRenderer.bones;
        if (newSMRBones == null || newSMRBones.Length == 0)
        {
            Debug.LogWarning($"ProcessBones: 未发现骨骼在 {actorRender.name}");
            return new Transform[0];
        }

        Transform[] newBones = ChangeSkin.GetUseBonesArray(newSMRBones.Length);
        bool hasBoneError = false;

        for (int i = 0; i < newSMRBones.Length; i++)
        {
            // 空指针检查
            if (newSMRBones[i] == null)
            {
                Debug.LogError($"ProcessBones: Null 骨骼 at index {i} in {actorRender.name}");
                hasBoneError = true;
                continue;
            }

            if (boneDic.TryGetValue(newSMRBones[i].name, out Transform bone))
            {
                newBones[i] = bone;
            }
            else
            {
                Debug.LogError($"找不到骨骼: {newSMRBones[i].name} 在 {gameObject.name}");
                hasBoneError = true;
            }
        }

        if (hasBoneError)
        {
            Debug.LogError($"骨骼映射失败，基础模型 {gameObject.name} -> 部位模型 {actorRender.name}");
        }

        return newBones;
    }

    private void UpdateMainRenderer(SkinnedMeshRenderer oldRenderer, SkinnedMeshRenderer newRenderer, ActorRender newActorRenderer, Transform[] bones)
    {
        if (oldRenderer == null || newRenderer == null || bones == null) return;
        
        // 临时禁用渲染器，避免中间状态验证错误
        bool wasEnabled = oldRenderer.enabled;
        oldRenderer.enabled = false;
        
        try
        {
            // 获取新的材质数据
            Material[] newMaterials = null;
            if (cachedActorRender != null && newActorRenderer.RenderList.Count > 0)
            {
                string rendererName = newRenderer.transform.name;
                foreach (var actRender in newActorRenderer.RenderList)
                {
                    if (actRender.renderer.name == rendererName)
                    {
                        newMaterials = actRender.pbrMaterials;
                        break;
                    }
                }
            }
            
            UpdateRendererDataSafely(oldRenderer, newRenderer.sharedMesh, bones, newMaterials);
            
            if (cachedActorRender != null && newMaterials != null)
            {
                cachedActorRender.UpdateRender(oldRenderer, newMaterials);
            }
        }
        finally
        {
            // 恢复渲染器启用状态
            oldRenderer.enabled = wasEnabled;
        }
    }

    private void UpdateRendererDataSafely(SkinnedMeshRenderer renderer, Mesh newMesh, Transform[] newBones, Material[] newMaterials)
    {
        var originalMesh = renderer.sharedMesh;
        var originalBones = renderer.bones;
        var originalMaterials = renderer.sharedMaterials;
        
        try
        {
            renderer.sharedMesh = newMesh;
            renderer.bones = newBones;
            
            if (newMaterials != null)
            {
                renderer.sharedMaterials = newMaterials;
            }
        }
        catch (System.Exception e)
        {
            // 如果更新失败，回滚到原始状态
            Debug.LogError($"更新渲染器失败，回滚到原始状态: {e.Message}");
            try
            {
                renderer.sharedMesh = originalMesh;
                renderer.bones = originalBones;
                renderer.sharedMaterials = originalMaterials;
            }
            catch (System.Exception rollbackException)
            {
                Debug.LogError($"回滚也失败了: {rollbackException.Message}");
            }
            throw;
        }
    }

    private void UpdateLODRenderer(SkinType type, ActorRender newPartAR, Transform[] bones)
    {
        if (!TryGetLodRenderer(type, out var lodRenderer)) return;

        var lodGroup = newPartAR.GetComponent<LODGroup>();
        if (lodGroup?.GetLODs() is { Length: > 1 } lods)
        {
            var targetRenderer = lods[1].renderers.FirstOrDefault() as SkinnedMeshRenderer;
            ChangeLOD(lodRenderer, targetRenderer, bones);
        }
        else
        {
            bool wasEnabled = lodRenderer.enabled;
            lodRenderer.enabled = false;
            
            try
            {
                lodRenderer.bones = null;
                lodRenderer.sharedMesh = null;
                lodRenderer.sharedMaterials = Array.Empty<Material>();
            }
            finally
            {
                lodRenderer.enabled = wasEnabled;
            }
        }
    }


    private void ChangeLOD(SkinnedMeshRenderer lodRenderer, SkinnedMeshRenderer targetRenderer, Transform[] bones)
    {
        if (lodRenderer == null || targetRenderer == null) return;
        
        bool wasEnabled = lodRenderer.enabled;
        lodRenderer.enabled = false;
        
        try
        {
            MaterialMgr.Instance.ResumeSharedMaterials(lodRenderer);
            UpdateRendererDataSafely(lodRenderer, targetRenderer.sharedMesh, bones, targetRenderer.sharedMaterials);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"LOD更新失败: {e.Message}");
            
            // 失败时清空LOD数据，避免渲染错误
            try
            {
                lodRenderer.bones = null;
                lodRenderer.sharedMesh = null;
                lodRenderer.sharedMaterials = Array.Empty<Material>();
            }
            catch (System.Exception clearException)
            {
                Debug.LogError($"清空LOD数据也失败: {clearException.Message}");
            }
        }
        finally
        {
            lodRenderer.enabled = wasEnabled;
        }
    }

    public void ChangeBodyMat(Material mat)
    {
        if (mat == null)
            return;

        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Body, out oldRenderer))
        {
            oldRenderer.material = mat;
        }
    }

    public void ChangeBodyMats(Material[] mats)
    {
        if (mats == null)
            return;

        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Body, out oldRenderer))
        {
            // 先恢复
            MaterialMgr.Instance.ResumeSharedMaterials(oldRenderer);
            oldRenderer.sharedMaterials = mats;
        }
        UpdateDyeDefultColor();
        ResetPartDyeColor();
    }

    public void ChangeHairMat(Material mat)
    {
        if (mat == null)
            return;

 
        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Hair, out oldRenderer))
        {
            oldRenderer.material = mat;
        }
    }

    public void ChangeHairMats(Material[] mats)
    {
        if (mats == null)
            return;

        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Hair, out oldRenderer))
        {
            // 先恢复
            MaterialMgr.Instance.ResumeSharedMaterials(oldRenderer);
            oldRenderer.sharedMaterials = mats;
        }
        UpdateDyeHairDefultColor();
        ResetPartHairDyeColor();
    }

    public void ChangeFaceMat(Material mat)
    {
        if (mat == null)
            return;

        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Face, out oldRenderer))
        {
            oldRenderer.material = mat;
        }
    }

    public void ChangeFaceMats(Material[] mats)
    {
        if (mats == null)
            return;

        SkinnedMeshRenderer oldRenderer;
        if (skinDic.TryGetValue((int)SkinType.Face, out oldRenderer))
        {
            // 先恢复
            MaterialMgr.Instance.ResumeSharedMaterials(oldRenderer);
            oldRenderer.sharedMaterials = mats;
        }
    }

    // 切换时装染色

    public void BatchSetPartDyeColor(string dye_color)
    {
        DyeColorItemList dye_color_list = JsonUtility.FromJson<DyeColorItemList>(dye_color);
        for (int i = 0; i < dye_color_list.dye_list.Count; i++)
        {
            DyeColorItem dye_color_item = dye_color_list.dye_list[i];
            this.ChangePartDyeColor(dye_color_item.dye_index, new Color(dye_color_item.r, dye_color_item.g, dye_color_item.b, dye_color_item.a));
        }
    }

    public void BatchReSetPartDyeColor(string dye_color)
    {
        DyeColorItemList dye_color_list = JsonUtility.FromJson<DyeColorItemList>(dye_color);
        if (PartIndexDefultColor == null)
            return;

        for (int i = 0; i < dye_color_list.dye_list.Count; i++)
        {
            DyeColorItem dye_color_item = dye_color_list.dye_list[i];
            if (PartIndexDefultColor.Length > dye_color_item.dye_index)
            {
                Color defult_color = PartIndexDefultColor[dye_color_item.dye_index];
                if (defult_color.r != 0 || defult_color.g != 0 || defult_color.b != 0 || defult_color.a != 0)
                {
                    ChangePartDyeColor(dye_color_item.dye_index, defult_color);
                }
            }
        }
    }

    public void ChangePartDyeColor(int dye_index, Color aim_color)
    {
        //SkinType part = (SkinType)part_type;
        if (!skinDic.TryGetValue((int)SkinType.Body, out SkinnedMeshRenderer skinnedMeshRenderer)) return;

        //获取对应的材质球下标
        int real_dye_index = dye_index - 1;
        if (real_dye_index < 0) return;

        int material_index = real_dye_index / 3;
        int dye_color_index = real_dye_index % 3 + 1;

        MaterialPropertyBlock block = BlockPool.Get();
        if (material_index >= skinnedMeshRenderer.sharedMaterials.Length) {
            return; 
        };

        Material mat = skinnedMeshRenderer.sharedMaterials[material_index];
        if (mat != null && mat.IsKeywordEnabled("ENABLE_DYE"))
        {
            skinnedMeshRenderer.GetPropertyBlock(block, material_index);
            string dye_color_name = $"_DyeColor{dye_color_index}";
            block.SetColor(dye_color_name, aim_color);
            skinnedMeshRenderer.SetPropertyBlock(block, material_index);
        }
        SafeReleaseBlock(block);
    }

    //重置默认色
    public void ResetPartDyeColor()
    {
        if (PartIndexDefultColor == null)
            return;

        for (int i = 0; i < PartIndexDefultColor.Length; i++)
        {
            Color defult_color = PartIndexDefultColor[i];
            if (defult_color.r != 0 || defult_color.g != 0 || defult_color.b != 0 || defult_color.a != 0)
            {
                ChangePartDyeColor(i, defult_color);
            }
        }
    }

    private void UpdateDyeDefultColor()
    {
        if (!skinDic.TryGetValue((int)SkinType.Body, out SkinnedMeshRenderer skinnedMeshRenderer)) return;
        int dye_index = 1;

        for (int i = 0; i < skinnedMeshRenderer.sharedMaterials.Length; i++)
        {
            Material mat = skinnedMeshRenderer.sharedMaterials[i];
            if(mat == null) continue;

            for (int j = 1; j < 4; j++)
            {
                string dye_color_name = $"_DyeColor{j}";
                if (mat.HasColor(dye_color_name))
                {
                    Color defult_color = mat.GetColor(dye_color_name);
                    PartIndexDefultColor[dye_index] = defult_color;
                }

                dye_index++;
            }
        }
    }

    private void UpdateDyeHairDefultColor()
    {
        if (!skinDic.TryGetValue((int)SkinType.Hair, out SkinnedMeshRenderer skinnedMeshRenderer)) return;

        for (int i = 0; i < skinnedMeshRenderer.sharedMaterials.Length; i++)
        {
            Material mat = skinnedMeshRenderer.sharedMaterials[i];
            if (mat != null && mat.HasColor("_HairColor"))
            {
                this.PartHairDefultColor = mat.GetColor("_HairColor");
            }
        }
    }

    //重置默认色
    public void ResetPartHairDyeColor()
    {
        if (this.PartHairDefultColor.r != 0 || this.PartHairDefultColor.g != 0 || this.PartHairDefultColor.b != 0 || this.PartHairDefultColor.a != 0)
            this.SetHeadCustomization(HeadCustomizationType.HairColor, this.PartHairDefultColor);
    }

    public bool IsArtMode()
    {
        int develop_mode = 1;
        if (UnityEngine.PlayerPrefs.HasKey("a3_fanli_develop_mode"))
        {
            develop_mode = UnityEngine.PlayerPrefs.GetInt("a3_fanli_develop_mode");
        }

        return develop_mode == 2;
    }

    static Transform[] GetUseBonesArray(int length)
    {
        if (length <= 0)
        {
            return new Transform[100];
        }

        // 检查池子中是否有足够长度的数组
        for (int i = useBonesPool.Count - 1; i >= 0; i--)
        {
            var array = useBonesPool[i];
            if (array != null && array.Length >= length)
            {
                useBonesPool.RemoveAt(i);
                return array;
            }
        }

        int newLength = Mathf.Max(length, 100);
        var newArray = new Transform[newLength];
        return newArray;
    }

    static void ReleaseUseBonesArray(Transform[] array)
    {
        if (array.Length > 100) return;

        System.Array.Clear(array, 0, array.Length);
        useBonesPool.Add(array);
    }

    public static void ClearBoneArray()
    {
        if (useBonesPool == null) return;

        useBonesPool.Clear();
    }

    #region 头部细节调整
    private void UpdateFaceCustomizations()
    {
        foreach (var config in blendShapeConfigs)
        {
            if (config.type == HeadCustomizationType.EyeballPosition) continue;

            if (config.type == HeadCustomizationType.EyePosition)
            {
                UpdateBlendShapes(config.type, config.weight);
                UpdateBlendShapes(HeadCustomizationType.EyeballPosition, config.weight);
            }
            else
                UpdateBlendShapes(config.type, config.weight);
        }

        UpdateFaceMaterial();
        UpdateEyeballMaterial();
        UpdateHairMaterial();
    }

    public void SetHeadCustomization(int type, object value)
    {
        SetHeadCustomization((HeadCustomizationType)type, value);
    }

    public void BatchSetHeadCustomization(LuaTable customizationData)
    {
        _batchBlendShapesList.Clear();
        _batchMaterialsList.Clear();

        LuaState luaState = LuaState.Get(IntPtr.Zero);
        int top = luaState.LuaGetTop(); // 当前栈顶位置
        try
        {
            luaState.Push(customizationData);// 将 LuaTable 压入栈顶
            luaState.LuaPushNil(); // 初始 key 为 nil
            while (luaState.LuaNext(-2)) // -2 表示栈中倒数第二个元素（即 table）
            {
                object rawKey = luaState.ToVariant(-2); // 键在 -2 位置
                object rawValue = luaState.ToVariant(-1); // 值在 -1 位置

                ProcessLuaKeyValue(rawKey, rawValue);
                luaState.LuaPop(1);// 弹出 value，保留 key 供下次迭代
            }

            luaState.LuaPop(1); // 弹出 table
        }
        finally
        {
            luaState.LuaSetTop(top);
        }

        if(_batchBlendShapesList.Count > 0)
        {
            UpdateBlendShapesBatch(_batchBlendShapesList);
            _batchBlendShapesList.Clear();
        }

        if (_batchMaterialsList.Count > 0)
        {
            UpdateMaterialProperties(_batchMaterialsList);
            _batchMaterialsList.Clear();
        }
    }

    private void ProcessLuaKeyValue(object rawKey, object rawValue)
    {
        HeadCustomizationType type;
        // 如果type传的number（Lua 中数字默认转成 double）
        if (rawKey is double numKey)
        {
            if (!Enum.IsDefined(typeof(HeadCustomizationType), (int)numKey))
            {
                Debug.LogError($"无效的枚举值: {numKey}");
                return;
            }
            type = (HeadCustomizationType)(int)numKey;
        }
        // 如果type传的HeadCustomizationType
        else if (rawKey is HeadCustomizationType enumKey)
        {
            type = enumKey;
        }
        else
        {
            Debug.LogError($"无效的键类型: {rawKey?.GetType()} 值: {rawKey}");
            return;
        }

        try
        {
            if (blendShapeDict.TryGetValue(type, out var config))
            {
                if (!_batchBlendShapesList.TryGetValue(config.partType, out var blendList))
                {
                    blendList = new List<(HeadCustomizationType type, float value)>();
                    _batchBlendShapesList[config.partType] = blendList;
                }
                blendList.Add((type, Convert.ToSingle(rawValue)));
            }

            if (materialPropertyMap.TryGetValue(type, out var matConfig))
            {
                if (!_batchMaterialsList.TryGetValue(matConfig.partType, out var matList))
                {
                    matList = new List<(HeadCustomizationType type, object value)>();
                    _batchMaterialsList[matConfig.partType] = matList;
                }
                matList.Add((type, rawValue));
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"设置 {type} 失败: {e.Message}");
        }
    }


    public void SetHeadCustomization(HeadCustomizationType type, object value)
    {
        try
        {
            switch (type)
            {
                case HeadCustomizationType.EyeSize:
                case HeadCustomizationType.MouthSize:
                case HeadCustomizationType.MouthPosition:
                case HeadCustomizationType.EyeAngle:
                case HeadCustomizationType.EyeClose:
                case HeadCustomizationType.EyebrowAngle:
                case HeadCustomizationType.NoseSize:
                case HeadCustomizationType.NoseAngle:
                case HeadCustomizationType.MouthAngle:
                case HeadCustomizationType.CheekSize:
                case HeadCustomizationType.ChinLength:
                    float blendValue = Convert.ToSingle(value);
                    UpdateBlendShapes(type, blendValue);
                    break;
                case HeadCustomizationType.EyePosition:
                    float blendVal = Convert.ToSingle(value);
                    UpdateBlendShapes(type, blendVal);
                    UpdateBlendShapes(HeadCustomizationType.EyeballPosition, blendVal);
                    break;
                default:
                    UpdateMaterialProperty(type, value);
                    break;
            }
        }
        catch (System.InvalidCastException e)
        {
            Debug.LogError($"类型转换错误: {e.Message}");
        }
    }

    private bool TryGetLodRenderer(SkinType type, out SkinnedMeshRenderer renderer)
    {
        renderer = type switch
        {
            SkinType.Body => bodyLODMesh,
            SkinType.Face => faceLODMesh,
            SkinType.Hair => hairLODMesh,
            SkinType.Eyeball => eyeballLODMesh,
            _ => null
        };
        return renderer != null;
    }

    private List<SkinnedMeshRenderer> GetRenderersForSkinType(SkinType type)
    {
        List<SkinnedMeshRenderer> renderers = new List<SkinnedMeshRenderer>(2);

        if (skinDic.TryGetValue((int)type, out SkinnedMeshRenderer mainRenderer))
        {
            renderers.Add(mainRenderer);
        }

        if (TryGetLodRenderer(type, out SkinnedMeshRenderer lodRenderer) && lodRenderer != null)
        {
            renderers.Add(lodRenderer);
        }

        return renderers;
    }


    #region BlendShape调整
    public float GetBlendShapeValue(SkinType skinType, HeadCustomizationType customType)
    {
        if (blendShapeDict.TryGetValue(customType, out var config))
        {
            if (skinDic.TryGetValue((int)skinType, out var renderer) && renderer.sharedMesh)
            {
                int posIndex = renderer.sharedMesh.GetBlendShapeIndex(config.positiveShape);
                return posIndex != -1 ? renderer.GetBlendShapeWeight(posIndex) : 0;
            }
            return 0;
        }

        return 0;
    }

    private void UpdateBlendShapes(HeadCustomizationType customType, float value)
    {
        if (blendShapeDict.TryGetValue(customType, out var config))
        {
            value = Mathf.Clamp(value, -100, 100);
            config.weight = value;
            UpdateRenderBlendShapes(config.partType, config.positiveShape, config.negativeShape, value);
        }
    }

    private void ChangeBlendShapeWeight(SkinnedMeshRenderer smr, string positiveShape, string negativeShape, float value)
    {
        if (!smr || !smr.sharedMesh) return;
        int posIndex = smr.sharedMesh.GetBlendShapeIndex(positiveShape);
        int negIndex = smr.sharedMesh.GetBlendShapeIndex(negativeShape);

        if (posIndex != -1) smr.SetBlendShapeWeight(posIndex, Mathf.Max(value, 0));
        if (negIndex != -1) smr.SetBlendShapeWeight(negIndex, Mathf.Max(-value, 0));
    }

    private void UpdateRenderBlendShapes(SkinType type, string positiveShape, string negativeShape, float value)
    {
        var renderers = GetRenderersForSkinType(type);
        foreach (var renderer in renderers)
        {
            ChangeBlendShapeWeight(renderer, positiveShape, negativeShape, value);
        }
    }

    // 批量处理BlendShapes, 合并权重计算，减少循环次数
    private void UpdateBlendShapesBatch(Dictionary<SkinType, List<(HeadCustomizationType type, float value)>> blends)
    {
        foreach (var kvp in blends)
        {
            SkinType skinType = kvp.Key;
            var renderers = GetRenderersForSkinType(skinType);
            if (renderers.Count == 0) continue;

            // 预先生成有效配置列表（同时包含type/value/config）
            var validConfigs = new List<(HeadCustomizationType type, float value, BlendShapeConfig config)>();
            foreach (var (type, value) in kvp.Value)
            {
                if (blendShapeDict.TryGetValue(type, out var config))
                {
                    validConfigs.Add((type, Mathf.Clamp(value, -100, 100), config));
                }
            }

            foreach (var renderer in renderers)
            {
                foreach (var (type, clampedValue, config) in validConfigs)
                {
                    config.weight = clampedValue;
                    ChangeBlendShapeWeight(renderer, config.positiveShape, config.negativeShape, clampedValue);
                }
            }
        }
    }
    #endregion

    #region 材质属性调整
    private void UpdateMaterialProperty(HeadCustomizationType type, object value)
    {
        if (!materialPropertyMap.TryGetValue(type, out var config)) return;

        // 处理所有关联的部件和材质索引
        foreach (var materialIndex in config.materialIndices)
        {
            UpdateSingleMaterialProperty(config.partType, materialIndex, block =>
            {
                SetMaterialPropertyValue(block, config.propertyName, value);
            });
        }

        UpdateSerializedField(type, value);
    }

    private void UpdateSingleMaterialProperty(SkinType partType, int materialIndex, Action<MaterialPropertyBlock> updateAction)
    {
        var renderers = GetRenderersForSkinType(partType);
        foreach (var renderer in renderers)
        {
            UpdateRendererMaterial(renderer, materialIndex, updateAction);
        }
    }

    private void UpdateRendererMaterial(SkinnedMeshRenderer renderer, int materialIndex, Action<MaterialPropertyBlock> updateAction)
    {
        if (renderer == null || renderer.sharedMaterials == null) return;
        if (materialIndex >= renderer.sharedMaterials.Length)
        {
            //Debug.LogWarning($"材质索引超出范围: {materialIndex} (总材质数: {renderer.sharedMaterials.Length})");
            return;
        }

        MaterialPropertyBlock block = BlockPool.Get();
        try
        {
            renderer.GetPropertyBlock(block, materialIndex);
            updateAction(block);
            renderer.SetPropertyBlock(block, materialIndex);
        }
        finally
        {
            SafeReleaseBlock(block);
        }
    }

    private void SetMaterialPropertyValue(MaterialPropertyBlock block, string propertyName, object value)
    {
        switch (value)
        {
            case Color color:
                if (color != null)
                    block.SetColor(propertyName, color);
                break;
            case Texture2D tex:
                if (tex != null)
                    block.SetTexture(propertyName, tex);
                break;
            case float floatVal:
                if (!float.IsNaN(floatVal))
                    block.SetFloat(propertyName, floatVal);
                break;
            case double doubleVal:
                float matVal = Convert.ToSingle(doubleVal);
                if (!float.IsNaN(matVal))
                    block.SetFloat(propertyName, matVal);
                break;
            case Vector4 Vector4Val:
                if (Vector4Val != null)
                    block.SetVector(propertyName, Vector4Val);
                break;
            default:
                Debug.LogError($"不支持的材质属性类型: {value.GetType()}");
                break;
        }
    }

    private void UpdateSerializedField(HeadCustomizationType type, object value)
    {
        switch (type)
        {
            case HeadCustomizationType.EyeShadowColor:
                eyeShadowColor = (Color)value;
                break;
            case HeadCustomizationType.MouthColor:
                mouthColor = (Color)value;
                break;
            case HeadCustomizationType.FaceDecalTex:
                faceDecalTex = (Texture2D)value;
                break;
            case HeadCustomizationType.FaceDecalTexST:
                faceDecalTexST = (Vector4)value;
                break;
            case HeadCustomizationType.FaceDecalFade:
                faceDecalFade = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.FaceDecalMirror:
                faceDecalMirror = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.FaceDecalColor:
                faceDecalColor = (Color)value;
                break;
            case HeadCustomizationType.FaceBaseTex:
                faceBaseTex = (Texture2D)value;
                break;
            case HeadCustomizationType.BodyBaseColor:
                bodyBaseColor = (Color)value;
                break;
            case HeadCustomizationType.PupilType_Left:
                pupilTypeLeft = (Texture2D)value;
                break;
            case HeadCustomizationType.IrisSize_Left:
                irisSizeLeft = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.PupilSize_Left:
                pupilSizeLeft = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.PupilColor_Left:
                pupilColorLeft = (Color)value;
                break;
            case HeadCustomizationType.PupilType_Right:
                pupilTypeRight = (Texture2D)value;
                break;
            case HeadCustomizationType.IrisSize_Right:
                irisSizeRight = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.PupilSize_Right:
                pupilSizeRight = Convert.ToSingle(value);
                break;
            case HeadCustomizationType.PupilColor_Right:
                pupilColorRight = (Color)value;
                break;
            case HeadCustomizationType.HairColor: 
                hairColor = (Color)value;
                break;
        }
    }
    
    // 批量处理材质属性修改， 优化MaterialPropertyBlock操作
    private void UpdateMaterialProperties(Dictionary<SkinType, List<(HeadCustomizationType type, object value)>> properties)
    {
        foreach (var kvp in properties)
        {
            SkinType skinType = kvp.Key;
            var renderers = GetRenderersForSkinType(skinType);

            // 预先生成有效配置列表
            var validConfigs = new List<(HeadCustomizationType type, MaterialPropertyConfig config, object value)>();
            foreach (var (type, value) in kvp.Value)
            {
                if (materialPropertyMap.TryGetValue(type, out var config))
                {
                    validConfigs.Add((type, config, value));
                }
            }

            // 按材质索引分组
            var groupedByMaterial = validConfigs
                .SelectMany(t => t.config.materialIndices.Select(i => (index: i, propName: t.config.propertyName, value: t.value, type: t.type)))
                .GroupBy(x => x.index);

            List<(HeadCustomizationType type, object value)> subList = kvp.Value;
            foreach (var renderer in renderers)
            {
                if (renderer == null || renderer.sharedMaterials == null) continue;

                MaterialPropertyBlock block = BlockPool.Get();
                try
                {
                    foreach (var materialGroup in groupedByMaterial)
                    {
                        int matIndex = materialGroup.Key;
                        if (matIndex >= renderer.sharedMaterials.Length) continue;
                        renderer.GetPropertyBlock(block, matIndex);
                        foreach (var prop in materialGroup)
                        {
                            SetMaterialPropertyValue(block, prop.propName, prop.value);
                            UpdateSerializedField(prop.type, prop.value);
                        }
                        renderer.SetPropertyBlock(block, matIndex);
                    }
                }
                finally
                {
                    BlockPool.Release(block);
                }
            }
        }
    }

    private void UpdateMaterialSerializeField(int skinTypeInt)
    {
        SkinnedMeshRenderer renderer = skinDic[skinTypeInt];
        if (renderer == null) return;

        Material[] materials = renderer.sharedMaterials;
        foreach (var pair in materialPropertyMap)
        {
            var config = pair.Value;
            if ((int)config.partType != skinTypeInt) continue;
            foreach (var materialIndex in config.materialIndices)
            {
                if (materialIndex >= materials.Length)
                {
                    //Debug.LogWarning($"材质索引越界: {materialIndex}");
                    continue;
                }

                Material mat = materials[materialIndex];
                if (mat == null) continue;

                switch (pair.Key)
                {
                    case HeadCustomizationType.EyeShadowColor when mat.HasProperty(config.propertyName):
                        eyeShadowColor = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.MouthColor when mat.HasProperty(config.propertyName):
                        mouthColor = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.FaceDecalTex when mat.HasProperty(config.propertyName):
                        faceDecalTex = mat.GetTexture(config.propertyName) as Texture2D;
                        break;
                    case HeadCustomizationType.FaceDecalTexST when mat.HasProperty(config.propertyName):
                        faceDecalTexST = mat.GetVector(config.propertyName);
                        break;
                    case HeadCustomizationType.FaceDecalFade when mat.HasProperty(config.propertyName):
                        faceDecalFade = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.FaceDecalMirror when mat.HasProperty(config.propertyName):
                        faceDecalMirror = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.FaceDecalColor when mat.HasProperty(config.propertyName):
                        faceDecalColor = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.FaceBaseTex when mat.HasProperty(config.propertyName):
                        faceBaseTex = mat.GetTexture(config.propertyName) as Texture2D;
                        break;
                    case HeadCustomizationType.BodyBaseColor when mat.HasProperty(config.propertyName):
                        bodyBaseColor = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilType_Right when mat.HasProperty(config.propertyName):
                        pupilTypeRight = mat.GetTexture(config.propertyName) as Texture2D;
                        break;
                    case HeadCustomizationType.IrisSize_Right when mat.HasProperty(config.propertyName):
                        irisSizeRight = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilSize_Right when mat.HasProperty(config.propertyName):
                        pupilSizeRight = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilColor_Right when mat.HasProperty(config.propertyName):
                        pupilColorRight = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilType_Left when mat.HasProperty(config.propertyName):
                        pupilTypeLeft = mat.GetTexture(config.propertyName) as Texture2D;
                        break;
                    case HeadCustomizationType.IrisSize_Left when mat.HasProperty(config.propertyName):
                        irisSizeLeft = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilSize_Left when mat.HasProperty(config.propertyName):
                        pupilSizeLeft = mat.GetFloat(config.propertyName);
                        break;
                    case HeadCustomizationType.PupilColor_Left when mat.HasProperty(config.propertyName):
                        pupilColorLeft = mat.GetColor(config.propertyName);
                        break;
                    case HeadCustomizationType.HairColor when mat.HasProperty(config.propertyName):
                        hairColor = mat.GetColor(config.propertyName);
                        break;
                }
            }
        }
    }

    private void UpdateFaceMaterial()
    {
        UpdateMaterialProperty(HeadCustomizationType.EyeShadowColor, eyeShadowColor);
        UpdateMaterialProperty(HeadCustomizationType.MouthColor, mouthColor);
        UpdateMaterialProperty(HeadCustomizationType.FaceDecalTex, faceDecalTex);
        UpdateMaterialProperty(HeadCustomizationType.FaceDecalTexST, faceDecalTexST);
        UpdateMaterialProperty(HeadCustomizationType.FaceDecalFade, faceDecalFade);
        UpdateMaterialProperty(HeadCustomizationType.FaceDecalMirror, faceDecalMirror);
        UpdateMaterialProperty(HeadCustomizationType.FaceDecalColor, faceDecalColor);
        UpdateMaterialProperty(HeadCustomizationType.FaceBaseTex, faceBaseTex);
        UpdateMaterialProperty(HeadCustomizationType.BodyBaseColor, bodyBaseColor);
    }

    private void UpdateEyeballMaterial()
    {
        UpdateMaterialProperty(HeadCustomizationType.PupilType_Right, pupilTypeRight);
        UpdateMaterialProperty(HeadCustomizationType.IrisSize_Right, irisSizeRight);
        UpdateMaterialProperty(HeadCustomizationType.PupilSize_Right, pupilSizeRight);
        UpdateMaterialProperty(HeadCustomizationType.PupilColor_Right, pupilColorRight);
        UpdateMaterialProperty(HeadCustomizationType.PupilType_Left, pupilTypeLeft);
        UpdateMaterialProperty(HeadCustomizationType.IrisSize_Left, irisSizeLeft);
        UpdateMaterialProperty(HeadCustomizationType.PupilSize_Left, pupilSizeLeft);
        UpdateMaterialProperty(HeadCustomizationType.PupilColor_Left, pupilColorLeft);
    }

    private void UpdateHairMaterial()
    {
        UpdateMaterialProperty(HeadCustomizationType.HairColor, hairColor);
    }
    #endregion

    #endregion
}