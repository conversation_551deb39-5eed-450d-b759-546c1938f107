﻿using Game;
using Nirvana;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 一键生成模型预制体工具
/// </summary>
public class OneKeyBuildModel : Editor
{
    #region 常量和配置

    private const string MATERIAL_TEMPLATE_PATH = "Assets/Game/Model/ModelMaterialTemplate.mat";
    private const string SUFFIX_MATCH_PATTERN = ".*(_{0}[\\._]).*";

    private static readonly RevertModelData revertModelData = new RevertModelData();

    /// <summary>
    /// 模型构建类型枚举
    /// </summary>
    public enum BuildModelType
    {
        CharacterBase, CharacterPart, Boss, Monster, Npc, <PERSON>ather, Wing, Mount,
        Yaoshi, Lianshi, Beishi, Child, Chongwu, Fabao, Zuoqi, UIOther,
        TianShen, TianShenWuQi, RoleWeapon, RoleBody, RoleHair, RoleFace,
        <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, EquipDrop, FallItem
    }

    /// <summary>
    /// 模型配置数据结构
    /// </summary>
    [Serializable]
    public class ModelConfig
    {
        public string animatorControllerPath;
        public string modelStartPath;
        public Dictionary<string, List<string>> specialAnimationClips;
        public bool needsAttachment;
        public bool needsClickable;
        public bool needsAnimatorOptimizer;
        public bool needsSpecialPoints; // 需要AdjustPoint和FaZhenPoint

        public ModelConfig(string animatorPath, string startPath, bool attachment = false,
                          bool clickable = false, bool optimizer = true, bool specialPoints = false)
        {
            animatorControllerPath = animatorPath;
            modelStartPath = startPath;
            needsAttachment = attachment;
            needsClickable = clickable;
            needsAnimatorOptimizer = optimizer;
            needsSpecialPoints = specialPoints;
            specialAnimationClips = new Dictionary<string, List<string>>();
        }
    }

    #endregion

    private static readonly Dictionary<string, string> WeaponCtrlAsset = new Dictionary<string, string>
    {
        { "Assets/Game/Model/Weapon/9012", "Assets/Game/Actors/Character/RoleMan/1102001/1102Shared/1102_Controller.overrideController" },
        { "Assets/Game/Model/Weapon/9001/9001", "Assets/Game/Actors/Character/RoleWoman/3101001/3101Shared/3101_Controller.overrideController" },
        { "Assets/Game/Model/Weapon/9001/9011", "Assets/Game/Actors/Character/RoleMan/1101001/1101Shared/1101_Controller.overrideController" },
        { "Assets/Game/Model/Weapon/9003/9003", "Assets/Game/Actors/Character/RoleWoman/3103001/3103Shared/3103_Controller.overrideController" },
        { "Assets/Game/Model/Weapon/9003/9013", "Assets/Game/Actors/Character/RoleMan/1103001/1103Shared/1103_Controller.overrideController" },
    };

    private static readonly Dictionary<BuildModelType, ModelConfig> ModelConfigs = new Dictionary<BuildModelType, ModelConfig>();

    static OneKeyBuildModel()
    {
        InitializeModelConfigs();
    }

    private static void InitializeModelConfigs()
    {
        // 基础配置
        ModelConfigs[BuildModelType.CharacterBase] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/Character");
        ModelConfigs[BuildModelType.Boss] = new ModelConfig("Assets/Game/Model/Boss/Shared/BossController.controller", "Assets/Game/Model/Boss", true, true, true);
        ModelConfigs[BuildModelType.Monster] = new ModelConfig("Assets/Game/Model/Boss/Shared/MonsterController.controller", "Assets/Game/Model/Boss", true, true, true);
        ModelConfigs[BuildModelType.Npc] = new ModelConfig("Assets/Game/Model/NPC/Shared/NpcController.controller", "Assets/Game/Model/NPC", true, true, true);
        ModelConfigs[BuildModelType.Wing] = new ModelConfig("Assets/Game/Model/Wings/Shared/WingController.controller", "Assets/Game/Model/Wings", false, false, true);
        ModelConfigs[BuildModelType.Yaoshi] = new ModelConfig("Assets/Game/Model/Yaoshi/Shared/Yaoshi_Controller.controller", "Assets/Game/Model/Yaoshi", false, false, true);
        ModelConfigs[BuildModelType.Beishi] = new ModelConfig("Assets/Game/Model/Beishi/Shared/BeishiController.controller", "Assets/Game/Model/Beishi", false, false, true);
        ModelConfigs[BuildModelType.Child] = new ModelConfig("Assets/Game/Model/Child/Shared/ChildController.controller", "Assets/Game/Model/Child", true, false, true);
        ModelConfigs[BuildModelType.Chongwu] = new ModelConfig("Assets/Game/Model/Chongwu/Shared/ChongwuController.controller", "Assets/Game/Model/Chongwu", true, false, true);
        ModelConfigs[BuildModelType.Fabao] = new ModelConfig("Assets/Game/Model/Fabao/Shared/FabaoController.controller", "Assets/Game/Model/Fabao", false, false, true);
        ModelConfigs[BuildModelType.Zuoqi] = new ModelConfig("Assets/Game/Model/Zuoqi/Shared/MountController.controller", "Assets/Game/Model/Zuoqi", false, false, true, true);
        ModelConfigs[BuildModelType.TianShen] = new ModelConfig("Assets/Game/Model/Tianshen/Shared/TianShenController.controller", "Assets/Game/Model/Tianshen", true, false, true);
        ModelConfigs[BuildModelType.TianShenWuQi] = new ModelConfig("Assets/Game/Model/Tianshen/Shared/TianShenController.controller", "Assets/Game/Model/Tianshenwuqi", false, false, true);
        ModelConfigs[BuildModelType.RoleWeapon] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/Weapon", true, false, true);
        ModelConfigs[BuildModelType.RoleBody] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Actors/Character", false, false, false);
        ModelConfigs[BuildModelType.RoleHair] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Actors/Character", false, false, false);
        ModelConfigs[BuildModelType.RoleFace] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Actors/Character", false, false, false);
        ModelConfigs[BuildModelType.YuShou] = new ModelConfig("Assets/Game/Model/Yushou/Shared/YuShouController.controller", "Assets/Game/Model/Yushou", true, false, true);
        ModelConfigs[BuildModelType.UIOther] = new ModelConfig("Assets/Game/Actors/Shared/MountController.controller", "Assets/Game/Model/UIother", false, false, false, true);
        ModelConfigs[BuildModelType.WuHun] = new ModelConfig("Assets/Game/Model/WuHun/Shared/WuHunController.controller", "Assets/Game/Model/WuHun", false, false, false, true);
        ModelConfigs[BuildModelType.Halo] = new ModelConfig("Assets/Game/Model/Halo/Shared/Halo_Controller.controller", "Assets/Game/Model/Halo", false, false, false, true);
        ModelConfigs[BuildModelType.EquipDrop] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/EquipDrop", false, false, false);
        ModelConfigs[BuildModelType.FallItem] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/Forge", false, false, false);
        ModelConfigs[BuildModelType.Gather] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/Gather", true, false, false);
        ModelConfigs[BuildModelType.Lianshi] = new ModelConfig("Assets/Game/Actors/Shared/RoleController.controller", "Assets/Game/Model/Lianshi", false, false, false);

        // 初始化特殊动画配置
        InitializeSpecialAnimations();
    }

    #region 动画配置

    private static readonly Dictionary<string, List<string>> AnimationClipDic = new Dictionary<string, List<string>>();

    private static void InitializeSpecialAnimations()
    {
        // 基础动画映射
        var basicAnimations = new Dictionary<string, string[]>
        {
            { "idle", new[] { "Idle", "idle" } },
            { "ui_idle", new[] { "idle_ui", "ui_idle" } },
            { "weapon_idle", new[] { "weapon_idle", "idle_weapon" } },
            { "rest", new[] { "Rest", "rest" } },
            { "run", new[] { "Run", "run" } },
            { "walk", new[] { "Walk", "walk" } },
            { "die", new[] { "Die", "die" } },
            { "dead", new[] { "Dead", "dead", "Death", "death" } },
            { "hurt", new[] { "hit" } },
            { "yu_jian", new[] { "idle_yj" } }
        };

        foreach (var anim in basicAnimations)
        {
            AnimationClipDic[anim.Key] = new List<string>(anim.Value);
        }

        // 连击和攻击动画
        for (int i = 1; i <= 4; i++)
        {
            AnimationClipDic[$"combo1_{i}"] = new List<string> { $"combo0{i}" };
            AnimationClipDic[$"combo1_{i}_back"] = new List<string> { $"combo0{i}_back" };
            AnimationClipDic[$"attack{i}"] = new List<string> { $"Attack0{i}", $"attack0{i}" };
            AnimationClipDic[$"attack{i}_back"] = new List<string> { $"Attack0{i}_back", $"attack0{i}_back" };
        }

        // 坐骑动画
        for (int i = 1; i <= 8; i++)
        {
            AnimationClipDic[$"mount_idle_{i}"] = new List<string> { $"mount_idle{i}" };
            AnimationClipDic[$"mount_run_{i}"] = new List<string> { $"mount_run{i}" };
        }

        // 特殊模型动画配置
        InitializeModelSpecificAnimations();
    }

    private static void InitializeModelSpecificAnimations()
    {
        // Boss特殊动画
        ModelConfigs[BuildModelType.Boss].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "ui_idle", AnimationClipDic["idle"] },
            { "magic1_1", new List<string> { "skill_pre" } },
            { "magic1_2", new List<string> { "skill_mid" } },
            { "magic1_3", new List<string> { "skill_back" } }
        };

        // Monster特殊动画
        ModelConfigs[BuildModelType.Monster].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "ui_idle", AnimationClipDic["idle"] }
        };

        // NPC特殊动画
        ModelConfigs[BuildModelType.Npc].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "rest", AnimationClipDic["idle"] },
            { "zuo", AnimationClipDic["idle"] }
        };

        // Wing特殊动画
        ModelConfigs[BuildModelType.Wing].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "run", AnimationClipDic["idle"] },
            { "scene_nor_idle", AnimationClipDic["idle"] }
        };

        // 天神特殊动画
        ModelConfigs[BuildModelType.TianShen].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "combo1_1", new List<string> { "Attack01", "attack01" } },
            { "combo1_1_back", new List<string> { "Attack01_back", "attack01_back" } },
            { "combo1_2", new List<string> { "Attack02", "attack02" } },
            { "combo1_2_back", new List<string> { "Attack02_back", "attack02_back" } },
            { "combo1_3", new List<string> { "Attack03", "attack03" } },
            { "combo1_3_back", new List<string> { "Attack03_back", "attack03_back" } },
            { "attack1", new List<string> { "Skill01", "skill01" } },
            { "attack2", new List<string> { "Skill02", "skill02" } },
            { "attack3", new List<string> { "Skill03", "skill03" } },
            { "attack1_back", new List<string> { "Skill01_back", "skill01_back" } },
            { "attack2_back", new List<string> { "Skill02_back", "skill02_back" } },
            { "attack3_back", new List<string> { "Skill03_back", "skill03_back" } },
            { "ui_idle", AnimationClipDic["idle"] }
        };

        // 其他模型的ui_idle映射
        var uiIdleModels = new[] { BuildModelType.Child, BuildModelType.Chongwu, BuildModelType.YuShou,
                                  BuildModelType.Beishi, BuildModelType.WuHun, BuildModelType.Halo };
        foreach (var model in uiIdleModels)
        {
            ModelConfigs[model].specialAnimationClips = new Dictionary<string, List<string>>
            {
                { "ui_idle", AnimationClipDic["idle"] }
            };
        }

        // 特殊配置
        ModelConfigs[BuildModelType.Chongwu].specialAnimationClips["yu_jian"] = AnimationClipDic["idle"];
        ModelConfigs[BuildModelType.YuShou].specialAnimationClips["attack2"] = new List<string> { "skill" };

        // Fabao特殊动画
        ModelConfigs[BuildModelType.Fabao].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "IdleFight", AnimationClipDic["idle"] },
            { "scene_nor_idle", AnimationClipDic["idle"] },
            { "scene_fight_idle", AnimationClipDic["idle"] }
        };

        // Zuoqi特殊动画
        ModelConfigs[BuildModelType.Zuoqi].specialAnimationClips = new Dictionary<string, List<string>>
        {
            { "mount_scene_idle", AnimationClipDic["idle"] }
        };
    }

    #endregion

    #region 菜单项定义

    // 角色相关
    [MenuItem("Assets/策划专用/一键生成模型/一键生成时装")] private static void BuildShiZhuang() => ProcessSelection(BuildModelType.RoleBody);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成头发")] private static void BuildTouFa() => ProcessSelection(BuildModelType.RoleHair);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成脸")] private static void BuildFace() => ProcessSelection(BuildModelType.RoleFace);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成角色武器")] private static void BuildRoleWeapon() => ProcessSelection(BuildModelType.RoleWeapon);

    // 天神相关
    [MenuItem("Assets/策划专用/一键生成模型/一键生成天神")] private static void BuildTianShen() => ProcessSelection(BuildModelType.TianShen);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成天神武器")] private static void BuildTianShenWuQi() => ProcessSelection(BuildModelType.TianShenWuQi);

    // 坐骑、翅膀
    [MenuItem("Assets/策划专用/一键生成模型/一键生成坐骑")] private static void BuildZuoqi() => ProcessSelection(BuildModelType.Zuoqi);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成翅膀")] private static void BuildWing() => ProcessSelection(BuildModelType.Wing);

    // 御兽、宠物
    [MenuItem("Assets/策划专用/一键生成模型/一键生成御兽")] private static void BuildYuShou() => ProcessSelection(BuildModelType.YuShou);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成宠物")] private static void BuildChongwu() => ProcessSelection(BuildModelType.Chongwu);

    // 怪物
    [MenuItem("Assets/策划专用/一键生成模型/一键生成Boss")] private static void BuildBoss() => ProcessSelection(BuildModelType.Boss);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成Monster")] private static void BuildMonster() => ProcessSelection(BuildModelType.Monster);

    // NPC、仙娃
    [MenuItem("Assets/策划专用/一键生成模型/一键生成NPC")] private static void BuildNpc() => ProcessSelection(BuildModelType.Npc);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成仙娃")] private static void BuildChild() => ProcessSelection(BuildModelType.Child);

    // 法宝、装饰
    [MenuItem("Assets/策划专用/一键生成模型/一键生成法宝")] private static void BuildFabao() => ProcessSelection(BuildModelType.Fabao);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成脸饰")] private static void BuildLianshi() => ProcessSelection(BuildModelType.Lianshi);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成腰饰")] private static void BuildYaoshi() => ProcessSelection(BuildModelType.Yaoshi);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成背饰")] private static void BuildBeishi() => ProcessSelection(BuildModelType.Beishi);

    // UI杂项、武魂、光环
    [MenuItem("Assets/策划专用/一键生成模型/一键生成UIOther")] private static void BuildUIOther() => ProcessSelection(BuildModelType.UIOther);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成武魂")] private static void BuildWuHun() => ProcessSelection(BuildModelType.WuHun);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成光环魂息")] private static void BuildHalo() => ProcessSelection(BuildModelType.Halo);

    // 掉落物、采集物
    [MenuItem("Assets/策划专用/一键生成模型/一键生成装备掉落")] private static void BuildEquipDrop() => ProcessSelection(BuildModelType.EquipDrop);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成掉落物")] private static void BuildFallItem() => ProcessSelection(BuildModelType.FallItem);
    [MenuItem("Assets/策划专用/一键生成模型/一键生成采集物")] private static void BuildGather() => ProcessSelection(BuildModelType.Gather);

    // 工具功能
    [MenuItem("Assets/策划专用/一键提取FBX动画片段")]
    private static void ExtractFBXAnimations()
    {
        var assetGuids = Selection.assetGUIDs;
        bool hasUpdates = false;

        foreach (var guid in assetGuids)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            if (AssetDatabase.IsValidFolder(path))
            {
                ExtractAnimationsFromFolder(path);
                hasUpdates = true;
            }
            else if (ExtractAnimationFromSingleFBX(path))
            {
                hasUpdates = true;
            }
        }

        if (hasUpdates)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    #endregion

    #region 核心处理方法

    /// <summary>
    /// 处理选中的资源并生成对应类型的模型
    /// </summary>
    private static void ProcessSelection(BuildModelType modelType)
    {
        var assetGuids = Selection.assetGUIDs;
        if (assetGuids.Length == 0)
        {
            Debug.LogError("请先选择要处理的文件夹！");
            return;
        }

        foreach (var guid in assetGuids)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            if (AssetDatabase.IsValidFolder(path))
            {
                ExtractAnimationsFromFolder(path);
                GenerateModel(modelType, path);
            }
            else
            {
                Debug.LogError("请选中文件夹进行操作！");
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
    }

    /// <summary>
    /// 从单个FBX文件中提取动画片段
    /// </summary>
    private static bool ExtractAnimationFromSingleFBX(string assetPath)
    {
        if (!assetPath.EndsWith(".fbx", StringComparison.OrdinalIgnoreCase) ||
            assetPath.Contains("@skin", StringComparison.OrdinalIgnoreCase))
            return false;

        var allAssets = AssetDatabase.LoadAllAssetsAtPath(assetPath);
        bool hasAnimations = false;
        bool hasModelData = false;
        bool isUpdate = false;

        foreach (var asset in allAssets)
        {
            if (asset is AnimationClip clip && !asset.name.Contains("__preview__"))
            {
                var newClip = new AnimationClip();
                EditorUtility.CopySerialized(clip, newClip);
                AnimationOptimize.OptionalFloatCurves(newClip);

                var directory = Path.GetDirectoryName(assetPath);
                var newClipPath = Path.Combine(directory, clip.name + ".anim");
                AssetDatabase.CreateAsset(newClip, newClipPath);

                hasAnimations = true;
                isUpdate = true;
            }
            else if (asset is Avatar || asset is Mesh)
            {
                hasModelData = true;
            }
        }

        // 如果只包含动画且没有模型数据，删除原FBX文件
        if (hasAnimations && !hasModelData)
        {
            AssetDatabase.DeleteAsset(assetPath);
        }

        return isUpdate;
    }

    /// <summary>
    /// 提取文件夹下所有FBX文件中的动画片段
    /// </summary>
    private static void ExtractAnimationsFromFolder(string folderPath)
    {
        var fbxGuids = AssetDatabase.FindAssets("t:Model", new[] { folderPath });
        bool hasUpdates = false;

        foreach (var guid in fbxGuids)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            if (ExtractAnimationFromSingleFBX(path))
            {
                hasUpdates = true;
            }
        }

        if (hasUpdates)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }
    }

    /// <summary>
    /// 统一的模型生成方法
    /// </summary>
    private static void GenerateModel(BuildModelType modelType, string path)
    {
        if (!ModelConfigs.TryGetValue(modelType, out var config))
        {
            Debug.LogError($"未找到模型类型 {modelType} 的配置");
            return;
        }

        // 验证路径
        if (!path.StartsWith(config.modelStartPath))
        {
            Debug.LogError($"请选择正确的资源路径！当前路径: {path}, 期望路径: {config.modelStartPath}");
            return;
        }

        // 特殊验证逻辑
        if (!ValidateModelPath(modelType, path))
            return;

        // 查找资源
        var guids = AssetDatabase.FindAssets("*", new[] { path });
        if (!FindModelResources(modelType, path, guids, out var resources))
            return;

        // 生成模型
        CreateModelPrefab(modelType, path, config, resources);
    }

    /// <summary>
    /// 模型资源数据结构
    /// </summary>
    private struct ModelResources
    {
        public string skinFbxPath;
        public string texturePath;
        public string animControllerPath;
        public bool hasSkin;
        public bool hasTexture;
        public bool hasAnimController;
    }

    /// <summary>
    /// 验证模型路径的特殊逻辑
    /// </summary>
    private static bool ValidateModelPath(BuildModelType modelType, string path)
    {
        var folderName = Path.GetFileName(path);

        // Boss和Monster的特殊验证
        if (modelType == BuildModelType.Boss && !folderName.StartsWith("8"))
        {
            Debug.LogError($"Boss资源应该以8开头，当前选择了: {path}");
            return false;
        }

        if (modelType == BuildModelType.Monster && !folderName.StartsWith("2"))
        {
            Debug.LogError($"Monster资源应该以2开头，当前选择了: {path}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 查找模型所需的资源文件
    /// </summary>
    private static bool FindModelResources(BuildModelType modelType, string path, string[] guids, out ModelResources resources)
    {
        resources = new ModelResources();

        foreach (var guid in guids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var assetLower = assetPath.ToLower();

            // 查找FBX文件
            if (assetLower.EndsWith(".fbx") && (IsSingleFbxInFolder(path) || assetLower.Contains("@skin")))
            {
                resources.skinFbxPath = assetPath;
                resources.hasSkin = true;
            }

            // 查找贴图文件
            if (assetLower.EndsWith(".tga") && !resources.hasTexture && IsMainTexture(assetLower))
            {
                resources.texturePath = assetPath;
                resources.hasTexture = true;
            }

            // 查找动画控制器
            if (assetLower.EndsWith("_controller.overridecontroller"))
            {
                resources.animControllerPath = assetPath;
                resources.hasAnimController = true;
            }

            if (resources.hasSkin && resources.hasTexture && resources.hasAnimController)
                break;
        }

        // 验证必需资源
        if (!resources.hasSkin)
        {
            Debug.LogError($"报错！！！路径：{path} 没有找到Skin FBX，该模型生成失败");
            return false;
        }

        if (!resources.hasTexture && modelType != BuildModelType.RoleFace)
        {
            Debug.LogError($"报错！！！路径：{path} 没有找到模型贴图，该模型生成失败");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 判断是否为主贴图
    /// </summary>
    private static bool IsMainTexture(string texturePath)
    {
        return !HasKeyWordInName(texturePath, "g") &&
               !HasKeyWordInName(texturePath, "n") && !HasKeyWordInName(texturePath, "nor") && !HasKeyWordInName(texturePath, "normal") &&
               !HasKeyWordInName(texturePath, "t") &&
               !HasKeyWordInName(texturePath, "pbr");
    }

    /// <summary>
    /// 创建模型预制体
    /// </summary>
    private static void CreateModelPrefab(BuildModelType modelType, string path, ModelConfig config, ModelResources resources)
    {
        var folderName = Path.GetFileName(path);
        var prefabPath = GetPrefabPath(modelType, path, folderName);

        // 检查是否需要重建
        bool isRebuild = revertModelData.Record(prefabPath);
        if (isRebuild)
        {
            AssetDatabase.DeleteAsset(prefabPath);
        }

        // 加载FBX模型
        var modelFBX = AssetDatabase.LoadAssetAtPath<GameObject>(resources.skinFbxPath);
        if (modelFBX == null)
        {
            Debug.LogError($"无法加载FBX文件: {resources.skinFbxPath}");
            return;
        }

        // 实例化模型
        var model = GameObject.Instantiate(modelFBX);

        try
        {
            if (isRebuild)
            {
                revertModelData.Revert(ref model);
            }
            else
            {
                SetupNewModel(modelType, path, config, resources, model, prefabPath);
            }

            // 保存预制体
            PrefabUtility.SaveAsPrefabAsset(model, prefabPath);
        }
        finally
        {
            GameObject.DestroyImmediate(model);
        }
    }

    #endregion

    #region 模型设置方法

    /// <summary>
    /// 获取预制体路径
    /// </summary>
    private static string GetPrefabPath(BuildModelType modelType, string path, string folderName)
    {
        return modelType switch
        {
            BuildModelType.RoleWeapon => $"{path}/{folderName}01.prefab",
            _ => $"{path}/{folderName}.prefab"
        };
    }

    /// <summary>
    /// 设置新模型的组件和属性
    /// </summary>
    private static void SetupNewModel(BuildModelType modelType, string path, ModelConfig config,
                                     ModelResources resources, GameObject model, string prefabPath)
    {
        // 创建材质
        if (resources.hasTexture || modelType == BuildModelType.RoleFace)
        {
            CreateMaterial(modelType, path, resources.texturePath, model, prefabPath);
        }

        // 添加基础组件
        SetupBasicComponents(modelType, config, model);

        // 设置动画控制器
        SetupAnimatorController(modelType, config, resources, path, model, prefabPath);

        // 添加特殊组件
        SetupSpecialComponents(modelType, config, model);

        // 角色相关特殊处理
        if (IsRoleModel(modelType))
        {
            SetupRoleModel(modelType, resources.skinFbxPath, model);
        }

        // 添加渲染组件
        var actorRenderer = model.GetOrAddComponent<ActorRender>();
        actorRenderer.AutoFetch();

        // 动画优化器
        if (config.needsAnimatorOptimizer)
        {
            CreateAnimatorOptimizer(model, prefabPath);
        }
    }

    /// <summary>
    /// 设置基础组件
    /// </summary>
    private static void SetupBasicComponents(BuildModelType modelType, ModelConfig config, GameObject model)
    {
        // 添加动画事件分发器
        if (config.needsAttachment || config.needsClickable)
        {
            model.GetOrAddComponent<AnimatorEventDispatcher>();
        }

        // 添加附着点组件
        if (config.needsAttachment)
        {
            CreateAttachment(model, "");
        }

        // 添加可点击组件
        if (config.needsClickable)
        {
            CreateClickable(model, Vector3.zero);
        }

        // 添加特殊点位
        if (config.needsSpecialPoints)
        {
            CreateSpecialPoints(model);
        }
    }

    /// <summary>
    /// 设置特殊组件 - 处理一些模型类型特有的组件设置
    /// </summary>
    private static void SetupSpecialComponents(BuildModelType modelType, ModelConfig config, GameObject model)
    {
        switch (modelType)
        {
            case BuildModelType.Gather:
                CreateAttachment(model, "");
                break;

            case BuildModelType.TianShen:
                CreateActorAnimatorSyncter(model);
                break;

            case BuildModelType.Halo:
            case BuildModelType.Wing:
            case BuildModelType.Lianshi:
            case BuildModelType.Yaoshi:
            case BuildModelType.Beishi:
            case BuildModelType.Fabao:
                model.GetOrAddComponent<AttachObject>();
                break;

            default:
                break;
        }
    }

    /// <summary>
    /// 判断是否需要AttachObject组件
    /// </summary>
    private static bool NeedsAttachObject(BuildModelType modelType)
    {
        return modelType switch
        {
            BuildModelType.Wing or BuildModelType.Lianshi or BuildModelType.Yaoshi or
            BuildModelType.Beishi or BuildModelType.Fabao or BuildModelType.Halo => true,
            _ => false
        };
    }

    /// <summary>
    /// 判断是否为角色模型
    /// </summary>
    private static bool IsRoleModel(BuildModelType modelType)
    {
        return modelType switch
        {
            BuildModelType.RoleBody or BuildModelType.RoleHair or BuildModelType.RoleFace => true,
            _ => false
        };
    }

    /// <summary>
    /// 设置角色模型特殊属性
    /// </summary>
    private static void SetupRoleModel(BuildModelType modelType, string fbxPath, GameObject model)
    {
        var skinName = ParticleSystemRenderMeshFixed.GetSkinNameByPath(fbxPath);
        ChangeModelSkinnedMeshRendererName(model, skinName);
    }

    /// <summary>
    /// 创建特殊点位（AdjustPoint和FaZhenPoint）
    /// </summary>
    private static void CreateSpecialPoints(GameObject model)
    {
        CreateChildObject(model, "AdjustPoint");
        CreateChildObject(model, "FaZhenPoint");
    }

    /// <summary>
    /// 创建子对象
    /// </summary>
    private static void CreateChildObject(GameObject parent, string name)
    {
        var child = new GameObject(name);
        child.transform.SetParent(parent.transform, false);
    }

    /// <summary>
    /// 设置动画控制器
    /// </summary>
    private static void SetupAnimatorController(BuildModelType modelType, ModelConfig config,
                                               ModelResources resources, string path, GameObject model, string prefabPath)
    {
        string animCtrlPath;
        bool hasExistingController = resources.hasAnimController;

        if (modelType == BuildModelType.RoleWeapon)
        {
            // 武器特殊处理
            animCtrlPath = GetWeaponControllerPath(path);
        }
        else
        {
            var folderName = Path.GetFileName(path);
            animCtrlPath = hasExistingController ? resources.animControllerPath :
                          $"{path}/{folderName}_Controller.overrideController";
        }

        CreateAnimatorCtrl(modelType, hasExistingController, animCtrlPath, path, prefabPath, model);
    }

    /// <summary>
    /// 获取武器控制器路径
    /// </summary>
    private static string GetWeaponControllerPath(string path)
    {
        foreach (var kvp in WeaponCtrlAsset)
        {
            if (path.StartsWith(kvp.Key))
            {
                return kvp.Value;
            }
        }
        return ""; // 如果没找到匹配的，返回空字符串
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 判断文件夹中是否只有一个FBX文件
    /// 如果目录下仅有一个FBX文件，但非@skin后缀命名，则认为是原模型FBX
    /// 若不是，策划再后续检查即可
    /// </summary>
    private static bool IsSingleFbxInFolder(string path)
    {
        var guids = AssetDatabase.FindAssets("t:model", new[] { path });
        return guids.Length == 1;
    }

    /// <summary>
    /// 检查文件名是否包含关键词
    /// </summary>
    public static bool HasKeyWordInName(string path, string keyword)
    {
        var pattern = string.Format(SUFFIX_MATCH_PATTERN, keyword);
        var match = Regex.Match(path, pattern, RegexOptions.IgnoreCase);
        return match.Success;
    }

    /// <summary>
    /// 修改模型SkinnedMeshRenderer的名称
    /// </summary>
    private static void ChangeModelSkinnedMeshRendererName(GameObject model, string nameFirst)
    {
        if (string.IsNullOrEmpty(nameFirst))
            return;

        var renderers = model.GetComponentsInChildren<SkinnedMeshRenderer>(true);
        if (renderers == null || renderers.Length == 0)
            return;

        if (renderers.Length == 1)
        {
            renderers[0].gameObject.name = nameFirst;
        }
        else if (renderers.Length == 2 && nameFirst == "RoleFace")
        {
            renderers[0].gameObject.name = "RoleEyeball";
            renderers[1].gameObject.name = nameFirst;
        }

        // 移除Animator组件
        var animator = model.GetComponent<Animator>();
        if (animator != null)
        {
            UnityEngine.Object.DestroyImmediate(animator);
        }
    }

    /// <summary>
    /// 创建材质
    /// </summary>
    private static void CreateMaterial(BuildModelType modelType, string path, string texturePath, GameObject model, string prefabPath)
    {
        // RoleFace特殊处理：即使没有贴图也要创建材质
        if (modelType == BuildModelType.RoleFace)
        {
            if (string.IsNullOrEmpty(texturePath))
            {
                // RoleFace没有贴图时的特殊处理
                CreateMaterial("", path, prefabPath, model);
                return;
            }
        }
        else if (string.IsNullOrEmpty(texturePath))
        {
            return; // 其他类型没有贴图时直接返回
        }

        var matPath = path + texturePath.Substring(texturePath.LastIndexOf('/')).Replace(".tga", ".mat");
        CreateMaterial(matPath, path, prefabPath, model);
    }

    private static void CreateAnimatorCtrl(BuildModelType modelType, bool isFindAnimCtrl, string animCtrlPath, string foldPath, string prefabPath, GameObject model)
    {
        if (!ModelConfigs.TryGetValue(modelType, out var config))
        {
            Debug.LogError($"未找到模型类型 {modelType} 的配置");
            return;
        }

        if (isFindAnimCtrl)
        {
            AssetDatabase.DeleteAsset(animCtrlPath);
        }

        var baseController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(config.animatorControllerPath);
        var controller = new AnimatorOverrideController(baseController);
        AssetDatabase.CreateAsset(controller, animCtrlPath);

        AnimatorOverrideController animatorController = AssetDatabase.LoadAssetAtPath(animCtrlPath, typeof(AnimatorOverrideController)) as AnimatorOverrideController;
        Dictionary<string, List<string>> specialAnimationClipDic = config.specialAnimationClips;

        var overrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
        animatorController.GetOverrides(overrides);
        bool isFindClipFbx = false;
        for (int i = 0; i < overrides.Count; i++)
        {
            string animName = overrides[i].Key.name;
            List<string> mapList;
            if ((specialAnimationClipDic != null && specialAnimationClipDic.TryGetValue(animName, out mapList)) || AnimationClipDic.TryGetValue(animName, out mapList))
            {
                foreach (string clipName in mapList)
                {
                    string animClipAsset = $"{foldPath}/{clipName}.anim";
                    AnimationClip animClip = AssetDatabase.LoadAssetAtPath<AnimationClip>(animClipAsset);

                    if (animClip)
                    {
                        overrides[i] = new KeyValuePair<AnimationClip, AnimationClip>(overrides[i].Key, animClip);
                        isFindClipFbx = true;
                        break;
                    }
                }
            }
        }

        if (isFindClipFbx)
        {
            animatorController.ApplyOverrides(overrides);
        }

        Animator animator = model.GetOrAddComponent<Animator>();
        animator.runtimeAnimatorController = animatorController;
        animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
    }

    private static void CreateClickable(GameObject model, Vector3 colPos, float height = 1, float radius = 1)
    {
        ClickableObject clickObj = model.GetOrAddComponent<ClickableObject>();
        GameObject clickableObj = new GameObject();
        clickableObj.transform.parent = model.transform;
        clickableObj.transform.localPosition = Vector3.zero;
        clickableObj.transform.localRotation = Quaternion.identity;
        clickableObj.transform.localScale = Vector3.one;
        clickableObj.gameObject.name = "Clickable";
        clickableObj.gameObject.layer = GameLayers.Clickable;
        CapsuleCollider collider = clickableObj.GetOrAddComponent<CapsuleCollider>();
        collider.transform.localPosition = colPos;
        collider.height = height;
        collider.radius = radius;
        Clickable clickable = clickableObj.GetOrAddComponent<Clickable>();
        clickable.Owner = clickObj;
    }

    private static void CreateMaterial(string varMatPath, string path, string prefabPath, GameObject model)
    {
        bool isNewMat = false;
        string matPath = varMatPath;
        Material newMat = null;
        Material mat = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
        if (mat == null)
        {
            //有些材质球带_PbrCf后缀，做容错再检查一次
            bool is_Contains_PbrCf = matPath.Contains("_PbrCf.mat");
            matPath = matPath.Replace(is_Contains_PbrCf ? "_PbrCf.mat" : ".mat", is_Contains_PbrCf ? ".mat" : "_PbrCf.mat");
            mat = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
        }
        if (mat == null || mat.shader.name != "Srp/Standard/SrpRole_PbrCf")
        {
            matPath = matPath.Replace("_PbrCf.mat", ".mat");
            AssetDatabase.CopyAsset(MATERIAL_TEMPLATE_PATH, matPath);
            newMat = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
            newMat.shader = Shader.Find("Srp/Standard/SrpRole_PbrCf");

            if (mat != null)
            {
                AssetDatabase.DeleteAsset(varMatPath);
            }
            isNewMat = true;
        }
        else
        {
            //如果是用了SrpRole_PbrCf的材质球，不要修改参数
            newMat = mat;
        }

        if (isNewMat)
        {
            bool isFindMainTex = false;
            string[] texGUIDs = AssetDatabase.FindAssets("t:texture", new string[] { path });
            foreach (string texGUID in texGUIDs)
            {
                var mainTexPath = AssetDatabase.GUIDToAssetPath(texGUID);
                if (!HasKeyWordInName(mainTexPath, "g")
                    && !HasKeyWordInName(mainTexPath, "n") && !HasKeyWordInName(mainTexPath, "nor") && !HasKeyWordInName(mainTexPath, "normal")
                    && !HasKeyWordInName(mainTexPath, "t")
                    && !HasKeyWordInName(mainTexPath, "pbr"))
                {
                    newMat.mainTexture = AssetDatabase.LoadAssetAtPath(mainTexPath, typeof(Texture)) as Texture;
                    isFindMainTex = true;
                    break;
                }
            }
            if (!isFindMainTex)
            {
                Debug.LogErrorFormat("未找到主贴图，请检查资源,{0}", path);
            }

            for (int i = 0; i < texGUIDs.Length; i++)
            {
                var TexPath = AssetDatabase.GUIDToAssetPath(texGUIDs[i]);
                if (HasKeyWordInName(TexPath, "n") || HasKeyWordInName(TexPath, "nor") || HasKeyWordInName(TexPath, "normal"))
                {
                    newMat.SetTexture("_NormalMap", AssetDatabase.LoadAssetAtPath(TexPath, typeof(Texture)) as Texture);
                }

                if (HasKeyWordInName(TexPath, "g") || HasKeyWordInName(TexPath, "pbr"))
                {
                    newMat.SetTexture("_MetallicGlossMap", AssetDatabase.LoadAssetAtPath(TexPath, typeof(Texture)) as Texture);
                }
            }
            EditorUtility.SetDirty(newMat);
        }

        Renderer renderer = model.GetComponentInChildren<Renderer>();
        if (renderer)
        {
            renderer.sharedMaterial = newMat;
        }
        else
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有挂载Mesh Renderer或者Skinned Mesh Renderer组件，请检查！！！", prefabPath);
        }
    }

    private static void CreateAttachment(GameObject model, string prefabPath)
    {
        ActorAttachment attachment = model.GetOrAddComponent<ActorAttachment>();
        attachment.AutoPick();
        if (attachment.GetAttachPoint(0) == null)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 该模型找不到UI挂点，请检查！！！", prefabPath);
        }
    }

    private static void CreateActorAnimatorSyncter(GameObject model)
    {
        ActorAnimatorSyncter animatorSyncter = model.GetOrAddComponent<ActorAnimatorSyncter>();
    }

    private static void CreateAnimatorOptimizer(GameObject model, string prefabPath)
    {
        if (prefabPath.StartsWith("Assets/Game/Actors/"))
        {
            return;
        }

        AnimatorOptimizer animatorOptimizer = model.GetOrAddComponent<AnimatorOptimizer>();
        animatorOptimizer.Optimize();
    }

    private static string GetRootPath(GameObject root, GameObject point, string rootPath)
    {
        GameObject parentPoint = point.transform.parent.gameObject;
        if (parentPoint == root)
        {
            return rootPath;
        }
        return GetRootPath(root, parentPoint, string.Format("{0}/{1}", parentPoint.gameObject.name, rootPath));
    }

    private static bool FindMatchResources(BuildModelType modelType, string path, string[] guids, out string skinFbxPath, out string texPath, out string animCtrlPath
        , out bool isFindSkin, out bool isFindTex, out bool isFindAnimCtrl)
    {
        bool _isFindSkin = false;
        bool _isFindTex = false;
        bool _isFindAnimCtrl = false;
        string _skinFbxPath = "";
        string _texPath = "";
        string _animCtrlPath = null;

        foreach (string guid in guids)
        {
            string asset = AssetDatabase.GUIDToAssetPath(guid);
            string asset_match = asset.ToLower();
            if (asset_match.EndsWith(".fbx") && (IsSingleFbxInFolder(path) || asset_match.Contains("@skin")))
            {
                _skinFbxPath = asset;
                _isFindSkin = true;
            }

            if (asset_match.EndsWith(".tga") && _isFindTex == false
                && !HasKeyWordInName(asset_match, "g")
                && !HasKeyWordInName(asset_match, "n") && !HasKeyWordInName(asset_match, "nor") && !HasKeyWordInName(asset_match, "normal")
                && !HasKeyWordInName(asset_match, "t")
                && !HasKeyWordInName(asset_match, "pbr"))
            {
                _texPath = asset;
                _isFindTex = true;
            }

            if (asset_match.EndsWith("_controller.overridecontroller"))
            {
                _animCtrlPath = asset;
                _isFindAnimCtrl = true;
            }

            if (_isFindSkin && _isFindTex && _isFindAnimCtrl)
            {
                break;
            }
        }

        isFindSkin = _isFindSkin;
        isFindTex = _isFindTex;
        isFindAnimCtrl = _isFindAnimCtrl;
        skinFbxPath = _skinFbxPath;
        texPath = _texPath;
        animCtrlPath = _animCtrlPath;

        if (!isFindSkin)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有找到Skin FBX，该模型生成失败", path);
            return false;
        }
        if (!isFindTex && modelType != BuildModelType.RoleFace)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有找到模型贴图，该模型生成失败", path);
            return false;
        }
        return true;
    }


    internal class RevertModelData
    {
        struct GameObjectAttachData
        {
            public string rootPath;
            public string pointName;
            public string bundleName;
            public string assetName;
        }
        private List<GameObjectAttachData> gameObjectAttachDatas = new List<GameObjectAttachData>();

        private string fixedScaleObjName;
        private Vector3[] fixedScaleObjTransformData;

        private AttachObject attachObject = new AttachObject();
        private AnimatorOptimizer animatorOptimizer = new AnimatorOptimizer();

        string[] originAnimatorOptimizerSearchPatterns = null;

        struct RenderListData
        {
            public string renderObjRootName;
            public Material[] originMats;
        }
        private List<RenderListData> renderListDatas = new List<RenderListData>();

        public bool Record(string originPrefabPath)
        {
            Reset();
            List<string> pointNameForGameObjectAttach = new List<string>();//骨骼优化后未将挂载GameObjectAttach的节点加入到排查列表中，导致还原出问题，这里做容错
            GameObject originPrefabObj = AssetDatabase.LoadAssetAtPath(originPrefabPath, typeof(GameObject)) as GameObject;
            if (originPrefabObj != null)
            {
                GameObjectAttach[] gameObjectAttacts = originPrefabObj.GetComponentsInChildren<GameObjectAttach>();
                if (gameObjectAttacts != null && gameObjectAttacts.Length > 0)
                {
                    foreach (GameObjectAttach attach in gameObjectAttacts)
                    {
                        GameObjectAttachData point = new GameObjectAttachData();
                        string rootPath = GetRootPath(originPrefabObj, attach.gameObject, "");
                        point.rootPath = rootPath;
                        point.pointName = attach.gameObject.name;
                        point.bundleName = attach.BundleName;
                        point.assetName = attach.AssetName;
                        gameObjectAttachDatas.Add(point);

                        string parentObjName = attach.transform.parent.gameObject.name;
                        pointNameForGameObjectAttach.Add($"/{parentObjName}$");
                    }
                }

                for (int i = 0; i < originPrefabObj.transform.childCount; i++)
                {
                    Transform childTrans = originPrefabObj.transform.GetChild(i);
                    if (childTrans.name.Contains("001_ScaleFix") || childTrans.name == "GameObject" && childTrans.childCount != 0)
                    {
                        fixedScaleObjName = childTrans.name;
                        fixedScaleObjTransformData = new Vector3[]
                        {
                            new Vector3(childTrans.localPosition.x, childTrans.localPosition.y, childTrans.localPosition.z),
                            new Vector3(childTrans.localEulerAngles.x, childTrans.localEulerAngles.y, childTrans.localEulerAngles.z),
                            new Vector3(1, 1, 1),
                            //new Vector3(childTrans.localScale.x, childTrans.localScale.y, childTrans.localScale.z),//A2、A3系列项目已不再强制调整模型大小
                        };
                    }
                }

                AttachObject originAttachObject = originPrefabObj.GetComponent<AttachObject>();
                if (null != originAttachObject)
                {
                    attachObject.physiqueConfig = new AttachObject.PhysiqueConfig[originAttachObject.physiqueConfig.Length];
                    Array.Copy(originAttachObject.physiqueConfig, attachObject.physiqueConfig, originAttachObject.physiqueConfig.Length);
                }

                AnimatorOptimizer originAnimatorOptimizer = originPrefabObj.GetComponent<AnimatorOptimizer>();
                if (null != originAnimatorOptimizer && (originAnimatorOptimizer.GetSearchPatterns.Length > 0 || pointNameForGameObjectAttach.Count > 0))
                {
                    if (pointNameForGameObjectAttach.Count > 0)
                    {
                        originAnimatorOptimizer.AddSearchPatterns(pointNameForGameObjectAttach.ToArray());
                    }
                    originAnimatorOptimizerSearchPatterns = new string[originAnimatorOptimizer.GetSearchPatterns.Length];
                    Array.Copy(originAnimatorOptimizer.GetSearchPatterns, originAnimatorOptimizerSearchPatterns, originAnimatorOptimizer.GetSearchPatterns.Length);
                }

                ActorRender actorRender = originPrefabObj.GetComponent<ActorRender>();
                if (null == actorRender)
                {
                    Debug.LogErrorFormat("找不到{0}上挂载的ActorRender组件，重新生成模型后无法还原该组件信息，请查看原Prefab文件是否异常");
                }
                else
                {
                    List<ActorRender.RenderItem> renderList = actorRender.RenderList;
                    foreach (ActorRender.RenderItem item in renderList)
                    {
                        RenderListData renderListData = new RenderListData();
                        renderListData.renderObjRootName = GetRootPath(originPrefabObj, item.renderer.gameObject, item.renderer.name);
                        renderListData.originMats = item.pbrMaterials;
                        this.renderListDatas.Add(renderListData);
                    }
                }

                return true;
            }

            return false;
        }

        public void Revert(ref GameObject newObj)
        {

            if (originAnimatorOptimizerSearchPatterns != null)
            {
                AnimatorOptimizer animatorOptimizer = newObj.GetOrAddComponent<AnimatorOptimizer>();
                animatorOptimizer.AddSearchPatterns(originAnimatorOptimizerSearchPatterns);
                animatorOptimizer.Optimize();
            }

            //还原GameObjectAttach组件
            foreach (GameObjectAttachData point in gameObjectAttachDatas)
            {
                Transform transform = newObj.transform.Find(string.Format("{0}{1}", point.rootPath, point.pointName));
                if (transform == null)
                {
                    Transform parentTrans = newObj.transform.Find(point.rootPath.Replace(string.Format("/{0}", point.pointName), ""));
                    if (parentTrans == null)
                    {
                        Debug.LogError(string.Format("无法找到路径{0}，请手动挂载特效预制体{1}", point.rootPath, point.assetName));
                        continue;//两个节点都无法找到，挂载节点匹配失败，可能是fbx骨骼点有变动，需要手动去挂载特效GameObjectAttach组件
                    }
                    GameObject gameObject = new GameObject(point.pointName);
                    gameObject.transform.parent = parentTrans;
                    gameObject.transform.localPosition = Vector3.zero;
                    gameObject.transform.localEulerAngles = Vector3.zero;
                    gameObject.transform.localScale = Vector3.one;
                    transform = gameObject.transform;
                }
                GameObjectAttach gameObjectAttach = transform.GetOrAddComponent<GameObjectAttach>();
                gameObjectAttach.AssetName = point.assetName;
                gameObjectAttach.BundleName = point.bundleName;
                gameObjectAttach.RefreshAssetBundleName();
            }

            //还原FixedScale点
            if (!string.IsNullOrEmpty(fixedScaleObjName))
            {
                GameObject fixedObj = new GameObject(fixedScaleObjName);
                fixedObj.transform.parent = newObj.transform;
                fixedObj.transform.localPosition = new Vector3(fixedScaleObjTransformData[0].x, fixedScaleObjTransformData[0].y, fixedScaleObjTransformData[0].z);
                fixedObj.transform.localEulerAngles = new Vector3(fixedScaleObjTransformData[1].x, fixedScaleObjTransformData[1].y, fixedScaleObjTransformData[1].z);
                fixedObj.transform.localScale = new Vector3(fixedScaleObjTransformData[2].x, fixedScaleObjTransformData[2].y, fixedScaleObjTransformData[2].z);

                int index = 0;
                for (int i = 0; i < newObj.transform.childCount; i++)
                {
                    Transform childTrans = newObj.transform.GetChild(index);
                    if (childTrans.gameObject != fixedObj)
                    {
                        Vector3 position = new Vector3(childTrans.localPosition.x, childTrans.localPosition.y, childTrans.localPosition.z);
                        Vector3 rotation = new Vector3(childTrans.localEulerAngles.x, childTrans.localEulerAngles.y, childTrans.localEulerAngles.z);
                        Vector3 scale = new Vector3(childTrans.localScale.x, childTrans.localScale.y, childTrans.localScale.z);
                        childTrans.parent = fixedObj.transform;
                        childTrans.localPosition = position;
                        childTrans.localEulerAngles = rotation;
                        childTrans.localScale = scale;
                    }
                    else
                    {
                        index += 1;
                    }
                }
            }

            if (renderListDatas.Count > 0)
            {
                foreach (RenderListData item in renderListDatas)
                {
                    Transform renderTrans = newObj.transform.Find(item.renderObjRootName);
                    if (null == renderTrans)
                    {
                        Debug.LogErrorFormat("找不到节点{0}，无法恢复部分RenderList数据", item.renderObjRootName);
                        continue;
                    }
                    SkinnedMeshRenderer renderer = renderTrans.GetComponent<SkinnedMeshRenderer>();
                    if (renderer == null)
                    {
                        MeshRenderer meshRenderer = renderTrans.GetComponent<MeshRenderer>();
                        if (meshRenderer == null)
                        {
                            Debug.LogErrorFormat("找不到Renderer组件{0}，无法恢复部分RenderList数据", item.renderObjRootName);
                            continue;
                        }
                        meshRenderer.sharedMaterials = item.originMats;
                    }
                    else
                    {
                        renderer.sharedMaterials = item.originMats;
                    }
                }
            }

            if (attachObject.physiqueConfig != null)
            {
                AttachObject newAttachObject = newObj.GetOrAddComponent<AttachObject>();
                newAttachObject.physiqueConfig = new AttachObject.PhysiqueConfig[attachObject.physiqueConfig.Length];
                Array.Copy(attachObject.physiqueConfig, newAttachObject.physiqueConfig, attachObject.physiqueConfig.Length);
            }
        }

        public void Reset()
        {
            gameObjectAttachDatas.Clear();
            this.renderListDatas.Clear();
            fixedScaleObjTransformData = null;
            attachObject.physiqueConfig = null;
            originAnimatorOptimizerSearchPatterns = null;
        }
    }
}
#endregion
