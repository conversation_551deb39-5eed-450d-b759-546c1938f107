﻿using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections.Generic;
using Game;
using Nirvana;

internal class SelectionHelper
{
    private static string selectObjPath;
    private static GameObject selectPrefab;

    [InitializeOnLoadMethod]
    private static void Start()
    {
        //在Hierarchy面板按空格键相当于开关GameObject
        EditorApplication.hierarchyWindowItemOnGUI += HierarchyWindowItemOnGUI;

        //在Project面板按空格键相当于Show In Explorer
        EditorApplication.projectWindowItemOnGUI += ProjectWindowItemOnGUI;
    }

    private static void ProjectWindowItemOnGUI(string guid, Rect selectionRect)
    {
        if (Event.current.type == EventType.KeyDown
            && Event.current.keyCode == KeyCode.Space
            && selectionRect.Contains(Event.current.mousePosition))
        {
            string strPath = AssetDatabase.GUIDToAssetPath(guid);
            if (Event.current.alt)
            {
                UnityEngine.Debug.Log(strPath);
                Event.current.Use();
                return;
            }

            if (Path.GetExtension(strPath) == string.Empty) //文件夹
            {
                Process.Start(Path.GetFullPath(strPath));
            }
            else //文件
            {
                Process.Start("explorer.exe", "/select," + Path.GetFullPath(strPath));
            }

            Event.current.Use();
        }
    }

    private static void HierarchyWindowItemOnGUI(int instanceID, Rect selectionRect)
    {
        Event e = Event.current;
        if (e.type == EventType.KeyDown)
        {
            switch (e.keyCode)
            {
                case KeyCode.Space:
                    ToggleGameObjcetActiveSelf();
                    e.Use();
                    break;
                case KeyCode.F1:
                    SaveActiveObject();
                    e.Use();
                    break;
                case KeyCode.F3:
                    CopyGameObjectAttach();
                    e.Use();
                    break;
                case KeyCode.F4:
                    PasteGameObjectAttach();
                    e.Use();
                    break;
            }
        }
        else if (e.type == EventType.MouseDown && e.button == 2)
        {
            SetAllActive();
            e.Use();
        }
    }

    internal static void ToggleGameObjcetActiveSelf()
    {
        Undo.RecordObjects(Selection.gameObjects, "Active");
        foreach (var go in Selection.gameObjects)
        {
            go.SetActive(!go.activeSelf);
        }
    }

    //按鼠标中键，将Root节点下的所有子物体显示出来
    static void SetAllActive()
    {
        var children = Selection.activeGameObject.GetComponentsInChildren<Transform>(true);
        foreach (var child in children)
        {
            var gameObj = child.gameObject;
            Undo.RecordObject(gameObj, "SetActive");
            gameObj.SetActive(true);
        }
    }

    private static void SaveActiveObject()
    {
        if (EditorApplication.isPlaying)
        {
            var instanceID = GetInstanceID(Selection.activeGameObject);
            selectPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GetAssetPath(instanceID));
            if (null != selectPrefab)
            {
                GameRoot.ShowMessage("保存成功", 1f);
            }
            else
            {
                GameRoot.ShowMessage("保存失败", 1f);
            }
        }
        else
        {
            JumpSelectObject();
        }
    }

    private static Dictionary<string, GameObject> gameObjectAttachDic = new Dictionary<string, GameObject>();
    private static void CopyGameObjectAttach()
    {
        gameObjectAttachDic.Clear();
        GameObject selectObj = Selection.activeGameObject;
        if (null == selectObj)
            return;

        var attachs = selectObj.GetComponentsInChildren<GameObjectAttach>(true);
        for (int i = 0; i < attachs.Length; ++i)
        {
            var attach = attachs[i];
            var target = attach.transform;
            string path = "";
            if (target.gameObject != selectObj)
            {
                path = target.name;
                while (target.transform.parent.gameObject != selectObj)
                {
                    target = target.transform.parent;
                    path = target.name + "/" + path;
                }
            }

            gameObjectAttachDic.Add(path, attach.gameObject);
        }
    }

    private static void PasteGameObjectAttach()
    {
        GameObject selectObj = Selection.activeGameObject;
        if (null == selectObj)
            return;

        foreach (var key in gameObjectAttachDic.Keys)
        {
            var go = gameObjectAttachDic[key];
            var attach = go.GetComponent<GameObjectAttach>();
            if (null == attach)
            {
                continue;
            }

            if (key == string.Empty)
            {
                var target = selectObj.gameObject.GetOrAddComponent<GameObjectAttach>();
                target.AssetGuid = attach.AssetGuid;
                target.RefreshAssetBundleName();
                continue;
            }

            var trans = selectObj.transform.Find(key);
            if (trans)
            {
                var target = trans.gameObject.GetOrAddComponent<GameObjectAttach>();
                target.AssetGuid = attach.AssetGuid;
                target.RefreshAssetBundleName();
            }
            else
            {
                var splits = key.Split('/');
                var nodeName = splits[splits.Length - 1];
                var newPath = key.Substring(0, key.Length - nodeName.Length);
                trans = selectObj.transform.Find(newPath);
                if (trans)
                {
                    var obj = new GameObject(nodeName);
                    obj.transform.SetParent(trans, false);
                    obj.transform.localPosition = go.transform.localPosition;
                    obj.transform.localRotation = go.transform.localRotation;
                    obj.transform.localScale = go.transform.localScale;
                    var target = obj.gameObject.GetOrAddComponent<GameObjectAttach>();
                    target.AssetGuid = attach.AssetGuid;
                    target.RefreshAssetBundleName();
                }
                else
                {
                    UnityEngine.Debug.LogError(string.Format("复制特效失败，找不到该路径: {0}", key));
                }
            }
        }
    }

    private static int GetInstanceID(GameObject go)
    {
        if (null == go)
        {
            selectObjPath = string.Empty;
            selectPrefab = null;
            return 0;
        }

        var instanceID = EditorResourceMgr.GetOriginalInstanceId(go);

        Stack<string> stack = new Stack<string>();
        while (0 == instanceID && go.transform.parent)
        {
            stack.Push(go.name);
            go = go.transform.parent.gameObject;
            instanceID = EditorResourceMgr.GetOriginalInstanceId(go);
        }

        if (stack.Count > 0)
        {
            selectObjPath = stack.Pop();
        }

        while (stack.Count > 0)
        {
            selectObjPath += "/" + stack.Pop();
        }
        return instanceID;
    }

    private static void JumpSelectObject()
    {
        if (null != selectPrefab)
        {
            var prefab = InstantiatePrefab();
            var target = prefab.transform.Find(selectObjPath);
            EditorGUIUtility.PingObject(target);
        }
    }

    private static GameObject InstantiatePrefab()
    {
        var scene = SceneManager.GetActiveScene();
        var activeObjs = scene.GetRootGameObjects();
        foreach (var obj in activeObjs)
        {
            var prefabParent = PrefabUtility.GetPrefabParent(obj);
            if (prefabParent == selectPrefab)
                return obj;
        }

        var prefab = PrefabUtility.InstantiatePrefab(selectPrefab) as GameObject;
        return prefab;
    }
}