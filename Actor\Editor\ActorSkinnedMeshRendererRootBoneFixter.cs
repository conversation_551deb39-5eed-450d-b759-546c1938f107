﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine.Playables;
using UnityEngine;

public class ActorSkinnedMeshRendererRootBoneFixter
{
    [MenuItem("Assets/策划专用/一键修复SkinnedMeshRenderer Root Bone")]
    public static void FixedCamera()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedCGInPaths(checkDirs);
                    }
                    else
                    {
                        FixedActor(Selection.gameObjects[i], path);
                    }

                    EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedCGInPaths(checkDirs);
                }
                else
                {
                    GameObject gameobj = GameObject.Instantiate(Selection.activeGameObject);
                    FixedActor(gameobj, path);

                    PrefabUtility.SaveAsPrefabAssetAndConnect(gameobj, path, InteractionMode.AutomatedAction);
                    GameObject.DestroyImmediate(gameobj);

                    //FixedActor(Selection.activeGameObject, path);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedCGInPaths(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == prefab)
            {
                continue;
            }
            GameObject gameobj = GameObject.Instantiate(prefab);
            FixedActor(gameobj, path);

            PrefabUtility.SaveAsPrefabAssetAndConnect(gameobj, path, InteractionMode.AutomatedAction);
            GameObject.DestroyImmediate(gameobj);

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }
    }

    public static void FixedActor(GameObject gameobj, string filePath)
    {
        SkinnedMeshRenderer[] smrs = gameobj.GetComponentsInChildren<SkinnedMeshRenderer>(true);
        for (int i = 0; i < smrs.Length; i++)
        {
            SkinnedMeshRenderer smr = smrs[i];
            Transform trans = smr.transform.parent.Find("root");
            if (trans != null)
            {
                smrs[i].rootBone = trans;
            }
            else
            {
                Debug.LogError("警告，设置SkinnedMeshRenderer Root Bone失败，无法找到root骨骼根节点，请检查，文件路径：" +  filePath);
            }
        }
    }

}