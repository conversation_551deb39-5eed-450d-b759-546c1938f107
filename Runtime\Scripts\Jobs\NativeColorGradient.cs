using System;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;

namespace UI_Spline_Renderer
{
    /// <summary>
    /// 原生颜色渐变结构
    /// 用于在Job System中高效处理颜色渐变计算
    /// 支持独立的颜色和透明度关键帧
    /// </summary>
    internal struct NativeColorGradient : IDisposable
    {
        /// <summary>透明度关键帧数组，y分量存储时间</summary>
        [ReadOnly]
        public NativeArray<float2> alphaKeyFrames;
        /// <summary>颜色关键帧数组，w分量存储时间</summary>
        [ReadOnly]
        public NativeArray<float4> colorKeyFrames;

        /// <summary>
        /// 在指定时间点评估颜色渐变
        /// 分别计算颜色和透明度，然后合并为最终颜色
        /// </summary>
        /// <param name="t">时间参数(0-1)</param>
        /// <returns>评估后的颜色</returns>
        public Color Evaluate(float t)
        {
            var nextAlphaIdx = -1;
            for (int i = 0; i < alphaKeyFrames.Length; i++)
            {
                if(t > alphaKeyFrames[i].y) continue;
                nextAlphaIdx = i;
                break;
            }

            var nextColorKeyIdx = -1;
            for (int i = 0; i < colorKeyFrames.Length; i++)
            {
                if (t > colorKeyFrames[i].w) continue;
                nextColorKeyIdx = i;
                break;
            }

            // 计算透明度值
            float alpha;
            if (nextAlphaIdx == -1)
            {
                alpha = alphaKeyFrames[^1].x;
            }
            else if (nextAlphaIdx == 0)
            {
                alpha = alphaKeyFrames[0].x;
            }
            else
            {
                var preAlpha = alphaKeyFrames[nextAlphaIdx - 1];
                var nextAlpha = alphaKeyFrames[nextAlphaIdx];
                var remappedT = t.Remap(0, 1, preAlpha.y, nextAlpha.y);
                alpha = math.lerp(preAlpha, nextAlpha, remappedT).x;
            }


            // 计算颜色值
            Color color;
            if(nextColorKeyIdx == -1)
            {
                color = toColor(colorKeyFrames[^1]);
            }
            else if(nextColorKeyIdx == 0)
            {
                color = toColor(colorKeyFrames[0]);
            }
            else
            {
                var preColor = toColor(colorKeyFrames[nextColorKeyIdx - 1]);
                var nextKey = toColor(colorKeyFrames[nextColorKeyIdx]);
                var remappedT = (t - preColor.a) / (nextKey.a - preColor.a);
                color = Color.Lerp(preColor, nextKey, remappedT);
            }

            // 合并颜色和透明度
            color.a = alpha;

            return color;
        }

        /// <summary>
        /// 将float4转换为Color
        /// 用于从原生数组中提取颜色数据
        /// </summary>
        /// <param name="f">float4颜色数据</param>
        /// <returns>Unity Color对象</returns>
        Color toColor(float4 f)
        {
            return new Color(f.x, f.y, f.z, f.w);
        }

        /// <summary>
        /// 释放原生数组资源
        /// 防止内存泄漏
        /// </summary>
        public void Dispose()
        {
            alphaKeyFrames.Dispose();
            colorKeyFrames.Dispose();
        }
    }
}