﻿using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using Nirvana;
using System.Collections.Generic;

class OneKeyBuildModelWindow : EditorWindow
{
    private static string material_template_string = "Assets/Game/Model/ModelMaterialTemplate.mat";

    private static Dictionary<BuildModelType, string> build_name_list = new Dictionary<BuildModelType, string>();
    enum BuildModelType
    {
        None,
        Boss,
        Monster,
        Npc,
        <PERSON><PERSON>,
        <PERSON>,
        Mount,
        Weiba,
        Yaoshi,
        Lianshi,
        Beishi,
        <PERSON>,
        <PERSON>,
        Fabao,
        TianShen,
        TianShenWuQi,
        JueSeWuQi,
        ShiZhuang,
        TouFa,
    }

    /// <summary>
    /// 动画控制器路径
    /// </summary>
    static Dictionary<BuildModelType, string> AnimatorCtrlAsset = new Dictionary<BuildModelType, string>()
    {
        { BuildModelType.Boss, "Assets/Game/Actors/Shared/BossController.controller"},
        { BuildModelType.Monster, "Assets/Game/Actors/Shared/MonsterController.controller"},
        { BuildModelType.Npc, "Assets/Game/Actors/Shared/NpcController.controller"},
        { BuildModelType.Wing, "Assets/Game/Actors/Shared/WingController.controller"},
        { BuildModelType.Weiba, "Assets/Game/Model/Weiba/Shared/Weiba_Controller.controller"},
        { BuildModelType.Yaoshi, "Assets/Game/Model/Yaoshi/Shared/Yaoshi_Controller.controller"},
        { BuildModelType.Beishi, "Assets/Game/Model/Beishi/Shared/Beishi_Controller.controller"},
        { BuildModelType.Child, "Assets/Game/Actors/Shared/MonsterController.controller"},
        { BuildModelType.Chongwu, "Assets/Game/Actors/Shared/MonsterController.controller"},
        { BuildModelType.Fabao, "Assets/Game/Actors/Shared/BaoJuController.controller"},
        { BuildModelType.Mount, "Assets/Game/Actors/Shared/MountController.controller"},
        { BuildModelType.TianShen, "Assets/Game/Actors/Shared/RoleController.controller" },
        { BuildModelType.TianShenWuQi, "Assets/Game/Actors/Shared/RoleController.controller" }, //不需要控制器随便填一个
        { BuildModelType.JueSeWuQi, "Assets/Game/Actors/Shared/RoleController.controller" },    //不需要控制器随便填一个
        { BuildModelType.ShiZhuang, "Assets/Game/Actors/Shared/RoleController.controller" },    //不需要控制器随便填一个
        { BuildModelType.TouFa, "Assets/Game/Actors/Shared/RoleController.controller" },        //不需要控制器随便填一个
    };

    static Dictionary<BuildModelType, Dictionary<int, List<string>>> AnimatorCtrlClip = new Dictionary<BuildModelType, Dictionary<int, List<string>>>()
    {
        { BuildModelType.Boss, new Dictionary<int, List<string>>() { { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                     { 4, new List<string>() { "@Rest", "@rest" } },            //d_rest
                                                                     { 6, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_ui_idle
                                                                     { 7, new List<string>() { "@Run", "@run" } },              //d_run
                                                                     { 12, new List<string>() { "@Attack01", "@attack01" } },        //d_attack1
                                                                     { 15, new List<string>() { "@Skill01Pre", "@skill01pre" } },      //d_magic1_1
                                                                     { 16, new List<string>() { "@Skill01Mid", "@skill01mid" } },      //d_magic1_2
                                                                     { 17, new List<string>() { "@Skill01Back", "@skill01back" } },     //d_magic1_3
                                                                     { 18, new List<string>() { "@Death", "@Die", "@death", "@die" } },   //d_die
                                                                     { 19, new List<string>() { "@Dead", "@dead" } },}           //d_dead
        },
        { BuildModelType.Monster, new Dictionary<int, List<string>>() { { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                     { 6, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_ui_idle
                                                                     { 7, new List<string>() { "@Run", "@run" } },              //d_run
                                                                     { 12, new List<string>() { "@Attack01", "@attack01" } },        //d_attack1
                                                                     { 15, new List<string>() { "@Attack01_back", "@attack01_back", "@Attack01_Back", "@attack01_Back" } },        //d_attack1_back
                                                                     { 18, new List<string>() { "@Die", "@die" } },   //d_die
                                                                     { 19, new List<string>() { "@Dead", "@Death", "@dead", "@death" } },}           //d_dead
        },
        { BuildModelType.Npc, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                    { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },             //d_rest(npc特殊，需要将reset也用idle)
                                                                     { 5, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}          //d_ui_idle
        },
        { BuildModelType.Wing, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                     { 1, new List<string>() { "@Rest", "@rest" } },                                //d_rest
                                                                     { 2, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },           //d_run
                                                                     { 3, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_scene_idle
        },
        { BuildModelType.Weiba, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.Yaoshi, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.Beishi, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.Child, new Dictionary<int, List<string>>() { { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                     { 4, new List<string>() { "@Rest", "@rest" } },            //d_rest
                                                                     { 6, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } }, }            //d_ui_idle
        },
        { BuildModelType.Chongwu, new Dictionary<int, List<string>>() { { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                     { 4, new List<string>() { "@Rest", "@rest" } },            //d_rest
                                                                     { 6, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_ui_idle
                                                                     { 7, new List<string>() { "@Run", "@run" } },              //d_run
                                                                     { 12, new List<string>() { "@Attack01", "@attack01" } },        //d_attack1
                                                                     { 13, new List<string>() { "@Attack01", "@attack01" } },        //d_attack2
                                                                     { 14, new List<string>() { "@Attack01", "@attack01" } },        //d_attack3
                                                                     { 18, new List<string>() { "@Die", "@die" } },   //d_die
                                                                     { 19, new List<string>() { "@Dead", "@Death", "@dead", "@death" } },    //d_dead
                                                                     { 20, new List<string>() { "@Die", "@die" } },}  // d_die_fly
        },
        { BuildModelType.Fabao, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },  //d_idle
                                                                     { 1, new List<string>() { "@Rest", "@rest" } },  //d_rest
                                                                     { 2, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },  //d_idle_fight
                                                                     { 4, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },  //d_scene_idle
                                                                     { 5, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } } }  //d_scene_fight_idle
        },
        { BuildModelType.Mount, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Run", "@run" } }, //d_run
                                                                     { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } }, //d_idle
                                                                     { 2, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } }, //d_rest
                                                                     { 3, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },} //d_mount_scene_idle
        },
        { BuildModelType.TianShen, new Dictionary<int, List<string>>() {{ 0, new List<string>() { "@Run", "@run" } },              //d_run
                                                                        { 1, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_idle
                                                                        { 2, new List<string>() { "@Jump", "@jump" } },
                                                                        { 9, new List<string>() { "@Skill03", "@skill03", "@Combo03", "@combo03" } },
                                                                        { 10, new List<string>() { "@Skill03_back", "@skill03_back", "@Combo03_back", "@combo03_back" } },
                                                                        { 11, new List<string>() { "@Skill01", "@skill01", "@Combo01", "@combo01" } },
                                                                        { 12, new List<string>() { "@Skill01_back", "@skill01_back", "@Combo01_back", "@combo01_back" } },
                                                                        { 13, new List<string>() { "@Skill02", "@skill02" , "@Combo02", "@combo02" } },
                                                                        { 14, new List<string>() { "@Skill02_back", "@skill02_back", "@Combo02_back", "@combo02_back" } },
                                                                        { 15, new List<string>() { "@Attack01", "@attack01" } },        //d_attack1
                                                                        { 16, new List<string>() { "@Attack02", "@attack02" } },        //d_attack2
                                                                        { 17, new List<string>() { "@Attack03", "@attack03" } },        //d_attack3
                                                                        { 19, new List<string>() { "@Attack01_back", "@attack01_back" } },        //d_attack1_back
                                                                        { 20, new List<string>() { "@Attack02_back", "@attack02_back" } },        //d_attack2_back
                                                                        { 21, new List<string>() { "@Attack03_back", "@attack03_back" } },        //d_attack3_back
                                                                        { 25, new List<string>() { "@Dead", "@Death", "@dead", "@death" } },    //d_dead
                                                                        { 32, new List<string>() { "@Die", "@die" } },   //d_die
                                                                        { 36, new List<string>() { "@Rest", "@rest" } },            //d_rest
                                                                        { 50, new List<string>() { "@Chuchang", "@chuchang" } },
                                                                        { 51, new List<string>() { "@Run", "@run" } },              //d_run_fight
                                                                        { 52, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },    //d_idle_fight
                                                                        { 64, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },            //d_ui_idle
                                                                        }  // d_die_fly
        },
        { BuildModelType.TianShenWuQi, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.JueSeWuQi, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.ShiZhuang, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
        { BuildModelType.TouFa, new Dictionary<int, List<string>>() { { 0, new List<string>() { "@Stand", "@Idle", "@stand", "@idle" } },}           //d_idle
        },
    };

    static Dictionary<BuildModelType, Vector3> FixedModelScale = new Dictionary<BuildModelType, Vector3>() {
        { BuildModelType.Boss, new Vector3(50, 50, 50)},
        { BuildModelType.Npc, new Vector3(120, 120, 120)},
        { BuildModelType.Wing, new Vector3(50, 50, 50)},
    };


    [MenuItem("自定义工具/策划工具/生成模型工具")]
    static void BuildModelPrefasbTools()
    {
        //EditorWindow window = GetWindow(typeof(OneKeyBuildModelWindow), true, "生成模型工具");
        EditorWindow window = CreateWindow<OneKeyBuildModelWindow>("生成模型工具");

        window.Show();
        window.Focus();
    }


    private BuildModelType cur_build_type;
    private bool isBuildController = true;
    private bool isBuildAttachment = true;
    private bool isBuildAttachObject = false;
    private bool isBuildClickableObject = false;
    private static bool isBuildClick_n = false;
    private string end_with = "";

    private void Awake()
    {
        build_name_list.Add(BuildModelType.None, "当前类型：无");
        build_name_list.Add(BuildModelType.Boss, "当前类型：Boss");
        build_name_list.Add(BuildModelType.Monster, "当前类型：怪物");
        build_name_list.Add(BuildModelType.Npc, "当前类型：NPC");
        build_name_list.Add(BuildModelType.Gather, "当前类型：采集物");
        build_name_list.Add(BuildModelType.Wing, "当前类型：翅膀");
        build_name_list.Add(BuildModelType.Mount, "当前类型：坐骑");
        build_name_list.Add(BuildModelType.Weiba, "当前类型：尾巴");
        build_name_list.Add(BuildModelType.Yaoshi, "当前类型：腰饰");
        build_name_list.Add(BuildModelType.Lianshi, "当前类型：脸饰");
        build_name_list.Add(BuildModelType.Beishi, "当前类型：背饰");
        build_name_list.Add(BuildModelType.Child, "当前类型：崽崽");
        build_name_list.Add(BuildModelType.Chongwu, "当前类型：宠物");
        build_name_list.Add(BuildModelType.Fabao, "当前类型：法宝");
        build_name_list.Add(BuildModelType.TianShen, "当前类型：天神");
        build_name_list.Add(BuildModelType.TianShenWuQi, "当前类型：天神武器");
        build_name_list.Add(BuildModelType.JueSeWuQi, "当前类型：角色武器");
        build_name_list.Add(BuildModelType.ShiZhuang, "当前类型：角色时装");
        build_name_list.Add(BuildModelType.TouFa, "当前类型：头饰");
    }


    private void OnGUI()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("选择转换类型", GUILayout.Width(100f));
        cur_build_type = (BuildModelType)EditorGUILayout.EnumPopup(cur_build_type, GUILayout.Width(150f));
        if (build_name_list.ContainsKey(cur_build_type))
        {
            GUILayout.Label(build_name_list[cur_build_type], GUILayout.Width(300f));
        }
        GUILayout.EndHorizontal();
        isBuildController = EditorGUILayout.Toggle("是否生成动画控制器", isBuildController, GUILayout.Width(500f));
        isBuildAttachment = EditorGUILayout.Toggle("是否生成Attachment组件", isBuildAttachment, GUILayout.Width(500f));
        isBuildAttachObject = EditorGUILayout.Toggle("是否生成AttachObject组件", isBuildAttachObject, GUILayout.Width(500f));
        isBuildClickableObject = EditorGUILayout.Toggle("是否生成ClickableObject组件", isBuildClickableObject, GUILayout.Width(500f));
        isBuildClick_n = EditorGUILayout.Toggle("材质中法线贴图是否匹配N", isBuildClick_n, GUILayout.Width(500f));
        end_with = EditorGUILayout.TextField("请输入名称增加后缀", end_with);

        if (GUILayout.Button("点击生成", GUILayout.Width(200)))
        {
            this.BuildModelPreafabs();
        }
    }

    private void BuildModelPreafabs()
    {
        if (cur_build_type == BuildModelType.None)
        {
            UnityEditor.EditorUtility.DisplayDialog("操作提示！！！", "请选中类型进行操作！！！", "确认");
            return;
        }


        string[] assets_guids = Selection.assetGUIDs;

        for (int i = 0; i < assets_guids.Length; i++)
        {
            string path = AssetDatabase.GUIDToAssetPath(assets_guids[i]);
            if (AssetDatabase.IsValidFolder(path))
            {
                LoopValidFolder(path);
            }
            else
            {
                Debug.LogErrorFormat("请选中文件夹进行操作！！！");
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    private void LoopValidFolder(string path)
    {
        string[] guids = Directory.GetDirectories(path);
        if (guids.Length > 0)
        {
            for (int i = 0; i < guids.Length; i++)
            {
                string guid = guids[i];
                LoopValidFolder(guid);
            }
        }
        else
        {
            CreateModel(path);
        }
    }

    private void CreateModel(string path)
    {
        path = path.Replace("\\", "/");
        string[] guids = AssetDatabase.FindAssets("*", new string[] { path });

        string skinFbxPath = "";
        string texPath = "";
        string animCtrlPath = "";
        bool isFindSkin = false;
        bool isFindTex = false;
        bool isFindAnimCtrl = false;
        string model_folder_name = path.Substring(path.LastIndexOf("/") + 1);

        // 处理所有的文件夹
        for (int i = 0; i < guids.Length; i++)
        {
            string guid = guids[i];
            string new_path = AssetDatabase.GUIDToAssetPath(guid);
            //查找文件
            if (new_path.EndsWith("@Skin.FBX") || new_path.EndsWith("@skin.FBX"))
            {
                skinFbxPath = new_path;
                isFindSkin = true;
            }

            if (new_path.EndsWith(".tga") && isFindTex == false)
            {
                texPath = new_path;
                isFindTex = true;
            }

            if (new_path.EndsWith("_Controller.overrideController"))
            {
                animCtrlPath = new_path;
                isFindAnimCtrl = true;
            }

            if (isFindSkin && isFindTex && isFindAnimCtrl)
            {
                break;
            }
        }

        if (!isFindSkin)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有找到Skin FBX，该模型生成失败", path);
            return;
        }

        if (!isFindTex)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有找到模型贴图，该模型生成失败", path);
            return;
        }

        string prefabPath = path + "/" + model_folder_name + end_with + ".prefab";
        string matPath = path + texPath.Substring((texPath.LastIndexOf('/'))).Replace(".tga", ".mat");
        animCtrlPath = isFindAnimCtrl ? animCtrlPath : (path + "/" + model_folder_name + "_Controller.overrideController");

        GameObject modelFBX = AssetDatabase.LoadAssetAtPath<GameObject>(skinFbxPath);
        if (modelFBX != null)
        {
            GameObject model = GameObject.Instantiate(modelFBX);
            CreateMaterial(matPath, path, prefabPath, model);
            if (FixedModelScale.ContainsKey(cur_build_type))
            {
                FixModelScale(cur_build_type, model, model_folder_name);
            }
            if (isBuildController)
            {
                model.GetOrAddComponent<AnimatorEventDispatcher>();
                CreateAnimatorCtrl(cur_build_type, isFindAnimCtrl, animCtrlPath, skinFbxPath, prefabPath, model);
            }
            if (isBuildAttachObject)
            {
                model.GetOrAddComponent<AttachObject>();
            }
            if (isBuildClickableObject)
            {
                CreateClickable(model, Vector3.zero);
            }

            if (isBuildAttachment)
            {
                CreateAttachment(model, prefabPath);
            }

            ActorRender actorRenderer = model.GetOrAddComponent<ActorRender>();
            actorRenderer.AutoFetch();

            if (File.Exists(prefabPath))
            {
                File.Delete(prefabPath);
            }

            PrefabUtility.SaveAsPrefabAsset(model, prefabPath);
            GameObject.DestroyImmediate(model);
        }
    }

    private static void CreateMaterial(string varMatPath, string path, string prefabPath, GameObject model)
    {
        string matPath = varMatPath.Replace(".mat", "");
        matPath = string.Format("{0}_PbrCf.mat", matPath);

        bool is_new = false;
        if (!File.Exists(matPath))
        {
            AssetDatabase.CopyAsset(material_template_string, matPath);
            is_new = true;
        }
        Material newMat = AssetDatabase.LoadAssetAtPath(matPath, typeof(Material)) as Material;
        if (is_new)
        {
            newMat.shader = Shader.Find("Srp/Standard/SrpRole_PbrCf");
            newMat.SetFloat("_MaterialStyle", 1);
            newMat.EnableKeyword("ENABLE_DIFFUSE");
            string[] texGUIDs = AssetDatabase.FindAssets("t:texture", new string[] { path });
            if (texGUIDs != null)
            {
                var mainTexPath = AssetDatabase.GUIDToAssetPath(texGUIDs[0]);
                newMat.mainTexture = AssetDatabase.LoadAssetAtPath(mainTexPath, typeof(Texture)) as Texture;

                for (int i = 0; i < texGUIDs.Length; i++)
                {
                    var TexPath = AssetDatabase.GUIDToAssetPath(texGUIDs[i]);
                    if (TexPath.Contains("nor") || TexPath.Contains("Nor"))
                    {
                        newMat.SetTexture("_NormalMap", AssetDatabase.LoadAssetAtPath(TexPath, typeof(Texture)) as Texture);
                    }
                    else if (isBuildClick_n && (TexPath.Contains("n") || TexPath.Contains("N")))
                    {
                        newMat.SetTexture("_NormalMap", AssetDatabase.LoadAssetAtPath(TexPath, typeof(Texture)) as Texture);
                    }

                    if (TexPath.Contains("pbr") || TexPath.Contains("Pbr"))
                    {
                        newMat.SetTexture("_MetallicGlossMap", AssetDatabase.LoadAssetAtPath(TexPath, typeof(Texture)) as Texture);
                    }
                }
            }
            EditorUtility.SetDirty(newMat);
        }

        Renderer renderer = model.GetComponentInChildren<Renderer>();
        if (renderer)
        {
            renderer.sharedMaterial = newMat;
        }
        else
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 没有挂载Mesh Renderer或者Skinned Mesh Renderer组件，请检查！！！", prefabPath);
        }
    }

    private static void FixModelScale(BuildModelType modelType, GameObject model, string fbxName)
    {
        if (!FixedModelScale.ContainsKey(modelType))
        {
            return;
        }

        Transform[] modelFirstChilds = model.GetComponentsInChildren<Transform>();
        Transform modelFirstChild = modelFirstChilds[1];//GetFirstChildTransform(model);
        GameObject scaleFixed = new GameObject();
        scaleFixed.name = fbxName + "001_ScaleFix";
        scaleFixed.transform.parent = model.transform;
        scaleFixed.transform.localScale = FixedModelScale[modelType];
        modelFirstChild.transform.parent = scaleFixed.transform;
        modelFirstChild.transform.localScale = Vector3.one;
    }
    private static void CreateClickable(GameObject model, Vector3 colPos, float height = 1, float radius = 1)
    {
        ClickableObject clickObj = model.GetOrAddComponent<ClickableObject>();
        GameObject clickableObj = new GameObject();
        clickableObj.transform.parent = model.transform;
        clickableObj.transform.localPosition = Vector3.zero;
        clickableObj.transform.localRotation = Quaternion.identity;
        clickableObj.transform.localScale = Vector3.one;
        clickableObj.gameObject.name = "Clickable";
        clickableObj.gameObject.layer = GameLayers.Clickable;
        CapsuleCollider collider = clickableObj.GetOrAddComponent<CapsuleCollider>();
        collider.transform.localPosition = colPos;
        collider.height = height;
        collider.radius = radius;
        Clickable clickable = clickableObj.GetOrAddComponent<Clickable>();
        clickable.Owner = clickObj;
    }
    private static void CreateAttachment(GameObject model, string prefabPath)
    {
        ActorAttachment attachment = model.GetOrAddComponent<ActorAttachment>();
        attachment.AutoPick();
        if (attachment.GetAttachPoint(0) == null)
        {
            Debug.LogErrorFormat("报错！！！路径：{0} 该模型找不到UI挂点，请检查！！！", prefabPath);
        }
    }

    private static void CreateAnimatorCtrl(BuildModelType modelType, bool isFindAnimCtrl, string animCtrlPath, string fbxPath, string prefabPath, GameObject model)
    {

        if (!File.Exists(animCtrlPath))
        {
            var baseController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(AnimatorCtrlAsset[modelType]);
            var controller = new AnimatorOverrideController(baseController);
            AssetDatabase.CreateAsset(controller, animCtrlPath);
        }

        AnimatorOverrideController animatorController = AssetDatabase.LoadAssetAtPath(animCtrlPath, typeof(AnimatorOverrideController)) as AnimatorOverrideController;
        Dictionary<int, List<string>> animCtrlClips = AnimatorCtrlClip[modelType];

        //for (int i = 0; i < animatorController.animationClips.Length; i++)
        //{
        //    Debug.LogErrorFormat("animatorController.animationClips{0}，{1}", i, animatorController.animationClips[i].name);
        //}

        foreach (KeyValuePair<int, List<string>> clip in animCtrlClips)
        {
            List<string> clipStrs = clip.Value;
            bool isFindClipFbx = false;
            foreach (string clipStr in clipStrs)
            {
                string animFbxAsset = fbxPath.Replace("@Skin", clipStr).Replace("@skin", clipStr);
                //Debug.LogError(animFbxAsset);
                AnimationClip animClip = AssetDatabase.LoadAssetAtPath<AnimationClip>(animFbxAsset) as AnimationClip;
                if (animClip)
                {
                    string clipName = animatorController.animationClips[clip.Key].name;
                    animatorController[clipName] = animClip;
                    isFindClipFbx = true;
                    break;
                }
            }

            if (!isFindClipFbx)
            {
                string clips = "";
                foreach (string clipStr in clipStrs)
                {
                    clips += clipStr + " ";
                }
                Debug.LogErrorFormat("绑定动画报错！！！找不到 {0} 的 {1}FBX文件", prefabPath, clips);
            }
        }

        Animator animator = model.GetOrAddComponent<Animator>();
        animator.runtimeAnimatorController = animatorController;
        animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
    }





}