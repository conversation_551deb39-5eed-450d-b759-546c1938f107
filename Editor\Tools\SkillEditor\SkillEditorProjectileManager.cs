using System.Collections;
using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 弹道管理器
/// 负责弹道的配置和播放
/// </summary>
public class SkillEditorProjectileManager
{
    #region 私有字段
    private SkillEditorPrefabDataConfig _dataConfig;
    private SkillEditorModelManager _modelManager;
    private int _selectProjectileId = 0;
    private Vector2 _projectileScroll;
    #endregion

    #region 属性
    public int SelectedProjectileId => _selectProjectileId;
    #endregion

    #region 初始化
    /// <summary>
    /// 初始化弹道管理器
    /// </summary>
    public void Initialize(SkillEditorPrefabDataConfig dataConfig, SkillEditorModelManager modelManager)
    {
        _dataConfig = dataConfig;
        _modelManager = modelManager;
    }
    #endregion

    #region GUI绘制
    /// <summary>
    /// 绘制弹道管理界面
    /// </summary>
    public void DrawProjectilesWindow()
    {
        GUILayout.BeginVertical();
        DrawAddProjectileButton();
        DrawProjectilesList();
        GUILayout.EndVertical();

        DrawDeleteProjectileButton();
    }

    /// <summary>
    /// 绘制添加弹道按钮
    /// </summary>
    private void DrawAddProjectileButton()
    {
        if (GUILayout.Button("增加弹道", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            AddNewProjectile();
        }
    }

    /// <summary>
    /// 绘制弹道列表
    /// </summary>
    private void DrawProjectilesList()
    {
        if (_dataConfig?.actorController?.projectiles == null) return;

        _projectileScroll = SkillEditorGUI.DrawScrollList(
            _projectileScroll,
            _dataConfig.actorController.projectiles,
            projectile => SkillEditorGUIConfig.GetDefaultButtonName(projectile.ProjectileBtnName),
            OnProjectileSelected);
    }

    /// <summary>
    /// 绘制删除弹道按钮
    /// </summary>
    private void DrawDeleteProjectileButton()
    {
        SkillEditorGUI.BeginWindowArea(SkillEditorGUIConfig.DeleteBtnRect2);

        if (_dataConfig?.actorController?.projectiles?.Count > 0)
        {
            if (GUILayout.Button("删除弹道", GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
                GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
            {
                DeleteSelectedProjectile();
            }
        }

        SkillEditorGUI.EndWindowArea();
    }

    /// <summary>
    /// 绘制弹道详细配置
    /// </summary>
    public void DrawProjectileDetails()
    {
        if (!IsValidProjectileSelection()) return;

        var data = _dataConfig.actorController.projectiles[_selectProjectileId];

        DrawProjectileBasicSettings(data);
        DrawProjectileAssetSettings(data);
        DrawProjectilePositionSettings(data);

        _dataConfig.actorController.projectiles[_selectProjectileId] = data;
    }
    #endregion

    #region 弹道配置绘制
    /// <summary>
    /// 绘制弹道基础设置
    /// </summary>
    private void DrawProjectileBasicSettings(SkillEditorPrefabDataConfig.ProjectileData data)
    {
        data.Action = SkillEditorGUI.DrawProjectileEventSelector(data.Action);
        data.ProjectileBtnName = SkillEditorGUI.DrawButtonNameField(data.ProjectileBtnName);
        
        data.HurtPosition = SkillEditorGUI.DrawEnumPopup(
            "Hurt Position", 
            data.HurtPosition, 
            SkillEditorGUIConfig.HurtPositions);
        
        data.DelayProjectileEff = SkillEditorGUI.DrawDelayField("延迟播放特效", data.DelayProjectileEff);
        data.DeleProjectileDelay = SkillEditorGUI.DrawDelayField("延迟删除特效", data.DeleProjectileDelay);
    }

    /// <summary>
    /// 绘制弹道资产设置
    /// </summary>
    private void DrawProjectileAssetSettings(SkillEditorPrefabDataConfig.ProjectileData data)
    {
        // 绘制特效释放点选择器
        Transform node = SkillEditorGUI.DrawNodeSelector(
            "特效释放点", 
            data.ProjectilNodeHierarchyPath, 
            _modelManager.MainAnimator?.transform);
        
        data.ProjectilNodeHierarchyPath = SkillEditorGUI.UpdateNodePath(
            node, 
            _modelManager.MainAnimator?.transform);

        // 绘制弹道特效选择器
        EditorGUI.BeginChangeCheck();
        Projectile projectile = SkillEditorGUI.DrawProjectileSelector(data.ProjectilGoName, data.Projectile);

        if (EditorGUI.EndChangeCheck())
        {
            UpdateProjectileAsset(data, projectile);
        }
    }

    /// <summary>
    /// 绘制弹道位置设置
    /// </summary>
    private void DrawProjectilePositionSettings(SkillEditorPrefabDataConfig.ProjectileData data)
    {
        Transform fromTransform = SkillEditorGUI.DrawNodeSelector(
            "From Position", 
            data.FromPosHierarchyPath, 
            _modelManager.MainAnimator?.transform);
        
        data.FromPosHierarchyPath = SkillEditorGUI.UpdateNodePath(
            fromTransform, 
            _modelManager.MainAnimator?.transform);
    }

    /// <summary>
    /// 更新弹道资产
    /// </summary>
    private void UpdateProjectileAsset(SkillEditorPrefabDataConfig.ProjectileData data, Projectile projectile)
    {
        if (projectile == null)
        {
            data.ProjectilGoName = string.Empty;
            data.Projectile = AssetID.Empty;
        }
        else
        {
            var path = AssetDatabase.GetAssetPath(projectile);
            var guid = AssetDatabase.AssetPathToGUID(path);
            var importer = AssetImporter.GetAtPath(path);

            data.Projectile = new AssetID(importer.assetBundleName, projectile.name)
            {
                AssetGUID = guid
            };
            data.ProjectilGoName = projectile.name;
        }
    }
    #endregion

    #region 弹道操作
    /// <summary>
    /// 添加新弹道
    /// </summary>
    private void AddNewProjectile()
    {
        if (_dataConfig?.actorController?.projectiles == null) return;

        var data = new SkillEditorPrefabDataConfig.ProjectileData();
        _dataConfig.actorController.projectiles.Add(data);
    }

    /// <summary>
    /// 删除选中的弹道
    /// </summary>
    private void DeleteSelectedProjectile()
    {
        if (_selectProjectileId == -1 || _dataConfig.actorController.projectiles.Count == 0)
            return;

        _dataConfig.actorController.projectiles.RemoveAt(_selectProjectileId);
        _selectProjectileId = _dataConfig.actorController.projectiles.Count == 0
            ? -1
            : _dataConfig.actorController.projectiles.Count - 1;
    }

    /// <summary>
    /// 弹道选择回调
    /// </summary>
    private void OnProjectileSelected(int index)
    {
        _selectProjectileId = index;
    }

    /// <summary>
    /// 验证弹道选择是否有效
    /// </summary>
    private bool IsValidProjectileSelection()
    {
        return _dataConfig?.actorController != null &&
               _selectProjectileId != -1 &&
               _selectProjectileId < _dataConfig.actorController.projectiles.Count;
    }
    #endregion

    #region 弹道播放
    /// <summary>
    /// 播放弹道
    /// </summary>
    public void PlayProjectile(string action, System.Action hited)
    {
        if (_dataConfig?.actorController?.projectiles == null) return;

        foreach (var projectile in _dataConfig.actorController.projectiles)
        {
            if (!ShouldPlayProjectile(projectile, action)) continue;

            var projectileData = GetProjectileTransformData(projectile);
            PlayProjectileWithDelay(projectile, projectileData, hited);
        }
    }

    /// <summary>
    /// 判断是否应该播放弹道
    /// </summary>
    private bool ShouldPlayProjectile(SkillEditorPrefabDataConfig.ProjectileData projectile, string action)
    {
        return projectile.Action == action && !projectile.Projectile.IsEmpty;
    }

    /// <summary>
    /// 获取弹道变换数据
    /// </summary>
    private ProjectileTransformData GetProjectileTransformData(SkillEditorPrefabDataConfig.ProjectileData projectile)
    {
        var hurtPoint = SkillEditorUtils.FindTransformFromPath(
            _modelManager.MainAnimator?.transform, 
            projectile.ProjectilNodeHierarchyPath);
        
        var fromTransform = SkillEditorUtils.FindTransformFromPath(
            _modelManager.MainAnimator?.transform, 
            projectile.FromPosHierarchyPath);
        
        var fromPosition = fromTransform?.position ?? _modelManager.MainAnimator?.transform.position ?? Vector3.zero;

        return new ProjectileTransformData
        {
            HurtPoint = hurtPoint,
            FromPosition = fromPosition
        };
    }

    /// <summary>
    /// 带延迟播放弹道
    /// </summary>
    private void PlayProjectileWithDelay(SkillEditorPrefabDataConfig.ProjectileData projectile,
        ProjectileTransformData transformData, System.Action hited)
    {
        if (projectile.DelayProjectileEff > 0f)
        {
            Scheduler.RunCoroutine(SkillEditorUtils.DelayedAction(
                projectile.DelayProjectileEff, 
                () => Scheduler.RunCoroutine(PlayProjectileWithEffect(projectile, transformData, hited))));
        }
        else
        {
            Scheduler.RunCoroutine(PlayProjectileWithEffect(projectile, transformData, hited));
        }
    }

    /// <summary>
    /// 播放弹道特效
    /// </summary>
    private IEnumerator PlayProjectileWithEffect(SkillEditorPrefabDataConfig.ProjectileData projectile,
        ProjectileTransformData transformData, System.Action hited)
    {
        yield return SpawnProjectileInstance(projectile, (projectileInstance) =>
        {
            if (projectileInstance == null)
            {
                hited?.Invoke();
                return;
            }

            SetupProjectileInstance(projectileInstance, transformData, projectile, hited);
        });
    }

    /// <summary>
    /// 生成弹道实例
    /// </summary>
    private IEnumerator SpawnProjectileInstance(SkillEditorPrefabDataConfig.ProjectileData projectile, System.Action<Projectile> callback)
    {
        var wait = GameObjectPool.Instance.SpawnAsset(projectile.Projectile);
        yield return wait;

        if (!string.IsNullOrEmpty(wait.Error))
        {
            Debug.LogError($"弹道生成错误: {wait.Error}");
            callback?.Invoke(null);
            yield break;
        }

        var projectileComponent = wait.Instance?.GetComponent<Projectile>();
        callback?.Invoke(projectileComponent);
    }

    /// <summary>
    /// 设置弹道实例
    /// </summary>
    private void SetupProjectileInstance(Projectile projectileComponent,
        ProjectileTransformData transformData, SkillEditorPrefabDataConfig.ProjectileData projectile, System.Action hited)
    {
        if (projectileComponent == null || _modelManager?.MainAnimator == null) return;

        projectileComponent.transform.position = transformData.FromPosition;
        projectileComponent.transform.localScale = _modelManager.MainAnimator.transform.lossyScale;
        projectileComponent.gameObject.SetLayerRecursively(_modelManager.MainAnimator.gameObject.layer);

        projectileComponent.Play(
            _modelManager.MainAnimator.transform.lossyScale,
            transformData.HurtPoint,
            _modelManager.MainAnimator.gameObject.layer,
            () => hited?.Invoke(),
            () => Scheduler.RunCoroutine(SkillEditorUtils.FreeGameObjectCoroutine(
                projectileComponent.gameObject, 
                projectile.DeleProjectileDelay)));
    }

    /// <summary>
    /// 弹道变换数据结构
    /// </summary>
    public struct ProjectileTransformData
    {
        public Transform HurtPoint;
        public Vector3 FromPosition;
    }
    #endregion
}
