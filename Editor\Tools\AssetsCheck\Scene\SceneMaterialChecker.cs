﻿using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

namespace AssetsCheck
{
    class SceneMaterialChecker : BaseChecker
    {
        private string[] checkDirs = { "Assets/Game/Environments" };

        override public string GetErrorDesc()
        {
            return string.Format("场景材质球设置费性能");
        }

        override protected void OnCheck()
        {

        }

        override protected void OnFix(string[] lines)
        {
            string[] guids = AssetDatabase.FindAssets("t:material", checkDirs);

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                Material material = AssetDatabase.LoadAssetAtPath<Material>(path);
                if (!material.shader.name.StartsWith("YifStandard"))
                {
                    continue;
                }

                if (material.shader.name != "YifStandard/YifStandardEnvironments")
                {
                    material.shader = Shader.Find("YifStandard/YifStandardEnvironments");
                }

                material.SetFloat("_MaterialStyle", 2);
                if (material.GetFloat("_RenderingMode") == 0)
                {
                    material.SetFloat("_ZWrite", 1);
                    material.SetFloat("_CullMode", 2);
                }
                else
                {
                    material.SetFloat("_RenderingMode", 1);
                    material.SetFloat("_ZWrite", 1);
                    material.SetFloat("_CullMode", (float)CullMode.Off);
                    material.SetFloat("_SrcBlend", (float)BlendMode.One);
                    material.SetFloat("_DstBlend", (float)BlendMode.Zero);
                    material.EnableKeyword("ENABLE_ALPHA_TEST");
                    material.DisableKeyword("ENABLE_ALPHA_BLEND");
                }

                if (path.IndexOf("/Grounds/") >= 0 && material.GetFloat("_RenderingMode") == 0)
                {
                    material.renderQueue = 1900;
                }
            }
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public int invalidComponentCount;
            public int illegalLightCount;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(this.asset);

                if (invalidComponentCount > 0)
                    builder.Append(string.Format("   invalidComponentCount={0}", invalidComponentCount));

                if (illegalLightCount > 0)
                    builder.Append(string.Format("   illegalLightCount={0}", illegalLightCount));

                return builder;
            }
        }
    }
}
