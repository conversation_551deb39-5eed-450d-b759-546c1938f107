<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="True">
    <uie:ObjectField label="Spline Container" binding-path="splineContainer" name="splineContainer" type="UnityEngine.Splines.SplineContainer, Unity.Splines" />
    <uie:ObjectField label="Material" binding-path="m_Material" name="material" type="UnityEngine.Material, UnityEngine.CoreModule" />
    <ui:Toggle label="Recursive Material" binding-path="_recursiveMaterial" name="recursivceMaterial" tooltip="如果为true，此渲染器的材质值也会应用到起始图像和结束图像。" style="padding-left: 20px;" />
    <uie:ColorField label="Color" value="#FFFFFFFF" name="color" binding-path="m_Color" />
    <uie:GradientField label="Color Gradient" name="colorGradient" binding-path="_colorGradient" />
    <ui:Toggle label="Recursive Color" binding-path="_recursiveColor" name="recursivceColor" tooltip="如果为true，此渲染器的颜色也会应用到起始图像和结束图像。" style="padding-left: 20px;" />
    <ui:Toggle label="Raycast Target" binding-path="m_RaycastTarget" name="raycastTarget" />
    <ui:Foldout text="Raycast Padding" name="raycastPaddingFoldout" value="false">
        <ui:Vector4Field binding-path="m_RaycastPadding" name="raycastPadding" />
    </ui:Foldout>
    <ui:Toggle label="Maskable" binding-path="m_Maskable" name="maskable" />
    <ui:VisualElement style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); height: 20px; max-height: 20px; min-height: 20px;" />
    <ui:FloatField label="Width" value="6" binding-path="_width" name="width" tooltip="此样条线渲染器的宽度" is-delayed="false" />
    <uie:CurveField label="Width Curve" binding-path="_widthCurve" tooltip="此样条线宽度的乘数。[0..1]" name="widthCurve" />
    <ui:VisualElement name="lineTextureArea" style="background-color: rgba(0, 0, 0, 0);">
        <ui:Toggle label="Keep Zero Z" binding-path="_keepZeroZ" name="keepZeroZ" tooltip="保持所有z位置和transform.localPosition.z为0。这使样条线在画布上保持平坦。" />
        <ui:Toggle label="Keep Billboard" binding-path="_keepBillboard" name="keepBillboard" tooltip="保持所有顶点的法线指向屏幕方向。这确保样条线以广告牌方式渲染，即使您为节点设置了奇怪的旋转。" />
        <ui:VisualElement style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); height: 20px; max-height: 20px; min-height: 20px;" />
        <ui:VisualElement name="presetButtons" style="flex-grow: 1; flex-direction: row; flex-shrink: 0;">
            <ui:Label text="Line Texture Preset" style="-unity-text-align: middle-left; align-self: stretch;" />
            <ui:Button text="Default (Anti-Alised)" display-tooltip-when-elided="true" name="default" tooltip="默认抗锯齿纹理。这是使用轻微模糊图像的简单技巧。" style="height: 30px; max-height: 30px; min-height: 30px; flex-grow: 1;" />
            <ui:Button text="UV Test" display-tooltip-when-elided="true" name="uvTest" tooltip="带数字的棋盘纹理。如果您想查看UV是如何应用的，这很有用。" style="height: 30px; max-height: 30px; min-height: 30px; flex-grow: 1;" />
            <ui:Button text="Custom" display-tooltip-when-elided="true" name="custom" tooltip="自定义纹理。如果您通过脚本设置纹理属性，它会自动更改。" style="height: 30px; max-height: 30px; min-height: 30px; flex-grow: 1;" />
        </ui:VisualElement>
        <uie:ObjectField label="Texture" binding-path="m_Texture" name="texture" type="UnityEngine.Texture, UnityEngine.CoreModule" style="padding-left: 20px;" />
        <ui:SliderInt picking-mode="Ignore" label="Resolution" value="2" high-value="10" low-value="1" name="resolution" binding-path="_resolution" show-input-field="true" />
        <ui:Label tabindex="-1" text="Vertex Count" display-tooltip-when-elided="true" name="vertexCount" tooltip="Unity顶点限制为65,000。请保持顶点数量低于此值。" style="padding-left: 20px; -unity-text-align: middle-left;" />
        <ui:MinMaxSlider picking-mode="Ignore" label="Clip Range" min-value="0" max-value="1" low-limit="0" high-limit="1" name="clipRange" binding-path="_clipRange" />
        <ui:EnumField label="UV Mode" type="UI_Spline_Renderer.UVMode, UISplineRenderer" binding-path="_uvMode" name="uvMode" />
    </ui:VisualElement>
    <ui:VisualElement name="startImageArea" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0);">
        <ui:VisualElement style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); height: 20px; max-height: 20px; min-height: 20px;" />
        <ui:Vector2Field label="UV Multiplier" binding-path="_uvMultiplier" name="uvMultiplier" x="1" y="1" />
        <ui:Vector2Field label="UV Offset" binding-path="_uvOffset" name="uvOffset" tooltip="线条纹理的UV偏移。您可以使用此属性制作简单的纹理动画。" />
        <ui:VisualElement name="presetButtons" style="flex-grow: 1; flex-shrink: 0; flex-direction: row;">
            <ui:Label text="Start Image Preset" style="-unity-text-align: middle-left; align-self: stretch;" />
            <ui:VisualElement name="vert" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); justify-content: space-around;">
                <ui:VisualElement name="hor1" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: row; height: 30px; max-height: 30px; min-height: 30px;">
                    <ui:Button text="None" display-tooltip-when-elided="true" name="none" style="flex-grow: 1;" />
                    <ui:Button text="Triangle" display-tooltip-when-elided="true" name="triangle" style="flex-grow: 1;" />
                    <ui:Button text="Arrow" display-tooltip-when-elided="true" name="arrow" style="flex-grow: 1;" />
                </ui:VisualElement>
                <ui:VisualElement name="hor2" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: row; height: 30px; max-height: 30px; min-height: 30px;">
                    <ui:Button text="Empty Circle" display-tooltip-when-elided="true" name="emptyCircle" style="flex-grow: 1;" />
                    <ui:Button text="Filled Circle" display-tooltip-when-elided="true" name="filledCircle" style="flex-grow: 1;" />
                    <ui:Button text="Custom" display-tooltip-when-elided="true" name="custom" style="flex-grow: 1;" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        <uie:ObjectField label="Start Image Sprite" binding-path="_startImageSprite" name="sprite" type="UnityEngine.Sprite, UnityEngine.CoreModule" style="padding-left: 20px;" />
        <ui:FloatField label="Start Image Size" value="32" binding-path="_startImageSize" name="size" style="padding-left: 20px;" />
        <ui:FloatField label="Start Image Offset" value="0" binding-path="_startImageOffset" name="offset" tooltip="从样条线第一个点的偏移量。如果值大于0，则沿着样条线移动。否则，相对于当前旋转向后移动。" style="padding-left: 20px;" />
    </ui:VisualElement>
    <ui:VisualElement name="endImageArea" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0);">
        <ui:VisualElement style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); height: 20px; max-height: 20px; min-height: 20px;" />
        <ui:VisualElement name="presetButtons" style="flex-grow: 1; flex-shrink: 0; flex-direction: row;">
            <ui:Label text="End Image Preset" style="-unity-text-align: middle-left; align-self: stretch;" />
            <ui:VisualElement name="vert" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); justify-content: space-around;">
                <ui:VisualElement name="hor1" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: row; height: 30px; max-height: 30px; min-height: 30px;">
                    <ui:Button text="None" display-tooltip-when-elided="true" name="none" style="flex-grow: 1;" />
                    <ui:Button text="Triangle" display-tooltip-when-elided="true" name="triangle" style="flex-grow: 1;" />
                    <ui:Button text="Arrow" display-tooltip-when-elided="true" name="arrow" style="flex-grow: 1;" />
                </ui:VisualElement>
                <ui:VisualElement name="hor2" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); flex-direction: row; height: 30px; max-height: 30px; min-height: 30px;">
                    <ui:Button text="Empty Circle" display-tooltip-when-elided="true" name="emptyCircle" style="flex-grow: 1;" />
                    <ui:Button text="Filled Circle" display-tooltip-when-elided="true" name="filledCircle" style="flex-grow: 1;" />
                    <ui:Button text="Custom" display-tooltip-when-elided="true" name="custom" style="flex-grow: 1;" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        <uie:ObjectField label="End Image Sprite" binding-path="_endImageSprite" name="sprite" type="UnityEngine.Sprite, UnityEngine.CoreModule" style="padding-left: 20px;" />
        <ui:FloatField label="End Image Size" value="32" binding-path="_endImageSize" name="size" style="padding-left: 20px;" />
        <ui:FloatField label="End Image Offset" value="0" binding-path="_endImageOffset" name="offset" tooltip="从样条线最后一个点的偏移量。如果值小于0，则沿着样条线移动。否则，相对于当前旋转向前移动。" style="padding-left: 20px;" />
    </ui:VisualElement>
</ui:UXML>
