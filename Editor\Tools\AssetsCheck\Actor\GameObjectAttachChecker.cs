﻿using UnityEngine;
using UnityEditor;
using System.Text;
using Nirvana;
using Game;

namespace AssetsCheck
{
    class GameObjectAttachChecker : BaseChecker
    {
        // 指定要检查的特效文件夹
        public static string[] checkDirs = { "Assets/Game/Actors", "Assets/Game/Model", };

        public override string GetErrorDesc()
        {
            return "检查 GameObjectAttach 组件丢失资源missing";
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            int assetCount = guids.Length;
            int curIndex = 0;

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                GameObjectAttach[] components = gameobj.GetComponentsInChildren<GameObjectAttach>(true);

                if (components == null || components.Length == 0)
                    continue;

                bool isMissing = false;
                string res_path = "";
                GameObject res_obj = null;
                foreach (GameObjectAttach component in components)
                {
                    Debug.LogError(component.name);
                    AssetID assetID = component.Asset;
                    if (assetID.IsEmpty || string.IsNullOrEmpty(assetID.AssetGUID) || string.IsNullOrEmpty(assetID.AssetName) || string.IsNullOrEmpty(assetID.BundleName))
                    {
                        isMissing = true;
                        break;
                    }

                    res_path = AssetDatabase.GUIDToAssetPath(assetID.AssetGUID);
                    res_obj = AssetDatabase.LoadAssetAtPath<GameObject>(res_path);
                    if (res_obj == null)
                    {
                        isMissing = true;
                        break;
                    }
                }

                if (isMissing)
                {
                    CheckItem item = new CheckItem();
                    item.asset = path;
                    this.outputList.Add(item);
                }

                EditorUtility.DisplayProgressBar("正在扫描",
                    string.Format("{0} / {1}", curIndex, assetCount), (float)curIndex / (float)assetCount);
                curIndex++;
            }

            EditorUtility.ClearProgressBar();
        }

        //一键修复
        protected override void OnFix(string[] lines)
        {
            string res_path = "";
            GameObject res_obj = null;

            for (int i = 0; i < lines.Length; i++)
            {
                string path = lines[i];
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                GameObjectAttach[] components = gameobj.GetComponentsInChildren<GameObjectAttach>(true);

                foreach (GameObjectAttach component in components)
                {
                    AssetID assetID = component.Asset;
                    if (string.IsNullOrEmpty(assetID.AssetGUID))    // guid没了直接删掉组件
                    {
                        UnityEngine.Object.DestroyImmediate(component, true);
                    }
                    else
                    {
                        res_path = AssetDatabase.GUIDToAssetPath(assetID.AssetGUID);
                        res_obj = AssetDatabase.LoadAssetAtPath<GameObject>(res_path);
                        if (res_obj == null) // guid原组件丢失了直接删掉组件
                        {
                            UnityEngine.Object.DestroyImmediate(component, true);
                        }
                        else
                        {
                            component.RefreshAssetBundleName();
                        }
                    }
                }

                EditorUtility.DisplayProgressBar("正在修复GameObjectAttach引用丢失问题",
                    string.Format("{0} / {1}", i + 1, lines.Length), (float)i + 1 / (float)lines.Length);

            }

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public int width;
            public int height;

            public string MainKey
            {
                get { return string.Format("{0}", asset); }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(this.asset);
                return builder;
            }
        }
    }

}