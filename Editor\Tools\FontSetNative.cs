﻿using Nirvana;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Game.Scripts.Editor.Tools
{
    class FontSetNative : BaseEditorWindow
    {
        private Font oldFont;
        private Font newFont;

        private Color oldColor = Color.black;
        private Color oldOutLine = Color.black;
        private Color newOutLint = Color.black;

        private Color newColor = Color.black;
        private Color oldGradient1 = Color.black;
        private Color oldGradient2 = Color.black;
        private Color newGradient1 = Color.black;
        private Color newGradient2 = Color.black;

        private bool hasOutLine = false;
        private bool hasGradient = false;
        private bool needOutLine = false;
        private bool needGradient = false;
        private bool changeColor = true;

        [MenuItem("Tools/换UI工具/View替换字体")]
        private static void ShowWindow()
        {
            EditorWindow.GetWindow<FontSetNative>(false, "替换字体");
        }

        private void OnGUI()
        {
            oldFont = EditorGUILayout.ObjectField("oldFont: ", oldFont, typeof(Font), true) as Font;
            oldColor = EditorGUILayout.ColorField("oldColor: ", oldColor);
            hasOutLine = EditorGUILayout.ToggleLeft("hasOutLine: ", hasOutLine);
            if (hasOutLine)
                oldOutLine = EditorGUILayout.ColorField("oldOutLine: ", oldOutLine);
            hasGradient = EditorGUILayout.ToggleLeft("hasGradient: ", hasGradient);
            if (hasGradient)
            {
                oldGradient1 = EditorGUILayout.ColorField("oldGradient1: ", oldGradient1);
                oldGradient2 = EditorGUILayout.ColorField("oldGradient2: ", oldGradient2);
            }
            
            EditorGUILayout.Space();
            newFont = EditorGUILayout.ObjectField("newFont: ", newFont, typeof(Font), true) as Font;
            newColor = EditorGUILayout.ColorField("newColor: ", newColor);
            needOutLine = EditorGUILayout.ToggleLeft("needOutLine: ", needOutLine);
            if (needOutLine)
                newOutLint = EditorGUILayout.ColorField("newOutLint: ", newOutLint);
            needGradient = EditorGUILayout.ToggleLeft("needGradient: ", needGradient);
            if (needGradient)
             {
                 newGradient1 = EditorGUILayout.ColorField("newGradient1: ", newGradient1);
                 newGradient2 = EditorGUILayout.ColorField("newGradient2: ", newGradient2);
             }
            GUILayout.Space(20);
            changeColor = EditorGUILayout.Toggle("changeColor:", changeColor);
            GUILayout.Space(20);
            if (GUILayout.Button("Change All"))
            {
                ChangeAll();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }

        }

        private void ChangeAll()
        {
            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { "Assets/Game/UIs/View" });
            int endIndex = guids.Length;
            if (endIndex < 1)
            {
                return;
            }
            float nextTime = 0;
            for (int i = 0; i < endIndex; i++)
            {
                var guid = guids[i];
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                this.Check(obj);
                if (nextTime <= Time.realtimeSinceStartup)
                {
                    bool cancel = EditorUtility.DisplayCancelableProgressBar("替换中", path, (float)i / endIndex);
                    nextTime = Time.realtimeSinceStartup + 0.1f;
                    if (cancel)
                    {
                        break;
                    }
                }
            }
            EditorUtility.ClearProgressBar();
        }

        private void Check(GameObject obj)
        {
            var texts = obj.GetComponentsInChildren<Text>(true);
            if (texts.Length < 1)
            {
                return;
            }
            Text text;
            UIGradient gradient;
            Outline outLine;
            for (int i = 0; i < texts.Length; ++i)
            {
                text = texts[i];
                if (text.font != oldFont)
                    continue;
                gradient = text.gameObject.GetComponent<Nirvana.UIGradient>();
                if (hasGradient && gradient != null)
                {
                    if (gradient.Color1 != oldGradient1)
                        continue;
                    else if (gradient.Color2 != oldGradient2)
                        continue;
                }
                else if (changeColor && text.color != oldColor)
                    continue;
                outLine = text.gameObject.GetComponent<Outline>();
                if (hasOutLine && outLine != null && outLine.effectColor != oldOutLine)
                    continue;

                text.font = newFont;
                if (needGradient)
                {
                    if (gradient == null)
                    {
                        gradient = text.gameObject.AddComponent<Nirvana.UIGradient>();
                    }
                    gradient.Color1 = newGradient1;
                    gradient.Color2 = newGradient2;
                }
                else
                {
                    if (changeColor)
                        text.color = newColor;
                    if (gradient != null)
                    {
                        GameObject.DestroyImmediate(gradient, true);
                    }
                }
                if (outLine != null)
                {
                    GameObject.DestroyImmediate(outLine, true);
                }
                if (needOutLine)
                {
                     outLine = text.gameObject.AddComponent<Outline>();
                     outLine.effectColor = newOutLint;
                }

                var serObj = new SerializedObject(text);
                serObj.Update();
                serObj.ApplyModifiedPropertiesWithoutUndo();
                EditorUtility.SetDirty(text);
            }
        }

    }
}
