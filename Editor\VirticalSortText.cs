﻿using UnityEditor;
[CustomEditor(typeof(VirticalText))]
public class VirticalSortText : Editor
{

    private SerializedObject test;//序列化
    private SerializedProperty orientation, spacing;
    void OnEnable()
    {
        test = new SerializedObject(target);
        orientation = test.FindProperty("orientation");
        spacing = test.FindProperty("spacing");
    }
    public override void OnInspectorGUI()
    {
        test.Update();
        EditorGUILayout.PropertyField(spacing);
        EditorGUILayout.PropertyField(orientation);
        //serializedObject.ApplyModifiedProperties();
        test.ApplyModifiedProperties();
    }
}