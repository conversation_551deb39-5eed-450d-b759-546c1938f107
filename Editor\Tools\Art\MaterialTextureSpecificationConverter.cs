

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

public class MaterialTextureSpecificationConverter
{
    //[MenuItem("自定义工具/美术专用/一键转换材质新贴图规则")]
    public static void OneKeyConvertextureSpecification()
    {
        HashSet<int> processedSet = new HashSet<int>();
        foreach (var obj in Selection.gameObjects)
        {
            foreach (var renderer in obj.GetComponentsInChildren<MeshRenderer>())
            {
                foreach (var mat in renderer.sharedMaterials)
                {
                    if (mat.shader.name.EndsWith("StylizedScene") &&
                        !mat.IsKeywordEnabled("_NEW_TEXTURE_SPECIFICATION"))
                    {
                        Texture2D _BaseMap = mat.GetTexture("_BaseMap") as Texture2D;
                        Texture2D _NormalMap = mat.GetTexture("_NormalMap") as Texture2D;
                        Texture2D _DetailMask = mat.GetTexture("_DetailMask") as Texture2D;
                        
                        if (_BaseMap != null && !processedSet.Contains(_BaseMap.GetInstanceID()) &&
                            _NormalMap != null &&
                            _DetailMask != null)
                        {
                            processedSet.Add(_BaseMap.GetInstanceID());
                            ConvertMaps(_BaseMap, _NormalMap, _DetailMask, out bool hasDetail);
                            if (hasDetail)
                            {
                                mat.SetTexture("_EmissionMap", _DetailMask);
                            }
                        }
                        else
                        {
                            continue;
                        }
                        
                        Texture2D __SecondaryBaseMap = mat.GetTexture("_SecondaryBaseMap") as Texture2D;
                        Texture2D _SecondaryBumpMap = mat.GetTexture("_SecondaryBumpMap") as Texture2D;
                        Texture2D _SecondaryDetailMask = mat.GetTexture("_SecondaryDetailMask") as Texture2D;
                        
                        if (__SecondaryBaseMap != null && !processedSet.Contains(__SecondaryBaseMap.GetInstanceID()) &&
                            _SecondaryBumpMap != null &&
                            _SecondaryDetailMask != null)
                        {
                            processedSet.Add(__SecondaryBaseMap.GetInstanceID());
                            ConvertMaps(__SecondaryBaseMap, _SecondaryBumpMap, _SecondaryDetailMask, out bool hasEmission);
                        }
                        CoreUtils.SetKeyword(mat, "_NEW_TEXTURE_SPECIFICATION", true);
                    }
                }
            }
        }
    }

    private static void ConvertMaps(Texture2D _BaseMap, Texture2D _NormalMap, Texture2D _DetailMask, out bool hasDetail)
    {
        var TextureAssetImporter =
            Type.GetType(
                "TextureAssetImporter,Assembly-CSharp-Editor, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null");
        var BeginTextureEditing =
            TextureAssetImporter.GetMethod("BeginTextureEditing",
                BindingFlags.Public | BindingFlags.Static);
        var EndTextureEditing =
            TextureAssetImporter.GetMethod("EndTextureEditing",
                BindingFlags.Public | BindingFlags.Static);

        BeginTextureEditing.Invoke(null, null);

        TextureImporterFormat format = TextureImporterFormat.RGBA32;

        hasDetail = false;

        int minSize = Mathf.Min(_BaseMap.width, _NormalMap.width, _DetailMask.width);

        //
        TextureImporter albedoImporter =
            TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(_BaseMap)) as TextureImporter;
        albedoImporter.isReadable = true;
        albedoImporter.sRGBTexture = true;
        albedoImporter.filterMode = FilterMode.Point;
        TextureImporterPlatformSettings windowsSettins =
            albedoImporter.GetPlatformTextureSettings("PC");
        windowsSettins.overridden = false;
        albedoImporter.SetPlatformTextureSettings(windowsSettins);
        TextureImporterPlatformSettings androidSettins =
            albedoImporter.GetPlatformTextureSettings("Android");
        androidSettins.overridden = false;
        albedoImporter.SetPlatformTextureSettings(androidSettins);
        TextureImporterPlatformSettings defaultSettings =
            albedoImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = minSize;
        defaultSettings.format = format;
        defaultSettings.textureCompression = TextureImporterCompression.Uncompressed;
        albedoImporter.SetPlatformTextureSettings(defaultSettings);
        albedoImporter.SaveAndReimport();

        //
        TextureImporter detailImporter =
            TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(_DetailMask)) as TextureImporter;
        detailImporter.isReadable = true;
        detailImporter.sRGBTexture = true;
        detailImporter.filterMode = FilterMode.Point;
        windowsSettins = detailImporter.GetPlatformTextureSettings("PC");
        windowsSettins.overridden = false;
        detailImporter.SetPlatformTextureSettings(windowsSettins);
        androidSettins = detailImporter.GetPlatformTextureSettings("Android");
        androidSettins.overridden = false;
        detailImporter.SetPlatformTextureSettings(androidSettins);
        defaultSettings = detailImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = minSize;
        defaultSettings.format = format;
        defaultSettings.textureCompression = TextureImporterCompression.Uncompressed;
        detailImporter.SetPlatformTextureSettings(defaultSettings);
        detailImporter.SaveAndReimport();

        //
        TextureImporter normalImporter =
            TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(_NormalMap)) as TextureImporter;
        normalImporter.isReadable = true;
        normalImporter.sRGBTexture = false;
        normalImporter.alphaSource = TextureImporterAlphaSource.FromInput;
        normalImporter.filterMode = FilterMode.Point;
        windowsSettins = normalImporter.GetPlatformTextureSettings("PC");
        windowsSettins.overridden = false;
        normalImporter.SetPlatformTextureSettings(windowsSettins);
        androidSettins = normalImporter.GetPlatformTextureSettings("Android");
        androidSettins.overridden = false;
        normalImporter.SetPlatformTextureSettings(androidSettins);
        defaultSettings = normalImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = minSize;
        defaultSettings.format = format;
        defaultSettings.textureCompression = TextureImporterCompression.Uncompressed;
        normalImporter.SetPlatformTextureSettings(defaultSettings);
        normalImporter.SaveAndReimport();

        //
        Color32[] albedoColors = _BaseMap.GetPixels32(0);
        Color32[] detailColors = _DetailMask.GetPixels32(0);
        Color32[] normalColors = _NormalMap.GetPixels32(0);

        for (int i = 0; i < albedoColors.Length; i++)
        {
            Color32 albedoRGBA = albedoColors[i];
            Color32 normalRGBA = normalColors[i];
            Color32 detailRGBA = detailColors[i];

            albedoColors[i] = new Color32
            (
                albedoRGBA.r,
                albedoRGBA.g,
                albedoRGBA.b,
                detailRGBA.a
            );
            normalColors[i] = new Color32
            (
                detailRGBA.r,
                normalRGBA.g,
                normalRGBA.b,
                normalRGBA.r
            );

            if (detailRGBA.b > 0 || albedoRGBA.a < 1)
                hasDetail = true;

            detailColors[i] = new Color32
            (
                detailRGBA.b,
                detailRGBA.b,
                detailRGBA.b,
                albedoRGBA.a
            );
        }

        _BaseMap.SetPixels32(albedoColors, 0);
        _BaseMap.Apply();
        string file = AssetDatabase.GetAssetPath(_BaseMap);
        byte[] bytes = _BaseMap.EncodeToTGA();
        File.WriteAllBytes(file, bytes);

        _NormalMap.SetPixels32(normalColors, 0);
        _NormalMap.Apply();
        file = AssetDatabase.GetAssetPath(_NormalMap);
        bytes = _NormalMap.EncodeToTGA();
        File.WriteAllBytes(file, bytes);

        if (hasDetail)
        {
            _DetailMask.SetPixels32(detailColors, 0);
            _DetailMask.Apply();
            file = AssetDatabase.GetAssetPath(_DetailMask);
            bytes = _DetailMask.EncodeToTGA();
            File.WriteAllBytes(file, bytes);
        }


        EndTextureEditing.Invoke(null, null);

        AssetDatabase.Refresh();

        //
        defaultSettings = albedoImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = 2048;
        albedoImporter.SetPlatformTextureSettings(defaultSettings);
        albedoImporter.filterMode = FilterMode.Bilinear;
        albedoImporter.SaveAndReimport();

        //
        defaultSettings = detailImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = 2048;
        albedoImporter.SetPlatformTextureSettings(defaultSettings);
        detailImporter.filterMode = FilterMode.Bilinear;
        detailImporter.SaveAndReimport();

        //
        defaultSettings = normalImporter.GetDefaultPlatformTextureSettings();
        defaultSettings.maxTextureSize = 2048;
        normalImporter.SetPlatformTextureSettings(defaultSettings);
        normalImporter.filterMode = FilterMode.Bilinear;
        normalImporter.SaveAndReimport();
    }
}
