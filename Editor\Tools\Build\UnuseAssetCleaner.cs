﻿using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class UnuseAssetCleaner
{
    public static void DelAllUnUseAsset()
    {
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game"});
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        DelUnuseMaterial();
        DelUnuseTexture();
    }

    // 删除无用的材质球
    public static void DelUnuseMaterial()
    {
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!CleanerUtil.IsBeReference(assetPath)
                && assetPath.EndsWith(".mat")
                && !assetPath.Contains("/Game/Misc/")
                && !assetPath.Contains("/Game/Shaders/"))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
    }

    // 删除无用的纹理
    public static void DelUnuseTexture()
    {
        string[] guids = AssetDatabase.FindAssets("t:texture", new string[] { "Assets/Game/Effects" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (!CleanerUtil.IsBeReference(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
    }

    // 清理Environments
    public static void DelAllUnUseEnvironments()
    {
        //首先清理Environments目录下的prefab
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        var guids = AssetDatabase.FindAssets("t:prefab", new string[] { "Assets/Game/Environments" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            
            if (!CleanerUtil.IsBeReference(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
        AssetDatabase.Refresh();

        //然后清理Environments目录下的fbx与材质球
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!CleanerUtil.IsBeReference(assetPath)
                && assetPath.StartsWith("Assets/Game/Environments")
                && (assetPath.EndsWith(".FBX") || assetPath.EndsWith(".anim") || assetPath.EndsWith(".mat")))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
        AssetDatabase.Refresh();

        // 最后清理Environments目录下的fbx与材质球
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        guids = AssetDatabase.FindAssets("t:texture", new string[] { "Assets/Game/Environments" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (!CleanerUtil.IsBeReference(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
    }

    // 清理Actors
    public static void DelAllUnUseActors()
    {
        // 清理Actors目录下的overrideController和controller
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        var assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!CleanerUtil.IsBeReference(assetPath)
                && assetPath.StartsWith("Assets/Game/Actors")
                && !assetPath.StartsWith("Assets/Game/Actors/Character")
                && !assetPath.StartsWith("Assets/Game/Actors/Shared")
                && (assetPath.EndsWith(".overrideController") || assetPath.EndsWith(".controller")))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
        AssetDatabase.Refresh();

        // 清理Actors目录下的fbx和mat
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        assetPaths = AssetDatabase.GetAllAssetPaths();
        foreach (var assetPath in assetPaths)
        {
            if (!CleanerUtil.IsBeReference(assetPath)
                && assetPath.StartsWith("Assets/Game/Actors")
                && !assetPath.StartsWith("Assets/Game/Actors/Character")
                && !assetPath.StartsWith("Assets/Game/Actors/Shared")
                && (assetPath.EndsWith(".FBX") || assetPath.EndsWith(".anim") || assetPath.EndsWith(".mat") || (assetPath.EndsWith(".asset") && assetPath.Contains("LOD") )))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
        AssetDatabase.Refresh();

        // 清理Actors目录下的贴图
        CleanerUtil.ClearRefrence();
        CleanerUtil.BuildRefrence("t:prefab", new string[] { "Assets/Game" });
        CleanerUtil.BuildRefrence("t:scene", new string[] { "Assets/Game" });
        var guids = AssetDatabase.FindAssets("t:texture", new string[] { "Assets/Game/Actors" });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            if (!CleanerUtil.IsBeReference(assetPath))
            {
                AssetDatabase.DeleteAsset(assetPath);
            }
        }
    }
}
