﻿using System.Collections.Generic;
using UnityEngine;

public class ActorAnimatorSyncter : MonoBehaviour
{
    private Animator actorAnimator;
    private List<Animator> drawPartAnimator = new();
    private List<Animator> waitPartAnimator = new();

    private void Start()
    {
        ClearAll();
        actorAnimator = this.GetComponent<Animator>();
        SetDrawPartAnimator(actorAnimator);
        if (waitPartAnimator.Count > 0)
        {
            for (int i = 0; i < waitPartAnimator.Count; i++)
            {
                Animator wpAnimator = waitPartAnimator[i];
                if (wpAnimator == null) continue;
                SetDrawPartAnimator(wpAnimator);
            }

            waitPartAnimator.Clear();
        }
    }

    private void LateUpdate()
    {
        if (actorAnimator == null || drawPartAnimator.Count == 0) return;

        //每5帧检测一遍
        //if (Time.frameCount % 5 != 0) return;

        AnimatorStateInfo curAnimatorState = actorAnimator.GetCurrentAnimatorStateInfo(0);
        int curAnimHash = curAnimatorState.fullPathHash;
        float curNormalizedTime = Mathf.Repeat(curAnimatorState.normalizedTime, 1f); // 处理循环时间
        foreach (Animator dpAnimator in drawPartAnimator)
        {
            if (dpAnimator == null || dpAnimator == actorAnimator || !dpAnimator.enabled) continue;
            AnimatorStateInfo dpAnimatorState = dpAnimator.GetCurrentAnimatorStateInfo(0);
            if (curAnimHash != dpAnimatorState.fullPathHash)
            {
                dpAnimator.Play(curAnimHash, 0, curNormalizedTime);
            }
            else
            {
                float dpNormalizedTime = Mathf.Repeat(dpAnimatorState.normalizedTime, 1f);
                // 时间差计算考虑循环特性
                float timeDifference = Mathf.Abs(curNormalizedTime - dpNormalizedTime);
                timeDifference = Mathf.Min(timeDifference, 1 - timeDifference);

                //Debug.LogErrorFormat($"检测时间----{timeDifference}----{curAnimatorState.normalizedTime}----{dpAnimatorState.normalizedTime}");
                if (timeDifference > 0.01f)
                {
                    //25.5.5，让当前需要同步的动画同步到当前主部位时间，立即更新
                    dpAnimator.Play(curAnimHash, 0, curNormalizedTime);
                    dpAnimator.Update(0f);
                    //actorAnimator.Play(curAnimHash, 0, curNormalizedTime);
                }
            }
        }
    }

    //立即同步动画
    public void SyncAnimationImmediate(Animator opAnimator)
    {
        if (opAnimator == null || actorAnimator == null || drawPartAnimator.Count == 0 || !drawPartAnimator.Contains(opAnimator))
        {
            return;
        }

        AnimatorStateInfo curAnimatorState = actorAnimator.GetCurrentAnimatorStateInfo(0);
        opAnimator.Play(curAnimatorState.fullPathHash, 0, curAnimatorState.normalizedTime);
    }

    private void OnDestroy()
    {
        ClearAll();
        waitPartAnimator.Clear();
    }

    private void ClearAll()
    {
        actorAnimator = null;
        drawPartAnimator.Clear();
    }

    public void SetDrawPartAnimator(Animator anim)
    {
        //Debug.LogErrorFormat("添加 动画的物体：{0}--自己的父物体{1}", anim.transform.name, anim.transform.parent.name);
        if (actorAnimator == null)
        {
            waitPartAnimator.Add(anim);
            return;
        }
        
        if (anim == null || anim.runtimeAnimatorController == null || drawPartAnimator.Contains(anim)) return;
        drawPartAnimator.Add(anim);
    }

    public void RemoveDrawPartAnimator(Animator anim)
    {
        //Debug.LogErrorFormat("移除 动画的物体：{0}", anim.transform.name);
        if (anim == null || !drawPartAnimator.Contains(anim)) return;
        drawPartAnimator.Remove(anim);
    }

    public void ClearSyncTargetAnimatorData()
    {
        ClearAll();
        waitPartAnimator.Clear();
        actorAnimator = this.GetComponent<Animator>();
        SetDrawPartAnimator(actorAnimator);
    }
}