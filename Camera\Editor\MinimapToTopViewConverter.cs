using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;

namespace Nirvana.Editor
{
    /// <summary>
    /// MinimapCamera到TopViewMapCamera的批量转换工具
    /// </summary>
    public class MinimapToTopViewConverter : OdinEditorWindow
    {
        [MenuItem("Tools/Nirvana/Minimap Camera Converter")]
        private static void OpenWindow()
        {
            GetWindow<MinimapToTopViewConverter>("小地图相机转换工具").Show();
        }

        #region Settings

        [TitleGroup("设置")]
        [FolderPath]
        [LabelText("场景目录")]
        [InfoBox("选择包含需要转换场景的目录")]
        public string scenesDirectory = "Assets/Game/Scenes/Map";

        [TitleGroup("设置")]
        [LabelText("场景名称过滤")]
        [InfoBox("只处理包含此关键词的场景")]
        public string sceneNameFilter = "_Main";

        [TitleGroup("设置")]
        [LabelText("主节点名称")]
        [InfoBox("在此节点下查找MinimapCamera组件")]
        public string mainNodeName = "Main";

        #endregion

        #region Scan Results

        [TitleGroup("扫描结果")]
        [ReadOnly]
        [ListDrawerSettings(ShowIndexLabels = true, ListElementLabelName = "sceneName")]
        public List<SceneInfo> foundScenes = new List<SceneInfo>();

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("总场景数")]
        public int totalScenes;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("只使用MinimapCamera")]
        public int usingMinimapOnlyCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("只使用TopViewMapCamera")]
        public int usingTopViewOnlyCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("同时使用两种组件")]
        public int usingBothComponentsCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("没有地图相机")]
        public int noMapCameraCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("配置错误")]
        public int configurationErrorCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("转换成功")]
        public int conversionSuccessCount;

        [TitleGroup("统计信息")]
        [ReadOnly]
        [LabelText("转换失败")]
        public int conversionFailedCount;

        public enum ConversionStatus
        {
            [LabelText("未处理")]
            NotProcessed,
            [LabelText("成功")]
            Success,
            [LabelText("失败")]
            Failed,
            [LabelText("已存在TopViewMapCamera")]
            AlreadyConverted,
            [LabelText("未找到MinimapCamera")]
            NoMinimapCameraFound,
            [LabelText("只使用MinimapCamera")]
            UsingMinimapOnly,
            [LabelText("只使用TopViewMapCamera")]
            UsingTopViewOnly,
            [LabelText("同时使用两种组件")]
            UsingBothComponents,
            [LabelText("没有找到任何地图相机")]
            NoMapCameraFound,
            [LabelText("TopViewMapCamera配置错误")]
            TopViewIncorrectlyConfigured
        }

        [System.Serializable]
        public class SceneInfo
        {
            public string scenePath;
            public string sceneName;
            public bool hasMinimapCamera;
            public string minimapCameraPath;
            public bool hasTopViewMapCamera;
            public string topViewMapCameraPath;
            public string componentIssues;
            public ConversionStatus status = ConversionStatus.NotProcessed;

            public SceneInfo(string path)
            {
                scenePath = path;
                sceneName = Path.GetFileNameWithoutExtension(path);
                hasMinimapCamera = false;
                hasTopViewMapCamera = false;
                minimapCameraPath = "";
                topViewMapCameraPath = "";
                componentIssues = "";
            }
        }

        #endregion

        #region Operations

        [TitleGroup("操作")]
        [Button(ButtonSizes.Large)]
        [GUIColor(0.4f, 0.8f, 1f)]
        private void ScanScenes()
        {
            foundScenes.Clear();
            
            if (string.IsNullOrEmpty(scenesDirectory) || !Directory.Exists(scenesDirectory))
            {
                ShowNotification(new GUIContent("场景目录无效"));
                return;
            }

            var sceneFiles = Directory.GetFiles(scenesDirectory, "*.unity", SearchOption.AllDirectories)
                .Where(path => Path.GetFileNameWithoutExtension(path).Contains(sceneNameFilter, StringComparison.OrdinalIgnoreCase))
                .ToArray();

            foreach (string scenePath in sceneFiles)
            {
                var sceneInfo = new SceneInfo(scenePath);
                CheckSceneForMapCameraComponents(sceneInfo);
                foundScenes.Add(sceneInfo);
            }

            // 更新统计信息
            UpdateStatistics();

            LogMessage($"扫描完成，找到 {foundScenes.Count} 个匹配的场景");
            LogMessage(GetStatisticsSummary());
        }

        private void UpdateStatistics()
        {
            totalScenes = foundScenes.Count;
            usingMinimapOnlyCount = foundScenes.Count(s => s.status == ConversionStatus.UsingMinimapOnly);
            usingTopViewOnlyCount = foundScenes.Count(s => s.status == ConversionStatus.UsingTopViewOnly);
            usingBothComponentsCount = foundScenes.Count(s => s.status == ConversionStatus.UsingBothComponents);
            noMapCameraCount = foundScenes.Count(s => s.status == ConversionStatus.NoMapCameraFound);
            configurationErrorCount = foundScenes.Count(s => s.status == ConversionStatus.TopViewIncorrectlyConfigured);
            conversionSuccessCount = foundScenes.Count(s => s.status == ConversionStatus.Success);
            conversionFailedCount = foundScenes.Count(s => s.status == ConversionStatus.Failed);
        }

        private string GetStatisticsSummary()
        {
            var summary = new System.Text.StringBuilder();
            summary.AppendLine("=== 场景组件使用统计 ===");
            summary.AppendLine($"总场景数: {totalScenes}");
            summary.AppendLine($"只使用MinimapCamera: {usingMinimapOnlyCount}");
            summary.AppendLine($"只使用TopViewMapCamera: {usingTopViewOnlyCount}");
            summary.AppendLine($"同时使用两种组件: {usingBothComponentsCount}");
            summary.AppendLine($"没有地图相机: {noMapCameraCount}");
            summary.AppendLine($"TopViewMapCamera配置错误: {configurationErrorCount}");
            summary.AppendLine($"转换成功: {conversionSuccessCount}");
            summary.AppendLine($"转换失败: {conversionFailedCount}");
            
            // 计算需要处理的场景数
            int needsAttentionCount = usingMinimapOnlyCount + usingBothComponentsCount + configurationErrorCount;
            summary.AppendLine($"需要处理的场景数: {needsAttentionCount}");
            
            return summary.ToString();
        }

        [TitleGroup("操作")]
        [Button(ButtonSizes.Large)]
        [GUIColor(0.8f, 1f, 0.4f)]
        [EnableIf("@foundScenes.Count > 0")]
        private void ConvertAllScenes()
        {
            if (EditorUtility.DisplayDialog("确认转换", 
                $"即将转换 {foundScenes.Count} 个场景中的MinimapCamera组件，此操作不可撤销！", 
                "确认", "取消"))
            {
                ConvertScenes(foundScenes);
            }
        }

        [TitleGroup("操作")]
        [Button("转换选中的场景")]
        [EnableIf("@GetSelectedScenes().Count > 0")]
        private void ConvertSelectedScenes()
        {
            var selectedScenes = GetSelectedScenes();
            if (EditorUtility.DisplayDialog("确认转换", 
                $"即将转换 {selectedScenes.Count} 个选中场景中的小地图相机组件，此操作不可撤销！", 
                "确认", "取消"))
            {
                ConvertScenes(selectedScenes);
            }
        }

        [TitleGroup("操作")]
        [Button("预览选中场景")]
        [EnableIf("@foundScenes.Count > 0")]
        private void PreviewSelectedScene()
        {
            // 查找第一个有问题的场景进行预览
            var sceneToPreview = foundScenes.FirstOrDefault(s => 
                s.status == ConversionStatus.UsingMinimapOnly || 
                s.status == ConversionStatus.UsingBothComponents ||
                s.status == ConversionStatus.TopViewIncorrectlyConfigured);
            
            if (sceneToPreview != null && !string.IsNullOrEmpty(sceneToPreview.scenePath))
            {
                EditorSceneManager.OpenScene(sceneToPreview.scenePath, OpenSceneMode.Single);
                LogMessage($"已打开场景进行预览: {sceneToPreview.sceneName} (状态: {sceneToPreview.status})");
            }
            else if (foundScenes.Count > 0)
            {
                var firstScene = foundScenes[0];
                EditorSceneManager.OpenScene(firstScene.scenePath, OpenSceneMode.Single);
                LogMessage($"已打开场景进行预览: {firstScene.sceneName} (状态: {firstScene.status})");
            }
        }

        [TitleGroup("操作")]
        [Button("清除日志")]
        private void ClearLogs()
        {
            logMessages = "";
        }

        [TitleGroup("操作")]
        [Button("转换只使用MinimapCamera的场景")]
        [GUIColor(1f, 0.6f, 0.4f)]
        [EnableIf("@usingMinimapOnlyCount > 0")]
        private void ConvertMinimapOnlyScenes()
        {
            var targetScenes = foundScenes.Where(s => s.status == ConversionStatus.UsingMinimapOnly).ToList();
            if (EditorUtility.DisplayDialog("确认转换", 
                $"即将转换 {targetScenes.Count} 个只使用MinimapCamera的场景，此操作不可撤销！", 
                "确认", "取消"))
            {
                ConvertScenes(targetScenes);
            }
        }

        [TitleGroup("操作")]
        [Button("查看有配置问题的场景")]
        [GUIColor(1f, 0.4f, 0.4f)]
        [EnableIf("@configurationErrorCount > 0")]
        private void ViewProblematicScenes()
        {
            var problematicScenes = foundScenes.Where(s => 
                s.status == ConversionStatus.TopViewIncorrectlyConfigured ||
                s.status == ConversionStatus.UsingBothComponents ||
                s.status == ConversionStatus.Failed).ToList();
            
            string message = "发现以下场景存在问题：\n\n";
            foreach (var scene in problematicScenes)
            {
                message += $"• {scene.sceneName} ({scene.status})\n";
                if (!string.IsNullOrEmpty(scene.componentIssues))
                {
                    message += $"  问题: {scene.componentIssues}\n";
                }
            }
            
            EditorUtility.DisplayDialog("场景问题报告", message, "确定");
        }

        [TitleGroup("操作")]
        [Button("修复配置错误的TopViewMapCamera")]
        [EnableIf("@configurationErrorCount > 0")]
        private void FixTopViewMapCameraConfiguration()
        {
            var configErrorScenes = foundScenes.Where(s => s.status == ConversionStatus.TopViewIncorrectlyConfigured).ToList();
            if (EditorUtility.DisplayDialog("确认修复", 
                $"即将尝试修复 {configErrorScenes.Count} 个配置错误的TopViewMapCamera场景", 
                "确认", "取消"))
            {
                FixTopViewMapCameraScenes(configErrorScenes);
            }
        }

        [TitleGroup("操作")]
        [Button("清理同时使用两种组件的场景")]
        [EnableIf("@usingBothComponentsCount > 0")]
        private void CleanupDuplicateComponents()
        {
            var duplicateScenes = foundScenes.Where(s => s.status == ConversionStatus.UsingBothComponents).ToList();
            if (EditorUtility.DisplayDialog("确认清理", 
                $"即将清理 {duplicateScenes.Count} 个同时使用两种组件的场景，将删除MinimapCamera组件", 
                "确认", "取消"))
            {
                CleanupDuplicateComponentScenes(duplicateScenes);
            }
        }

        [TitleGroup("操作")]
        [Button("验证转换结果")]
        [EnableIf("@foundScenes.Count > 0")]
        private void ValidateConversionResults()
        {
            // 重新扫描以获取最新状态
            foreach (var sceneInfo in foundScenes)
            {
                CheckSceneForMapCameraComponents(sceneInfo);
            }
            
            // 更新统计信息
            UpdateStatistics();
            
            LogMessage("=== 验证转换结果 ===");
            LogMessage(GetStatisticsSummary());
            
            // 生成详细报告
            var report = GenerateDetailedReport();
            LogMessage(report);
        }

        private void FixTopViewMapCameraScenes(List<SceneInfo> scenesToFix)
        {
            // 实现修复逻辑的占位符
            LogMessage($"开始修复 {scenesToFix.Count} 个配置错误的场景");
            // 这里可以添加具体的修复逻辑，比如设置默认值等
        }

        private void CleanupDuplicateComponentScenes(List<SceneInfo> scenesToCleanup)
        {
            // 实现清理重复组件的逻辑
            LogMessage($"开始清理 {scenesToCleanup.Count} 个有重复组件的场景");
            // 这里可以添加删除旧组件的逻辑
        }

        private string GenerateDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 详细转换报告 ===");
            
            if (usingTopViewOnlyCount > 0)
            {
                report.AppendLine($"✓ 已正确使用TopViewMapCamera的场景: {usingTopViewOnlyCount}");
            }
            
            if (usingMinimapOnlyCount > 0)
            {
                report.AppendLine($"⚠ 仍在使用MinimapCamera的场景: {usingMinimapOnlyCount}");
                report.AppendLine("  建议: 执行转换操作");
            }
            
            if (usingBothComponentsCount > 0)
            {
                report.AppendLine($"⚠ 同时使用两种组件的场景: {usingBothComponentsCount}");
                report.AppendLine("  建议: 清理旧组件");
            }
            
            if (configurationErrorCount > 0)
            {
                report.AppendLine($"❌ 配置错误的场景: {configurationErrorCount}");
                report.AppendLine("  建议: 修复配置问题");
            }
            
            if (noMapCameraCount > 0)
            {
                report.AppendLine($"❓ 没有地图相机的场景: {noMapCameraCount}");
                report.AppendLine("  建议: 检查场景结构");
            }
            
            float completionRate = totalScenes > 0 ? (float)usingTopViewOnlyCount / totalScenes * 100 : 0;
            report.AppendLine($"\n转换完成率: {completionRate:F1}%");
            
            return report.ToString();
        }

        #endregion

        #region Progress and Logs

        [TitleGroup("进度和日志")]
        [ProgressBar(0, 1)]
        [HideLabel]
        public float conversionProgress;

        [TitleGroup("进度和日志")]
        [TextArea(5, 10)]
        [HideLabel]
        public string logMessages = "";

        #endregion

        #region Core Logic

        private void CheckSceneForMapCameraComponents(SceneInfo sceneInfo)
        {
            try
            {
                // 临时打开场景检查
                var originalScene = SceneManager.GetActiveScene();
                var scene = EditorSceneManager.OpenScene(sceneInfo.scenePath, OpenSceneMode.Additive);
                
                try
                {
                    GameObject mainNode = FindMainNode(scene);
                    if (mainNode != null)
                    {
                        // 查找MinimapCamera组件
                        var minimapCameraObj = FindMinimapCameraObjectInHierarchy(mainNode);
                        if (minimapCameraObj != null)
                        {
                            sceneInfo.hasMinimapCamera = true;
                            sceneInfo.minimapCameraPath = GetGameObjectPath(minimapCameraObj);
                        }
                        
                        // 查找TopViewMapCamera组件
                        var topViewCameraObj = FindTopViewMapCameraObjectInHierarchy(mainNode);
                        if (topViewCameraObj != null)
                        {
                            sceneInfo.hasTopViewMapCamera = true;
                            sceneInfo.topViewMapCameraPath = GetGameObjectPath(topViewCameraObj);
                            
                            // 验证TopViewMapCamera组件配置
                            var configIssues = ValidateTopViewMapCameraConfiguration(topViewCameraObj);
                            if (!string.IsNullOrEmpty(configIssues))
                            {
                                sceneInfo.componentIssues = configIssues;
                            }
                        }
                        
                        // 确定场景状态
                        DetermineSceneStatus(sceneInfo);
                    }
                    else
                    {
                        sceneInfo.status = ConversionStatus.NoMapCameraFound;
                        sceneInfo.componentIssues = $"未找到主节点: {mainNodeName}";
                    }
                }
                finally
                {
                    EditorSceneManager.CloseScene(scene, true);
                }
            }
            catch (System.Exception e)
            {
                LogMessage($"检查场景 {sceneInfo.sceneName} 时出错: {e.Message}");
                sceneInfo.status = ConversionStatus.Failed;
                sceneInfo.componentIssues = e.Message;
            }
        }

        private void DetermineSceneStatus(SceneInfo sceneInfo)
        {
            if (sceneInfo.hasMinimapCamera && sceneInfo.hasTopViewMapCamera)
            {
                sceneInfo.status = ConversionStatus.UsingBothComponents;
            }
            else if (sceneInfo.hasMinimapCamera && !sceneInfo.hasTopViewMapCamera)
            {
                sceneInfo.status = ConversionStatus.UsingMinimapOnly;
            }
            else if (!sceneInfo.hasMinimapCamera && sceneInfo.hasTopViewMapCamera)
            {
                if (!string.IsNullOrEmpty(sceneInfo.componentIssues))
                {
                    sceneInfo.status = ConversionStatus.TopViewIncorrectlyConfigured;
                }
                else
                {
                    sceneInfo.status = ConversionStatus.UsingTopViewOnly;
                }
            }
            else
            {
                sceneInfo.status = ConversionStatus.NoMapCameraFound;
            }
        }

        private GameObject FindTopViewMapCameraObjectInHierarchy(GameObject parent)
        {
            // 查找TopViewMapCamera组件
            var topViewMapCamera = parent.GetComponentInChildren<TopViewMapCamera>();
            return topViewMapCamera?.gameObject;
        }

        private string ValidateTopViewMapCameraConfiguration(GameObject topViewCameraObj)
        {
            var issues = new List<string>();
            var topViewCamera = topViewCameraObj.GetComponent<TopViewMapCamera>();
            
            if (topViewCamera == null)
            {
                issues.Add("TopViewMapCamera组件缺失");
                return string.Join("; ", issues);
            }
            
            // 检查Camera组件
            var camera = topViewCameraObj.GetComponent<Camera>();
            if (camera == null)
            {
                issues.Add("Camera组件缺失");
            }
            else
            {
                if (!camera.orthographic)
                {
                    issues.Add("相机未设置为正交投影");
                }
            }
            
            // 检查地图纹理
            if (topViewCamera.MapTexture == null)
            {
                issues.Add("地图纹理未设置");
            }
            
            // 检查纹理尺寸
            if (topViewCamera.MapTextureWidth <= 0 || topViewCamera.MapTextureHeight <= 0)
            {
                issues.Add("地图纹理尺寸无效");
            }
            
            return string.Join("; ", issues);
        }

        private void ConvertScenes(List<SceneInfo> scenesToConvert)
        {
            logMessages = "";
            float totalScenes = scenesToConvert.Count;
            int processedScenes = 0;

            foreach (var sceneInfo in scenesToConvert)
            {
                conversionProgress = processedScenes / totalScenes;
                
                try
                {
                    ConvertSingleScene(sceneInfo);
                }
                catch (System.Exception e)
                {
                    sceneInfo.status = ConversionStatus.Failed;
                    sceneInfo.componentIssues = e.Message;
                    LogMessage($"转换场景 {sceneInfo.sceneName} 失败: {e.Message}");
                }

                processedScenes++;
                EditorUtility.DisplayProgressBar("转换进度", 
                    $"正在处理: {sceneInfo.sceneName}", 
                    conversionProgress);
            }

            conversionProgress = 1f;
            EditorUtility.ClearProgressBar();
            
            // 重新扫描所有场景以获取最新状态
            foreach (var sceneInfo in foundScenes)
            {
                CheckSceneForMapCameraComponents(sceneInfo);
            }
            
            // 更新统计信息
            UpdateStatistics();
            
            LogMessage("批量转换完成！");
            LogMessage(GetStatisticsSummary());
            
            // 刷新资源
            AssetDatabase.Refresh();
        }

        private void ConvertSingleScene(SceneInfo sceneInfo)
        {
            // 检查场景是否需要转换
            if (sceneInfo.status != ConversionStatus.UsingMinimapOnly && 
                sceneInfo.status != ConversionStatus.UsingBothComponents)
            {
                LogMessage($"场景 {sceneInfo.sceneName}: 无需转换 (当前状态: {sceneInfo.status})");
                return;
            }

            // 打开场景
            var scene = EditorSceneManager.OpenScene(sceneInfo.scenePath, OpenSceneMode.Single);
            
            try
            {
                GameObject mainNode = FindMainNode(scene);
                if (mainNode == null)
                {
                    throw new System.Exception($"未找到主节点: {mainNodeName}");
                }

                var minimapCameraObj = FindMinimapCameraObjectInHierarchy(mainNode);
                if (minimapCameraObj == null)
                {
                    throw new System.Exception("未找到MinimapCamera组件");
                }

                // 检查是否已经有TopViewMapCamera组件
                var existingTopView = minimapCameraObj.GetComponent<TopViewMapCamera>();
                if (existingTopView != null)
                {
                    // 如果已存在TopViewMapCamera，只需删除MinimapCamera
                    var oldMinimapCamera = minimapCameraObj.GetComponent<MinimapCamera>();
                    if (oldMinimapCamera != null)
                    {
                        DestroyImmediate(oldMinimapCamera);
                        LogMessage($"场景 {sceneInfo.sceneName}: 已删除重复的MinimapCamera组件");
                    }
                    
                    sceneInfo.status = ConversionStatus.Success;
                    LogMessage($"场景 {sceneInfo.sceneName}: 清理完成");
                }
                else
                {
                    // 执行完整的组件转换
                    bool success = ConvertMinimapCameraComponent(minimapCameraObj);
                    
                    if (success)
                    {
                        sceneInfo.status = ConversionStatus.Success;
                        LogMessage($"场景 {sceneInfo.sceneName}: 转换成功");
                    }
                    else
                    {
                        sceneInfo.status = ConversionStatus.Failed;
                        LogMessage($"场景 {sceneInfo.sceneName}: 转换失败");
                    }
                }
                
                // 保存场景
                if (sceneInfo.status == ConversionStatus.Success)
                {
                    EditorSceneManager.MarkSceneDirty(scene);
                    EditorSceneManager.SaveScene(scene);
                }
            }
            catch (System.Exception e)
            {
                sceneInfo.status = ConversionStatus.Failed;
                sceneInfo.componentIssues = e.Message;
                LogMessage($"场景 {sceneInfo.sceneName}: {e.Message}");
            }
        }

        private bool ConvertMinimapCameraComponent(GameObject targetObject)
        {
            try
            {
                // 获取旧的MinimapCamera组件
                var oldMinimapCamera = targetObject.GetComponent<MinimapCamera>();
                if (oldMinimapCamera == null)
                {
                    LogMessage("未找到MinimapCamera组件");
                    return false;
                }

                // 获取Camera组件
                var cameraComponent = targetObject.GetComponent<Camera>();
                if (cameraComponent == null)
                {
                    LogMessage("未找到Camera组件");
                    return false;
                }

                // 检查是否已经有TopViewMapCamera组件
                var existingTopViewCamera = targetObject.GetComponent<TopViewMapCamera>();
                if (existingTopViewCamera != null)
                {
                    LogMessage($"对象 {targetObject.name} 已存在TopViewMapCamera组件");
                    return true;
                }

                // 备份旧MinimapCamera组件的数据
                var minimapData = BackupMinimapCameraData(oldMinimapCamera);
                var cameraData = BackupCameraData(cameraComponent);

                // 添加新的TopViewMapCamera组件
                var newTopViewCamera = targetObject.AddComponent<TopViewMapCamera>();

                // 恢复数据到新组件
                RestoreDataToTopViewCamera(newTopViewCamera, minimapData);

                // 删除旧的MinimapCamera组件
                DestroyImmediate(oldMinimapCamera);

                LogMessage($"成功转换组件: {targetObject.name} (MinimapCamera -> TopViewMapCamera)");
                return true;
            }
            catch (System.Exception e)
            {
                LogMessage($"转换组件时出错: {e.Message}");
                return false;
            }
        }

        #endregion

        #region Helper Methods

        private GameObject FindMainNode(Scene scene)
        {
            var rootObjects = scene.GetRootGameObjects();
            return rootObjects.FirstOrDefault(obj => obj.name == mainNodeName);
        }

        private GameObject FindMinimapCameraObjectInHierarchy(GameObject parent)
        {
            // 直接查找MinimapCamera组件（它存在于其他dll中）
            var minimapCamera = parent.GetComponentInChildren<MinimapCamera>();
            return minimapCamera?.gameObject;
        }

        private string GetGameObjectPath(GameObject obj)
        {
            string path = obj.name;
            Transform parent = obj.transform.parent;
            
            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }
            
            return path;
        }

        private List<SceneInfo> GetSelectedScenes()
        {
            // 返回需要转换的场景（只使用MinimapCamera的场景）
            return foundScenes.Where(scene => 
                scene.status == ConversionStatus.UsingMinimapOnly ||
                scene.status == ConversionStatus.UsingBothComponents).ToList();
        }

        private void LogMessage(string message)
        {
            string timestamp = System.DateTime.Now.ToString("HH:mm:ss");
            logMessages += $"[{timestamp}] {message}\n";
            Debug.Log($"[MinimapConverter] {message}");
        }

        #endregion

        #region Data Backup and Restore

        [System.Serializable]
        public class MinimapCameraData
        {
            public Texture2D mapTexture;
            public int mapTextureWidth;
            public int mapTextureHeight;
            public Camera sceneCamera;
            public GameObject[] ignoreList;
            public float snapEulerAnglesY;
        }

        [System.Serializable]
        public class CameraData
        {
            public bool orthographic;
            public float orthographicSize;
            public float fieldOfView;
            public float nearClipPlane;
            public float farClipPlane;
            public Vector3 position;
            public Quaternion rotation;
            public int cullingMask;
            public Color backgroundColor;
            public CameraClearFlags clearFlags;
            public float depth;
            public RenderingPath renderingPath;
            public bool useOcclusionCulling;
            public bool allowHDR;
            public bool allowMSAA;
        }

        private MinimapCameraData BackupMinimapCameraData(MinimapCamera oldCamera)
        {
            return new MinimapCameraData
            {
                mapTexture = oldCamera.MapTexture,
                mapTextureWidth = oldCamera.MapTextureWidth,
                mapTextureHeight = oldCamera.MapTextureHeight,
                sceneCamera = GetPrivateField<Camera>(oldCamera, "sceneCamera"),
                ignoreList = GetPrivateField<GameObject[]>(oldCamera, "ignoreList"),
                snapEulerAnglesY = GetPrivateField<float>(oldCamera, "snapEulerAnglesY")
            };
        }

        private CameraData BackupCameraData(Camera camera)
        {
            return new CameraData
            {
                orthographic = camera.orthographic,
                orthographicSize = camera.orthographicSize,
                fieldOfView = camera.fieldOfView,
                nearClipPlane = camera.nearClipPlane,
                farClipPlane = camera.farClipPlane,
                position = camera.transform.position,
                rotation = camera.transform.rotation,
                cullingMask = camera.cullingMask,
                backgroundColor = camera.backgroundColor,
                clearFlags = camera.clearFlags,
                depth = camera.depth,
                renderingPath = camera.renderingPath,
                useOcclusionCulling = camera.useOcclusionCulling,
                allowHDR = camera.allowHDR,
                allowMSAA = camera.allowMSAA
            };
        }

        private void RestoreDataToTopViewCamera(TopViewMapCamera newCamera, MinimapCameraData oldData)
        {
            // 设置公共属性
            if (oldData.mapTexture != null)
            {
                newCamera.MapTexture = oldData.mapTexture;
            }
            
            // 使用反射设置私有字段
            SetPrivateField(newCamera, "mapTextureWidth", oldData.mapTextureWidth);
            SetPrivateField(newCamera, "mapTextureHeight", oldData.mapTextureHeight);
            SetPrivateField(newCamera, "snapEulerAnglesY", oldData.snapEulerAnglesY);
            
            // 设置场景相机引用
            if (oldData.sceneCamera != null)
            {
                SetPrivateField(newCamera, "sceneCamera", oldData.sceneCamera);
            }
            
            // 设置忽略列表
            if (oldData.ignoreList != null)
            {
                SetPrivateField(newCamera, "ignoreList", oldData.ignoreList);
            }
            
            LogMessage($"已迁移数据: 纹理尺寸={oldData.mapTextureWidth}x{oldData.mapTextureHeight}, " +
                      $"快照角度={oldData.snapEulerAnglesY}, " +
                      $"忽略对象数量={oldData.ignoreList?.Length ?? 0}");
        }

        private T GetPrivateField<T>(object target, string fieldName)
        {
            try
            {
                var field = target.GetType().GetField(fieldName, 
                    System.Reflection.BindingFlags.NonPublic | 
                    System.Reflection.BindingFlags.Instance);
                    
                if (field != null)
                {
                    return (T)field.GetValue(target);
                }
                else
                {
                    LogMessage($"警告: 未找到字段 {fieldName}");
                    return default(T);
                }
            }
            catch (System.Exception e)
            {
                LogMessage($"获取字段 {fieldName} 失败: {e.Message}");
                return default(T);
            }
        }

        private void SetPrivateField(object target, string fieldName, object value)
        {
            try
            {
                var field = target.GetType().GetField(fieldName, 
                    System.Reflection.BindingFlags.NonPublic | 
                    System.Reflection.BindingFlags.Instance);
                    
                if (field != null)
                {
                    field.SetValue(target, value);
                }
                else
                {
                    LogMessage($"警告: 未找到字段 {fieldName}");
                }
            }
            catch (System.Exception e)
            {
                LogMessage($"设置字段 {fieldName} 失败: {e.Message}");
            }
        }

        #endregion
    }
} 