﻿using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.Universal;

[RequireComponent(typeof(Camera))]
[DisallowMultipleComponent]
public class URPCamera : MonoBehaviour
{
    public bool m_AutoOnCameraEnableToBase = false;
    private Camera m_Camera;
    private CameraRenderType m_CameraRenderType = CameraRenderType.Overlay;
    private UniversalAdditionalCameraData m_CameraData;
    private bool m_CameraEnabled = false;

    private static Camera m_OnlyBaseCamera;
    private static UniversalAdditionalCameraData m_OnlyBaseCameraData;
    private static List<URPCamera> m_OverlayCameras = new();

    void Awake()
    {
        if (TryGetComponent<Camera>(out m_Camera))
        {
            //Debug.LogError($"Awake    {m_Camera.name}");
            m_CameraEnabled = m_Camera.enabled;
            m_CameraData = m_Camera.GetUniversalAdditionalCameraData();
            m_CameraRenderType = m_CameraData.renderType;
            InitializeCamera();
            UpdateBaseCameraStack();
        }
    }

    private void InitializeCamera()
    {
        if (m_CameraRenderType == CameraRenderType.Base)
        {
            string m_OnlyBaseCameraname = m_OnlyBaseCamera != null ? m_OnlyBaseCamera.name : null;
            //Debug.LogError($"InitializeCamera {m_Camera.name} {m_OnlyBaseCameraname}  {m_OnlyBaseCamera == null}  {m_OnlyBaseCameraData == null}");
            if (m_OnlyBaseCamera == null || m_OnlyBaseCameraData == null)
            {
                m_OnlyBaseCamera = m_Camera;
                m_OnlyBaseCameraData = m_CameraData;
            }
        }
        else
        {
            m_OverlayCameras.Add(m_Camera.GetComponent<URPCamera>());
        }
    }

    void OnEnable()
    {
        if (m_AutoOnCameraEnableToBase && m_CameraEnabled)
        {
            ChangeCameraToBase();
        }
    }

    void Update()
    {
        if (m_Camera.enabled != m_CameraEnabled)
        {
            m_CameraEnabled = m_Camera.enabled;
            //Debug.LogError($"Update {m_Camera.name}  {m_CameraEnabled}");
            if (m_AutoOnCameraEnableToBase && m_CameraEnabled)
            {
                ChangeCameraToBase();
            }
        }
    }

    /*
     * 更新主摄像机的队列
     */
    private void UpdateBaseCameraStack()
    {
        if (m_OnlyBaseCameraData == null || m_OnlyBaseCameraData.renderType != CameraRenderType.Base) return;

        foreach (URPCamera c in m_OverlayCameras)
        {
            if (!m_OnlyBaseCameraData.cameraStack.Contains(c.m_Camera))
            {
                m_OnlyBaseCameraData.cameraStack.Add(c.m_Camera);
            }
        }

        m_OnlyBaseCameraData.cameraStack.Sort((A, B) => { return A.depth.CompareTo(B.depth); });
    }

    private void OnBaseCameraRemove()
    {
        if (m_Camera == m_OnlyBaseCamera)
        {
            m_OnlyBaseCamera = null;
            m_OnlyBaseCameraData = null;
        }
    }

    private void OnOverlayCameraRemove()
    {
        m_OverlayCameras.Remove(this);
        if (m_OnlyBaseCameraData == null || m_OnlyBaseCameraData.renderType != CameraRenderType.Base) return;
        if (m_OnlyBaseCameraData.cameraStack.Contains(m_Camera))
        {
            m_OnlyBaseCameraData.cameraStack.Remove(m_Camera);
            m_OnlyBaseCameraData.cameraStack.Sort((A, B) => { return A.depth.CompareTo(B.depth); });
        }
    }

    public void ChangeCameraToBase()
    {
        //Debug.LogError($"ChangeCameraToBase {m_OnlyBaseCamera != null}  {m_OnlyBaseCameraData != null}  {m_OnlyBaseCamera.isActiveAndEnabled} {m_OnlyBaseCamera.name} {m_Camera.name} ");
        if (m_OnlyBaseCamera != null && m_OnlyBaseCameraData != null && m_OnlyBaseCamera.isActiveAndEnabled && m_OnlyBaseCamera != m_Camera)
        {
            //Debug.LogError($"--1--  {m_Camera.name}");
            URPCamera oldBaseUrpCamera = m_OnlyBaseCamera.GetComponent<URPCamera>();
            if (oldBaseUrpCamera != null && !oldBaseUrpCamera.m_AutoOnCameraEnableToBase)
            {
                oldBaseUrpCamera.m_CameraRenderType = CameraRenderType.Overlay;
                m_OnlyBaseCameraData.renderType = CameraRenderType.Overlay;
                m_OverlayCameras.Add(oldBaseUrpCamera);
            }
        }

        m_CameraData.renderType = CameraRenderType.Base;
        m_CameraRenderType = CameraRenderType.Base;
        m_OverlayCameras.Remove(this);
        m_OnlyBaseCamera = m_Camera;
        m_OnlyBaseCameraData = m_CameraData;
        //Debug.LogError($"--2--  {m_Camera.name}  {m_OnlyBaseCameraData == null}");
        UpdateBaseCameraStack();
    }

    private void OnDestroy()
    {
        if (m_CameraRenderType == CameraRenderType.Base)
        {
            OnBaseCameraRemove();
            if (m_CameraData != null)
            {
                m_CameraData.cameraStack.Clear();
            }
        }
        else
        {
            OnOverlayCameraRemove();
        }
    }
}