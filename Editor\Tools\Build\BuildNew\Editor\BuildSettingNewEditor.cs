using Nirvana.Editor;
using System;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

[CustomEditor(typeof(BuildSettingNew))]
internal sealed class BuildSettingNewEditor : Editor
{
    public override void OnInspectorGUI()
    {
        BuildSettingNew buildSetting = (BuildSettingNew)base.target;
        base.serializedObject.Update();
        EditorGUILayout.PropertyField(this.buildName, Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.buildDevice, Array.Empty<GUILayoutOption>());
        EditorGUI.BeginChangeCheck();
        this.configList.DoLayoutList();
        bool flag = EditorGUI.EndChangeCheck();
        if (flag)
        {
            buildSetting.RefreshBuildConfig();
        }
        this.customProcessList.DoLayoutList();
        GUILayoutEx.BeginContents();
        this.DrawPlayer();
        GUILayoutEx.EndContents();
        base.serializedObject.ApplyModifiedProperties();
        this.DrawBuildConfig();
        this.DrawDeviceConfig();
    }

    private void DrawBuildConfig()
    {
        BuildSettingNewEditor.showBasicConfig = GUILayoutEx.Title("Config Basic", BuildSettingNewEditor.showBasicConfig);
        bool flag = !BuildSettingNewEditor.showBasicConfig;
        if (!flag)
        {
            GUILayoutEx.BeginContents();
            BuildSettingNew buildSetting = (BuildSettingNew)base.target;
            BuildConfigDataNew configData = buildSetting.ConfigData;
            EditorGUILayout.LabelField("Company Name:", configData.CompanyName, Array.Empty<GUILayoutOption>());
            EditorGUILayout.LabelField("Product Name:", configData.ProductName, Array.Empty<GUILayoutOption>());
            EditorGUILayout.LabelField("Version:", configData.Version, Array.Empty<GUILayoutOption>());
            EditorGUILayout.PrefixLabel("Define Symbols:");
            GUILayoutEx.BeginContents();
            bool flag2 = configData.DefineSymbols == null || configData.DefineSymbols.Length == 0;
            if (flag2)
            {
                GUILayout.Label("Empty", Array.Empty<GUILayoutOption>());
            }
            else
            {
                foreach (string text in configData.DefineSymbols)
                {
                    GUILayout.Label(text, Array.Empty<GUILayoutOption>());
                }
            }
            GUILayoutEx.EndContents();
            EditorGUILayout.PrefixLabel("Scenes:");
            GUILayoutEx.BeginContents();
            bool flag3 = configData.Scenes == null || configData.Scenes.Length == 0;
            if (flag3)
            {
                GUILayout.Label("Empty", Array.Empty<GUILayoutOption>());
            }
            else
            {
                foreach (string text2 in configData.Scenes)
                {
                    GUILayout.Label(text2, Array.Empty<GUILayoutOption>());
                }
            }
            GUILayoutEx.EndContents();
            bool flag4 = configData.SplashScreen != null;
            if (flag4)
            {
                EditorGUILayout.PrefixLabel("Splash Screen:");
                EditorGUILayout.ObjectField(configData.SplashScreen, configData.SplashScreen.GetType(), false, Array.Empty<GUILayoutOption>());
            }
            GUILayoutEx.EndContents();
        }
    }

    private void DrawDeviceConfig()
    {
        BuildSettingNew buildSetting = (BuildSettingNew)base.target;
        BuildConfigDataNew configData = buildSetting.ConfigData;
        switch (this.buildDevice.enumValueIndex)
        {
            case 0:
                {
                    BuildSettingNewEditor.showDeviceConfig = GUILayoutEx.Title("Config iOS", BuildSettingNewEditor.showDeviceConfig);
                    bool flag = BuildSettingNewEditor.showDeviceConfig;
                    if (flag)
                    {
                        GUILayoutEx.BeginContents();
                        this.DrawBuildIOSConfig(configData);
                        GUILayoutEx.EndContents();
                    }
                    break;
                }
            case 1:
                {
                    BuildSettingNewEditor.showDeviceConfig = GUILayoutEx.Title("Config Android", BuildSettingNewEditor.showDeviceConfig);
                    bool flag2 = BuildSettingNewEditor.showDeviceConfig;
                    if (flag2)
                    {
                        GUILayoutEx.BeginContents();
                        this.DrawBuildAndroidConfig(configData);
                        GUILayoutEx.EndContents();
                    }
                    break;
                }
            case 2:
            case 3:
                {
                    BuildSettingNewEditor.showDeviceConfig = GUILayoutEx.Title("Config Desktop", BuildSettingNewEditor.showDeviceConfig);
                    bool flag3 = BuildSettingNewEditor.showDeviceConfig;
                    if (flag3)
                    {
                        GUILayoutEx.BeginContents();
                        this.DrawBuildDesktopConfig(configData);
                        GUILayoutEx.EndContents();
                    }
                    break;
                }
        }
    }

    private void DrawBuildIOSConfig(BuildConfigDataNew config)
    {
        EditorGUILayout.LabelField("Application Identifier:", config.IOSApplicationIdentifier, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Provisioning Profile:", config.IOSProvisioningProfile, Array.Empty<GUILayoutOption>());
        EditorGUILayout.Toggle("Enable BitCode:", config.IOSEnableBitCode, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Code SignIdentity:", config.IOSCodeSignIdentity, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Bundle Number:", config.IOSBuildNumber, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Application Display Name:", config.IOSApplicationDisplayName, Array.Empty<GUILayoutOption>());
        EditorGUILayout.Toggle("Enable Automatic Signing:", config.IOSEnableAutomaticSigning, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Developer Team ID:", config.IOSDeveloperTeamID, Array.Empty<GUILayoutOption>());
        EditorGUILayout.LabelField("Enable bit code:", config.IOSEnableBitCode ? "YES" : "NO", Array.Empty<GUILayoutOption>());
        bool flag = config.IOSFrameworks != null;
        if (flag)
        {
            EditorGUILayout.LabelField("Frameworks:", Array.Empty<GUILayoutOption>());
            GUILayoutEx.BeginContents();
            foreach (string text in config.IOSFrameworks)
            {
                EditorGUILayout.LabelField(text, Array.Empty<GUILayoutOption>());
            }
            GUILayoutEx.EndContents();
        }
        EditorGUILayout.ObjectField("Icon 180x180", config.IOSIcon180, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 167x167", config.IOSIcon167, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 152x152", config.IOSIcon152, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 144x144", config.IOSIcon144, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 120x120", config.IOSIcon120, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 114x114", config.IOSIcon114, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 76x76", config.IOSIcon76, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 72x72", config.IOSIcon72, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 57x57", config.IOSIcon57, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Spotlight Icon 120x120", config.IOSSpotlightIcon120, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Spotlight Icon 80x80", config.IOSSpotlightIcon80, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Spotlight Icon 40x40", config.IOSSpotlightIcon40, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Settings Icon 87x87", config.IOSSettingsIcon87, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Settings Icon 58x58", config.IOSSettingsIcon58, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Settings Icon 29x29", config.IOSSettingsIcon29, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Notification Icon 60x60", config.IOSNotificationIcon60, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Notification Icon 40x40", config.IOSNotificationIcon40, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Notification Icon 20x20", config.IOSNotificationIcon20, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("AppstoreIcon Icon 1024x1024", config.IOSAppstoreIcon1024, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
    }

    private void DrawBuildAndroidConfig(BuildConfigDataNew config)
    {
        EditorGUILayout.LabelField("Application Identifier:", config.AndroidApplicationIdentifier, Array.Empty<GUILayoutOption>());
        EditorGUILayout.IntField("Bundle Version Code:", config.AndroidBundleVersionCode, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 192x192", config.AndroidIcon192, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 144x144", config.AndroidIcon144, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 96x96", config.AndroidIcon96, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 72x72", config.AndroidIcon72, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 48x48", config.AndroidIcon48, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 36x36", config.AndroidIcon36, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
    }

    private void DrawBuildDesktopConfig(BuildConfigDataNew config)
    {
        EditorGUILayout.ToggleLeft("Default Is FullScreen", config.DesktopDefaultIsFullScreen, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ToggleLeft("Default Is Native Resolution", config.DesktopDefaultIsNativeResolution, Array.Empty<GUILayoutOption>());
        EditorGUILayout.IntField("Default Screen Width", config.DesktopDefaultScreenWidth, Array.Empty<GUILayoutOption>());
        EditorGUILayout.IntField("Default Screen Height", config.DesktopDefaultScreenHeight, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ToggleLeft("Resizable Window", config.DesktopResizableWindow, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 1024x1024", config.DesktopIcon1024, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 512x512", config.DesktopIcon512, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 256x256", config.DesktopIcon256, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 128x128", config.DesktopIcon128, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 48x48", config.DesktopIcon48, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 32x32", config.DesktopIcon32, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
        EditorGUILayout.ObjectField("Icon 16x16", config.DesktopIcon16, typeof(Texture2D), false, Array.Empty<GUILayoutOption>());
    }

    private void DrawPlayer()
    {
        switch (this.buildDevice.intValue)
        {
            case 0:
                this.DrawIOSPlayer();
                break;
            case 1:
                this.DrawAndroidPlayer();
                break;
            case 2:
            case 3:
                this.DrawDesktopPlayer();
                break;
        }
    }

    private void DrawIOSPlayer()
    {
        EditorGUILayout.PropertyField(this.development, new GUIContent("Development"), Array.Empty<GUILayoutOption>());
        GUI.enabled = this.development.boolValue;
        EditorGUILayout.PropertyField(this.allowDebugging, new GUIContent("Allow Debugging"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectToHost, new GUIContent("Connect With Host"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectWithProfiler, new GUIContent("Connect With Profiler"), Array.Empty<GUILayoutOption>());
        GUI.enabled = true;
    }

    private void DrawAndroidPlayer()
    {
        EditorGUILayout.PropertyField(this.development, new GUIContent("Development"), Array.Empty<GUILayoutOption>());
        GUI.enabled = this.development.boolValue;
        EditorGUILayout.PropertyField(this.allowDebugging, new GUIContent("Allow Debugging"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectToHost, new GUIContent("Connect With Host"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectWithProfiler, new GUIContent("Connect With Profiler"), Array.Empty<GUILayoutOption>());
        GUI.enabled = true;
    }

    private void DrawDesktopPlayer()
    {
        EditorGUILayout.PropertyField(this.development, new GUIContent("Development"), Array.Empty<GUILayoutOption>());
        GUI.enabled = this.development.boolValue;
        EditorGUILayout.PropertyField(this.allowDebugging, new GUIContent("Allow Debugging"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectToHost, new GUIContent("Connect With Host"), Array.Empty<GUILayoutOption>());
        EditorGUILayout.PropertyField(this.connectWithProfiler, new GUIContent("Connect With Profiler"), Array.Empty<GUILayoutOption>());
        GUI.enabled = true;
    }

    private void OnEnable()
    {
        BuildSettingNew buildSetting = (BuildSettingNew)base.target;
        buildSetting.RefreshBuildConfig();
        SerializedObject serializedObject = base.serializedObject;
        this.buildName = serializedObject.FindProperty("buildName");
        this.buildDevice = serializedObject.FindProperty("buildDevice");
        this.configs = serializedObject.FindProperty("configs");
        this.customProcesses = serializedObject.FindProperty("customProcesses");
        this.development = serializedObject.FindProperty("development");
        this.allowDebugging = serializedObject.FindProperty("allowDebugging");
        this.connectToHost = serializedObject.FindProperty("connectToHost");
        this.connectWithProfiler = serializedObject.FindProperty("connectWithProfiler");
        this.configList = new ReorderableList(base.serializedObject, this.configs);
        this.configList.drawHeaderCallback = delegate (Rect rect)
        {
            GUI.Label(rect, "Build Config:");
        };
        this.configList.elementHeight = EditorGUIUtility.singleLineHeight;
        this.configList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            SerializedProperty arrayElementAtIndex = this.configs.GetArrayElementAtIndex(index);
            EditorGUI.PropertyField(rect, arrayElementAtIndex);
        };
        this.customProcessList = new ReorderableList(base.serializedObject, this.customProcesses);
        this.customProcessList.drawHeaderCallback = delegate (Rect rect)
        {
            GUI.Label(rect, "Custom Processes:");
        };
        this.customProcessList.elementHeight = EditorGUIUtility.singleLineHeight;
        this.customProcessList.drawElementCallback = delegate (Rect rect, int index, bool selected, bool focused)
        {
            SerializedProperty arrayElementAtIndex = this.customProcesses.GetArrayElementAtIndex(index);
            EditorGUI.PropertyField(rect, arrayElementAtIndex);
        };
    }

    // Token: 0x04000395 RID: 917
    private static bool showBasicConfig;

    // Token: 0x04000396 RID: 918
    private static bool showDeviceConfig;

    // Token: 0x04000397 RID: 919
    private SerializedProperty buildName;

    // Token: 0x04000398 RID: 920
    private SerializedProperty buildDevice;

    // Token: 0x04000399 RID: 921
    private SerializedProperty configs;

    // Token: 0x0400039A RID: 922
    private SerializedProperty customProcesses;

    // Token: 0x0400039B RID: 923
    private SerializedProperty development;

    // Token: 0x0400039C RID: 924
    private SerializedProperty allowDebugging;

    // Token: 0x0400039D RID: 925
    private SerializedProperty connectToHost;

    // Token: 0x0400039E RID: 926
    private SerializedProperty connectWithProfiler;

    // Token: 0x0400039F RID: 927
    private ReorderableList configList;

    // Token: 0x040003A0 RID: 928
    private ReorderableList customProcessList;
}