﻿using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana.Editor;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System;

namespace Build
{
    public class ShaderLabOptimize
    {
        public static string prefix = "Variants_";
        private static string autoMaterialsDir = "Assets/Game/Shaders/Materials";
        private static string variantShadersDir = "Assets/Game/Shaders/Materials/VariantsShaders";
        public static bool isOpenOptimize = false;

        [MenuItem("自定义工具/发布版本/ShaderLab优化/创建变种shader")]
        public static void CreateVariantsShaders()
        {
            if (!isOpenOptimize)
                return;

            var dirPath = string.Format("{0}/../{1}", Application.dataPath, variantShadersDir);
            if (!Directory.Exists(dirPath))
                Directory.CreateDirectory(dirPath);

            string[] guids = AssetDatabase.FindAssets("t:material", new string[] { autoMaterialsDir });
            var checkCount = 0;
            HashSet<string> set = new HashSet<string>();
            foreach (var guid in guids)
            {
                //if (checkCount >= 10)
                //    break;

                var matPath = AssetDatabase.GUIDToAssetPath(guid);
                var material = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                var shader = material.shader;
                var keywordStr = GetKeywordsStr(material);
                var variantShaderName = prefix + shader.name + keywordStr;
                variantShaderName = variantShaderName.Replace("/", "_");
                variantShaderName = filterAdditionalKeywords(variantShaderName); //把动态设置的keyworld去掉，不需要生成
                // D:/uc01_cn/client/u3d_proj/Assets/../Assets/Game/Shaders/Materials/VariantsShaders/Varinats_YifStandard_1.shader
                if (!set.Contains(variantShaderName) && CreateVariantShader(shader, variantShaderName))
                {
                    set.Add(variantShaderName);
                    checkCount++;
                }
            }

            AssetDatabase.Refresh();
        }

        private static string GetKeywordsStr(Material material)
        {
            var name = material.name;
            var keywordStr = name.Replace("YY_YYStandardT4M", "")
                                    .Replace("YY_YYStandardMap", "")
                                    .Replace("YY_YYStandard", "")
                                    .Replace("YifStandard", "");
            keywordStr = keywordStr.Replace("YifStandard_YifStandardActor", "")
                                    .Replace("YifStandard_YifStandardEnvironments", "")
                                    .Replace("YifStandard_YifStandardParticle", "");
            keywordStr = keywordStr.Replace("Srp_Standard_EffectCf", "")
                                    .Replace("Srp_Standard_GPUTerrainCf", "")
                                    .Replace("Srp_Standard_SrpMap_PbrCf", "")
                                    .Replace("Srp_Standard_SrpRole_PbrCf", "")
                                    .Replace("Srp_Standard_SrpRoleHairAlphaTestPass_PbrCf", "")
                                    .Replace("Srp_Standard_SrpTerrainCf", "");
            keywordStr = keywordStr.Replace("Srp_DistortCf", "")
                                    .Replace("Srp_DistortMaskCf", "")
                                    .Replace("Srp_GrassCf", "")
                                    .Replace("UniversalRenderPipeline_Terrain_Lit", "")
                                    .Replace("SrpFeatures_SrpPlanarWaterCf", "")
                                    .Replace("SrpFeatures_SrpPlanarUIWaterCf", "")
                                    .Replace("Srp_Terrain_URPTerrainCf", "")
                                    .Replace("Srp_Terrain_URPTerrainAddCf", "")
                                    .Replace("JYShaders_StylizedScene", "")
                                    .Replace("JYShaders_StylizedTerrain", "")
                                    .Replace("JYShaders_StylizedFace", "")
                                    .Replace("JYShaders_StylizedHair", "")
                                    .Replace("JYShaders_StylizedSkin", "")
                                    .Replace("JYShaders_StylizedEyes", "")
                                    .Replace("JYShaders_StylizedEyeLash", "");

            return keywordStr;
        }

        public static bool IsValidShader(Shader shader)
        {
            if (null == shader)
                return false;

            return shader.name == "YifStandard"
                || shader.name == "YifStandard/YifStandardActor"
                || shader.name == "YifStandard/YifStandardEnvironments"
                || shader.name == "YifStandard/YifStandardParticle"
                || shader.name == "YY/YYStandard"
                || shader.name == "YY/YYStandardMap"
                || shader.name == "YY/YYStandardT4M"

                || shader.name == "Srp/Standard/EffectCf"
                || shader.name == "Srp/Standard/GPUTerrainCf"
                || shader.name == "Srp/Standard/SrpMap_PbrCf"
                || shader.name == "Srp/Standard/SrpRole_PbrCf"
                || shader.name == "Srp/Standard/SrpRoleHairAlphaTestPass_PbrCf"
                || shader.name == "Srp/Standard/SrpTerrainCf"

                || shader.name == "Srp/DistortCf"
                || shader.name == "Srp/DistortMaskCf"
                || shader.name == "Srp/GrassCf"
                || shader.name == "JYShaders/ScreenEffect"

                || shader.name == "Universal Render Pipeline/Terrain/Lit"
                || shader.name == "Srp/Terrain/URPTerrainCf"
                || shader.name == "Srp/Terrain/URPTerrainAddCf"
                || shader.name == "SrpFeatures/SrpPlanarWaterCf"
                || shader.name == "SrpFeatures/SrpPlanarUIWaterCf"
                || shader.name == "JYShaders/StylizedScene"
                || shader.name == "JYShaders/StylizedTerrain"
                || shader.name == "JYShaders/StylizedFace"
                || shader.name == "JYShaders/StylizedHair"
                || shader.name == "JYShaders/StylizedSkin"
                || shader.name == "JYShaders/StylizedEyes"
                || shader.name == "JYShaders/StylizedGem"
                || shader.name == "JYShaders/StylizedEyeLash";
        }

        private static bool CreateVariantShader(Shader shader, string variantShaderName)
        {
            if (!IsValidShader(shader))
                return false;

            string[] strs1 = new string[]
            {
                "Shader \"YifStandard\"",
                "#include \"YifShadow.cginc\"",
                "#include \"YifStandardCore.cginc\"",

                "Shader \"YY/YYStandard\"",
                "Shader \"YY/YYStandardMap\"",
                "Shader \"YY/YYStandardT4M\"",
                "#include \"./Include/YYInclude.cginc\"",
                "#include \"./Include/YYCodeFrag.cginc\"",
                "#include \"./Include/YYCodeVertex.cginc\"",
                "#include \"./Include/YYCodeAdd.cginc\"",
                "#include \"./Include/YYStandardShadow.cginc\"",
                "#include \"./Include/Map/YYCodeFragMap.cginc\"",
                "#include \"./Include/T4M/YYCodeFragT4M.cginc\"",
            };

            string[] strs2 = new string[]
            {
                string.Format("Shader \"{0}\"", variantShaderName),
                "#include \"Assets/Game/Shaders/YifStandard/YifShadow.cginc\"",
                "#include \"Assets/Game/Shaders/YifStandard/YifStandardCore.cginc\"",

                 string.Format("Shader \"{0}\"", variantShaderName),
                 string.Format("Shader \"{0}\"", variantShaderName),
                 string.Format("Shader \"{0}\"", variantShaderName),
                "#include \"Assets/Game/Shaders/YYStandard/Include/YYInclude.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/YYCodeFrag.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/YYCodeVertex.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/YYCodeAdd.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/YYStandardShadow.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/Map/YYCodeFragMap.cginc\"",
                "#include \"Assets/Game/Shaders/YYStandard/Include/T4M/YYCodeFragT4M.cginc\"",
            };

            var shaderPath = string.Format("{0}/../{1}", Application.dataPath, AssetDatabase.GetAssetPath(shader));
            var variantShaderPath = string.Format("{0}/../{1}/{2}.shader", Application.dataPath, variantShadersDir, variantShaderName);
            var content = File.ReadAllText(shaderPath);

            for (int i = 0; i < strs1.Length; i++)
            {
                content = content.Replace(strs1[i], strs2[i]);
            }

            File.WriteAllText(variantShaderPath, content);
            return true;
        }

        [MenuItem("自定义工具/发布版本/ShaderLab优化/恢复shader")]
        public static void ResumeOrignalShaders()
        {
            if (!isOpenOptimize)
                return;

            var guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets" });
            //var guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game/Shaders/Materials" });
            foreach (var guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                var material = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
                var shaderName = material.shader.name;
                var orignalShaderName = shaderName;
                if (shaderName.Contains("Variants_YY_YYStandardMap"))
                {
                    orignalShaderName = "YY/YYStandardMap";
                }
                else if (shaderName.Contains("Variants_YY_YYStandardT4M"))
                {
                    orignalShaderName = "YY/YYStandardT4M";
                }
                else if (shaderName.Contains("Variants_YY_YYStandard"))
                {
                    orignalShaderName = "YY/YYStandard";
                }
                else if (shaderName.Contains("Variants_YifStandard_YifStandardActor"))
                {
                    orignalShaderName = "YifStandard/YifStandardActor";
                }
                else if (shaderName.Contains("Variants_YifStandard_YifStandardEnvironments"))
                {
                    orignalShaderName = "YifStandard/YifStandardEnvironments";
                }
                else if (shaderName.Contains("Variants_YifStandard_YifStandardParticle"))
                {
                    orignalShaderName = "YifStandard/YifStandardParticle";
                }
                else if (shaderName.Contains("Variants_YifStandard"))
                {
                    orignalShaderName = "YifStandard";
                }
                //URP SRP Standard
                else if (shaderName.Contains("Variants_Srp_Standard_EffectCf"))
                {
                    orignalShaderName = "Srp/Standard/EffectCf";
                }
                else if (shaderName.Contains("Variants_Srp_Standard_GPUTerrainCf"))
                {
                    orignalShaderName = "Srp/Standard/GPUTerrainCf";
                }
                else if (shaderName.Contains("Variants_Srp_Standard_SrpMap_PbrCf"))
                {
                    orignalShaderName = "Srp/Standard/SrpMap_PbrCf";
                }
                else if (shaderName.Contains("Variants_Srp_Standard_SrpRole_PbrCf"))
                {
                    orignalShaderName = "Srp/Standard/SrpRole_PbrCf";
                }
                else if (shaderName.Contains("Variants_Srp_Standard_SrpRoleHairAlphaTestPass_PbrCf"))
                {
                    orignalShaderName = "Srp/Standard/SrpRoleHairAlphaTestPass_PbrCf";
                }
                else if (shaderName.Contains("Variants_Srp_Standard_SrpTerrainCf"))
                {
                    orignalShaderName = "Srp/Standard/SrpTerrainCf";
                }
                //URP SRP
                else if (shaderName.Contains("Variants_Srp_DistortCf"))
                {
                    orignalShaderName = "Srp/DistortCf";
                }
                else if (shaderName.Contains("Variants_Srp_DistortMaskCf"))
                {
                    orignalShaderName = "Srp/DistortMaskCf";
                }
                else if (shaderName.Contains("Variants_Srp_GrassCf"))
                {
                    orignalShaderName = "Srp/GrassCf";
                }
                else if (shaderName.Contains("Variants_SrpFeatures_SrpPlanarWaterCf"))
                {
                    orignalShaderName = "SrpFeatures/SrpPlanarWaterCf";
                }
                //URP Water Reflection
                else if (shaderName.Contains("Variants_SrpFeatures_SrpPlanarUIWaterCf"))
                {
                    orignalShaderName = "SrpFeatures/SrpPlanarUIWaterCf";
                }
                else if (shaderName.Contains("Variants_UniversalRenderPipeline_Terrain_Lit"))
                {
                    orignalShaderName = "Universal Render Pipeline/Terrain/Lit";
                }
                else if (shaderName.Contains("Variants_Srp_Terrain_URPTerrainCf"))
                {
                    orignalShaderName = "Srp/Terrain/URPTerrainCf";
                }
                else if (shaderName.Contains("Variants_Srp_Terrain_URPTerrainAddCf"))
                {
                    orignalShaderName = "Srp/Terrain/URPTerrainAddCf";
                }
                else if (shaderName.Contains("Variants_JYShaders_StylizedScene"))
                {
                    orignalShaderName = "JYShaders/StylizedScene";
                }
                else if (shaderName.Contains("Variants_JYShaders_StylizedTerrain"))
                {
                    orignalShaderName = "JYShaders/StylizedTerrain";
                }   
                else if (shaderName.Contains("Variants_JYShaders_StylizedFace"))
                {
                    orignalShaderName = "JYShaders/StylizedFace";
                }   
                else if (shaderName.Contains("Variants_JYShaders_StylizedHair"))
                {
                    orignalShaderName = "JYShaders/StylizedHair";
                }   
                else if (shaderName.Contains("Variants_JYShaders_StylizedEyes"))
                {
                    orignalShaderName = "JYShaders/StylizedEyes";
                }   
                else if (shaderName.Contains("Variants_JYShaders_StylizedSkin"))
                {
                    orignalShaderName = "JYShaders/StylizedSkin";
                }   
                else if (shaderName.Contains("Variants_JYShaders_StylizedEyeLash"))
                {
                    orignalShaderName = "JYShaders/StylizedEyeLash";
                }   

                if (shaderName != orignalShaderName)
                {
                    var orignalShader = Shader.Find(orignalShaderName);
                    if (null == orignalShader)
                    {
                        Debug.LogErrorFormat("ResumeOrignalShaders fail {0} {1} {2}", material.name, shaderName, orignalShaderName);
                        return;
                    }

                    var oldQueue = material.renderQueue;
                    material.shader = orignalShader;
                    material.renderQueue = oldQueue;
                    Debug.LogFormat("ResumeOrignalShaders succ, {0}, {1} {2}", material.name, shaderName, orignalShaderName);
                }
            }
        }

        [MenuItem("自定义工具/发布版本/ShaderLab优化/使用变种shader")]
        public static void UseVariantShaders()
        {
            if (!isOpenOptimize)
                return;

            var keywordStrIdDic = new Dictionary<string, int>();
            BuildMaterial.GetKeywordIdDic(keywordStrIdDic, new HashSet<int>());
            //  var guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets" });
            var guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game/Shaders/Materials" });
            int checkCount = 0;
            foreach (var guid in guids)
            {
                //if (checkCount >= 10)
                //    break;
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                var material = AssetDatabase.LoadAssetAtPath<Material>(assetPath);
                if (null == material || !IsValidShader(material.shader))
                {
                    continue;
                }

                var variantShader = GetVariantShader(material, keywordStrIdDic);
                if (null == variantShader)
                {
                    Debug.LogErrorFormat("UseVariantShaders Fail, {0}, {1}", material.name, material.shader.name);
                    return;
                }

                checkCount++;
                var oldQueue = material.renderQueue;
                material.shader = variantShader;
                material.renderQueue = oldQueue;
                Debug.LogFormat("UseVariantShaders succ, {0}, {1} {2}", material.name, material.shader.name, variantShader.name);
            }

          //  ResumeOrignalShaders();
        }

        private static Shader GetVariantShader(Material material, Dictionary<string, int> keywordStrIdDic)
        {
            List<int> keyWordIdList = new List<int>();
            var keywords = material.shaderKeywords;
            foreach (var keyword in keywords)
            {
                int keyword_id = 0;
                if (!keywordStrIdDic.TryGetValue(keyword, out keyword_id))
                {
                    Debug.LogErrorFormat("GetVariantShader Fail, keyword_id is error {0}", keyword);
                //    return null;
                }

                keyWordIdList.Add(keyword_id);
            }

            // 对id进行排序
            keyWordIdList.Sort();
            var nameBuilder = new StringBuilder();
            nameBuilder.Append("Variants_");
            nameBuilder.Append(material.shader.name.Replace("/", "_").Replace(" ", ""));
            for (int i = 0; i < keyWordIdList.Count; i++)
            {
                nameBuilder.Append('_');
                nameBuilder.Append(keyWordIdList[i]);
            }
            var variantShaderName = nameBuilder.ToString();
            var variantShader = Shader.Find(variantShaderName);
            if (null == variantShader)
            {
                Debug.LogErrorFormat("GetVariantShader Error, not found variantShader {0} {1} {2}", material.name, material.shader.name, variantShaderName);
                return null;
            }

            return variantShader;
        }

        // 类似把Yif_1_3改为Yif_3
        private static string filterAdditionalKeywords(string str)
        {
            var keywrods = GetAdditionalKeyWords();
            foreach (var item in keywrods)
            {
                str = str.Replace("_" + item, "");
            }
            return str;
        }

        // 找到动态设置的keyword地应的id,在生成shader和设置ab名字的时候需要过滤掉
        private static string[] additionalKeywords;
        private static string[] GetAdditionalKeyWords()
        {
            if (null != additionalKeywords)
                return additionalKeywords;

            List<string> keywords = new List<string>();
            string[] lines = File.ReadAllLines(string.Format("{0}/Game/Shaders/Materials/keyword_id.txt", Application.dataPath));
            string[] keywordnames = new string[] 
            { 
               /*这些是旧shader的动态keyword*/ "ENABLE_POST_EFFECT", "FOG_LINEAR", "ENABLE_CLIP_RECT", "LIGHTMAP_ON", "LIGHTPROBE_SH", "_PROBE_BLEND" 
            };
            foreach (var line in lines)
            {
                for (int i = 0; i < keywordnames.Length; i++)
                {
                    var ary = line.Split(' ');
                    if (ary.Length == 2 && ary[0] == keywordnames[i])
                    {
                        keywords.Add(ary[1]);
                    }
                }
            }
            additionalKeywords = keywords.ToArray();
            return additionalKeywords;
        }

    }
}

