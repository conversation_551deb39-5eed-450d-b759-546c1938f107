using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
public class TrailScaleAdapter : MonoBehaviour
{
    private float m_RawStartWidth;
    private float m_RawEndWidth;

    private TrailRenderer[] m_Renderers;
    private float[] m_LastScales;
    private Vector2[] m_RawWidth;

    private void OnEnable()
    {
        m_Renderers = this.GetComponentsInChildren<TrailRenderer>();
        m_LastScales = new float[m_Renderers.Length];
        m_RawWidth = new Vector2[m_Renderers.Length];
        for (int i = 0; i < m_RawWidth.Length; i++)
        {
            TrailRenderer renderer = m_Renderers[i];
            m_RawWidth[i] = new Vector2(renderer.startWidth, renderer.endWidth);
            m_LastScales[i] = float.MinValue;
        }
    }
    
    private void OnDisable()
    {
        for (int i = 0; i < m_Renderers.Length; i++)
        {
            TrailRenderer renderer = m_Renderers[i];
            Vector2 rawWidth = m_RawWidth[i];

            renderer.startWidth = rawWidth.x;
            renderer.endWidth = rawWidth.y;

            m_LastScales[i] = 1;
        }
    }

    void Update()
    {
        for (int i = 0; i < m_Renderers.Length; i++)
        {
            float scaleFactor = this.transform.lossyScale.z;
            if (Mathf.Approximately(m_LastScales[i], scaleFactor))
            {
                continue;
            }

            TrailRenderer renderer = m_Renderers[i];
            Vector2 rawWidth = m_RawWidth[i];

            renderer.startWidth = rawWidth.x * scaleFactor;
            renderer.endWidth = rawWidth.y * scaleFactor;

            m_LastScales[i] = scaleFactor;
        }
    }
}
