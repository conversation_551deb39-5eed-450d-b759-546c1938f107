using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System;
using System.Text.RegularExpressions;

public class TextureRulesEditor : EditorWindow
{
    Vector2 ruleGroupScrollPos;
    Vector2 textureStatsScrollPos;
    bool ruleRootFoldout = true;
    bool ruleGroupFoldout = true;
    int ruleGroupMaxHeight = 520;
    bool textureStatsFoldout = false;
    int textureStatsMaxHeight = 10;
    RuleGroup curRuleGroup;
    private string jsonFilePath = "Assets/Config/TextureConfig.json";
    private int selectedGroupIndex = 0;
    private string jsonString;
    private RootConfig rootConfig;
    private string[] maxSizeOptions = new string[] { "128", "256", "512", "1024", "2048", "4096"};
    private string[] compressorQualityOptions = new string[] {"Fast", "Normal", "Best"}; 
    private GUIStyle m_tempFontStyle = new GUIStyle();
    private GUIStyle headerStyle = new GUIStyle();
    private GUIStyle rowStyle = new GUIStyle();
    private GUIStyle textureStatsErrorFontStyle = new GUIStyle();

    private List<TextureStats> textureStatsList = new();

    [MenuItem("自定义工具/资源优化/纹理/纹理规则配置编辑器")]
    public static void ShowWindow()
    {
        GetWindow<TextureRulesEditor>("纹理规则配置编辑器");
    }

    private void OnEnable()
    {
        headerStyle.normal.background = MakeTex(2, 2, new Color(0.1f, 0.1f, 0.1f, 1.0f));
        rowStyle.normal.background = MakeTex(2, 2, new Color(0.5f, 0.5f, 0.5f, 0.2f));
        m_tempFontStyle.normal.textColor = Color.yellow;
        m_tempFontStyle.fontSize = 14;
        textureStatsErrorFontStyle.normal.textColor = Color.white;

        LoadJson();
    }

    private Texture2D MakeTex(int width, int height, Color col)
    {
        Color[] pix = new Color[width * height];
        for (int i = 0; i < pix.Length; i++)
            pix[i] = col;

        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pix);
        result.Apply();
        return result;
    }

    // 加载配置
    private void LoadJson()
    {
        if (File.Exists(jsonFilePath))
        {
            jsonString = File.ReadAllText(jsonFilePath);
            rootConfig = JsonUtility.FromJson<RootConfig>(jsonString);
        }
        else
        {
            rootConfig = new RootConfig();
        }
    }

    // 检查所以配置
    private bool CheckConfig()
    {
        foreach (var group in rootConfig.Rules)
        {
            if (string.IsNullOrEmpty(group.Name) || string.IsNullOrWhiteSpace(group.Name))
            {
                EditorUtility.DisplayDialog("提示", "规则组名未配置", "知道了");
                return false;
            }

            foreach (var rule in group.Rules)
            {
                if (string.IsNullOrEmpty(rule.Name) || string.IsNullOrWhiteSpace(rule.Name))
                {
                    string errorMsg = string.Format("【{0}】规则大类未配置子规则名称", group.Name);
                    EditorUtility.DisplayDialog("提示", errorMsg, "知道了");
                    return false;
                }

                if (string.IsNullOrEmpty(rule.KeyWord) || string.IsNullOrWhiteSpace(rule.KeyWord))
                {
                    string errorMsg = string.Format("【{0}】规则的【{1}】未配置关键字", group.Name, rule.Name);
                    EditorUtility.DisplayDialog("提示", errorMsg, "知道了");
                    return false;
                }

                if (rule.PathList.Count == 0)
                {
                    string errorMsg = string.Format("【{0}】规则的【{1}】未配置路径", group.Name, rule.Name);
                    EditorUtility.DisplayDialog("提示", errorMsg, "知道了");
                    return false;
                }

                foreach (var path in rule.PathList)
                {
                    if (string.IsNullOrEmpty(path) || string.IsNullOrWhiteSpace(path))
                    {
                        string errorMsg = string.Format("【{0}】规则的【{1}】未配置有效路径", group.Name, rule.Name);
                        EditorUtility.DisplayDialog("提示", errorMsg, "知道了");
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // 保存配置
    private void SaveJson()
    {
        TextureAssetImporter.configLoaded = false;
        
        if (!CheckConfig())
        {
            return;
        }
        
        string json = JsonUtility.ToJson(rootConfig, true);
        File.WriteAllText(jsonFilePath, json);
        AssetDatabase.Refresh();
    }
    
    private void OnGUI()
    {
        if (rootConfig == null) return;
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("保存所有配置", GUILayout.Height(40)))
        {
            SaveJson();
            TextureAssetImporter.ReloadRootConfig();
        }

        EditorGUILayout.Space();
        if (GUILayout.Button("执行所有规则", GUILayout.Height(40)))
        {
            SaveJson();
            foreach (var group in rootConfig.Rules)
            {
                foreach (var rule in group.Rules)
                {
                    ApplyTextureSettings(rule);
                }
            }
        }

        EditorGUILayout.Space();
        if (GUILayout.Button("刷新所有统计信息", GUILayout.Height(40)))
        {
            textureStatsList.Clear();
            foreach (var group in rootConfig.Rules)
            {
                foreach (var rule in group.Rules)
                {
                    AnalyzeTextures(rule);
                }
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.LabelField("未定义规则的贴图，导入时执行旧导入逻辑", m_tempFontStyle);
        EditorGUILayout.Space();

        string[] groupNames = GetGroupNames();
        if (groupNames.Length == 0) return;
        EditorGUILayout.BeginVertical(GUI.skin.box);
        ruleRootFoldout = EditorGUILayout.Foldout(ruleRootFoldout, "配置选项");
        if (ruleRootFoldout)
        {
            EditorGUILayout.BeginHorizontal();
            selectedGroupIndex = EditorGUILayout.Popup("规则分组", selectedGroupIndex, groupNames);
            curRuleGroup = rootConfig.Rules[selectedGroupIndex];
            if (GUILayout.Button("+", GUILayout.Width(20)))
            {
                rootConfig.Rules.Add(new RuleGroup());
                selectedGroupIndex = rootConfig.Rules.Count - 1;
                rootConfig.Rules[selectedGroupIndex].Rules.Add(new Rule());
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
                return;
            }

            if (GUILayout.Button("-", GUILayout.Width(20)) && rootConfig.Rules.Count > 1)
            {
                rootConfig.Rules.RemoveAt(selectedGroupIndex);
                selectedGroupIndex = 0;
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
                return;
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            curRuleGroup.Name = EditorGUILayout.TextField("规则组名", curRuleGroup.Name);
            if (GUILayout.Button("执行当前规则组", GUILayout.Width(160)))
            {
                SaveJson();
                foreach (var rule in curRuleGroup.Rules)
                {
                    ApplyTextureSettings(rule);
                }
            }
            if (GUILayout.Button("刷新当前组统计信息", GUILayout.Width(160)))
            {
                textureStatsList.Clear();
                foreach (var rule in curRuleGroup.Rules)
                {
                    AnalyzeTextures(rule);
                }
            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();
        if(ruleGroupFoldout)
        {
            ruleGroupMaxHeight = textureStatsFoldout ? 580 : 880;
        }
        else
        {
            ruleGroupMaxHeight = 10;
        }
        EditorGUILayout.BeginVertical(GUI.skin.box, GUILayout.MaxHeight(ruleGroupMaxHeight));
        ruleGroupFoldout = EditorGUILayout.Foldout(ruleGroupFoldout, "规则设置");
        if(ruleGroupFoldout && curRuleGroup != null)
        {
            ruleGroupScrollPos = EditorGUILayout.BeginScrollView(ruleGroupScrollPos, false, true);
            DisplaySelectedGroup(curRuleGroup);
            EditorGUILayout.EndScrollView();
        }
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space();
        if (textureStatsFoldout)
        {
            textureStatsMaxHeight = ruleGroupFoldout ? 300 : 880;
        }
        else
        {
            textureStatsMaxHeight = 10;
        }
        EditorGUILayout.BeginVertical(GUI.skin.box, GUILayout.MaxHeight(textureStatsMaxHeight));
        textureStatsFoldout = EditorGUILayout.Foldout(textureStatsFoldout, "统计信息");
        if (textureStatsFoldout && textureStatsList != null)
        {
            DisplayTextureStats();
        }
        EditorGUILayout.EndVertical();
    }

    // 规则组名列表
    private string[] GetGroupNames()
    {
        List<string> names = new List<string>();
        foreach (var group in rootConfig.Rules)
        {
            names.Add(group.Name);
        }
        return names.ToArray();
    }

    #region 规则设置
    #region 展示规则
    private void DisplaySelectedGroup(RuleGroup group)
    {
        for (int i = 0; i < group.Rules.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            {
                EditorGUILayout.BeginVertical();
                if (GUILayout.Button("+", GUILayout.Width(20)))
                {
                    group.Rules.Insert(i + 1, new Rule());
                }

                if (GUILayout.Button("-", GUILayout.Width(20)))
                {
                    group.Rules.RemoveAt(i);
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.EndVertical();
                    break;
                }

                if (GUILayout.Button("↑", GUILayout.Width(20)) && i > 0)
                {
                    Rule temp = group.Rules[i];
                    group.Rules[i] = group.Rules[i - 1];
                    group.Rules[i - 1] = temp;
                }

                if (GUILayout.Button("↓", GUILayout.Width(20)) && i < group.Rules.Count - 1)
                {
                    Rule temp = group.Rules[i];
                    group.Rules[i] = group.Rules[i + 1];
                    group.Rules[i + 1] = temp;
                }
                EditorGUILayout.EndVertical();
            }

            {
                var rule = group.Rules[i];
                EditorGUILayout.BeginVertical();
                EditorGUILayout.BeginVertical(GUI.skin.box);
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("规则名称", GUILayout.Width(70));
                    rule.Name = EditorGUILayout.TextField(group.Rules[i].Name, GUILayout.MaxWidth(780));
                    if (GUILayout.Button("执行当前规则", GUILayout.Width(160)))
                    {
                        SaveJson();
                        ApplyTextureSettings(rule);
                    }
                    if (GUILayout.Button("刷新统计信息", GUILayout.Width(160)))
                    {
                        textureStatsList.Clear();
                        AnalyzeTextures(rule);
                    }
                    EditorGUILayout.EndHorizontal();
                }

                ShowPathsList(rule.PathList);
                rule.KeyWord = EditorGUILayout.TextField("关键字", rule.KeyWord);
                {
                    int currentMaxSizeIndex = Array.IndexOf(maxSizeOptions, rule.MaxSize.ToString());
                    if (currentMaxSizeIndex == -1) currentMaxSizeIndex = 0;
                    int selectedMaxSizeIndex = EditorGUILayout.Popup("MaxSize", currentMaxSizeIndex, maxSizeOptions);
                    rule.MaxSize = int.Parse(maxSizeOptions[selectedMaxSizeIndex]);
                }

                {
                    TextureImporterType selectedTexTypeIndex = (TextureImporterType)EditorGUILayout.EnumPopup("Texture Type", (TextureImporterType)rule.TextureType);
                    rule.TextureType = (int)selectedTexTypeIndex;
                }

                {
                    TextureImporterAlphaSource selectedAlphaSourceIndex = (TextureImporterAlphaSource)EditorGUILayout.EnumPopup("Alpha Source", (TextureImporterAlphaSource)rule.AlphaSource);
                    rule.AlphaSource = (int)selectedAlphaSourceIndex;
                }

                rule.sRGB = EditorGUILayout.Toggle("sRGB", rule.sRGB);
                rule.NonPowOf2 = (int)(TextureImporterNPOTScale)EditorGUILayout.EnumPopup("Non Power of 2", (TextureImporterNPOTScale)rule.NonPowOf2);
                rule.ReadWriteEnable = EditorGUILayout.Toggle("Read Write Enabled", rule.ReadWriteEnable);
                rule.GenerateMipMaps = EditorGUILayout.Toggle("Generate Mip Maps", rule.GenerateMipMaps);
                {
                    TextureWrapMode selectedWrapModeIndex = (TextureWrapMode)EditorGUILayout.EnumPopup("Wrap Mode", (TextureWrapMode)rule.WrapMode);
                    rule.WrapMode = (int)selectedWrapModeIndex;
                }
                
                {
                    FilterMode selectedFilterModeIndex = (FilterMode)EditorGUILayout.EnumPopup("Filter Mode", (FilterMode)rule.FilterMode);
                    rule.FilterMode = (int)selectedFilterModeIndex;
                }

                {
                    TextureImporterFormat selectedFormatIndex = (TextureImporterFormat)EditorGUILayout.EnumPopup("Format", (TextureImporterFormat)rule.Format);
                    rule.Format = (int)selectedFormatIndex;
                }
                rule.NotPlatformOverride = EditorGUILayout.Toggle("Not Platform Override", rule.NotPlatformOverride);
                rule.CompressorQuality = EditorGUILayout.Popup("Compressor Quality", rule.CompressorQuality, compressorQualityOptions);

                {
                    int currentPCMaxSizeIndex = Array.IndexOf(maxSizeOptions, rule.PCMaxSize.ToString());
                    if (currentPCMaxSizeIndex == -1) currentPCMaxSizeIndex = 0;
                    int selectedPCMaxSizeIndex = EditorGUILayout.Popup("PC MaxSize", currentPCMaxSizeIndex, maxSizeOptions);
                    rule.PCMaxSize = int.Parse(maxSizeOptions[selectedPCMaxSizeIndex]);
                }

                {
                    TextureImporterFormat selectedPCFormatIndex = (TextureImporterFormat)EditorGUILayout.EnumPopup("PC Format", (TextureImporterFormat)rule.PCFormat);
                    rule.PCFormat = (int)selectedPCFormatIndex;
                }

                EditorGUILayout.EndVertical();
                EditorGUILayout.Space(5);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndHorizontal();
        }
    }
    #endregion

    #region 展示路径列表
    private void ShowPathsList(List<string> paths)
    {
        EditorGUILayout.BeginHorizontal();
        {
            EditorGUILayout.LabelField("路径");
            if (GUILayout.Button("+", GUILayout.Width(20)))
            {
                paths.Add("");
            }
        }
        EditorGUILayout.EndHorizontal();

        for (int i = 0; i < paths.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            paths[i] = EditorGUILayout.TextField(paths[i]);
            if (GUILayout.Button("-", GUILayout.Width(20)))
            {
                paths.RemoveAt(i);
                if(paths.Count == 0)
                {
                    paths.Add("");
                }
                EditorGUILayout.EndHorizontal();
                break;
            }
            EditorGUILayout.EndHorizontal();
        }
    }
    #endregion

    #region 执行规则
    private void ApplyTextureSettings(Rule rule)
    {
        foreach (string path in rule.PathList)
        {
            string[] assetPaths = AssetDatabase.FindAssets("t:Texture", new[] { path });
            foreach (string assetPath in assetPaths)
            {
                string filePath = AssetDatabase.GUIDToAssetPath(assetPath);
                if (Regex.IsMatch(filePath, rule.KeyWord, RegexOptions.IgnoreCase))
                {
                    TextureImporter importer = AssetImporter.GetAtPath(filePath) as TextureImporter;
                    if (importer != null)
                    {
                        // 重新导入 触发重新导入规则
                        importer.SaveAndReimport();
                    }
                }
            }
        }

        AssetDatabase.Refresh();
    }
    #endregion
    #endregion

    #region 改名
    private void ChangeFileName()
    {
        string[] checkDirs = { "Assets/Game/Model", "Assets/Game/Actors" };
        string[] guids = AssetDatabase.FindAssets("t:Texture", checkDirs);
        string d = ".*(_D[\\.])";
        string n = ".*(_N[\\.])";
        string pbr = ".*(_pbr[\\.])";
        string t = ".*(_T[\\.])";
        string e = ".*(_E[\\.])";
        string end_pattern = @"/([^/]+)\.[^/.]+$";

        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (!Regex.IsMatch(path, d, RegexOptions.IgnoreCase) && 
                !Regex.IsMatch(path, n, RegexOptions.IgnoreCase) &&
                !Regex.IsMatch(path, pbr, RegexOptions.IgnoreCase) &&
                !Regex.IsMatch(path, t, RegexOptions.IgnoreCase) &&
                !Regex.IsMatch(path, e, RegexOptions.IgnoreCase))
            {
                string fileType = path.Substring(path.LastIndexOf(".") + 1);
                Match end_match = Regex.Match(path, end_pattern);
                if (end_match.Success)
                {
                    string allName = string.Format("{0}_D.{1}", end_match.Groups[1].Value, fileType);
                    AssetDatabase.RenameAsset(path, allName);
                }
                else
                {
                    Debug.LogError("命名不规范  " + path);
                }
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    #endregion

    #region 贴图数据分析收集
    private void AnalyzeTextures(Rule rule)
    {
        foreach (string path in rule.PathList)
        {
            string[] assetPaths = AssetDatabase.FindAssets("t:Texture", new[] { path });
            foreach (string assetPath in assetPaths)
            {
                string filePath = AssetDatabase.GUIDToAssetPath(assetPath);
                if (Regex.IsMatch(filePath, rule.KeyWord, RegexOptions.IgnoreCase))
                {
                    string shortFilePath = filePath.Replace("Assets/Game/", "");
                    TextureImporter importer = AssetImporter.GetAtPath(filePath) as TextureImporter;
                    Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(filePath);
                    if (importer != null && texture != null)
                    {
                        TextureStats stats = new TextureStats
                        {
                            Texture = texture,
                            Path = shortFilePath,
                            CompressedSize = GetTextureMemorySize(importer, texture), // 假设这是一个获取压缩纹理大小的辅助方法
                            Size = texture.width >= texture.height ? texture.width : texture.height,
                            Type = importer.textureType.ToString(),
                            AlphaSource = importer.alphaSource.ToString(),
                            sRGB = importer.sRGBTexture,
                            ReadWrite = importer.isReadable,
                            GenerateMipMaps = importer.mipmapEnabled,
                            WrapMode = importer.wrapMode.ToString(),
                            Pass = CheckIfPassesRequirements(importer, texture, rule),
                        };

                        var androidSettings = importer.GetPlatformTextureSettings("Android");
                        stats.Format = androidSettings.format.ToString();

                        textureStatsList.Add(stats);
                    }
                }
            }
        }
    }
    

    #region 获取贴图内存大小的近似值
    private double GetTextureMemorySize(TextureImporter importer, Texture2D texture)
    {
        var androidSettings = importer.GetPlatformTextureSettings("Android");
        int bitsPerPixel = GetBitsPerPixel(androidSettings.format);
        float memorySize = texture.width * texture.height * bitsPerPixel / 8; // 初始纹理大小

        if (importer.mipmapEnabled)
        {
            memorySize *= 1.33f; // 大约多33%的内存用于mipmap
        }

        // 转换为合适的单位
        //string sizeStr;
        //if (memorySize < 1024)
        //    sizeStr = memorySize.ToString("F2") + " B";
        //else if (memorySize < 1024 * 1024)
        //    sizeStr = (memorySize / 1024).ToString("F2") + " KB";
        //else
        //    sizeStr = (memorySize / (1024 * 1024)).ToString("F2") + " MB";

        double sizeKB = Math.Round(memorySize / 1024, 2);
        return sizeKB;
    }

    /*TextureFormat
    private int GetBitsPerPixel(TextureFormat format)
    {
        switch (format)
        {
            case TextureFormat.Alpha8: // Alpha only, 8-bit
                return 8;
            case TextureFormat.ARGB4444: // 16-bit
                return 16;
            case TextureFormat.RGBA4444: // 16-bit
                return 16;
            case TextureFormat.RGB24: // 24-bit
                return 24;
            case TextureFormat.RGBA32: // 32-bit
                return 32;
            case TextureFormat.ARGB32: // 32-bit
                return 32;
            case TextureFormat.RGB565: // 16-bit
                return 16;
            case TextureFormat.R16: // 16-bit
                return 16;
            case TextureFormat.DXT1: // 4-bit (Compressed)
                return 4;
            case TextureFormat.DXT5: // 8-bit (Compressed)
                return 8;
            case TextureFormat.PVRTC_RGB2: // 2-bit (Compressed)
                return 2;
            case TextureFormat.PVRTC_RGBA2: // 2-bit (Compressed)
                return 2;
            case TextureFormat.PVRTC_RGB4: // 4-bit (Compressed)
                return 4;
            case TextureFormat.PVRTC_RGBA4: // 4-bit (Compressed)
                return 4;
            case TextureFormat.ETC_RGB4: // 4-bit (Compressed)
                return 4;
            case TextureFormat.EAC_R: // Single channel (R), 4-bit (Compressed)
                return 4;
            case TextureFormat.EAC_R_SIGNED: // Single channel (R), signed, 4-bit (Compressed)
                return 4;
            case TextureFormat.EAC_RG: // Two channels (RG), 8-bit (Compressed)
                return 8;
            case TextureFormat.EAC_RG_SIGNED: // Two channels (RG), signed, 8-bit (Compressed)
                return 8;
            case TextureFormat.ETC2_RGB: // 4-bit (Compressed)
                return 4;
            case TextureFormat.ETC2_RGBA1: // 4-bit + 1-bit alpha (Compressed)
                return 5;
            case TextureFormat.ETC2_RGBA8: // 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_4x4: // 4x4 block, 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_5x5: // 5x5 block, 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_6x6: // 6x6 block, 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_8x8: // 8x8 block, 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_10x10: // 10x10 block, 8-bit (Compressed)
                return 8;
            case TextureFormat.ASTC_12x12: // 12x12 block, 8-bit (Compressed)
                return 8;
            // 这里可以根据需要添加更多格式
            default:
                Debug.LogWarning("Texture format not accounted for: " + format.ToString());
                return 0;
        }
    }
    */

    private int GetBitsPerPixel(TextureImporterFormat format)
    {
        switch (format)
        {
            case TextureImporterFormat.Alpha8:
                return 8;
            case TextureImporterFormat.ARGB16:
                return 16;
            case TextureImporterFormat.RGB24:
                return 24;
            case TextureImporterFormat.RGBA32:
                return 32;
            case TextureImporterFormat.ARGB32:
                return 32;

            // 标准压缩格式
            case TextureImporterFormat.DXT1:
            case TextureImporterFormat.DXT1Crunched:
                return 4;
            case TextureImporterFormat.DXT5:
            case TextureImporterFormat.DXT5Crunched:
                return 8;

            // ETC formats
            case TextureImporterFormat.ETC_RGB4:
                return 4;
            case TextureImporterFormat.ETC2_RGB4:
                return 4;
            case TextureImporterFormat.ETC2_RGBA8:
                return 8;

            // PVRTC formats
            case TextureImporterFormat.PVRTC_RGB2:
            case TextureImporterFormat.PVRTC_RGBA2:
                return 2;
            case TextureImporterFormat.PVRTC_RGB4:
            case TextureImporterFormat.PVRTC_RGBA4:
                return 4;

            // ASTC formats (block-based format, bit rate per block)
            case TextureImporterFormat.ASTC_4x4:
            case TextureImporterFormat.ASTC_HDR_4x4:
                return 128 / (4 * 4);
            case TextureImporterFormat.ASTC_5x5:
            case TextureImporterFormat.ASTC_HDR_5x5:
                return 128 / (5 * 5);
            case TextureImporterFormat.ASTC_6x6:
            case TextureImporterFormat.ASTC_HDR_6x6:
                return 128 / (6 * 6);
            case TextureImporterFormat.ASTC_8x8:
            case TextureImporterFormat.ASTC_HDR_8x8:
                return 128 / (8 * 8);
            case TextureImporterFormat.ASTC_10x10:
            case TextureImporterFormat.ASTC_HDR_10x10:
                return 128 / (10 * 10);
            case TextureImporterFormat.ASTC_12x12:
            case TextureImporterFormat.ASTC_HDR_12x12:
                return 128 / (12 * 12);

            default:
                Debug.LogWarning("未知格式或不支持的格式: " + format.ToString());
                return -1; // 未知格式或不支持的格式
        }
    }
    #endregion

    #region 贴图是否符合规则
    private bool CheckIfPassesRequirements(TextureImporter importer, Texture2D texture, Rule rule)
    {
        if (importer == null) return false;
        if (rule == null) return false;
        if(importer.textureType != (TextureImporterType)rule.TextureType)
            return false;

        if (importer.alphaSource != (TextureImporterAlphaSource)rule.AlphaSource)
            return false;

        if (importer.sRGBTexture != rule.sRGB)
            return false;

        if (importer.isReadable != rule.ReadWriteEnable)
            return false;

        if (importer.mipmapEnabled != rule.GenerateMipMaps)
            return false;

        if (importer.wrapMode != (TextureWrapMode)rule.WrapMode)
            return false;
        
        if (importer.filterMode != (FilterMode)rule.FilterMode)
            return false;

        int size = texture.width >= texture.height ? texture.width : texture.height;
        if (size > rule.MaxSize)
            return false;

        var androidSettings = importer.GetPlatformTextureSettings("Android");
        if (androidSettings.format != (TextureImporterFormat)rule.Format)
            return false;

        return true;
    }
    #endregion
    #endregion

    #region 展示贴图数据
    private void DisplayTextureStats()
    {

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("结果总数", GUILayout.Width(80));
        EditorGUILayout.LabelField(textureStatsList.Count.ToString());
        EditorGUILayout.EndHorizontal();
        if (textureStatsList.Count == 0)
        {
            return;
        }

        EditorGUILayout.BeginHorizontal(headerStyle);
        GUILayout.Label("Pass", GUILayout.Width(80));
        GUILayout.Label("| Path", GUILayout.Width(600));
        GUILayout.Label("| CompressedSize(kb)", GUILayout.Width(140));
        GUILayout.Label("| Size", GUILayout.Width(80));
        GUILayout.Label("| Format", GUILayout.Width(100));
        GUILayout.Label("| Type", GUILayout.Width(100));
        GUILayout.Label("| AlphaSource", GUILayout.Width(100));
        GUILayout.Label("| sRGB", GUILayout.Width(80));
        GUILayout.Label("| R/W", GUILayout.Width(80));
        GUILayout.Label("| GenerateMipMaps", GUILayout.Width(120));
        GUILayout.Label("| WrapMode", GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();

        int widthOffest = 4;
        textureStatsScrollPos = EditorGUILayout.BeginScrollView(textureStatsScrollPos, true, true);
        for (int i = 0; i < textureStatsList.Count; i++)
        {
            TextureStats stats = textureStatsList[i];
            EditorGUILayout.BeginHorizontal(i % 2 == 0 ? rowStyle : GUIStyle.none, GUILayout.Height(20));
            textureStatsErrorFontStyle.normal.textColor = stats.Pass ? Color.white : Color.red;
            GUILayout.Label("  " + stats.Pass.ToString(), textureStatsErrorFontStyle, GUILayout.Width(90 + widthOffest));
            GUILayout.Label(stats.Path, textureStatsErrorFontStyle, GUILayout.Width(600 + widthOffest));
            GUILayout.Label(stats.CompressedSize.ToString(), textureStatsErrorFontStyle, GUILayout.Width(140 + widthOffest));
            GUILayout.Label(stats.Size.ToString(), textureStatsErrorFontStyle, GUILayout.Width(80 + widthOffest));
            GUILayout.Label(stats.Format, textureStatsErrorFontStyle, GUILayout.Width(100 + widthOffest));
            GUILayout.Label(stats.Type, textureStatsErrorFontStyle, GUILayout.Width(100 + widthOffest));
            GUILayout.Label(stats.AlphaSource, textureStatsErrorFontStyle, GUILayout.Width(100 + widthOffest));
            GUILayout.Label(stats.sRGB.ToString(), textureStatsErrorFontStyle, GUILayout.Width(80 + widthOffest));
            GUILayout.Label(stats.ReadWrite.ToString(), textureStatsErrorFontStyle, GUILayout.Width(80 + widthOffest));
            GUILayout.Label(stats.GenerateMipMaps.ToString(), textureStatsErrorFontStyle, GUILayout.Width(120 + widthOffest));
            GUILayout.Label(stats.WrapMode, textureStatsErrorFontStyle, GUILayout.Width(120));
            EditorGUILayout.EndHorizontal();
            Rect rect = GUILayoutUtility.GetLastRect();
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                Selection.activeObject = stats.Texture;
                EditorGUIUtility.PingObject(stats.Texture);
                Event.current.Use();
            }
        }
        EditorGUILayout.EndScrollView();
    }
    #endregion

    [System.Serializable]
    public class RootConfig
    {
        public List<RuleGroup> Rules = new List<RuleGroup>();
    }

    [System.Serializable]
    public class RuleGroup
    {
        public string Name = "";
        public List<Rule> Rules = new();
    }

    [System.Serializable]
    public class Rule
    {
        public string Name = "";
        public List<string> PathList = new() {""};
        public string KeyWord = "";
        public int TextureType = 0; 
        public int AlphaSource = 0;
        public bool sRGB = false;
        public int NonPowOf2 = 1;
        public bool ReadWriteEnable = false;
        public bool GenerateMipMaps = false;
        public int FilterMode = 1;
        public int WrapMode = 0;
        public int MaxSize = 512;
        public int Format = 51;       
        public int CompressorQuality = 1;
        public bool NotPlatformOverride = false;
        public int PCMaxSize = 1024;
        public int PCFormat = 4;
    }

    public class TextureStats
    {
        public Texture2D Texture;
        public bool Pass;
        public string Path;
        public double CompressedSize;
        public int Size;
        public string Format;
        public string Type;
        public string AlphaSource;
        public bool sRGB;
        public bool ReadWrite;
        public bool GenerateMipMaps;
        public string WrapMode;
        public int PCSize;
        public string PCFormat;
    }
}
