﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class CharacterCollector : EditorWindow
{
    private TMP_FontAsset m_TitleFont;

    private string path = "Assets/Game/UIs/TMP";
    [MenuItem("Tools/字体/常用字收集器")]
    protected static void Init()
    {
        var window = EditorWindow.GetWindow<CharacterCollector>();
        window.titleContent = new GUIContent("常用字收集器");
    }

    private static void AddText(HashSet<char> characters, string text)
    {
        foreach (var c in text)
        {
            characters.Add(c);
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginVertical("box");
        GUILayout.Label("Font_Text", EditorStyles.boldLabel);

        if (GUILayout.Button("收集Language里的字符"))
        {
            try
            {
                var characters = new HashSet<char>();
                var files = Directory.GetFiles("Assets/Game/Lua/language", "language.lua", SearchOption.AllDirectories);
                for (var i = 0; i < files.Length; ++i)
                {
                    var file = files[i];

                    EditorUtility.DisplayProgressBar("收集字符", file, i / (float)files.Length);
                    AddText(characters, File.ReadAllText(file));
                }
                var files2 = Directory.GetFiles("Assets/Game/Lua/gameui/common", "common_ui_wg_datamanager.lua", SearchOption.AllDirectories);
                for (var i = 0; i < files2.Length; ++i)
                {
                    var file = files2[i];

                    EditorUtility.DisplayProgressBar("收集字符", file, i / (float)files2.Length);
                    AddText(characters, File.ReadAllText(file));
                }

                var result = string.Join(string.Empty, characters.Where(c => c >= 0x4e00 && c <= 0x9fff).OrderBy(c => c));

                File.WriteAllText($@"{path}/char_in_language.txt", result);
                Debug.Log("Language字符收集完成");

            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        if (GUILayout.Button("收集界面里的字符"))
        {
            try
            {
                var characters = new HashSet<char>();

                // 加载所有要处理的界面Prefab
                var prefabs = AssetDatabase.FindAssets("t:Prefab", new string[] { @"Assets/Game/UIs/View"})
                    .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
                    .Select(path => AssetDatabase.LoadAssetAtPath<GameObject>(path))
                    .ToList();

                for (var i = 0; i < prefabs.Count; ++i)
                {
                    var prefab = prefabs[i];

                    EditorUtility.DisplayProgressBar("收集界面字符", prefab.name, i / (float)prefabs.Count);
                    foreach (var tmp_txt in prefab.GetComponentsInChildren<Text>(includeInactive: true))
                    {
                        AddText(characters, tmp_txt.text);
                    }
                }

                var result = string.Join(string.Empty, characters.Where(c => c >= 0x4e00 && c <= 0x9fff).OrderBy(c => c));
                File.WriteAllText($@"{path}/char_in_ui.txt", result);
                Debug.Log("界面字符收集完成");

            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        if (GUILayout.Button("收集配置里的字符"))
        {
            try
            {
                var characters = new HashSet<char>();
                var files = Directory.GetFiles("Assets/Game/Lua/config", "*.lua", SearchOption.AllDirectories);
                for (var i = 0; i < files.Length; ++i)
                {
                    var file = files[i];

                    EditorUtility.DisplayProgressBar("收集字符", file, i / (float)files.Length);
                    AddText(characters, File.ReadAllText(file));
                }

                var result = string.Join(string.Empty, characters.Where(c => c >= 0x4e00 && c <= 0x9fff).OrderBy(c => c));
                File.WriteAllText($@"{path}/char_in_excel.txt", result);
                Debug.Log("配置字符收集完成");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        if (GUILayout.Button("合并所有字符"))
        {
            var characters = new HashSet<char>();
            var files = Directory.GetFiles(path, "*.txt");
            for (var i = 0; i < files.Length; ++i)
            {
                var file = files[i];
                if (Path.GetFileName(file).StartsWith("char_in_")|| Path.GetFileName(file).StartsWith("always_chars"))
                {
                    AddText(characters, File.ReadAllText(file));
                }
            }

            var chinese_chars = string.Join(string.Empty, characters.Where(c => c >= 0x4e00 && c <= 0x9fff).OrderBy(c => c));
            var common_chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ`-=[];'\\,./~!@#$%^&*()_+{}:\" |<>?·-=【】；‘、，。/ ~！@#￥%……&*（）——+{}：“|《》？☆∞≥≤";
            File.WriteAllText($@"{path}/all_chars.txt", common_chars + chinese_chars);

            Debug.Log("合并完成");
        }

        if (GUILayout.Button("check"))
        {
            var old_chars = File.ReadAllText($@"{path}/always_chars.txt");
            var new_chars = File.ReadAllText($@"{path}/all_chars.txt");

            Debug.Log("相对于常用字缺少了：" + string.Join(string.Empty, old_chars.Except(new_chars)));
            Debug.Log("相对于常用字新增了：" + string.Join(string.Empty, new_chars.Except(old_chars)));
        }

        GUILayout.EndVertical();
    }
}
