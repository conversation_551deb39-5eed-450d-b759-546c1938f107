﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using UnityEngine;

/// <summary>
/// The moveable object is used to control the actor movement for lua script.
/// </summary>
public sealed class MoveableObject : MonoBehaviour
{
    private static RaycastHit[] hits = new RaycastHit[8];

    private bool rotating;
    private Quaternion rotateTarget;
    private float rotateSpeed;
    private Action<int> rotateCallback;

    private bool moving;
    private Vector3 moveTarget;
    private float moveSpeed;
    private Action<int> moveCallback;
    private Vector3 offset;
    private bool checkWater = false;
    private float waterHeight = 0f;
    private bool isInWater = false;
    private Action<bool> enterWaterCallBack;

    public bool isWalkFree = false;
    private bool isFly = false;
    private Vector3 flyOffset;
    private float flyHeight = 0f;
    private float min_fly_height = 2f;
    private float max_fly_height = 120f;

    public bool enableQingGong = false;
    public bool checkBuilding = false;
    public bool enableMitsurugi = false;
    public static float heightDifference =1f;
    public float mitsurugi_hight = 5f;
    private float jumpHorizonSpeed = 5f;
    private bool isSimpleJump = false;
    private bool isOnGround = true;
    private ActorQingGong qingGong;
    private Action<QingGongState> stateCallBack;

    private bool isKeepGroundH = false;
    private bool is_Mitsurugi = false;

    private ActorQingGong QingGong
    {
        get
        {
            if (null == this.qingGong)
            {
                this.qingGong = this.gameObject.AddComponent<ActorQingGong>();
                this.qingGong.enabled = false;
                this.qingGong.SetStateChangeCallBack(this.QingGongStateChanged);
            }
            return this.qingGong;
        }
    }

    public void Reset()
    {
        moving = false;
        rotating = false;
        enableQingGong = false;
        checkBuilding = false;
        isSimpleJump = false;
        isOnGround = true;
        isFly = false;
        this.CheckWater = false;
        isInWater = false;

        offset = Vector3.zero;
        flyOffset = Vector3.zero;

        rotateCallback = null;
        moveCallback = null;
        enterWaterCallBack = null;
        stateCallBack = null;
        isKeepGroundH = false;

        if (null != qingGong)
        {
            Destroy(qingGong);
            qingGong = null;
        }
    }

    public bool IsOnGround
    {
        private set
        {
            if (value != this.isOnGround)
            {
                if (!value)
                {
                    this.StopMove();
                    this.StopRotate();
                }
                this.isOnGround = value;
            }
        }
        get
        {
            return this.isOnGround;
        }
    }

    public float JumpHorizonSpeed
    {
        get
        {
            return this.jumpHorizonSpeed;
        }
        set
        {
            this.jumpHorizonSpeed = value;
            if (enableQingGong)
                this.QingGong.forwardSpeed = value;
        }
    }

    float hightGroud;
    float heightInFixToGround = float.NegativeInfinity;
    bool isInWaterInFixToGround = false;
    float hightWater = 0f;
    public Vector3 FixToGround(Vector3 position)
    {
        // 检查输入位置是否有效
        if (!IsValidPosition(position))
        {
            return this.transform.position;
        }

        if (enableQingGong)
        {
            hightGroud = GetNearestHeight(position);
        }
        else if (checkBuilding)
        {
            hightGroud = HeightByMask(1 << GameLayers.Walkable | 1 << GameLayers.SmallBuilding | 1 << GameLayers.BigBuilding, position);
        }
        else
        {
            hightGroud = Height(GameLayers.Walkable, position);
        }

        if (this.CheckWater)
        {
            hightWater = Height(GameLayers.Water, position);
            if (hightWater - hightGroud > this.waterHeight)
            {
                heightInFixToGround = hightWater;
                isInWaterInFixToGround = true;
            }
            else
            {
                heightInFixToGround = hightGroud;
            }
            if (null != this.enterWaterCallBack)
            {
                if (this.isInWater != isInWaterInFixToGround)
                    this.enterWaterCallBack(isInWaterInFixToGround);
            }
            this.isInWater = isInWaterInFixToGround;
        }
        else
        {
            heightInFixToGround = hightGroud;
        }

        if (heightInFixToGround > float.NegativeInfinity)
        {
            Vector3 result = new Vector3(position.x, heightInFixToGround, position.z);

            // 检查结果位置是否有效
            if (!IsValidPosition(result))
            {
                return this.transform.position; // 返回当前位置作为安全值
            }

            return result;
        }
        else
        {
            if (isKeepGroundH && !enableQingGong)
            {
                return new Vector3(position.x, this.transform.position.y, position.z);
            }

            return position;
        }
    }

    /// <summary>
    /// Set the callback to receive the rotate event.
    /// </summary>
    public void SetRotateCallback(Action<int> rotateCallback)
    {
        this.rotateCallback = rotateCallback;
    }

    /// <summary>
    /// Set the callback to receive the move event.
    /// </summary>
    public void SetMoveCallback(Action<int> moveCallback)
    {
        this.moveCallback = moveCallback;
    }

    /// <summary>
    /// Set the position offset.
    /// </summary>
    public void SetOffset(Vector3 offset)
    {
        this.offset = offset;

        if (!this.isOnGround)
        {
            return;
        }

        this.transform.position = FixToGround(this.transform.position) + this.offset + this.flyOffset;
    }

    public float GetOffsetY()
    {
        return this.offset.y;
    }

    public Vector3 GetOffset()
    {
        return this.offset;
    }


    /// <summary>
    /// Set the position fly offset.
    /// </summary>
    public void SetFlyOffset(Vector3 flyOffset)
    {
        this.flyOffset = flyOffset;
        Vector3 newPos = FixToGround(this.transform.position) + this.offset + this.flyOffset;
        if (IsValidPosition(newPos))
        {
            this.transform.position = newPos;
        }
    }

    /// <summary>
    /// Set the position of this object and fix to ground.
    /// </summary>
    public void SetPosition(float x, float y, float z)
    {
        this.SetPosition(new Vector3(x, y, z));
    }

    /// <summary>
    /// Set the position of this object and fix to ground.
    /// </summary>
    public void SetPosition(Vector3 position)
    {
        if (!this.isOnGround)
        {
            return;
        }

        //this.transform.position = position;
        Vector3 newPos = FixToGround(position) + this.offset + this.flyOffset;
        if (IsValidPosition(newPos))
        {
            this.transform.position = newPos;
        }
    }

    /// <summary>
    /// Set the rotation of this object and fix to ground.
    /// </summary>
    public void SetRotation(float x, float y, float z)
    {
        this.SetRotation(Quaternion.Euler(x, y, z));
    }

    /// <summary>
    /// Set the rotation of this object and fix to ground.
    /// </summary>
    public void SetRotation(Quaternion rotation)
    {
        this.transform.rotation = rotation;
    }

    public void SetRotationOffset(float x, float y, float z)
    {
        this.SetRotationOffset(new Vector3(x, y, z));
    }

    public void SetRotationOffset(Vector3 offset)
    {
        var rotation = this.transform.eulerAngles + offset;
        this.transform.rotation = Quaternion.Euler(rotation);
    }

    public void SetRotationX(float x)
    {
        var rotation = new Vector3(x, this.transform.eulerAngles.y, 0);
        this.transform.rotation = Quaternion.Euler(rotation);
    }

    /// <summary>
    /// Rotate to the target direction by specify rotate speed.
    /// </summary>
    public void RotateTo(float x, float y, float z, float speed)
    {
        this.RotateTo(new Vector3(x, y, z), speed);
    }

    /// <summary>
    /// Rotate to the target direction by specify rotate speed.
    /// </summary>
    public void RotateTo(Vector3 target, float speed)
    {
        if (!this.isOnGround)
        {
            return;
        }

        var offset = target - transform.position;
        offset.y = 0;
        if (offset.sqrMagnitude > float.Epsilon)
        {
            this.rotateTarget = Quaternion.LookRotation(offset);
            this.rotateSpeed = speed;
            this.rotating = true;
        }
    }

    /// <summary>
    /// Stop the rotate.
    /// </summary>
    public void StopRotate()
    {
        this.rotating = false;
        if (this.rotateCallback != null)
        {
            this.rotateCallback(0);
        }
    }

    /// <summary>
    /// Move to specify position at specify speed.
    /// </summary>
    public void MoveTo(float x, float y, float z, float speed)
    {
        this.MoveTo(new Vector3(x, y, z), speed);
    }

    /// <summary>
    /// Move to specify position at specify speed.
    /// </summary>
    public void MoveTo(Vector3 target, float speed)
    {
        if (!this.isOnGround)
        {
            return;
        }

        this.moveTarget = target;
        this.moveSpeed = speed;
        this.moving = true;
    }

    /// <summary>
    /// Stop moving.
    /// </summary>
    public void StopMove()
    {
        this.moving = false;
        if (this.moveCallback != null)
        {
            this.moveCallback(0);
        }
    }

    private void Update()
    {
        if (!this.isOnGround)
        {
            return;
        }

        if (this.moving && this.rotating)
        {
            var position = this.DoPosition(this.transform.position);
            var rotation = this.DoRotation(this.transform.rotation);
            this.transform.SetPositionAndRotation(position, rotation);
        }
        else if (this.moving)
        {
            var position = this.DoPosition(this.transform.position);
            this.transform.position = position;
        }
        else if (this.rotating)
        {
            var rotation = this.DoRotation(this.transform.rotation);
            this.transform.rotation = rotation;
        }
    }

    Vector3 moveDir = Vector3.zero;
    private void FixedUpdate()
    {
        if (!this.IsOnGround && this.isSimpleJump)
        {
            moveDir = this.moveTarget - this.transform.position;
            moveDir.y = 0;
            moveDir.Normalize();
            this.AdjustMoveMent(moveDir.x, moveDir.z);
        }
    }

    float curFlyHeight = 0;
    float groundHeight = 0;
    private void LateUpdate()
    {
        if (this.isFly && this.flyHeight > 0)
        {
            groundHeight = this.Height(GameLayers.Walkable);
            if (groundHeight == float.NegativeInfinity)
            {
                return;
            }
            this.flyHeight = Mathf.Max(groundHeight + this.min_fly_height, this.flyHeight);
            curFlyHeight = this.flyHeight - groundHeight;
            this.SetFlyOffset(new Vector3(0, curFlyHeight, 0));
        }
    }

    Vector3 offsetInDoPosition = Vector3.zero;
    Vector3 movementInDoPosition = Vector3.zero;
    Vector3 newPosition;
    private Vector3 DoPosition(Vector3 position)
    {
        offsetInDoPosition = this.moveTarget - position;

        //非御剑不计算Y轴，不然会产生加速停顿
        if (!this.QingGong.is_Mitsurugi)
        {
            offsetInDoPosition.y = 0;
        }
        movementInDoPosition = offsetInDoPosition.normalized * Time.unscaledDeltaTime * this.moveSpeed;
        if (movementInDoPosition.sqrMagnitude >= offsetInDoPosition.sqrMagnitude)
        {
            position = this.moveTarget;
            this.moving = false;
            if (this.moveCallback != null)
            {
                this.moveCallback(1);
            }
        }
        else
        {
            position += movementInDoPosition;
        }

        if (enableQingGong)
        {
            float height = GetNearestHeight(position);
            newPosition = new Vector3(position.x, height, position.z);

            if (transform.position.y - height > heightDifference)
            {
                IsOnGround = false;
                QingGong.enabled = true;
                newPosition = new Vector3(position.x, transform.position.y, position.z);
            }
            else if (height - transform.position.y > heightDifference)
            {
                newPosition = transform.position;
                this.moving = false;
                if (this.moveCallback != null)
                {
                    this.moveCallback(2);
                }
            }
        }
        else if (this.isWalkFree)
        {
            if (this.moveTarget == position)
            {
                position.y = this.transform.position.y;
            }
            newPosition = position;
        }
        else
        {
            newPosition = FixToGround(position);
        }

        if (this.isWalkFree)
        {
            return newPosition;
        }
        else
        {
            return newPosition + this.offset + flyOffset;
        }
    }

    private Quaternion DoRotation(Quaternion rotation)
    {
        rotation = Quaternion.Slerp(
            rotation,
            this.rotateTarget,
            Time.unscaledDeltaTime * this.rotateSpeed);

        if (Quaternion.Angle(rotation, this.rotateTarget) < 0.01f)
        {
            rotation = this.rotateTarget;
            this.rotating = false;
            if (this.rotateCallback != null)
            {
                this.rotateCallback(1);
            }
        }

        return rotation;
    }

    public float Height(int layer, Vector3 position = new Vector3())
    {
        return HeightByMask(1 << layer, position);
    }

    float height = float.NegativeInfinity;
    private float HeightByMask(int layerMask, Vector3 position)
    {
        if (position == Vector3.zero)
        {
            position = this.gameObject.transform.position;
        }
		height = float.NegativeInfinity;
        var ray = new Ray(position + (10000.0f * Vector3.up), Vector3.down);

        if (is_Mitsurugi && QingGong.curState == QingGongState.Mitsurugi)
        {
            ray = new Ray(position, Vector3.down);
        }

        var count = Physics.RaycastNonAlloc(
            ray, hits, float.PositiveInfinity, layerMask);
        if (count > 0 && count <= hits.Length)
        {
            for (int i = 0; i < count; ++i)
            {
                var hit = hits[i];
                if (height < hit.point.y)
                {
                    float offset_y = 0f;
                    if (this.is_Mitsurugi && QingGong.curState == QingGongState.Mitsurugi)
                    {
                        offset_y = mitsurugi_hight;
                    }
                    height = hit.point.y + offset_y;
                }
            }
        }

        //御剑只提不降
        if (is_Mitsurugi && QingGong.curState == QingGongState.Mitsurugi)
        {
            if (height == float.NegativeInfinity)
            {
                height = position.y;
            }
            else
            {
                if (height < position.y)
                {
                    height = position.y;
                }

            }
            //height = position.y;
            //height = Mathf.Max(this.gameObject.transform.position.y, height);
        }

        return height;
    }

    /// <summary>
    /// 设置当前飞行高度
    /// </summary>
    public float SetFlyHeight
    {
        get { return this.flyHeight; }
        set { this.flyHeight = value; }
    }

    /// <summary>
    /// 最小飞行高度
    /// </summary>
    public float MinFlyHeight
    {
        get { return this.min_fly_height; }
        set { this.min_fly_height = value; }
    }

    /// <summary>
    /// 最大飞行高度
    /// </summary>
    public float MaxFlyHeight
    {
        get { return this.max_fly_height; }
        set { this.max_fly_height = value; }
    }

    public bool IsFly
    {
        get { return this.isFly; }
        set
        {
            if (value)
            {
                this.flyHeight = this.min_fly_height + this.Height(GameLayers.Walkable);
            }
            else
            {
                this.flyHeight = 0f;
                this.SetFlyOffset(Vector3.zero);
            }
            this.isFly = value;
        }
    }

    public bool CheckWater
    {
        get { return this.checkWater; }
        set
        {
            var oldValue = this.checkWater;
            this.checkWater = value;
            if (oldValue != value)
            {
                this.transform.position = FixToGround(this.transform.position) + this.offset + this.flyOffset;
            }
        }
    }

    public float WaterHeight
    {
        get { return this.waterHeight; }
        set
        {
            var oldValue = this.waterHeight;
            this.waterHeight = value;
            if (this.CheckWater && oldValue != value)
            {
                this.transform.position = FixToGround(this.transform.position) + this.offset + this.flyOffset;
            }
        }
    }

    public bool IsInWater
    {
        get { return this.isInWater; }
    }

    public void SetEnterWaterCallBack(Action<bool> callBack)
    {
        this.enterWaterCallBack = callBack;
        if (null != callBack)
        {
            callBack(this.isInWater);
        }
    }

    private void QingGongStateChanged(QingGongState state)
    {
        if (state == QingGongState.OnGround || state == QingGongState.Stop)
        {
            IsOnGround = true;
            this.QingGong.enabled = false;
        }
        else if (state == QingGongState.Mitsurugi)
        {
            IsOnGround = true;
        }
        else
        {
            IsOnGround = false;
        }

        if (null != this.stateCallBack)
        {
            this.stateCallBack(state);
        }
    }

    public void SetStateChangeCallBack(Action<QingGongState> stateCallBack)
    {
        this.stateCallBack = stateCallBack;
    }

    public void Jump(ActorQingGongObject qinggongObject)
    {
        if (!enableQingGong)
            return;

        this.isSimpleJump = false;
        this.QingGong.enabled = true;
        this.QingGong.autoJump = false;
        this.QingGong.forwardSpeed = this.jumpHorizonSpeed;
        this.QingGong.AddJumpForce(qinggongObject, null);
    }

    //增加御剑，停留在空中移动
    public void Mitsurugi(ActorQingGongObject qinggongObject)
    {
        this.QingGong.enabled = true;
        this.QingGong.autoJump = false;
        this.QingGong.forwardSpeed = this.jumpHorizonSpeed;
        this.QingGong.is_Mitsurugi = true;

        this.QingGong.AddJumpForce(qinggongObject, () =>
        {
            this.is_Mitsurugi = true;
        });
    }

    public void StopMitsurugi()
    {
        this.is_Mitsurugi = false;
        this.QingGong.enabled = true;
        this.QingGong.StopMitsurugi();
    }

    public void SimpleJump(ActorQingGongObject qinggongObject, Vector3 target, bool autoJump = false)
    {
        if (!enableQingGong)
            return;

        this.moveTarget = target;
        this.isSimpleJump = true;
        this.QingGong.enabled = true;
        this.QingGong.autoJump = autoJump;
        this.QingGong.forwardSpeed = this.jumpHorizonSpeed;
        this.QingGong.AddJumpForce(qinggongObject, null);
    }

    public void ForceLanding()
    {
        if (enableQingGong)
        {
            if (QingGong.enabled)
            {
                QingGong.ForceLanding();
            }
        }
    }

    public void StopQinggong()
    {
        if (QingGong.enabled)
        {
            QingGong.StopQinggong();
        }
    }
    public void AdjustMoveMent(float fx, float fy)
    {
        if (!enableQingGong || this.IsOnGround)
            return;

        this.QingGong.AdjustMoveMent(fx, fy);
    }

    public void SetQingGongTarget(Vector3 target)
    {
        if (!enableQingGong || !isSimpleJump)
            return;

        this.moveTarget = target;
    }

    public void SetGravityMultiplier(float multiplier)
    {
        if (!enableQingGong)
            return;

        this.QingGong.m_GravityMultiplier = multiplier;
    }

    public static void SetGridFindWay(GridFindWay gridFindWay)
    {
        ActorQingGong.gridFindWay = gridFindWay;
    }

    public static void SetLogicMap(int origin_x, int origin_y, int width, int height)
    {
        ActorQingGong.SetLogicMap(origin_x, origin_y, width, height);
    }

    private bool IsTunnelArea(Vector3 position)
    {
        if (enableQingGong)
        {
            return ActorQingGong.IsTunnelArea(position);
        }

        return false;
    }

    float groundHeightInGetNearestHeight = 0f;
    float roofHeightInGetNearestHeight = 0f;
    float difference_1 = 0f;
    float difference_2 = 0f;
    private float GetNearestHeight(Vector3 position)
    {
        groundHeightInGetNearestHeight = Height(GameLayers.Walkable, position);
        groundHeightInGetNearestHeight = groundHeightInGetNearestHeight == float.NegativeInfinity ? transform.position.y : groundHeightInGetNearestHeight;
        roofHeightInGetNearestHeight = HeightByMask(1 << GameLayers.BigBuilding | 1 << GameLayers.SmallBuilding, position);
        roofHeightInGetNearestHeight = roofHeightInGetNearestHeight == float.NegativeInfinity ? groundHeightInGetNearestHeight : roofHeightInGetNearestHeight;

        if (IsTunnelArea(position))
        {
            difference_1 = Math.Abs(transform.position.y - roofHeightInGetNearestHeight);
            difference_2 = Math.Abs(transform.position.y - groundHeightInGetNearestHeight);
            return difference_1 <= difference_2 ? roofHeightInGetNearestHeight : groundHeightInGetNearestHeight;
        }
        else
        {
            return roofHeightInGetNearestHeight >= groundHeightInGetNearestHeight ? roofHeightInGetNearestHeight : groundHeightInGetNearestHeight;
        }
    }

    public void SetDrag(float drag)
    {
        if (!enableQingGong)
            return;

        this.QingGong.Drag = drag;
    }

    public void JumpFormAir(float height, Vector3 target, ActorQingGongObject qinggongObject, float progress = 0)
    {
        if (!enableQingGong)
            return;

        transform.position = new Vector3(transform.position.x, height, transform.position.z);
        target.y = transform.position.y;
        transform.LookAt(target);
        moveTarget = target;
        isSimpleJump = true;
        QingGong.enabled = true;
        QingGong.forwardSpeed = this.jumpHorizonSpeed;
        this.QingGong.AddJumpForce(qinggongObject, null, progress);
        IsOnGround = false;
    }

    public bool GetIsRotating()
    {
        return this.rotating;
    }

    public void SetIsKeepGroundH(bool isKeep)
    {
        this.isKeepGroundH = isKeep;
    }

    /// <summary>
    /// 检查3D位置是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidPosition(Vector3 position)
    {
        return !float.IsNaN(position.x) && !float.IsNaN(position.y) && !float.IsNaN(position.z) &&
               !float.IsInfinity(position.x) && !float.IsInfinity(position.y) && !float.IsInfinity(position.z);
    }
}
