using UnityEditor;
using UnityEngine;
using Nirvana;

/// <summary>
/// 技能编辑器GUI工具类
/// 提供通用的GUI绘制方法和样式
/// </summary>
public static class SkillEditorGUI
{
    #region 样式缓存
    private static GUIStyle _normalStyle;
    private static GUIStyle _activeStyle;
    private static Texture2D _buttonTexture;
    private static bool _stylesInitialized = false;

    /// <summary>
    /// 初始化样式（必须在OnGUI中调用）
    /// </summary>
    public static void InitializeStyles()
    {
        if (_stylesInitialized) return;

        try
        {
            if (Event.current != null) // 确保在GUI上下文中
            {
                _buttonTexture = GUI.skin.button.normal.background;

                _normalStyle = new GUIStyle
                {
                    normal = { textColor = Color.black, background = _buttonTexture }
                };

                _activeStyle = new GUIStyle
                {
                    normal = { textColor = Color.red, background = _buttonTexture }
                };

                _stylesInitialized = true;
            }
        }
        catch (System.Exception)
        {
            // 如果不在GUI上下文中，忽略错误，稍后重试
        }
    }

    /// <summary>
    /// 确保样式已初始化
    /// </summary>
    private static void EnsureStylesInitialized()
    {
        if (!_stylesInitialized)
        {
            InitializeStyles();
        }
    }

    public static GUIStyle NormalStyle
    {
        get
        {
            EnsureStylesInitialized();
            return _normalStyle ?? (_normalStyle = new GUIStyle());
        }
    }

    public static GUIStyle ActiveStyle
    {
        get
        {
            EnsureStylesInitialized();
            return _activeStyle ?? (_activeStyle = new GUIStyle());
        }
    }
    #endregion

    #region 通用控件
    /// <summary>
    /// 绘制标题标签
    /// </summary>
    public static void DrawTitle(string title)
    {
        GUILayout.Label(title, EditorStyles.boldLabel);
    }

    /// <summary>
    /// 绘制分隔线
    /// </summary>
    public static void DrawSeparator()
    {
        GUILayout.Label("--------------------------------------------------");
    }

    /// <summary>
    /// 绘制带默认值的文本字段
    /// </summary>
    public static string DrawTextFieldWithDefault(string label, string value, string defaultValue = "")
    {
        string result = EditorGUILayout.TextField(label, value);
        return SkillEditorUtils.IsNullOrEmpty(result) ? defaultValue : result;
    }

    /// <summary>
    /// 绘制按钮名称字段
    /// </summary>
    public static string DrawButtonNameField(string value)
    {
        return DrawTextFieldWithDefault("按钮名称", value, SkillEditorGUIConfig.DEFAULT_BUTTON_NAME);
    }

    /// <summary>
    /// 绘制延迟时间字段
    /// </summary>
    public static float DrawDelayField(string label, float value)
    {
        return EditorGUILayout.FloatField(label, value);
    }
    #endregion

    #region 事件选择器
    /// <summary>
    /// 绘制事件选择下拉框
    /// </summary>
    public static string DrawEventSelector(string currentEvent, string label = "Event Name")
    {
        int selectedIndex = 0;
        string[] eventNames = SkillEditorEventDrawer.eventNames;

        if (!SkillEditorUtils.IsNullOrEmpty(currentEvent))
        {
            selectedIndex = SkillEditorUtils.GetSafeArrayIndex(eventNames, currentEvent);
        }

        selectedIndex = EditorGUILayout.Popup(label, selectedIndex, eventNames);
        return eventNames[selectedIndex];
    }

    /// <summary>
    /// 绘制弹道事件选择器
    /// </summary>
    public static string DrawProjectileEventSelector(string currentEvent)
    {
        int selectedIndex = SkillEditorUtils.GetSafeArrayIndex(SkillEditorEventDrawer.projectileEventNames, currentEvent);
        selectedIndex = EditorGUILayout.Popup("Event Name", selectedIndex, SkillEditorEventDrawer.projectileEventNames);
        return SkillEditorEventDrawer.projectileEventNames[selectedIndex];
    }
    #endregion

    #region 资产选择器
    /// <summary>
    /// 绘制特效控制器选择器
    /// </summary>
    public static EffectControl DrawEffectSelector(string effectName, string assetPath, string label = "特效")
    {
        if (SkillEditorUtils.IsNullOrEmpty(effectName))
        {
            return EditorGUILayout.ObjectField(label, null, typeof(EffectControl), true) as EffectControl;
        }
        else
        {
            var prefab = SkillEditorUtils.LoadGameObjectFromPath(assetPath);
            return EditorGUILayout.ObjectField(label, prefab, typeof(EffectControl), true) as EffectControl;
        }
    }

    /// <summary>
    /// 绘制音频项目选择器
    /// </summary>
    public static AudioItem DrawAudioSelector(string audioName, string assetPath, string label = "Audio Asset")
    {
        if (SkillEditorUtils.IsNullOrEmpty(audioName))
        {
            return EditorGUILayout.ObjectField(label, null, typeof(AudioItem), true) as AudioItem;
        }
        else
        {
            var audioItem = AssetDatabase.LoadAssetAtPath<AudioItem>(assetPath);
            return EditorGUILayout.ObjectField(label, audioItem, typeof(AudioItem), true) as AudioItem;
        }
    }

    /// <summary>
    /// 绘制弹道选择器
    /// </summary>
    public static Projectile DrawProjectileSelector(string projectileName, AssetID assetId)
    {
        if (SkillEditorUtils.IsNullOrEmpty(projectileName))
        {
            return EditorGUILayout.ObjectField("特效", null, typeof(Projectile), true) as Projectile;
        }
        else
        {
            string guid = AssetDatabase.AssetPathToGUID(assetId.GetAssetPath());
            var prefabPath = AssetDatabase.GUIDToAssetPath(guid);
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            return EditorGUILayout.ObjectField("特效", prefab, typeof(Projectile), true) as Projectile;
        }
    }
    #endregion

    #region 变换选择器
    /// <summary>
    /// 绘制节点选择器
    /// </summary>
    public static Transform DrawNodeSelector(string label, string hierarchyPath, Transform root)
    {
        Transform node = SkillEditorUtils.FindTransformFromPath(root, hierarchyPath);
        return EditorGUILayout.ObjectField(label, node, typeof(Transform), true) as Transform;
    }

    /// <summary>
    /// 更新节点路径
    /// </summary>
    public static string UpdateNodePath(Transform node, Transform root)
    {
        return node ? SkillEditorUtils.GetHierarchyPath(node, root) : string.Empty;
    }
    #endregion

    #region 列表绘制
    /// <summary>
    /// 绘制滚动列表
    /// </summary>
    public static Vector2 DrawScrollList<T>(Vector2 scrollPos, System.Collections.Generic.List<T> items, 
        System.Func<T, string> getButtonText, System.Action<int> onItemClick)
    {
        scrollPos = EditorGUILayout.BeginScrollView(
            scrollPos,
            GUILayout.Width(SkillEditorGUIConfig.SCROLL_WIDTH),
            GUILayout.Height(SkillEditorGUIConfig.SCROLL_HEIGHT));

        for (int i = 0; i < items.Count; i++)
        {
            if (GUILayout.Button(getButtonText(items[i])))
            {
                onItemClick?.Invoke(i);
            }
        }

        EditorGUILayout.EndScrollView();
        return scrollPos;
    }

    /// <summary>
    /// 绘制添加删除按钮组
    /// </summary>
    public static void DrawAddDeleteButtons(string addLabel, string deleteLabel, 
        System.Action onAdd, System.Action onDelete, bool canDelete = true)
    {
        GUILayout.BeginHorizontal();
        
        if (GUILayout.Button(addLabel, GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            onAdd?.Invoke();
        }

        if (canDelete && GUILayout.Button(deleteLabel, GUILayout.Width(SkillEditorGUIConfig.BUTTON_WIDTH), 
            GUILayout.Height(SkillEditorGUIConfig.BUTTON_HEIGHT)))
        {
            onDelete?.Invoke();
        }

        GUILayout.EndHorizontal();
    }
    #endregion

    #region 枚举选择器
    /// <summary>
    /// 绘制枚举下拉框
    /// </summary>
    public static T DrawEnumPopup<T>(string label, T currentValue, string[] displayNames) where T : System.Enum
    {
        int currentIndex = System.Convert.ToInt32(currentValue);
        int selectedIndex = EditorGUILayout.Popup(label, currentIndex, displayNames);
        return (T)System.Enum.ToObject(typeof(T), selectedIndex);
    }
    #endregion

    #region 窗口绘制
    /// <summary>
    /// 开始窗口区域
    /// </summary>
    public static void BeginWindowArea(Rect rect)
    {
        GUILayout.BeginArea(rect);
    }

    /// <summary>
    /// 结束窗口区域
    /// </summary>
    public static void EndWindowArea()
    {
        GUILayout.EndArea();
    }

    /// <summary>
    /// 绘制工具栏
    /// </summary>
    public static int DrawToolbar(int selected, string[] options, float maxWidth = 600f)
    {
        return GUILayout.Toolbar(selected, options, GUILayout.MaxWidth(maxWidth));
    }
    #endregion
}
