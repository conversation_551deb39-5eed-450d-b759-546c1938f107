﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;
using System;
using System.Threading;

public class ClearMaterialShader : EditorWindow
{
    public UnityEngine.Object file;
    public string change_value;
    public string[] setPath;
    public int setIndex = 0;
    public float nextTimer = 0;
    public HashSet<string> operaList = new HashSet<string>();
    public Dictionary<string, int> seDic = new Dictionary<string, int>();
    public bool isRebuild = true;
    public UnityEngine.Object dicFile;
    public bool isSaveDic = false;
    public bool isReadDic = false;
    public string dicSavePath = "";
    public string logFlag = "//MatOperaLog.txt";
    public UnityEngine.Object explaceLog;
    public Dictionary<string, string> explaceDic = new Dictionary<string, string>();

    private enum ShaderType {
        isNone,
        isParticle,
        isUiParticle,
        isStandard,
        isWorldStandard,
        isActorUnlit,
        isTerrain4,
        isGrass,
    }

    [MenuItem("自定义工具/策划工具/材质球处理")]
    static void Init()
    {
        ClearMaterialShader window = GetWindow(typeof(ClearMaterialShader), false, "ClearShader") as ClearMaterialShader;
        window.Show();
    }
    // Use this for initialization
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {
        if (this.nextTimer <= Time.realtimeSinceStartup && setPath != null && setPath.Length > 0 && setIndex < setPath.Length && setPath[setIndex] != null)
        {
            var path = setPath[setIndex];
            if (path.EndsWith(".mat"))
            {
                var real_path = path.Replace('\\', '/');
                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                Selection.activeObject = mat;
                EditorUtility.SetDirty(mat);
                this.nextTimer = Time.realtimeSinceStartup + 0.25f;
                setIndex = setIndex + 1;
            }
            else
            {
                setIndex = setIndex + 1;
            }

            if (setIndex >= setPath.Length)
            {
                AssetDatabase.SaveAssets();
            }
        }
    }

    void OnGUI()
    {
        // change_value = EditorGUILayout.TextField("修改的属性:", change_value);
        EditorGUILayout.BeginHorizontal();
        file = EditorGUILayout.ObjectField("操作应用目录: ", file, typeof(UnityEngine.Object), false);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        explaceLog = EditorGUILayout.ObjectField("日志忽略列表: ", explaceLog, typeof(UnityEngine.Object), false);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Clear"))
        {
            ClearPro();
            //SelectMat();
        }

        if (GUILayout.Button("Copy"))
        {
            CopyPro();
        }

        if (GUILayout.Button("Find"))
        {
            FindShader();
        }

        if (GUILayout.Button("SelectMat"))
        {
            SelectMat();
        }

        if (GUILayout.Button("BatchingChange"))
        {
            ChangeValue();
        }

        if (GUILayout.Button("BuildLogDic"))
        {
            BuildExplaceDic();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        dicFile = EditorGUILayout.ObjectField("创建无用材质球搜索字典: ", dicFile, typeof(UnityEngine.Object), false);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        isRebuild = GUILayout.Toggle(isRebuild, "是否重新构建搜索字典");
        isSaveDic = GUILayout.Toggle(isSaveDic, "是否保存搜索字典");
        isReadDic = GUILayout.Toggle(isReadDic, "是否读取本地缓存搜索字典");
        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("ClearInvaildMat"))
        {
            ClearInvaildMat();
        }

        if (GUILayout.Button("AutoSelectMat"))
        {
            AutoSelectMat();
        }
    }

    private void ClearPro()
    {
        /* if (string.IsNullOrEmpty(change_value))
         {
             return;
         }

         var tab = Regex.Split(change_value, ",");
         if (tab.Length < 1)
         {
             return;
         }*/

        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        var is_change = false;
        Debug.LogError("Find File");
        for (int i = 0; i < assetsPath.Length; i++)
        {
            //过滤meta和prefab文件
            if (assetsPath[i].EndsWith(".mat"))
            {
                var real_path = assetsPath[i].Replace('\\', '/');
                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                //if (mat.shader.name == "Game/Particle" || mat.shader.name == "Game/UIParticle" || mat.shader.name == "Game/Standard" || mat.shader.name == "Game/WorldStandard" || mat.shader.name == "Actor/Unlit" || mat.shader.name == "Game/Terrain4" || mat.shader.name == "Game/Grass")
                //{
                    Selection.activeObject = mat;
                    SerializedObject psSource = new SerializedObject(mat);
                    SerializedProperty emissionProperty = psSource.FindProperty("m_SavedProperties");
                    SerializedProperty texEnvs = emissionProperty.FindPropertyRelative("m_TexEnvs");
                    SerializedProperty floats = emissionProperty.FindPropertyRelative("m_Floats");
                    SerializedProperty colos = emissionProperty.FindPropertyRelative("m_Colors");

                    CleanMaterialSerializedProperty(texEnvs, mat);
                    CleanMaterialSerializedProperty(floats, mat);
                    CleanMaterialSerializedProperty(colos, mat);
                    psSource.ApplyModifiedProperties();
                    EditorUtility.SetDirty(mat);

                    if (!is_change)
                        is_change = true;
                //}
            }
        }

        if (is_change)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    private ShaderType GetShaderType(string name)
    {
        ShaderType matShaderType = ShaderType.isNone;
        bool isParticle = name.Contains("Game/Particle");
        bool isUiParticle = name.Contains("Game/UIParticle");
        bool isStandard = name.Contains("Game/Standard");
        bool isWorldStandard = name.Contains("Game/WorldStandard");
        bool isActorUnlit = name.Contains("Actor/Unlit");
        bool isTerrain4 = name.Contains("Game/Terrain4");
        bool isGrass = name.Contains("Game/Grass");

        if (isParticle)
            matShaderType = ShaderType.isParticle;
        else if (isUiParticle)
            matShaderType = ShaderType.isUiParticle;
        else if (isStandard)
            matShaderType = ShaderType.isStandard;
        else if (isWorldStandard)
            matShaderType = ShaderType.isWorldStandard;
        else if (isActorUnlit)
            matShaderType = ShaderType.isActorUnlit;
        else if (isTerrain4)
            matShaderType = ShaderType.isTerrain4;
        else if (isGrass)
            matShaderType = ShaderType.isGrass;

        return matShaderType;
    }

    private void CopyPro()
    {
        /* if (string.IsNullOrEmpty(change_value))
         {
             return;
         }

         var tab = Regex.Split(change_value, ",");
         if (tab.Length < 1)
         {
             return;
         }*/

        List<string> copyList = new List<string>();
        operaList.Clear();
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        var is_change = false;
        Debug.LogError("Find File");
        for (int i = 0; i < assetsPath.Length; i++)
        {
            //过滤meta和prefab文件
            if (assetsPath[i].EndsWith(".mat"))
            {
                var real_path = assetsPath[i].Replace('\\', '/');

                Debug.Log("Opera file:  " + real_path);
                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                string dicName = Path.GetDirectoryName(assetsPath[i]);
                string assetName = Path.GetFileName(assetsPath[i]);
                assetName = "copy_" + assetName;
                string copyPath = Path.Combine(dicName, assetName);
                Material newMat = null;

                string operaMatName = mat.shader.name;
                string ignoreName = this.GetExplaceName(assetsPath[i]);
                bool isIgnore = false;
                if (ignoreName != null && !ignoreName.Equals(string.Empty))
                {
                    operaMatName = ignoreName;
                    AssetDatabase.CopyAsset(assetsPath[i], copyPath);
                    newMat = (Material)AssetDatabase.LoadAssetAtPath(copyPath, typeof(Material));
                    isIgnore = true;
                    if (!copyList.Contains(copyPath))
                    {
                        copyList.Add(copyPath);
                    }
                }

                ShaderType matShaderType = GetShaderType(operaMatName);

                if (!isIgnore && matShaderType == ShaderType.isParticle)
                {
                    newMat = new Material(Shader.Find("Game/Particle"));
                    //newMat.shader = Shader.Find("Game/Particle");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isUiParticle)
                {
                    newMat = new Material(Shader.Find("Game/UIParticle"));
                    //newMat.shader = Shader.Find("Game/UIParticle");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isStandard)
                {
                    newMat = new Material(Shader.Find("Game/Standard"));
                    //newMat.shader = Shader.Find("Game/Standard");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isWorldStandard)
                {
                    newMat = new Material(Shader.Find("Game/WorldStandard"));
                    //newMat.shader = Shader.Find("Game/WorldStandard");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isActorUnlit)
                {
                    newMat = new Material(Shader.Find("Actor/Unlit"));
                    //newMat.shader = Shader.Find("Actor/Unlit");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isTerrain4)
                {
                    newMat = new Material(Shader.Find("Game/Terrain4"));
                    //newMat.shader = Shader.Find("Game/Terrain4t");
                    newMat.CopyPropertiesFromMaterial(mat);
                }
                else if (!isIgnore && matShaderType == ShaderType.isGrass)
                {
                    newMat = new Material(Shader.Find("Game/Grass"));
                    newMat.CopyPropertiesFromMaterial(mat);
                }

                //newMat.CopyPropertiesFromMaterial(mat);
                if (mat.shader != null && newMat != null)
                {
                    if (matShaderType != ShaderType.isNone)
                    {
                        if (!operaList.Contains(assetsPath[i]))
                        {
                            operaList.Add(assetsPath[i]);
                        }

                        String shaderName = operaMatName;
                        SerializedObject psSource = new SerializedObject(mat);
                        SerializedProperty emissionProperty = psSource.FindProperty("m_SavedProperties");
                        SerializedProperty texEnvs = emissionProperty.FindPropertyRelative("m_TexEnvs");
                        SerializedProperty floats = emissionProperty.FindPropertyRelative("m_Floats");
                        SerializedProperty colos = emissionProperty.FindPropertyRelative("m_Colors");

                        
                        int materialStyle = 0;
                        string shader = "YifStandard/YifStandardActor";
                        if (matShaderType == ShaderType.isStandard || matShaderType == ShaderType.isActorUnlit)
                        {
                            shader = "YifStandard/YifStandardActor";
                            materialStyle = 1;
                        }
                        if (matShaderType == ShaderType.isWorldStandard || matShaderType == ShaderType.isGrass || matShaderType == ShaderType.isTerrain4)
                        {
                            materialStyle = 2;
                            shader = "YifStandard/YifStandardEnvironments";
                        }

                        if (matShaderType == ShaderType.isParticle || matShaderType == ShaderType.isUiParticle)
                        {
                            materialStyle = 3;
                            shader = "YifStandard/YifStandardParticle";
                        }

                        mat.shader = Shader.Find(shader);
                        mat.SetFloat("_MaterialStyle", materialStyle);
                        if (mat.shader != null)
                        {
                            if (matShaderType == ShaderType.isParticle || matShaderType == ShaderType.isUiParticle)
                                RplaceKeyValue(newMat, mat);
                            else if (matShaderType == ShaderType.isStandard || matShaderType == ShaderType.isActorUnlit)
                                RplaceStandardKeyValue(newMat, mat, matShaderType);
                            else if (matShaderType == ShaderType.isWorldStandard)
                                RplaceWorldStandardKeyValue(newMat, mat);
                            else if (matShaderType == ShaderType.isTerrain4)
                                RplaceTerrain4KeyValue(newMat, mat);
                            else if (matShaderType == ShaderType.isGrass)
                                ReplaceGrassKeyVale(newMat, mat);

                            RplaceTex(texEnvs, newMat, mat);
                            RplaceFloat(floats, newMat, mat, matShaderType);
                            RplaceColor(colos, newMat, mat, matShaderType);
                            is_change = true;
                        }
                    }
                }

                //AssetDatabase.DeleteAsset(copyPath);
            }
        }

        for (int i = 0; i < copyList.Count; i ++)
        {
            if (File.Exists(copyList[i]))
            {
                AssetDatabase.DeleteAsset(copyList[i]);
            }
        }

        if (is_change)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    private static void CleanMaterialSerializedProperty(SerializedProperty property, Material mat)
    {
        for (int j = property.arraySize - 1; j >= 0; j--)
        {
            string propertyName = property.GetArrayElementAtIndex(j).FindPropertyRelative("first").stringValue;
            Debug.Log("Find property in serialized object : " + propertyName);

            if (!mat.HasProperty(propertyName))
            {
                property.DeleteArrayElementAtIndex(j);
                Debug.Log("Delete legacy property in serialized object : " + propertyName);
            }
        }
    }

    private static void RplaceTex(SerializedProperty property, Material mat, Material target)
    {
        for (int j = property.arraySize - 1; j >= 0; j--)
        {
            var pro = property.GetArrayElementAtIndex(j).FindPropertyRelative("first");
            string propertyName = pro.stringValue;
            var oldPropertyName = propertyName;

            if (target.HasProperty(oldPropertyName))
            {
                if (mat.HasProperty(propertyName))
                {
                    var tex = mat.GetTexture(oldPropertyName);
                    var texOff = mat.GetTextureOffset(oldPropertyName);
                    var texScale = mat.GetTextureScale(oldPropertyName);
                    target.SetTexture(propertyName, tex);
                    target.SetTextureOffset(propertyName, texOff);
                    target.SetTextureScale(propertyName, texScale);
                }
                else
                {
                    var tex = (Texture)property.GetArrayElementAtIndex(j).FindPropertyRelative("second").FindPropertyRelative("m_Texture").objectReferenceValue;
                    if (tex != null)
                    {
                        var texScale = property.GetArrayElementAtIndex(j).FindPropertyRelative("second").FindPropertyRelative("m_Scale").vector2Value;
                        var texOff = property.GetArrayElementAtIndex(j).FindPropertyRelative("second").FindPropertyRelative("m_Offset").vector2Value;

                        target.SetTexture(propertyName, tex);
                        target.SetTextureOffset(propertyName, texOff);
                        target.SetTextureScale(propertyName, texScale);

                        Debug.Log("has property in serialized object : " + mat.name + "|" + propertyName + "|" + tex.name);
                    }
                }

                Debug.Log("has property in serialized object : " + propertyName);
            }
        }
    }

    private static void RplaceFloat(SerializedProperty property, Material mat, Material target, ShaderType matShaderType)
    {
        for (int j = property.arraySize - 1; j >= 0; j--)
        {
            string propertyName = property.GetArrayElementAtIndex(j).FindPropertyRelative("first").stringValue;
            var oldPropertyName = propertyName;
            Debug.Log("Find float property in serialized object : " + propertyName);
            float oldValue = 0f;
            if (propertyName == "_UVCut")
            {
                oldPropertyName = propertyName;
                propertyName = "_VertAnimGrassUVCut";
            }
            else if (propertyName == "_WaveScale")
            {
                oldPropertyName = propertyName;
                propertyName = "_VertAnimGrassWaveScale";
            }
            else if (propertyName == "_WaveControl1")
            {
                oldPropertyName = propertyName;
                propertyName = "_VertAnimGrassWaveControl";
            }
            else if (propertyName == "_TimeControl1")
            {
                oldPropertyName = propertyName;
                propertyName = "_VertAnimGrassTimeControl";
            }

            if (target.HasProperty(propertyName))
            {
                float value = 0f;
                if (mat.HasProperty(oldPropertyName))
                {
                    value = mat.GetFloat(oldPropertyName);
                }
                else
                {
                    value = property.GetArrayElementAtIndex(j).FindPropertyRelative("second").floatValue;
                }

                if (propertyName == "_RenderingMode")
                {
                    if (matShaderType == ShaderType.isParticle || matShaderType == ShaderType.isUiParticle)
                    {
                        if (value == 3)
                            value = 0;
                        else if (value == 0)
                            value = 3;
                        else if (value == 1)
                            value = 4;
                        else if (value == 2)
                            value = 1;
                    }
                    else if (matShaderType == ShaderType.isStandard || matShaderType == ShaderType.isActorUnlit)
                    {
                        if (value == 1 || value == 2 || value == 3 || value == 4)
                            value = 3;
                    }
                    else if (matShaderType == ShaderType.isTerrain4)
                    {
                        value = 0;
                    }
                    else if (matShaderType == ShaderType.isGrass)
                    {
                        value = 1;
                    }
                    else if (matShaderType == ShaderType.isWorldStandard)
                    {
                        if (value == 2)
                            value = 3;
                        else if (value == 3)
                            value = 2;
                        else if (value == 4)
                            value = 3;
                        else if (value == 5)
                            value = 3;
                    }
                }
                target.SetFloat(propertyName, value);
                Debug.Log("has float property in serialized object value: " + propertyName);
            }
        }
    }

    private static void RplaceColor(SerializedProperty property, Material mat, Material target, ShaderType matShaderType)
    {
        for (int j = property.arraySize - 1; j >= 0; j--)
        {
            string propertyName = property.GetArrayElementAtIndex(j).FindPropertyRelative("first").stringValue;
            string valueStr = propertyName;         
            Debug.Log("Find color property in serialized object : " + propertyName);
            bool isSpecial = false;
            Color tineColor = new Color();
            if (propertyName == "_TintColor")
            {
                propertyName = "_MainColor";
                isSpecial = true;
            }

            if (target.HasProperty(propertyName) || isSpecial)
            {
                if (mat.HasProperty(valueStr))
                {
                    var value = mat.GetColor(valueStr);
                    target.SetColor(propertyName, value);
                }
                else
                {
                    var value = property.GetArrayElementAtIndex(j).FindPropertyRelative("second").colorValue;
                    target.SetColor(propertyName, value);
                    Debug.Log("get color info  propertyName: " + propertyName + " | valueStr: " + valueStr + "   |shaderName:    " + matShaderType + "   |value:    " + value);
                }
                //else
                //    Debug.Log("get color info  propertyName: " + propertyName + " | valueStr: " + valueStr + "   |shaderName:    " + matShaderType);

                Debug.Log("has color property in serialized object value: " + propertyName);
            }
        }
    }

    private static void RplaceKeyValue(Material mat, Material target)
    {
        string[] oldTab = mat.shaderKeywords;
        Debug.Log("Old Material Shader Key Num：" + oldTab.Length);

        // List<string> newTab = new List<string>();
        HashSet<string> newTab = new HashSet<string>();
        Dictionary<String, bool> dic = new Dictionary<String, bool>();
        Dictionary<String, bool> newDic = new Dictionary<String, bool>();

        foreach (string key in oldTab)
        {
            bool kValue = false;
            if (!dic.TryGetValue(key, out kValue))
            {
                dic.Add(key, true);
            }
        }


        newDic.Add("ENABLE_VERT_COLOR", true);

        foreach (string key in oldTab)
        {
            Debug.Log("Old Material Shader Key：" + key);
            bool kValue;
            var value = "";
            if (key == "_ALPHA_TEST")
                value = "ENABLE_ALPHA_TEST";
            else if (key == "_ALPHA_BLEND")
                value = "ENABLE_ALPHA_BLEND";
            else if (key == "_ALPHA_PREMULTIPLY")
                value = "ENABLE_MAIN_TEX_USE_CHANNEL_A";
            else if (key == "_DECAL_CHANNEL_A")
            {
                if (dic.TryGetValue("ENABLE_DECAL", out kValue))
                {
                    value = "ENABLE_DECAL_ALPHA";
                }
            }
            else if (key == "ENABLE_DECAL")
            {
                if (!dic.TryGetValue("_DECAL_CHANNEL_A", out kValue))
                {
                    value = "ENABLE_DECAL_ALL";
                }
            }
            else if (key == "ENABLE_DISSLOVE")
            {
                if (!dic.TryGetValue("ENABLE_DISSLOVE_VERTEX_COLOR", out kValue))
                {
                    value = "ENABLE_DISSOLVE_AMOUNT";
                }
            }
            else if (key == "ENABLE_DISSLOVE_VERTEX_COLOR")
            {
                if (dic.TryGetValue("ENABLE_DISSLOVE", out kValue))
                {
                    value = "ENABLE_DISSOLVE_VERTEX";
                }
            }
            else if (key == "ENABLE_DISSLOVE_OUTLINE")
            {
                if (dic.TryGetValue("ENABLE_DISSLOVE", out kValue))
                {
                    value = "ENABLE_DISSLOVE_OUTLINE";
                }
            }
            else if (key == "ENABLE_UV_NOISE")
            {
                if (!newDic.TryGetValue("ENABLE_UV_NOISE", out kValue))
                {
                    newDic.Add("ENABLE_UV_NOISE", true);
                }

                value = "";
            }
            else if (key == "_CHANNEL_A")
            {
                value = "ENABLE_MAIN_TEX_USE_CHANNEL_A";
            }
            else
                value = key;

            //if (value != "" && !newTab.Contains(value))
            //{
            //    newTab.Add(value);
            //}

            if (value != "" && !newDic.TryGetValue(value, out kValue))
            {
                newDic.Add(value, true);
            }
        }

        //if (newTab.Count > 0)
        //{
        //    string[] setTab = new string[newTab.Count];
        //    var index = 0;
        //    foreach (string key in newTab)
        //    {
        //        setTab[index] = key;
        //        index = index + 1;
        //        Debug.LogFormat("Set New Keyword:index {0} key {1}", index, key);
        //    }

        //    target.shaderKeywords = setTab;
        //}

        if (newDic.Count > 0)
        {
            string[] setTab = new string[newDic.Count];
            var index = 0;
            foreach (KeyValuePair<string, bool> v in newDic)
            {
                if (v.Value)
                {
                    setTab[index] = v.Key;
                    index = index + 1;
                    Debug.LogFormat("Set New Keyword:index {0} key {1}", index, v.Key);
                }
            }

            target.shaderKeywords = setTab;
        }
    }

    private static void RplaceStandardKeyValue(Material mat, Material target, ShaderType matShaderType)
    {
        string[] oldTab = mat.shaderKeywords;
        Debug.Log("Old Material Shader Key Num：" + oldTab.Length);

        // List<string> newTab = new List<string>();
        HashSet<string> newTab = new HashSet<string>();
        Dictionary<String, bool> dic = new Dictionary<String, bool>();
        Dictionary<String, bool> newDic = new Dictionary<String, bool>();

        foreach (string key in oldTab)
        {
            bool kValue = false;
            if (!dic.TryGetValue(key, out kValue))
            {
                dic.Add(key, true);
            }
        }

        if (matShaderType == ShaderType.isStandard)
        {
            newDic.Add("ENABLE_DIFFUSE", true);
        }

        foreach (string key in oldTab)
        {
            Debug.Log("Old Material Shader Key：" + key);
            bool kValue;
            var value = "";
            if (key == "_ALPHA_TEST")
                value = "ENABLE_ALPHA_TEST";
            else if (key == "_ALPHA_BLEND")
                value = "ENABLE_ALPHA_BLEND";
            else if (key == "_ALPHA_PREMULTIPLY" || key == "_CHANNEL_A")
                value = "ENABLE_MAIN_TEX_USE_CHANNEL_A";
            else if (key == "ENABLE_SEPCULAR")
                value = "ENABLE_SPECULAR";
            else if (key == "ENABLE_NORMAL")
                value = "ENABLE_NORMAL_TEX";
            else if (key == "ENABLE_FLOW_ADD")
                value = "ENABLE_FLOW_ADD";
            else
                value = key;

            if (value != "" && !newDic.TryGetValue(value, out kValue))
            {
                newDic.Add(value, true);
            }
        }

        if (newDic.Count > 0)
        {
            string[] setTab = new string[newDic.Count];
            var index = 0;
            foreach (KeyValuePair<string, bool> v in newDic)
            {
                if (v.Value)
                {
                    setTab[index] = v.Key;
                    index = index + 1;
                    Debug.LogFormat("Set New Keyword:index {0} key {1}", index, v.Key);
                }
            }

            target.shaderKeywords = setTab;
        }
    }

    private void RplaceWorldStandardKeyValue(Material mat, Material target)
    {
        string[] oldTab = mat.shaderKeywords;
        Debug.Log("Old Material Shader Key Num：" + oldTab.Length);

        // List<string> newTab = new List<string>();
        HashSet<string> newTab = new HashSet<string>();
        Dictionary<String, bool> dic = new Dictionary<String, bool>();
        Dictionary<String, bool> newDic = new Dictionary<String, bool>();

        foreach (string key in oldTab)
        {
            bool kValue = false;
            if (!dic.TryGetValue(key, out kValue))
            {
                dic.Add(key, true);
            }
        }

        newDic.Add("ENABLE_FOG", true);

        foreach (string key in oldTab)
        {
            Debug.Log("Old Material Shader Key：" + key);
            bool kValue;
            var value = "";
            if (key == "_ALPHA_TEST")
                value = "ENABLE_ALPHA_TEST";
            else if (key == "_ALPHA_BLEND")
                value = "ENABLE_ALPHA_BLEND";
            else if (key == "_ALPHA_PREMULTIPLY" || key == "_CHANNEL_A")
                value = "ENABLE_MAIN_TEX_USE_CHANNEL_A";
            else if (key == "ENABLE_NORMAL")
                value = "ENABLE_NORMAL_TEX";
            else
                value = key;

            if (value != "" && !newDic.TryGetValue(value, out kValue))
            {
                newDic.Add(value, true);
            }
        }

        if (newDic.Count > 0)
        {
            string[] setTab = new string[newDic.Count];
            var index = 0;
            foreach (KeyValuePair<string, bool> v in newDic)
            {
                if (v.Value)
                {
                    setTab[index] = v.Key;
                    index = index + 1;
                    Debug.LogFormat("Set New Keyword:index {0} key {1}", index, v.Key);
                }
            }

            target.shaderKeywords = setTab;
        }
    }

    private void RplaceTerrain4KeyValue(Material mat, Material target)
    {
        string[] oldTab = mat.shaderKeywords;
        Debug.Log("Old Material Shader Key Num：" + oldTab.Length);

        // List<string> newTab = new List<string>();
        HashSet<string> newTab = new HashSet<string>();
        Dictionary<String, bool> dic = new Dictionary<String, bool>();
        Dictionary<String, bool> newDic = new Dictionary<String, bool>();

        foreach (string key in oldTab)
        {
            bool kValue = false;
            if (!dic.TryGetValue(key, out kValue))
            {
                dic.Add(key, true);
            }
        }

        newDic.Add("ENABLE_FOG", true);
        newDic.Add("ENABLE_MIX_TEXTURE4", true);
        foreach (string key in oldTab)
        {
            Debug.Log("Old Material Shader Key：" + key);
            bool kValue;
            var value = "";
            if (key == "_ALPHA_TEST")
                value = "ENABLE_ALPHA_TEST";
            else if (key == "_ALPHA_BLEND")
                value = "ENABLE_ALPHA_BLEND";
            else if (key == "_ALPHA_PREMULTIPLY" || key == "_CHANNEL_A")
                value = "ENABLE_MAIN_TEX_USE_CHANNEL_A";
            else if (key == "ENABLE_NORMAL")
                value = "ENABLE_NORMAL_TEX";
            else if (key == "ENABLE_SEPCULAR")
                value = "ENABLE_SPECULAR";
            else
                value = key;

            if (value != "" && !newDic.TryGetValue(value, out kValue))
            {
                newDic.Add(value, true);
            }
        }

        if (newDic.Count > 0)
        {
            string[] setTab = new string[newDic.Count];
            var index = 0;
            foreach (KeyValuePair<string, bool> v in newDic)
            {
                if (v.Value)
                {
                    setTab[index] = v.Key;
                    index = index + 1;
                    Debug.LogFormat("Set New Keyword:index {0} key {1}", index, v.Key);
                }
            }

            target.shaderKeywords = setTab;
        }
    }

    private void ReplaceGrassKeyVale(Material mat, Material target)
    {
        string[] oldTab = mat.shaderKeywords;
        Debug.Log("Old Material Shader Key Num：" + oldTab.Length);

        // List<string> newTab = new List<string>();
        HashSet<string> newTab = new HashSet<string>();
        Dictionary<String, bool> dic = new Dictionary<String, bool>();
        Dictionary<String, bool> newDic = new Dictionary<String, bool>();

        foreach (string key in oldTab)
        {
            bool kValue = false;
            if (!dic.TryGetValue(key, out kValue))
            {
                dic.Add(key, true);
            }
        }

        newDic.Add("ENABLE_FOG", true);
        newDic.Add("ENABLE_VERT_ANIM_GRASS_WAVE", true);
        foreach (string key in oldTab)
        {
            Debug.Log("Old Material Shader Key：" + key);
            bool kValue;
            var value = "";
            value = key;

            if (value != "" && !newDic.TryGetValue(value, out kValue))
            {
                newDic.Add(value, true);
            }
        }

        if (newDic.Count > 0)
        {
            string[] setTab = new string[newDic.Count];
            var index = 0;
            foreach (KeyValuePair<string, bool> v in newDic)
            {
                if (v.Value)
                {
                    setTab[index] = v.Key;
                    index = index + 1;
                    Debug.LogFormat("Set New Keyword:index {0} key {1}", index, v.Key);
                }
            }

            target.shaderKeywords = setTab;
        }
    }

    private void SelectMat()
    {
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);
        if (this.operaList.Count == 0)
        {
            Debug.LogError("Copy opera not change mat");
            return;
        }

        //string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        var is_change = false;
        Debug.LogError("Find File");
        string[] pathList = new String[this.operaList.Count];
        int pathIndex = 0;
        foreach (string path in this.operaList)
        {
            pathList[pathIndex] = path;
            pathIndex = pathIndex + 1;
        }

        this.setPath = pathList;
        //this.setPath = assetsPath;
        this.setIndex = 0;
        // this.startTime = Time.frameCount;
        /*for (int i = 0; i < assetsPath.Length; i++)
         {
             //过滤meta和prefab文件
             if (assetsPath[i].EndsWith(".mat"))
             {
                 var real_path = assetsPath[i].Replace('\\', '/');
                 Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                 Selection.activeObject = mat;

                 EditorUtility.SetDirty(mat);
                 if (!is_change)
                     is_change = true;
             }
         }*/
    }

    private void AutoSelectMat()
    {
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);
        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.mat", SearchOption.AllDirectories);
        var is_change = false;
        string[] pathList = new String[assetsPath.Length];
        int pathIndex = 0;
        foreach (string path in assetsPath)
        {
            pathList[pathIndex] = path;
            pathIndex = pathIndex + 1;
        }

        this.setPath = pathList;
        this.setIndex = 0;
    }

    private void setMatOpera(System.Object state)
    {
        /*if (path.EndsWith(".mat"))
        {
            var real_path = path.Replace('\\', '/');
            Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
            Selection.activeObject = mat;
            EditorUtility.SetDirty(mat);

            Selection.activeObject = null;
        }*/
    }

    public void BuildExplaceDic()
    {
        string name = string.Empty;
        if (this.explaceLog != null)
        {
            string logPath = AssetDatabase.GetAssetPath(this.explaceLog);
            if (File.Exists(logPath))
            {
                explaceDic.Clear();
                var allLine = File.ReadAllLines(logPath);
                foreach (string line in allLine)
                {
                    string[] kv = line.Split('|');
                    if (kv[0] != null && !kv[0].Equals(string.Empty) && kv[1] != null && !kv[1].Equals(string.Empty))
                    {
                        explaceDic.Add(kv[0], kv[1]);
                    }
                }
            }
        }
    }

    string GetExplaceName(string path)
    {
        string name = string.Empty;
        string checkPath = path.Replace('\\', '/');
        checkPath = checkPath.Replace("Assets/Export/", "");
        if (explaceDic.TryGetValue(checkPath, out name))
        {
        }

        return name;
    }

    private void FindShader()
    {
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        var is_change = false;
        Debug.LogError("Find File");
        for (int i = 0; i < assetsPath.Length; i++)
        {
            //过滤meta和prefab文件
            if (assetsPath[i].EndsWith(".mat"))
            {
                var real_path = assetsPath[i].Replace('\\', '/');

                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                Material newMat = new Material(Shader.Find("Game/Particle"));
                newMat.CopyPropertiesFromMaterial(mat);
                if (mat.shader != null)
                {
                    if (mat.shader.name == "Game/Particle")
                    {
                        Debug.Log("The mat use Game/Partice: " + assetsPath[i]);
                    }
                }
            }
        }
    }


    private void ChangeValue()
    {
        /* if (string.IsNullOrEmpty(change_value))
         {
             return;
         }

         var tab = Regex.Split(change_value, ",");
         if (tab.Length < 1)
         {
             return;
         }*/
        operaList.Clear();
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        Debug.LogError(StrCheckFolderPath);

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        var is_change = false;
        Debug.LogError("Find File");
        for (int i = 0; i < assetsPath.Length; i++)
        {
            //过滤meta和prefab文件
            if (assetsPath[i].EndsWith(".mat"))
            {
                var real_path = assetsPath[i].Replace('\\', '/');
                Material mat = (Material)AssetDatabase.LoadAssetAtPath(real_path, typeof(Material));
                if (mat != null)
                {
					//强制修复，直接指定shader、MaterialStyle，有出入再手动修复
                    mat.shader = Shader.Find("YifStandard/YifStandardParticle");
                    mat.SetFloat("_MaterialStyle", 3);

                    if (!operaList.Contains(assetsPath[i]))
                    {
                        operaList.Add(assetsPath[i]);
                    }
                }
            }
        }

        //if (is_change)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    void ClearInvaildMat(bool buildFlag = false)
    {
        if (file == null)
        {
            Debug.LogError("The File Is Null");
            return;
        }

        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        string dic = Path.GetDirectoryName(StrCheckFolderPath);
        dicSavePath = System.IO.Directory.GetCurrentDirectory() + "//MatOperaLog.txt";

        if (!buildFlag && isRebuild)
        {
            if (isReadDic)
            {
                if (File.Exists(dicSavePath))
                {
                    seDic.Clear();
                    var allLine = File.ReadAllLines(dicSavePath);
                    foreach (string line in allLine)
                    {
                        seDic.Add(line, 1);
                    }
                }
                else
                {
                    Debug.LogError("MatOperaLog.txt file is invaild!");
                    return;
                }
            }
            else
            {
                BuildDic();
                return;
            }
        }

        if (seDic.Count <= 0)
        {
            Debug.LogError("The dic is null");
            return;
        }

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        int startIndex = 0;
        if (EditorApplication.update == null)
        {
            EditorApplication.update = delegate ()
            {
                string file = assetsPath[startIndex];

                bool isCancel = EditorUtility.DisplayCancelableProgressBar("正在检查无效材质球", file, (float)startIndex / (float)assetsPath.Length);
                if (file.EndsWith(".mat"))
                {
                    var real_path = file.Replace('\\', '/');
                    if (!seDic.ContainsKey(real_path))
                    {
                        Debug.LogError("The mat is no depen:    " + real_path);
                        AssetDatabase.DeleteAsset(real_path);
                    }
                }

                startIndex++;
                if (isCancel || startIndex >= assetsPath.Length)
                {
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update = null;
                    startIndex = 0;
                    Debug.Log("检查完毕");
                    AssetDatabase.Refresh();
                }

            };
        }

        //for (int i = 0; i < assetsPath.Length; i++)
        //{
        //    if (assetsPath[i].EndsWith(".mat"))
        //    {
        //        var real_path = assetsPath[i].Replace('\\', '/');
        //        if (!seDic.ContainsKey(real_path))
        //        {
        //            Debug.LogError("The mat is no depen:    "+ real_path);
        //        }
        //    }
        //}
    }

    void BuildDic()
    {
        if (dicFile == null)
        {
            Debug.LogError("BuildDic is fail, buildFile is null");
            return;
        }
        seDic.Clear();
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(dicFile);
        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
        List<string> fileType = new List<string>() { ".prefab", ".unity", ".asset" };

        int startIndex = 0;
        EditorApplication.update = delegate ()
        {
            string file = assetsPath[startIndex];

            bool isCancel = EditorUtility.DisplayCancelableProgressBar("建立查找字典中", file, (float)startIndex / (float)assetsPath.Length);

            for (int j = 0; j < fileType.Count; j++)
            {
                if (file.EndsWith(fileType[j]))
                {
                    var real_path = file.Replace('\\', '/');
                    string[] depenList = AssetDatabase.GetDependencies(real_path);
                    for (int l = 0; l < depenList.Length; l++)
                    {
                        if (depenList[l].EndsWith(".mat") && !seDic.ContainsKey(depenList[l]))
                        {
                            seDic.Add(depenList[l], 1);
                            Debug.LogError("Add mat on dic: " + depenList[l]);
                        }
                    }

                    break;
                }
            }

            startIndex++;
            if (isCancel || startIndex >= assetsPath.Length)
            {
                EditorUtility.ClearProgressBar();
                EditorApplication.update = null;
                string dic = Path.GetDirectoryName(StrCheckFolderPath);
                Debug.Log("字典创建结束 " + System.IO.Directory.GetCurrentDirectory());
                if (isCancel)
                {
                    seDic.Clear();
                }

                bool isNext = startIndex >= assetsPath.Length;
                startIndex = 0;
                if (isNext)
                {
                    if (isSaveDic)
                    {
                        string logPath = System.IO.Directory.GetCurrentDirectory() + "//MatOperaLog.txt";
                        if (File.Exists(logPath))
                        {
                            File.Delete(logPath);
                        }

                        Debug.Log(logPath.Replace("\\", "\\\\"));
                        //var fileS = File.CreateText(logPath);

                        FileStream fs = new FileStream(logPath.Replace("\\", "\\\\"), FileMode.Create, FileAccess.ReadWrite);
                        StreamWriter sw = new StreamWriter(fs);
                        foreach (var k in seDic)
                        {
                            sw.WriteLine(k.Key);
                        }
                        sw.Close();
                        fs.Close();
                    }

                    ClearInvaildMat(true);
                }
            }

        };

        //for (int i = 0; i < assetsPath.Length; i++)
        //{
        //    for (int j = 0; j < fileType.Count; j++)
        //    {
        //        if (assetsPath[i].EndsWith(fileType[j]))
        //        {
        //            var real_path = assetsPath[i].Replace('\\', '/');
        //            string [] depenList = AssetDatabase.GetDependencies(real_path);
        //            for (int l = 0; l < depenList.Length; l++)
        //            {
        //                if (depenList[l].EndsWith(".mat") && !seDic.ContainsKey(depenList[l]))
        //                {
        //                    seDic.Add(depenList[l], 1);
        //                    Debug.LogError("Add mat on dic: "+ depenList[l]);
        //                }
        //            }

        //            break;
        //        }
        //    }
        //}
    }
}
