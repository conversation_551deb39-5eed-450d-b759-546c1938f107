﻿using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class DataToPrefab : EditorWindow
{
    private static RectTransform _panel;
    private static bool _isFirst = true;    //第一个panel计算一次pos
    private static string _viewName;
    private static UnityEngine.Object _prefab;
    private static PrefabData _data;
    private static Nirvana.UINameTable _nameTable;
    private static Vector2 _lastPos = new Vector2();
    private static List<string> _list = new List<string>();

    [MenuItem("自定义工具/UI类/通过配置文件生成prefab")]
    public static void CreatePrefab()
    {
        EditorWindow window = EditorWindow.GetWindow(typeof(DataToPrefab), false, "通过配置文件生成prefab");
        window.position = new Rect(Screen.width / 2, 400, 400, 300);
    }

    private void OnDisable()
    {
        _list.Clear();
        _data = null;
        _panel = null;
        _nameTable = null;
        _prefab = null;
    }

    private void OnGUI()
    {
        _prefab = EditorGUILayout.ObjectField("界面json路径:", _prefab, typeof(UnityEngine.Object), false);

        if (_prefab == null) return;

        if(GUILayout.Button("创建prefab"))
        {
            var folders = AssetDatabase.GetAssetPath(_prefab);
            string[] files = Directory.GetFiles(folders, "*.*", SearchOption.TopDirectoryOnly);

            int count = 0;
            int totalCount = files.Length;

            foreach (var file in files)
            {
                if (file.EndsWith(".json"))
                {
                    CreateEmptyGo(file);
                    var str = File.ReadAllText(file);
                    try
                    {
                        _data = JsonConvert.DeserializeObject<PrefabData>(str);
                    }
                    catch (Exception ex)
                    {
                        Debug.Log(file + "------------");
                        Debug.Log(ex.Message);
                    }
                    if (_data != null)
                    {
                        var parent = GameObject.Find("Canvas");
                        CreateChildList(_data, parent.GetComponent<RectTransform>());
                        _isFirst = true;
                        _data = null;
                        _panel.anchoredPosition = _lastPos;
                        ResetPosition(_panel);
                        CreatePrefabGo(_panel.gameObject, file);
                    }
                }
                EditorUtility.DisplayProgressBar("Create Prefab", string.Format("{0}/{1}{2}", count, totalCount, file), count / (float)totalCount);
                count++;
            }
            EditorUtility.ClearProgressBar();
        }
    }

    private static void ResetPosition(RectTransform tran)
    {
        if (tran == null || tran.name == "ScrollImage" || tran.name == "Text") return;
        
        if (tran.childCount > 0)
        {
            for(int i = 0; i < tran.childCount; i++)
            {
                ResetPosition(tran.GetChild(i).GetComponent<RectTransform>());
            }
        }

        tran.anchorMax = new Vector2(0.5f, 0.5f);
        tran.anchorMin = new Vector2(0.5f, 0.5f);

        float x = 0;
        float y = 0;
        if(tran.parent == null)
        {
            tran.anchoredPosition = new Vector2(x, y);
            return;
        }
        var parent = tran.parent.GetComponent<RectTransform>();
        if (parent.name != "Canvas")
        {
            x = tran.localPosition.x - (0.5f * parent.rect.width) * 2;
            y = tran.localPosition.y - (0.5f * parent.rect.height) * 2;

        }
        else
        {
            x = tran.localPosition.x - 1134 * 0.5f;
            y = tran.localPosition.y - 768 * 0.5f;
        }

        tran.anchoredPosition = new Vector2(x, y);
    }

    //界面根节点
    private static void CreateEmptyGo(string file)
    {
        var n = file.Split('\\')[1].Split('_');
        //var n = file.Split('/')[5].Split('.')[0].Split('_');
        string name = string.Empty;
        for (int i = 0; i < n.Length; i++)
        {
            if (n[i] == "cfg") break;
            if (n[i] == "ui")
                name += "UI";
            else
                name += char.ToUpper(n[i][0]) + n[i].Substring(1);
        }

        if (_list.Contains(name)) return;

        _list.Add(name);
        RectTransform go = CreateCanvas();
        go.name = name;
        ResetPosition(go);
        var parent = GameObject.Find("Canvas");
        go.SetParent(parent.GetComponent<RectTransform>());
        CreatePrefabGo(go.gameObject, file);
    }

    private static void CreatePrefabGo(GameObject go, string file)
    {
        var n = file.Split('\\')[1].Split('_');
        //var n = file.Split('/')[5].Split('.')[0].Split('_');
        string folderName = string.Empty;
        for (int i = 0; i < n.Length; i++)
        {
            if (n[i] == "cfg") break;
            if (i == n.Length - 1 || n[i + 1] == "cfg")
                folderName += n[i];
            else
                folderName += n[i] + "_";
        }

        var baseDir = string.Format("Assets/Game/UIs/View/{0}/", folderName);
        if (!Directory.Exists(baseDir))
        {
            Directory.CreateDirectory(baseDir);
        }

        var path = Path.Combine(baseDir, go.name + ".prefab").Replace("\\", "/");
        if(File.Exists(path))
        {
            var targetPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            PrefabUtility.ReplacePrefab(go, targetPrefab);
        }
        else
        {
            PrefabUtility.CreatePrefab(path, go);
        }
    }

    private static string GetFolderName(string str)
    {
        string name = string.Empty;
        var n = str.Split('\\')[1].Split('.')[0].Split('_');
        for (int i = 0; i < n.Length - 1; i++)
        {
            if (i == n.Length - 2)
            {
                name += n[i];
            }
            else
            {
                name = name + n[i] + "_";
            }
        }
        return name;
    }

    private static RectTransform CreateCanvas()
    {
        GameObject go = new GameObject();
        go.layer = 5;
        RectTransform tran = go.AddComponent<RectTransform>();
        var canvas = go.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.pixelPerfect = false;
        canvas.sortingOrder = 0;
        canvas.targetDisplay = 0;
        canvas.additionalShaderChannels = AdditionalCanvasShaderChannels.None;

        tran.localScale = new Vector3(1, 1, 1);

        var scaler = go.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1334, 768);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.referencePixelsPerUnit = 100;
        scaler.matchWidthOrHeight = 1f;
        var reyCaster = go.AddComponent<GraphicRaycaster>();
        reyCaster.ignoreReversedGraphics = true;
        reyCaster.blockingObjects = GraphicRaycaster.BlockingObjects.None;

        return tran;
    }

    private static void CreateChildList(PrefabData pb, RectTransform parent)
    {
        var tran = CreateItem(pb, parent);
        for (int i = 0; i < pb.childArray.Count; i++)
        {
            CreateChildList(pb.childArray[i], tran);
        }
    }

    private static RectTransform CreateItem(PrefabData data, RectTransform parent)
    {
        RectTransform tran = null;
        switch (data.t)
        {
            case "img9":
                tran = CreateImg9(parent, data.n == "" ? "Img9" : data.n, data.p, data.x, data.y, data.w, data.h, data.cx, data.cy, data.cw, data.ch);
                break;
            case "btn":
                tran = CreateBtn(parent, data.n == "" ? "Btn" : data.n, data.pn, data.x, data.y, data.w, data.h, data.txt, data.r, data.g, data.b, data.tfs);
                break;
            case "img":
                tran = CreateImage(parent, data.n == "" ? "Img" : data.n, data.p, data.x, data.y, data.w, data.h, data.sx, data.sy);
                break;
            case "text":
                tran = CreateText(parent, data.n == "" ? "Txt" : data.n, data.txt, data.x, data.y, data.w, data.h, data.ta, data.tfs, data.r, data.g, data.b);
                break;
            case "ph":
                tran = CreateEmptyObj(parent, data.n == "" ? "ph" : data.n, data.x, data.y, data.w, data.h, data.ax, data.ay);
                break;
            case "layout":
                tran = CreateLayout(parent, data.n == "" ? "Layout" : data.n, data.x, data.y, data.w, data.h);
                break;
            case "prog9":
                tran = CreateProp9(parent, data.n == "" ? "Scollbar" : data.n, data.x, data.y, data.w, data.h, data.progress, data.cx, data.cy, data.cw, data.ch);
                break;
            case "rich":
                tran = CreateRichText(parent, data.n == "" ? "RichTxt" : data.n, data.txt, data.x, data.y, data.w, data.h, data.ta, data.tfs, data.r, data.g, data.b);
                break;
            default:
                break;
        }

        return tran;
    }

    private static void AddGoToNameTable(string name, GameObject go)
    {
        if (!_nameTable.Lookup.ContainsKey(name))
        {
            _nameTable.Add(name, go);
        }
        else
        {
            var idx = 1;
            while (true)
            {
                var newName = string.Format("{0}_{1}", name, idx++);
                if (!_nameTable.Lookup.ContainsKey(newName))
                {
                    _nameTable.Add(newName, go);
                    break;
                }
            }
        }
    }

    private static RectTransform CreateLayout(RectTransform parent, string name, float x, float y, float w, float h)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        var panel = go.AddComponent<RectTransform>();
        panel.SetParent(parent);
        if (_isFirst)
        {
            _isFirst = false;
            _viewName = name;
            _nameTable = go.AddComponent<Nirvana.UINameTable>();
            _lastPos = new Vector2(x, y);
            _panel = panel;
        }
        panel.anchorMax = new Vector2(0, 0);
        panel.anchorMin = new Vector2(0, 0);
        panel.pivot = new Vector2(0, 0);
        panel.localPosition = new Vector3(x, y, 0);
        panel.sizeDelta = new Vector2(w, h);
        panel.localScale = new Vector3(1, 1, 1);
        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }
        return panel;
    }

    private static RectTransform CreateText(RectTransform parent, string name, string txt, float x, float y, float w,
        float h, float ta, int tfs, float r, float g, float b)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        Text t = go.AddComponent<Text>();
        t.text = txt;
        t.alignment = (TextAnchor)(ta + 3);
        t.fontSize = tfs;
        t.color = new Color(r / 255f, g / 255f, b / 255f, 1f);
        var rectTrans = t.GetComponent<RectTransform>();
        rectTrans.SetParent(parent);
        rectTrans.anchorMax = new Vector2(0, 0);
        rectTrans.anchorMin = new Vector2(0, 0);
        rectTrans.pivot = new Vector2(0, 1);
        rectTrans.localPosition = new Vector2(x, y);
        rectTrans.sizeDelta = new Vector2(w, h);
        rectTrans.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }

        return rectTrans;
    }

    private static RectTransform CreateImage(RectTransform parent, string name, string p, float x,
        float y, float w, float h, float sx, float sy)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        var image = go.AddComponent<Image>();
        image.sprite = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/Game/UIs/UIRes/" + p);

        var tran = image.GetComponent<RectTransform>();
        tran.SetParent(parent);
        tran.anchorMax = new Vector2(0, 0);
        tran.anchorMin = new Vector2(0, 0);
        tran.pivot = new Vector2(0.5f, 0.5f);
        tran.localPosition = new Vector2(x, y);
        tran.sizeDelta = new Vector2(w, h);
        tran.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }

        return tran;
    }

    private static RectTransform CreateBtn(RectTransform parent, string name, string pn, float x, float y, float w, float h,
        string txt, int r, int g, int b, int tfs)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        var btn = go.AddComponent<Button>();
        var image = btn.gameObject.AddComponent<Image>();
        image.sprite = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/Game/UIs/UIRes/" + pn);

        GameObject lbl = new GameObject();
        lbl.name = "Text";
        var text = lbl.AddComponent<Text>();
        text.text = txt;
        text.color = new Color(r / 255f, g / 255f, b / 255f, 1f);
        text.rectTransform.pivot = new Vector2(0, 1);
        text.rectTransform.localPosition = new Vector3(-20, 14, 0);
        text.fontSize = tfs;
        lbl.transform.SetParent(go.transform);

        var tran = btn.GetComponent<RectTransform>();
        tran.SetParent(parent);
        tran.anchorMax = new Vector2(0, 0);
        tran.anchorMin = new Vector2(0, 0);
        tran.pivot = new Vector2(0.5f, 0.5f);
        tran.localPosition = new Vector2(x, y);
        tran.sizeDelta = new Vector2(w, h);
        tran.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }
        return tran;
    }

    private static RectTransform CreateImg9(RectTransform parent, string name, string p, float x, float y, float w, float h,
        float cx, float cy, float cw, float ch)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;

        TextureImporter texture = AssetImporter.GetAtPath("Assets/Game/UIs/UIRes/" + p) as TextureImporter;
        if(texture != null)
        {
            texture.spriteBorder = new Vector4(cx, ch + cy, cw + cx, cy);
            texture.SaveAndReimport();
        }
        else
        {
            //Debug.LogError(string.Format("没有找到资源{0}", p));
        }

        var image = go.AddComponent<Image>();
        var sprite = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/Game/UIs/UIRes/" + p);
        image.sprite = sprite;
        image.type = Image.Type.Sliced;

        var tran = image.GetComponent<RectTransform>();
        tran.SetParent(parent);
        tran.anchorMax = new Vector2(0, 0);
        tran.anchorMin = new Vector2(0, 0);
        tran.pivot = new Vector2(0.5f, 0.5f);
        tran.localPosition = new Vector2(x, y);
        tran.sizeDelta = new Vector2(w, h);
        tran.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }
        return tran;
    }

    private static RectTransform CreateEmptyObj(RectTransform parent, string name, float x,
        float y, float w, float h, float ax, float ay)
    {
        GameObject go = new GameObject();
        go.name = name;
        go.layer = 5;

        var tran = go.AddComponent<RectTransform>();
        tran.SetParent(parent);
        tran.anchorMax = new Vector2(0, 0);
        tran.anchorMin = new Vector2(0, 0);
        tran.pivot = new Vector2(0, 0);
        tran.localPosition = new Vector2(x, y);
        tran.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }
        return tran;
    }

    private static RectTransform CreateProp9(RectTransform parent, string name, float x, float y, float w, float h,
        string progress, float cx, float cy, float cw, float ch)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        Scrollbar scroll = go.AddComponent<Scrollbar>();

        var tran = scroll.GetComponent<RectTransform>();
        CreateImg9(tran, "ScrollImage", progress, 0, 0, w, h, cx, cy, cw, ch);
        tran.SetParent(parent);
        tran.anchorMax = new Vector2(0, 0);
        tran.anchorMin = new Vector2(0, 0);
        tran.pivot = new Vector2(0.5f, 0.5f);
        tran.localPosition = new Vector2(x, y);
        tran.sizeDelta = new Vector2(w, h);
        tran.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }

        var scrollImage = scroll.transform.Find("ScrollImage").GetComponent<RectTransform>();
        scrollImage.anchorMax = new Vector2(0.5f, 0.5f);
        scrollImage.anchorMin = new Vector2(0.5f, 0.5f);
        scrollImage.pivot = new Vector2(0.5f, 0.5f);
        scrollImage.localPosition = new Vector2(0, 0);
        return tran;
    }

    private static RectTransform CreateRichText(RectTransform parent, string name, string txt, float x, float y, float w,
        float h, int ta, int tfs, int r, int g, int b)
    {
        GameObject go = new GameObject();
        go.layer = 5;
        go.name = name;
        Text text = go.AddComponent<Text>();
        text.text = txt;
        text.alignment = (TextAnchor)(ta + 3);
        text.fontSize = tfs;
        text.color = new Color(r / 255, g / 255, b / 255, 1f);
        text.text = txt;
        var rectTrans = text.GetComponent<RectTransform>();
        rectTrans.SetParent(parent);
        rectTrans.anchorMax = new Vector2(0, 0);
        rectTrans.anchorMin = new Vector2(0, 0);
        rectTrans.pivot = new Vector2(0, 1);
        rectTrans.localPosition = new Vector2(x, y);
        rectTrans.sizeDelta = new Vector2(w, h);
        rectTrans.localScale = new Vector3(1, 1, 1);

        if (!name.StartsWith("autoname_"))
        {
            AddGoToNameTable(name, go);
        }
        return rectTrans;
    }
}

public class PrefabData
{
    public List<PrefabData> childArray = new List<PrefabData>();

    public string t;
    public string n;
    public string p;
    public string txt;
    public string pn;
    public string progress;

    public float x;
    public float y;
    public float w;
    public float h;
    public float cx;
    public float cy;
    public float cw;
    public float ch;
    public int tfs; //字体大小
    public int ta;  //对齐方式
    public int r;
    public int g;
    public int b;

    public float sx;
    public float sy;
    public float ax;
    public float ay;
}
