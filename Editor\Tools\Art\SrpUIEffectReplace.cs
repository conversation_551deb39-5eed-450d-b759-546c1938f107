﻿using Nirvana;
using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UI;

class SrpUIEffectReplace
{
    [MenuItem("Assets/美术专用/一键替换SrpUIEffect组件")]
    public static void Fixed()
    {
        string[] checkDirs = null;
        if (null != Selection.activeObject || null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
        {
            if (null != Selection.instanceIDs && Selection.instanceIDs.Length > 0)
            {
                int totalCount = Selection.instanceIDs.Length;
                for (int i = 0; i < totalCount; i++)
                {
                    int instanceID = Selection.instanceIDs[i];
                    string path = AssetDatabase.GetAssetPath(instanceID);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        checkDirs = new string[] { path };
                        FixedInPaths(checkDirs);
                    }
                    else
                    {
                        GameObject gameObject = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath<GameObject>(path));
                        if (null != gameObject && null != gameObject.GetComponent<UIEffect>())
                        {
                            FixedSingle(path);
                        }
                    }

                    EditorUtility.DisplayProgressBar("正在处理..."
                        , string.Format("{0} / {1}", i + 1, totalCount)
                        , (float)(i + 1) / (float)totalCount);
                }
            }
            else if (null != Selection.activeObject)
            {
                string path = AssetDatabase.GetAssetPath(Selection.activeObject);
                if (AssetDatabase.IsValidFolder(path))
                {
                    checkDirs = new string[] { path };
                    FixedInPaths(checkDirs);
                }
                else
                {
                    GameObject gameObject = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath<GameObject>(path));
                    if (null != gameObject && null != gameObject.GetComponent<UIEffect>())
                    {
                        FixedSingle(path);
                    }
                }
            }
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    public static void FixedInPaths(string[] checkDirs)
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        int totalCount = guids.Length;
        int cutIndex = 0;
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameObject = GameObject.Instantiate(AssetDatabase.LoadAssetAtPath<GameObject>(path));
            if (null == gameObject)
            {
                continue;
            }

            if (null != gameObject && null != gameObject.GetComponent<UIEffect>())
            {
                FixedSingle(path);
            }

            GameObject.DestroyImmediate(gameObject);

            cutIndex++;
            EditorUtility.DisplayProgressBar("正在处理"
                        , string.Format("{0} / {1}", cutIndex, totalCount)
                        , (float)(cutIndex) / totalCount);
        }
    }


    public static void FixedSingle(string filePath)
    {
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(filePath);
        GameObject gameObject = GameObject.Instantiate(prefab);
        GameObject.DestroyImmediate(gameObject.transform.GetComponent<UIEffect>());
        SrpUIEffect srpUIEffect = gameObject.AddComponent<SrpUIEffect>();
        srpUIEffect.raycastTarget = false;
        srpUIEffect.color = new Color(1, 1, 1, 0);

        try
        {
            ParticleSystemRenderer[] particleSystemRenderers = gameObject.GetComponentsInChildren<ParticleSystemRenderer>();
            if (particleSystemRenderers.Length > 0)
            {
                for (int i = 0; i < particleSystemRenderers.Length; i++)
                {
                    Material[] materials = particleSystemRenderers[i].sharedMaterials;
                    for (int j = 0; j < materials.Length; j++)
                    {
                        if (null != materials[j] && null != materials[j].shader && materials[j].shader.name == "YifStandard/YifStandardParticle")
                        {
                            string matPath = AssetDatabase.GetAssetPath(materials[j].GetInstanceID());
                            Material material = NewShaderReplace.CreateNewSrpEffectMat(matPath);
                            if (material != null)
                            {
                                particleSystemRenderers[i].sharedMaterials[j] = material;
                            }
                        }
                    }
                }
            }

            Image[] images = gameObject.GetComponentsInChildren<Image>();
            if (images.Length > 0)
            {
                for (int i = 0; i < images.Length; i++)
                {
                    if (null != images[i].material && null != images[i].material.shader && images[i].material.shader.name == "YifStandard/YifStandardParticle")
                    {
                        string matPath = AssetDatabase.GetAssetPath(images[i].GetInstanceID());
                        Material material = NewShaderReplace.CreateNewSrpEffectMat(matPath);
                        if (material != null)
                        {
                            images[i].material = material;
                        }
                    }
                }
            }

            PrefabUtility.SaveAsPrefabAsset(gameObject, filePath);
            GameObject.DestroyImmediate(gameObject);
        }
        catch (Exception ex)
        {
            Debug.LogError("filePath : " + filePath);
            Debug.LogError(ex.ToString());
            EditorUtility.ClearProgressBar();
        }
    }
}