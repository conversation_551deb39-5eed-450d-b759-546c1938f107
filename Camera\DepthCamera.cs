﻿using UnityEngine;

public class DepthCamera : MonoBehaviour {
    private bool isOn = false;
    private Camera m_Camera;

    private void Awake ()
    {
        m_Camera = GetComponent<Camera>();
    }

    private void UpdateStatus()
    {
        if (isOn)
        {
            m_Camera.depthTextureMode = DepthTextureMode.Depth;
        }
        else
        {
            m_Camera.depthTextureMode = DepthTextureMode.None;
        }
    }

    private void OnEnable()
    {
        CameraDepthMgr.Instance.Add(this);
        isOn = CameraDepthMgr.Instance.IsOpenCameraDepth;
        UpdateStatus();
    }

    private void OnDisable()
    {
        CameraDepthMgr.Instance.Remove(this);
    }

    public void SetIsOpenDepth(bool isOn)
    {
        this.isOn = isOn;
        UpdateStatus();
    }
}
