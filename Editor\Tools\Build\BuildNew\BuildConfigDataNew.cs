﻿using UnityEngine;
using <PERSON><PERSON><PERSON>.Editor;

public struct BuildConfigDataNew
{
    public string CompanyName;

    public string ProductName;

    public ProductNameI18NNew ProductNameI18N;

    public string Version;

    public string[] DefineSymbols;

    public string[] Scenes;

    public SplashScreenConfigNew SplashScreen;

    public string IOSApplicationIdentifier;

    public string IOSProvisioningProfile;

    public string IOSCodeSignIdentity;

    public string IOSBuildNumber;

    public string IOSApplicationDisplayName;

    public bool IOSEnableAutomaticSigning;

    public string IOSDeveloperTeamID;

    public bool IOSEnableBitCode;

    public string[] IOSFrameworks;

    public Texture2D IOSIcon180;

    public Texture2D IOSIcon167;

    public Texture2D IOSIcon152;

    public Texture2D IOSIcon144;

    public Texture2D IOSIcon120;

    public Texture2D IOSIcon114;

    public Texture2D IOSIcon76;

    public Texture2D IOSIcon72;

    public Texture2D IOSIcon57;

    public Texture2D IOSSpotlightIcon120;

    public Texture2D IOSSpotlightIcon80;

    public Texture2D IOSSpotlightIcon40;

    public Texture2D IOSSettingsIcon87;

    public Texture2D IOSSettingsIcon58;

    public Texture2D IOSSettingsIcon29;

    public Texture2D IOSNotificationIcon60;

    public Texture2D IOSNotificationIcon40;

    public Texture2D IOSNotificationIcon20;

    public Texture2D IOSAppstoreIcon1024;

    public string AndroidApplicationIdentifier;

    public int AndroidBundleVersionCode;

    public string AndroidKeystoreName;

    public string AndroidKeystorePass;

    public string AndroidKeyaliasName;

    public string AndroidKeyaliasPass;

    public Texture2D AndroidIcon192;

    public Texture2D AndroidIcon144;

    public Texture2D AndroidIcon96;

    public Texture2D AndroidIcon72;

    public Texture2D AndroidIcon48;

    public Texture2D AndroidIcon36;

    public bool DesktopDefaultIsFullScreen;

    public bool DesktopDefaultIsNativeResolution;

    public int DesktopDefaultScreenWidth;

    public int DesktopDefaultScreenHeight;

    public bool DesktopResizableWindow;

    public Texture2D DesktopIcon1024;

    public Texture2D DesktopIcon512;

    public Texture2D DesktopIcon256;

    public Texture2D DesktopIcon128;

    public Texture2D DesktopIcon48;

    public Texture2D DesktopIcon32;

    public Texture2D DesktopIcon16;
}
