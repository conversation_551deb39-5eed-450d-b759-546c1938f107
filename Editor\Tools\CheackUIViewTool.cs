﻿using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using Assets.Game.Scripts.Editor.Tools;
using UnityEngine.UI;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;

public class CheackUIViewTool : EditorWindow
{
	private Vector2 m_ScrollPosition = Vector2.zero;
	private int m_SelectedTab = 0;
	private Dictionary<int, bool> m_FoldedDic = new Dictionary<int, bool>();
	private List<Dictionary<string, GameObject>> m_gameObjList = new List<Dictionary<string, GameObject>>();

	private string[] m_SpecificationText = {
		"0:长宽、小数异常：请检查节点与父节点是否异常。若数值正常，请手动重新输入Position。" +
		"\nCanvasRender组件没有关联Graphic：请移除CanvasRender组件。工具列表有批量移除。",
		"1:选择 Assets下的预制体 再点按钮。",
		"2:可使用'自定义工具/修复工具/移除所有预制体多余的CanvasRenderer'工具。\n或使用此工具 选择 Assets下的预制体 批量移除修复。",
		"3:检测运行中保存预制体导致的GameObjectAttach节点下同时保存了生成的多余特效。",
		"4:检测图片的透明度是否是100%。",
		"5:修正预制体内所有图片节点的RectTransform宽高匹配图片的大小。",
	};
	private string m_folderPath;
	private GameObject m_gameObject = null;
	private Sprite m_sprite = null;
	private Sprite m_need_change_sprite = null;

	[MenuItem("Tools/UI Tools/检查 UI预制体 是否符合规范", priority = 1)]
	public static void ShowWindow()
	{
		GetWindow<CheackUIViewTool>("检测UI Prefab");
	}

	/// <summary>
	/// 折叠列表按钮
	/// </summary>
	/// <param name="index">唯一索引</param>
	/// <param name="lable">按钮文本</param>
	/// <param name="is_show">是否默认显示</param>
	/// <returns></returns>
	private bool GetFoldedDicValue(int index, string lable, bool is_show = true)
	{
		if (!m_FoldedDic.ContainsKey(index))
		{
			m_FoldedDic.Add(index, is_show);
		}

		GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
		buttonStyle.wordWrap = true;
		buttonStyle.fixedWidth = 100f;
		buttonStyle.normal.textColor = Color.yellow;

		if (GUILayout.Button(lable, buttonStyle))
		{
			m_FoldedDic[index] = !m_FoldedDic[index];
		}

		return m_FoldedDic.GetValueOrDefault(index);
	}

	/// <summary>
	/// 选择文件夹
	/// </summary>
	private void SelectFolder(ref string folder)
	{
		EditorGUILayout.BeginHorizontal();

		folder = EditorGUILayout.TextField(folder);

		if (GUILayout.Button("浏览", GUILayout.Width(80)))
		{
			string selectPath = EditorUtility.OpenFolderPanel("选择文件夹", "Assets/Game/UIs/View", "");
			if (!string.IsNullOrEmpty(selectPath))
			{
				folder = selectPath.Substring(selectPath.IndexOf("Assets"));
			}
		}
		EditorGUILayout.EndHorizontal();
	}

	private void OnGUI()
	{
		GUILayout.Space(10);
		EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

		GUIStyle toggleStyle = new GUIStyle(EditorStyles.toolbarButton);
		toggleStyle.fontSize = 20;
		toggleStyle.fixedHeight = 40;
		toggleStyle.onNormal.textColor = Color.white;

		if (GUILayout.Toggle(m_SelectedTab == 0, "检测预制体异常", toggleStyle))
		{
			m_SelectedTab = 0;
		}

		if (GUILayout.Toggle(m_SelectedTab == 1, "工具列表", toggleStyle))
		{
			m_SelectedTab = 1;
		}

		if (GUILayout.Toggle(m_SelectedTab == 2, "HatsuneMiKu", toggleStyle))
		{
			m_SelectedTab = 2;
		}

		EditorGUILayout.EndHorizontal();
		GUILayout.Space(40);

		m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
		GUILayout.Space(10);

		switch (m_SelectedTab)
		{
			case 0:
				DrawContentCheckUIView();
				break;
			case 1:
				DrawContentBtnList();
				break;
			case 2:
				GUILayout.Label(m_HatsuneMiKu, EditorStyles.boldLabel);
				break;
		}

		GUILayout.Space(10);
		EditorGUILayout.EndScrollView();
	}

	//---------------------------------------------------检测预制体异常---------------------------------------------------
	private void DrawContentCheckUIView()
	{
		GUILayout.Label("工具说明:", EditorStyles.boldLabel);
		GUILayout.Label(m_SpecificationText[0], EditorStyles.wordWrappedLabel);

		GUILayout.Space(20);
		if (GetFoldedDicValue(0, "选择UI Prefab:"))
		{
			m_gameObject = (GameObject)EditorGUILayout.ObjectField(m_gameObject, typeof(GameObject), true, GUILayout.MinWidth(100f));

			GUILayout.Space(10);
			if (GUILayout.Button("检查 UI Prefab 是否符合规范", GUILayout.Height(30)))
			{
				CheckUIViewByPrefab();
			}
		}

		GUILayout.Space(20);
		if (GetFoldedDicValue(1, "选择文件夹:", false))
		{
			SelectFolder(ref m_folderPath);

			GUILayout.Space(10);
			if (GUILayout.Button("检查文件夹下所有 UI Prefab 是否符合规范", GUILayout.Height(30)))
			{
				CheckUIViewByFolder();
			}
		}
	}

	private void CheckUIViewByFolder()
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { m_folderPath });
		int count = guids.Length;
		int index = 0;
		string path = "";
		string bundleName = "";
		string assetName = "";
		try
		{
			CheckController.ClearCheckUIViewList();

			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);
				if (!path.StartsWith("Assets/Game/UIs/View"))
				{
					continue;
				}

				AssetImporter importer = AssetImporter.GetAtPath(path);
				bundleName = importer.assetBundleName;
				GameObject gameObj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
				assetName = string.Format("{0}.prefab", gameObj.name);

				CheckController.CheckUIView(bundleName, assetName);

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	private void CheckUIViewByPrefab()
	{
		try
		{
			CheckController.ClearCheckUIViewList();

			Check(m_gameObject);
		}
		catch (Exception ex)
		{
			Debug.LogErrorFormat("Exception:{1}", ex.ToString());
		}
	}

	[MenuItem("GameObject/技术专用/UI/(可多选)检查UI预制体是否符合规范")]
	static void CheckUIViewgameObjecttBtn()
	{
		CheckUIView(true);
	}

	[MenuItem("Assets/技术专用/UI/(可多选)检查UI预制体是否符合规范")]
	static void CheckUIViewAssetsBtn()
	{
		CheckUIView();
	}

	private static void CheckUIView(bool is_check_prefab = false)
	{
		GameObject[] objs = Selection.gameObjects;
		GameObject gameObject;
		try
		{
			CheckController.ClearCheckUIViewList();

			foreach (var obj in objs)
			{
				gameObject = obj;

				if (is_check_prefab)
				{
					gameObject = PrefabUtility.GetCorrespondingObjectFromSource(obj);
				}

				Check(gameObject);
			}
		}
		catch (Exception ex)
		{
			Debug.LogErrorFormat("Exception:{0}", ex.ToString());
		}
	}

	private static void Check(GameObject gameObject)
	{
		string path = "";
		string bundleName = "";
		string assetName = "";
		path = AssetDatabase.GetAssetPath(gameObject);
		if (!path.StartsWith("Assets/Game/UIs/View"))
		{
			return;
		}

		AssetImporter importer = AssetImporter.GetAtPath(path);
		bundleName = importer.assetBundleName;
		assetName = string.Format("{0}.prefab", gameObject.name);

		CheckController.CheckUIView(bundleName, assetName);
	}

	//---------------------------------------------------工具列表---------------------------------------------------
	private void DrawContentBtnList()
	{
		//foldedIndex是唯一索引.
		void DrawNormalStrucFunction(int foldedIndex, string titleName, bool foldedIsShow, string specification, Action action)
		{
			if (GetFoldedDicValue(foldedIndex, titleName, foldedIsShow))
			{
				GUILayout.Space(10);

				GUILayout.Label("工具说明:", EditorStyles.boldLabel);
				GUILayout.Label(specification, EditorStyles.wordWrappedLabel);

				GUILayout.Space(10);

				action();

				GUILayout.Space(20);
			}

			GUIStyle lableStyle = new GUIStyle();
			lableStyle.wordWrap = true;
			lableStyle.fontStyle = FontStyle.Bold;
			lableStyle.normal.textColor = Color.green;
			GUILayout.Label("-----------------------------------------------------------------------------------------------------", lableStyle);

			GUILayout.Space(10);
		}

		DrawNormalStrucFunction(20, "1.修正RectTransform小数", false, m_SpecificationText[1],
			() =>
			{
				if (GUILayout.Button("修正RectTransform小数", GUILayout.Height(30)))
				{
					ChangeRecttransformDecimal.FixedSelection();
				};
			}
		);

		DrawNormalStrucFunction(30, "2.移除CanvasRenderer", false, m_SpecificationText[2],
			() =>
			{
				if (GUILayout.Button("移除选择的预制体多余的UI CanvasRenderer", GUILayout.Height(30)))
				{
					CanvasRendererFixter.RemvoeUselessCanvasRenderer();
				}
			}
		);

		DrawNormalStrucFunction(40, "3.检测预制体是否存在特效", false, m_SpecificationText[3],
			() =>
			{
				DrawCheckUIHasEffect();
			}
		);

		DrawNormalStrucFunction(50, "4.检测图片的透明度", false, m_SpecificationText[4],
			() =>
			{
				DrawCheckUIAlpha();
			}
		);

		DrawNormalStrucFunction(60, "5.修正RectTransform匹配图片大小", false, m_SpecificationText[5],
			() =>
			{
				DrawCheckUIImageRectTransform();
			}
		);
	}

	//---------------------------------------------------检测预制体是否存在特效---------------------------------------------------
	private void DrawCheckUIHasEffect()
	{
		GUILayout.Space(20);
		if (GetFoldedDicValue(41, "选择UI Prefab:", false))
		{
			m_gameObject = (GameObject)EditorGUILayout.ObjectField(m_gameObject, typeof(GameObject), true, GUILayout.MinWidth(100f));

			GUILayout.Space(10);
			if (GUILayout.Button("检查 UI Prefab 是否存在特效预制体", GUILayout.Height(30)))
			{
				CheckUIHasEffectByPrefab();
			}
		}

		GUILayout.Space(20);
		if (GetFoldedDicValue(42, "选择文件夹:"))
		{
			SelectFolder(ref m_folderPath);

			GUILayout.Space(10);
			if (GUILayout.Button("检查文件夹下所有 UI Prefab 是否存在特效预制体", GUILayout.Height(30)))
			{
				CheckUIHasEffectByFolder();
			}
		}

		GUILayout.Space(20);

		if (m_gameObjList.Count > 0)
		{
			GUILayout.Label($"{m_gameObjList.Count}个预制体有特效存在，请检查是否错误绑定", EditorStyles.boldLabel);

			for (int i = 0; i < m_gameObjList.Count; i++)
			{
				if (m_gameObjList[i] != null)
				{
					foreach (var item in m_gameObjList[i])
					{
						GUILayout.Space(10);

						GUIStyle lableStyle = new GUIStyle();
						lableStyle.wordWrap = true;
						lableStyle.fontStyle = FontStyle.Bold;
						lableStyle.normal.textColor = Color.white;

						GUILayout.Label(item.Key, lableStyle);
						EditorGUILayout.ObjectField(item.Value, typeof(GameObject), true);
					}
				}
			}
		}
		else
		{
			GUILayout.Label("预制体正常", EditorStyles.boldLabel);
		}
	}

	private void CheckUIHasEffectByPrefab()
	{
		string path = "";
		try
		{
			m_gameObjList.Clear();

			path = AssetDatabase.GetAssetPath(m_gameObject);
			if (!path.StartsWith("Assets/Game/UIs/View"))
			{
				return;
			}

			if (null != m_gameObject)
			{
				CheckUIHasEffect(m_gameObject, path);
			}
		}
		catch (Exception ex)
		{
			Debug.LogErrorFormat("Exception:{0}", ex.ToString());
		}
	}

	private void CheckUIHasEffectByFolder()
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { m_folderPath });
		int count = guids.Length;
		int index = 0;
		string path = "";
		try
		{
			m_gameObjList.Clear();

			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);
				if (!path.StartsWith("Assets/Game/UIs/View"))
				{
					continue;
				}

				GameObject gameObj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

				if (null != gameObj)
				{
					CheckUIHasEffect(gameObj, path);
				}

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	private void CheckUIHasEffect(GameObject gameObj, string path)
	{
		string bundleName = "";
		string assetName = "";
		string nodeNames = "";
		bool flag = false;

		UIEffect[] uIEffects = gameObj.GetComponentsInChildren<UIEffect>(true);
		SrpUIEffect[] srpUiEffects = gameObj.GetComponentsInChildren<SrpUIEffect>(true);

		if (uIEffects.Length > 0)
		{
			flag = true;

			for (int i = 0; i < uIEffects.Length; i++)
			{
				nodeNames += "---节点:" + uIEffects[i].name;
			}
		}

		if (srpUiEffects.Length > 0)
		{
			flag = true;

			for (int i = 0; i < srpUiEffects.Length; i++)
			{
				nodeNames += "---节点:" + srpUiEffects[i].name;
			}
		}

		if (flag)
		{
			AssetImporter importer = AssetImporter.GetAtPath(path);
			bundleName = importer.assetBundleName;
			assetName = string.Format("{0}.prefab", gameObj.name);

			Dictionary<string, GameObject> effectGameObjDic = new Dictionary<string, GameObject>();
			string text = string.Format($"共计{uIEffects.Length + srpUiEffects.Length}个节点有问题   {bundleName}/{assetName}   {nodeNames}");
			effectGameObjDic.Add(text, gameObj);
			m_gameObjList.Add(effectGameObjDic);
		}
	}

	//---------------------------------------------------检测图片的透明度---------------------------------------------------
	private void DrawCheckUIAlpha()
	{
		GUILayout.Space(20);

		GUILayout.Label("选择需要检测的图片：", EditorStyles.boldLabel);
		m_sprite = (Sprite)EditorGUILayout.ObjectField(m_sprite, typeof(Sprite), true, GUILayout.MinWidth(100f));
		GUILayout.Label("选择需要替换的图片：", EditorStyles.boldLabel);
		m_need_change_sprite = (Sprite)EditorGUILayout.ObjectField(m_need_change_sprite, typeof(Sprite), true, GUILayout.MinWidth(100f));

		GUILayout.Space(20);
		if (GetFoldedDicValue(51, "选择文件夹:"))
		{
			SelectFolder(ref m_folderPath);

			GUILayout.Space(10);
			if (GUILayout.Button("检查文件夹下所有 UI Prefab 的图片透明度", GUILayout.Height(30)))
			{
				CheckUIAlphaByFolder();
			}

			GUILayout.Space(10);
			if (GUILayout.Button("检查并替换文件夹下所有 UI Prefab 的图片并设置100%透明度", GUILayout.Height(30)))
			{
				CheckUIAlphaByFolder(true);
			}
		}

		GUILayout.Space(20);

		if (m_gameObjList.Count > 0)
		{
			GUILayout.Label($"{m_gameObjList.Count}个预制体的图片透明度不是100%，请检查", EditorStyles.boldLabel);

			for (int i = 0; i < m_gameObjList.Count; i++)
			{
				if (m_gameObjList[i] != null)
				{
					foreach (var item in m_gameObjList[i])
					{
						GUILayout.Space(10);

						GUIStyle lableStyle = new GUIStyle();
						lableStyle.wordWrap = true;
						lableStyle.fontStyle = FontStyle.Bold;
						lableStyle.normal.textColor = Color.white;

						GUILayout.Label(item.Key, lableStyle);
						EditorGUILayout.ObjectField(item.Value, typeof(GameObject), true);
					}
				}
			}
		}
		else
		{
			GUILayout.Label("检测无异常", EditorStyles.boldLabel);
		}
	}

	private void CheckUIAlphaByFolder(bool is_need_change = false)
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { m_folderPath });
		int count = guids.Length;
		int index = 0;
		string path = "";
		try
		{
			m_gameObjList.Clear();

			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);
				if (!path.StartsWith("Assets/Game/UIs/View"))
				{
					continue;
				}

				GameObject gameObj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

				if (null != gameObj)
				{
					CheckUIAlpha(gameObj, path, is_need_change);
				}

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	private void CheckUIAlpha(GameObject gameObj, string path, bool is_need_change = false)
	{
		string bundleName = "";
		string assetName = "";
		string nodeNames = "";
		int sum = 0;
		bool flag = false;
		GameObject go = null;
		Image[] images = null;

		if (is_need_change && m_need_change_sprite != null)
		{
			go = GameObject.Instantiate(gameObj);
			images = go.GetComponentsInChildren<Image>(true);
		}
		else
		{
			images = gameObj.GetComponentsInChildren<Image>(true);
		}

		if (images.Length > 0)
		{
			for (int i = 0; i < images.Length; i++)
			{
				if (images[i].sprite != null && images[i].sprite == m_sprite && images[i].color.a < 1)
				{
					flag = true;
					sum++;
					nodeNames += "---节点:" + images[i].name;

					if (is_need_change && m_need_change_sprite != null)
					{
						images[i].sprite = m_need_change_sprite;
						images[i].color = new Color(1, 1, 1, 1);
					}
				}
			}
		}

		if (flag)
		{
			AssetImporter importer = AssetImporter.GetAtPath(path);
			bundleName = importer.assetBundleName;
			assetName = string.Format("{0}.prefab", gameObj.name);

			Dictionary<string, GameObject> gameObjDic = new Dictionary<string, GameObject>();
			string lable = is_need_change ? "已替换" : "等待替换";
			string text = string.Format($"共计{sum}个节点透明度不是100%, {lable}   {bundleName}/{assetName}   {nodeNames}");
			gameObjDic.Add(text, gameObj);
			m_gameObjList.Add(gameObjDic);

			if (is_need_change)
			{
				PrefabUtility.SaveAsPrefabAsset(go, path);
			}
		}

		if (go != null)
		{
			GameObject.DestroyImmediate(go);
		}
	}

	//---------------------------------------------------修正文件夹下所有预制体的RectTransform匹配图片大小---------------------------------------------------
	private void DrawCheckUIImageRectTransform()
	{
		GUILayout.Space(20);
		if (GetFoldedDicValue(61, "选择文件夹:"))
		{
			SelectFolder(ref m_folderPath);

			GUILayout.Space(10);
			if (GUILayout.Button("修正文件夹下所有预制体的RectTransform匹配图片大小", GUILayout.Height(30)))
			{
				SetUIImageRectTransformByFolder();
			}
		}
	}

	private void SetUIImageRectTransformByFolder()
	{
		string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { m_folderPath });
		int count = guids.Length;
		int index = 0;
		string path = "";
		try
		{
			m_gameObjList.Clear();

			foreach (string guid in guids)
			{
				path = AssetDatabase.GUIDToAssetPath(guid);

				GameObject gameObj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

				if (null != gameObj)
				{
					SetUIImageRectTransform(gameObj, path);
				}

				index++;
				EditorUtility.DisplayProgressBar("", string.Format("正在处理:{0}/{1}", index, count), (float)index / (float)count);
			}
		}
		catch (Exception ex)
		{
			EditorUtility.ClearProgressBar();
			Debug.LogErrorFormat("path:{0}, Exception:{1}", path, ex.ToString());
		}
		EditorUtility.ClearProgressBar();
	}

	private void SetUIImageRectTransform(GameObject gameObj, string path)
	{
		bool flag = false;
		GameObject go = null;
		Image[] images = null;

		go = GameObject.Instantiate(gameObj);
		images = go.GetComponentsInChildren<Image>(true);

		if (images.Length > 0)
		{
			for (int i = 0; i < images.Length; i++)
			{
				if (images[i].sprite != null && images[i].sprite.rect != images[i].rectTransform.rect)
				{
					flag = true;
					images[i].SetNativeSize();
				}
			}
		}

		if (flag)
		{
			PrefabUtility.SaveAsPrefabAsset(go, path);
		}

		if (go != null)
		{
			GameObject.DestroyImmediate(go);
		}
	}

	private string m_HatsuneMiKu = @"
               #########                       
              ############                     
              #############                    
             ##  ###########                   
            ###  ###### #####                  
            ### #######   ####                 
           ###  ########## ####                
          ####  ########### ####               
         ####   ###########  #####             
        #####   ### ########   #####           
       #####   ###   ########   ######         
      ######   ###  ###########   ######       
     ######   #### ##############  ######      
    #######  #####################  ######     
    #######  ######################  ######    
   #######  ###### #################  ######   
   #######  ###### ###### #########   ######   
   #######    ##  ######   ######     ######   
   #######        ######    #####     #####    
    ######        #####     #####     ####     
     #####        ####      #####     ###      
      #####       ###        ###      #        
        ###       ###        ###               
         ##       ###        ###               
__________#_______####_______####______________
                我们的未来没有BUG                ";
}
