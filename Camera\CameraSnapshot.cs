﻿using UnityEngine;
using UnityEngine.UI;
using LuaInterface;

public class CameraSnapshot : MonoBehaviour
{
    [SerializeField]
    [Tooltip("The shader for blur pass.")]
    private Shader blurPassShader;

    private bool isSnapShoting = false;
    private int nextStopRender = 0;
    new private Camera camera = null;
    private Camera sceneCamera = null;
    private RawImage snapshotImg = null;
    private RenderTexture renderTexture = null;
    private Material blurPassMaterial;
    private static int offsetsID = -1;

    void Awake()
    {
        camera = this.GetComponent<Camera>();
        sceneCamera = Camera.main;
        this.blurPassMaterial = new Material(this.blurPassShader);
    }

    private void OnDestroy()
    {
        if (null != renderTexture)
        {
            RenderTexture.ReleaseTemporary(renderTexture);
            renderTexture = null;
        }
    }

    public void SnapShot()
    {
        if (isSnapShoting)
        {
            return;
        }

        isSnapShoting = true;
        float width = 0;
        float height = 0;
        GameObject attacher = GameObject.Find("GameRoot/UILayer/SnapShotBackground");

        if (attacher)
        {
            if (null == snapshotImg)
            {
                snapshotImg = attacher.AddComponent<RawImage>();
            }

            var rectTransform = attacher.GetComponent<RectTransform>();
            width = rectTransform.rect.width;
            height = rectTransform.rect.height;
        }

#if UNITY_EDITOR
        if (null != renderTexture)
        {
            RenderTexture.ReleaseTemporary(renderTexture);
            renderTexture = null;
        }
#endif

        if (null == renderTexture && width > 0 && height > 0)
        {
            renderTexture = RenderTexture.GetTemporary((int)width, (int)height, 0, RenderTextureFormat.ARGB32, RenderTextureReadWrite.Default);
            renderTexture.name = "CameraSnapshot";
            renderTexture.autoGenerateMips = false;
        }

        if (camera && renderTexture && snapshotImg)
        {
            nextStopRender = 0;
            snapshotImg.texture = renderTexture;
            snapshotImg.enabled = true;
            camera.targetTexture = renderTexture;
        }

        SyncCamera();
        //sceneCamera.enabled = false;
        camera.enabled = true;
    }

    private void OnRenderImage(RenderTexture source, RenderTexture destination)
    {
        if (!isSnapShoting)
        {
            Graphics.Blit(source, destination);
            return;
        }

        var temp1 = RenderTexture.GetTemporary(
                    source.width, source.height, 0, RenderTextureFormat.ARGB32);
        temp1.autoGenerateMips = false;

        float widthOverHeight = (1.0f * source.width) / (1.0f * source.height);
        float oneOverBaseSize = 1.0f / 512.0f;

        // vertical blur
        var offset = new Vector4(0.0f, 3f * oneOverBaseSize, 0.0f, 0.0f);
        this.blurPassMaterial.SetVector(OffsetsID, offset);
        Graphics.Blit(source, temp1, this.blurPassMaterial, 0);

        // horizontal blur
        offset = new Vector4((3f / widthOverHeight) * oneOverBaseSize, 0.0f, 0.0f, 0.0f);
        this.blurPassMaterial.SetVector(OffsetsID, offset);
        Graphics.Blit(temp1, destination, this.blurPassMaterial, 0);

        RenderTexture.ReleaseTemporary(temp1);
    }

    private static int OffsetsID
    {
        get
        {
            if (offsetsID == -1)
            {
                offsetsID = Shader.PropertyToID("_Offsets");
            }

            return offsetsID;
        }
    }

    private void Update()
    {
        if (isSnapShoting && null != camera)
        {
            nextStopRender++;
            if (nextStopRender == 2)
            {
                camera.enabled = false;
            }
        }
    }

    public void DelSnapShotImg()
    {
        if (isSnapShoting)
        {
            isSnapShoting = false;
            nextStopRender = 0;

            if (null != camera)
            {
                // sceneCamera.enabled = true;
                camera.enabled = false;
                camera.targetTexture = null;
            }

            if (null != snapshotImg)
            {
                snapshotImg.texture = null;
                snapshotImg.enabled = false;
            }
        }
    }

    private void SyncCamera()
    {
        camera.gameObject.transform.localPosition = sceneCamera.gameObject.transform.localPosition;
        camera.gameObject.transform.localRotation = sceneCamera.gameObject.transform.localRotation;
        camera.gameObject.transform.localScale = sceneCamera.gameObject.transform.localScale;

        camera.clearFlags = sceneCamera.clearFlags;
        camera.backgroundColor = sceneCamera.backgroundColor;
        camera.farClipPlane = sceneCamera.farClipPlane;
        camera.nearClipPlane = sceneCamera.nearClipPlane;
        camera.orthographic = sceneCamera.orthographic;
        camera.fieldOfView = sceneCamera.fieldOfView;
        camera.aspect = sceneCamera.aspect;
        camera.orthographicSize = sceneCamera.orthographicSize;
        camera.cullingMask = sceneCamera.cullingMask;
        camera.allowHDR = sceneCamera.allowHDR;
    }

#if UNITY_EDITOR
    [NoToLua]
    public Shader BlurPassShader
    {
        get { return this.blurPassShader; }
        set { this.blurPassShader = value; }
    }
#endif
}
