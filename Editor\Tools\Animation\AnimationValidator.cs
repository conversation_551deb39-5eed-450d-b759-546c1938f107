using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Nirvana;

public class AnimationValidator : OdinEditorWindow
{
    [MenuItem("自定义工具/资源检查/动画异常检测")]
    static void Init()
    {
        GetWindow<AnimationValidator>("动画异常检测").Show();
    }

    [System.Serializable]
    public class AnimationIssue
    {
        public string issueName;
        public string issueType;
        public GameObject prefab;
        public GameObject animationObject;
        public string description;
        public AnimationClip animationClip;
        public Component component;
    }

    [Title("检测范围配置")]
    [LabelText("检测文件夹路径")]
    [FolderPath]
    public string[] targetFolders =
    {
        "Assets/Game/Actors",
        "Assets/Game/Model",
        "Assets/Game/Effects",
        "Assets/Game/Environments",
    };

    [LabelText("过滤路径（跳过检测）")]
    [InfoBox("在文件夹检测时，跳过这些路径下的预制体")]
    [FolderPath]
    public string[] excludeFolders = new string[0];

    [LabelText("过滤文件名关键词")]
    [InfoBox("跳过包含这些关键词的预制体文件名，支持通配符 * 和 ?")]
    public string[] excludeFileNamePatterns = new string[0];

    [HorizontalGroup("过滤配置")]
    [Button("添加常用过滤", ButtonSizes.Small)]
    [GUIColor(0.8f, 0.9f, 1f)]
    public void AddCommonExcludes()
    {
        var excludeList = new List<string>(excludeFolders ?? new string[0]);
        var patternList = new List<string>(excludeFileNamePatterns ?? new string[0]);

        // 添加常用的过滤文件夹
        string[] commonExcludeFolders = {
            "Assets/Game/Model/Test",
            "Assets/Game/Effects/Test",
            "Assets/Game/Actors/Test"
        };

        foreach (string folder in commonExcludeFolders)
        {
            if (!excludeList.Contains(folder))
                excludeList.Add(folder);
        }

        // 添加常用的过滤模式
        string[] commonPatterns = {
            "*_test*",
            "*_temp*",
            "*_backup*",
            "test_*"
        };

        foreach (string pattern in commonPatterns)
        {
            if (!patternList.Contains(pattern))
                patternList.Add(pattern);
        }

        excludeFolders = excludeList.ToArray();
        excludeFileNamePatterns = patternList.ToArray();

        Debug.Log("已添加常用过滤配置");
    }

    [HorizontalGroup("过滤配置")]
    [Button("清除过滤", ButtonSizes.Small)]
    [GUIColor(1f, 0.8f, 0.8f)]
    public void ClearExcludes()
    {
        excludeFolders = new string[0];
        excludeFileNamePatterns = new string[0];
        Debug.Log("已清除所有过滤配置");
    }

    [HorizontalGroup("过滤配置")]
    [Button("预览过滤结果", ButtonSizes.Small)]
    [GUIColor(0.9f, 0.9f, 0.7f)]
    public void PreviewFilterResults()
    {
        if (targetFolders == null || targetFolders.Length == 0)
        {
            Debug.LogWarning("请先设置检测文件夹路径！");
            return;
        }

        List<string> allPrefabs = new List<string>();
        List<string> excludedPrefabs = new List<string>();

        foreach (string folder in targetFolders)
        {
            if (AssetDatabase.IsValidFolder(folder))
            {
                string[] guids = AssetDatabase.FindAssets("t:GameObject", new[] { folder });
                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (path.EndsWith(".prefab"))
                    {
                        allPrefabs.Add(path);
                        if (ShouldExcludePath(path))
                        {
                            excludedPrefabs.Add(path);
                        }
                    }
                }
            }
        }

        Debug.Log($"=== 过滤预览结果 ===");
        Debug.Log($"总预制体数量: {allPrefabs.Count}");
        Debug.Log($"将被过滤的数量: {excludedPrefabs.Count}");
        Debug.Log($"实际检测数量: {allPrefabs.Count - excludedPrefabs.Count}");

        if (excludedPrefabs.Count > 0)
        {
            Debug.Log($"被过滤的预制体列表:");
            foreach (string path in excludedPrefabs)
            {
                Debug.Log($"  - {path}");
            }
        }
        else
        {
            Debug.Log("没有预制体会被过滤");
        }
    }

    [LabelText("单个预制体检测")]
    [AssetsOnly]
    public GameObject[] individualPrefabs = new GameObject[0];

    [Title("单独修复工具")]
    [LabelText("指定动画片段修复")]
    [AssetsOnly]
    public AnimationClip[] specificAnimationClips = new AnimationClip[0];

    [BoxGroup("快速配置")]
    [HorizontalGroup("快速配置/Buttons")]
    [Button("全部启用", ButtonSizes.Medium)]
    [GUIColor(0.7f, 1f, 0.7f)]
    public void EnableAllChecks()
    {
        checkAnimatorComponent = true;
        checkAnimationComponent = true;
        checkAnimationCurveValues = true;
        checkAnimationCurveExtremes = true;
        checkTransformExtremes = true;
        checkSkinnedMeshBounds = true;
        checkMissingBoneReferences = true;
        checkBoneTransformIssues = true;
    }

    [HorizontalGroup("快速配置/Buttons")]
    [Button("全部禁用", ButtonSizes.Medium)]
    [GUIColor(1f, 0.7f, 0.7f)]
    public void DisableAllChecks()
    {
        checkAnimatorComponent = false;
        checkAnimationComponent = false;
        checkAnimationCurveValues = false;
        checkAnimationCurveExtremes = false;
        checkTransformExtremes = false;
        checkSkinnedMeshBounds = false;
        checkMissingBoneReferences = false;
        checkBoneTransformIssues = false;
    }

    [HorizontalGroup("快速配置/Buttons")]
    [Button("仅检测无效值", ButtonSizes.Medium)]
    [GUIColor(0.7f, 0.9f, 1f)]
    public void EnableOnlyInvalidValueChecks()
    {
        checkAnimatorComponent = true;
        checkAnimationComponent = true;
        checkAnimationCurveValues = true;
        checkAnimationCurveExtremes = false;
        checkTransformExtremes = false;
        checkSkinnedMeshBounds = false;
        checkMissingBoneReferences = false;
        checkBoneTransformIssues = false;
    }

    [FoldoutGroup("动画组件检测")]
    [LabelText("检测Animator组件")]
    [InfoBox("检测使用Animator组件的动画问题")]
    public bool checkAnimatorComponent = true;

    [FoldoutGroup("动画组件检测")]
    [LabelText("检测Animation组件")]
    [InfoBox("检测使用Animation组件的动画问题")]
    public bool checkAnimationComponent = true;
    [FoldoutGroup("动画曲线检测")]
    [LabelText("检测动画曲线无效值")]
    [InfoBox("检测NaN、Infinity等无效数值")]
    public bool checkAnimationCurveValues = true;

    [FoldoutGroup("动画曲线检测")]
    [LabelText("检测动画曲线极值")]
    [InfoBox("检测过大或过小的动画值")]
    public bool checkAnimationCurveExtremes = true;

    [FoldoutGroup("动画曲线检测")]
    [ShowIf("checkAnimationCurveExtremes")]
    [LabelText("曲线值阈值")]
    public float maxCurveValue = 10000f;

    [FoldoutGroup("Transform检测")]
    [LabelText("检测Transform极值")]
    [InfoBox("检测Transform组件的位置、缩放极值")]
    public bool checkTransformExtremes = true;

    [FoldoutGroup("Transform检测")]
    [ShowIf("checkTransformExtremes")]
    [LabelText("位置值阈值")]
    public float maxPositionValue = 10000f;

    [FoldoutGroup("Transform检测")]
    [ShowIf("checkTransformExtremes")]
    [LabelText("缩放值阈值")]
    public float maxScaleValue = 100f;

    [FoldoutGroup("SkinnedMesh检测")]
    [LabelText("检测SkinnedMeshRenderer边界")]
    [InfoBox("检测SkinnedMeshRenderer的边界框问题")]
    public bool checkSkinnedMeshBounds = true;

    [FoldoutGroup("SkinnedMesh检测")]
    [LabelText("检测骨骼引用丢失")]
    [InfoBox("检测SkinnedMeshRenderer的骨骼引用问题")]
    public bool checkMissingBoneReferences = true;

    [FoldoutGroup("SkinnedMesh检测")]
    [LabelText("检测骨骼Transform异常")]
    [InfoBox("检测骨骼Transform的数值异常")]
    public bool checkBoneTransformIssues = true;

    [BoxGroup("检测统计")]
    [HorizontalGroup("检测统计/Stats", 0.5f)]
    [LabelText("检测的预制体数量")]
    [ReadOnly]
    public int checkedPrefabCount = 0;

    [HorizontalGroup("检测统计/Stats")]
    [LabelText("检测的动画片段数量")]
    [ReadOnly]
    public int checkedAnimationClipCount = 0;

    [HorizontalGroup("检测统计/Stats2", 0.5f)]
    [LabelText("发现的问题数量")]
    [ReadOnly]
    public int totalIssueCount = 0;

    [HorizontalGroup("检测统计/Stats2")]
    [LabelText("最后检测时间")]
    [ReadOnly]
    public string lastCheckTime = "未检测";

    [TableList(ShowIndexLabels = true, IsReadOnly = true)]
    [LabelText("检测到的问题")]
    public List<AnimationIssue> detectedIssues = new List<AnimationIssue>();

    [HorizontalGroup("Actions")]
    [Button("开始文件夹检测", ButtonSizes.Large)]
    [GUIColor(0.4f, 0.8f, 1f)]
    public void StartFolderValidation()
    {
        if (targetFolders == null || targetFolders.Length == 0)
        {
            Debug.LogError("请先设置检测文件夹路径！");
            return;
        }

        detectedIssues.Clear();
        checkedPrefabCount = 0;
        checkedAnimationClipCount = 0;
        totalIssueCount = 0;

        List<string> allPrefabPaths = new List<string>();
        int excludedCount = 0;

        foreach (string folder in targetFolders)
        {
            if (AssetDatabase.IsValidFolder(folder))
            {
                string[] guids = AssetDatabase.FindAssets("t:GameObject", new[] { folder });
                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    if (path.EndsWith(".prefab"))
                    {
                        // 检查是否应该过滤此路径
                        if (ShouldExcludePath(path))
                        {
                            excludedCount++;
                            Debug.Log($"跳过检测: {path}");
                        }
                        else
                        {
                            allPrefabPaths.Add(path);
                        }
                    }
                }
            }
        }

        if (excludedCount > 0)
        {
            Debug.Log($"开始检测 {allPrefabPaths.Count} 个预制体（已过滤 {excludedCount} 个）...");
        }
        else
        {
            Debug.Log($"开始检测 {allPrefabPaths.Count} 个预制体...");
        }
        
        for (int i = 0; i < allPrefabPaths.Count; i++)
        {
            string path = allPrefabPaths[i];
            EditorUtility.DisplayProgressBar("动画异常检测", $"检测中... ({i + 1}/{allPrefabPaths.Count})", (float)i / allPrefabPaths.Count);
            
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (prefab != null)
            {
                ValidatePrefab(prefab);
            }
        }
        
        EditorUtility.ClearProgressBar();

        // 更新统计信息
        checkedPrefabCount = allPrefabPaths.Count;
        totalIssueCount = detectedIssues.Count;
        lastCheckTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

        Debug.Log($"检测完成！检测了 {checkedPrefabCount} 个预制体，{checkedAnimationClipCount} 个动画片段，发现 {totalIssueCount} 个问题。");
    }

    [HorizontalGroup("Actions")]
    [Button("开始单个预制体检测", ButtonSizes.Large)]
    [GUIColor(0.8f, 1f, 0.4f)]
    public void StartIndividualValidation()
    {
        if (individualPrefabs == null || individualPrefabs.Length == 0)
        {
            Debug.LogError("请先设置要检测的预制体！");
            return;
        }

        detectedIssues.Clear();
        checkedPrefabCount = 0;
        checkedAnimationClipCount = 0;
        totalIssueCount = 0;

        Debug.Log($"开始检测 {individualPrefabs.Length} 个预制体...");
        
        for (int i = 0; i < individualPrefabs.Length; i++)
        {
            GameObject prefab = individualPrefabs[i];
            if (prefab != null)
            {
                EditorUtility.DisplayProgressBar("动画异常检测", $"检测中... ({i + 1}/{individualPrefabs.Length})", (float)i / individualPrefabs.Length);
                ValidatePrefab(prefab);
            }
        }
        
        EditorUtility.ClearProgressBar();

        // 更新统计信息
        checkedPrefabCount = individualPrefabs.Length;
        totalIssueCount = detectedIssues.Count;
        lastCheckTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

        Debug.Log($"检测完成！检测了 {checkedPrefabCount} 个预制体，{checkedAnimationClipCount} 个动画片段，发现 {totalIssueCount} 个问题。");
    }

    [Button("导出检测报告", ButtonSizes.Medium)]
    [GUIColor(1f, 0.8f, 0.4f)]
    public void ExportReport()
    {
        if (detectedIssues.Count == 0)
        {
            Debug.Log("没有检测到任何问题或统计信息，无需导出报告。");
            return;
        }

        string reportPath = EditorUtility.SaveFilePanel("保存动画检测报告", "", "动画异常检测报告", "txt");
        if (string.IsNullOrEmpty(reportPath)) return;

        using (StreamWriter writer = new StreamWriter(reportPath))
        {
            writer.WriteLine("=== 动画异常检测报告 ===");
            writer.WriteLine($"检测时间: {System.DateTime.Now}");
            writer.WriteLine($"总问题数: {detectedIssues.Count}");
            writer.WriteLine();

            // 导出问题详情
            if (detectedIssues.Count > 0)
            {
                writer.WriteLine("=== 检测到的问题 ===");
                var groupedIssues = detectedIssues.GroupBy(i => i.issueType);
                foreach (var group in groupedIssues)
                {
                    writer.WriteLine($"=== {group.Key} ({group.Count()}个问题) ===");
                    foreach (var issue in group)
                    {
                        writer.WriteLine($"问题: {issue.issueName}");
                        writer.WriteLine($"预制体: {AssetDatabase.GetAssetPath(issue.prefab)}");
                        writer.WriteLine($"对象: {issue.animationObject?.name ?? "N/A"}");
                        writer.WriteLine($"描述: {issue.description}");
                        if (issue.animationClip != null)
                        {
                            writer.WriteLine($"动画片段: {AssetDatabase.GetAssetPath(issue.animationClip)}");
                        }
                        writer.WriteLine();
                    }
                    writer.WriteLine();
                }
            }
        }

        Debug.Log($"检测报告已导出到: {reportPath}");
    }

    [Button("清除检测结果", ButtonSizes.Medium)]
    [GUIColor(1f, 0.6f, 0.6f)]
    public void ClearResults()
    {
        detectedIssues.Clear();
        checkedPrefabCount = 0;
        checkedAnimationClipCount = 0;
        totalIssueCount = 0;
        lastCheckTime = "未检测";
    }

    [Button("修复动画曲线无效值", ButtonSizes.Large)]
    [GUIColor(0.8f, 1f, 0.8f)]
    public void FixAnimationCurveInvalidValues()
    {
        if (detectedIssues.Count == 0)
        {
            Debug.LogWarning("没有检测到问题，请先运行检测。");
            return;
        }

        var curveIssues = detectedIssues.Where(issue =>
            issue.issueType == "动画曲线异常" &&
            issue.issueName == "动画曲线包含无效值" &&
            issue.animationClip != null).ToList();

        if (curveIssues.Count == 0)
        {
            Debug.LogWarning("没有找到动画曲线无效值问题。");
            return;
        }

        Debug.Log($"开始修复 {curveIssues.Count} 个动画曲线无效值问题...");

        int fixedCount = 0;
        foreach (var issue in curveIssues)
        {
            if (FixAnimationClipInvalidValues(issue.animationClip))
            {
                fixedCount++;
                Debug.Log($"已修复动画片段: {AssetDatabase.GetAssetPath(issue.animationClip)}");
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        Debug.Log($"修复完成！成功修复了 {fixedCount} 个动画片段的无效值问题。");

        // 重新检测以验证修复结果
        if (fixedCount > 0)
        {
            Debug.Log("正在重新检测以验证修复结果...");
            if (individualPrefabs != null && individualPrefabs.Length > 0)
            {
                StartIndividualValidation();
            }
            else
            {
                StartFolderValidation();
            }
        }
    }

    [Button("修复指定动画片段", ButtonSizes.Medium)]
    [GUIColor(0.6f, 1f, 0.8f)]
    public void FixSpecificAnimationClips()
    {
        if (specificAnimationClips == null || specificAnimationClips.Length == 0)
        {
            Debug.LogError("请先设置要修复的动画片段！");
            return;
        }

        Debug.Log($"开始修复 {specificAnimationClips.Length} 个指定的动画片段...");

        int fixedCount = 0;
        foreach (var clip in specificAnimationClips)
        {
            if (clip != null)
            {
                Debug.Log($"检查动画片段: {AssetDatabase.GetAssetPath(clip)}");

                // 先检查是否有问题
                bool hasIssues = CheckAnimationClipForInvalidValues(clip);

                if (hasIssues)
                {
                    if (FixAnimationClipInvalidValues(clip))
                    {
                        fixedCount++;
                        Debug.Log($"✓ 已修复动画片段: {clip.name}");
                    }
                    else
                    {
                        Debug.LogWarning($"✗ 修复失败: {clip.name}");
                    }
                }
                else
                {
                    Debug.Log($"○ 动画片段正常，无需修复: {clip.name}");
                }
            }
        }

        if (fixedCount > 0)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Debug.Log($"修复完成！成功修复了 {fixedCount} 个动画片段。");
        }
        else
        {
            Debug.Log("所有指定的动画片段都没有发现问题。");
        }
    }

    private void ValidatePrefab(GameObject prefab)
    {
        if (prefab == null) return;

        try
        {
            // 检测Animator组件
            if (checkAnimatorComponent)
            {
                Animator[] animators = prefab.GetComponentsInChildren<Animator>(true);
                foreach (var animator in animators)
                {
                    CheckAnimatorIssues(prefab, animator);
                }
            }

            // 检测Animation组件
            if (checkAnimationComponent)
            {
                Animation[] animations = prefab.GetComponentsInChildren<Animation>(true);
                foreach (var animation in animations)
                {
                    CheckAnimationComponentIssues(prefab, animation);
                }
            }

            // 检测SkinnedMeshRenderer
            if (checkSkinnedMeshBounds || checkMissingBoneReferences || checkBoneTransformIssues)
            {
                SkinnedMeshRenderer[] skinnedRenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);
                foreach (var renderer in skinnedRenderers)
                {
                    CheckSkinnedMeshRendererIssues(prefab, renderer);
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检测预制体 {prefab.name} 时发生错误: {ex.Message}");
        }
    }

    #region 检测方法

    #region Animator检测
    private void CheckAnimatorIssues(GameObject prefab, Animator animator)
    {
        if (animator == null || animator.runtimeAnimatorController == null) return;

        // 检测AnimationClip
        AnimationClip[] clips = animator.runtimeAnimatorController.animationClips;
        foreach (var clip in clips)
        {
            if (clip != null)
            {
                CheckAnimationClipIssues(prefab, animator.gameObject, clip, animator);
            }
        }

        // 检测Avatar和骨骼
        if (animator.avatar != null && !animator.avatar.isHuman)
        {
            CheckGenericAvatarIssues(prefab, animator);
        }
    }
    #endregion

    #region Animation组件检测
    private void CheckAnimationComponentIssues(GameObject prefab, Animation animation)
    {
        if (animation == null) return;

        foreach (AnimationState state in animation)
        {
            if (state.clip != null)
            {
                CheckAnimationClipIssues(prefab, animation.gameObject, state.clip, animation);
            }
        }
    }
    #endregion

    #region AnimationClip检测
    private void CheckAnimationClipIssues(GameObject prefab, GameObject animationObject, AnimationClip clip, Component component)
    {
        if (clip == null) return;

        // 统计检测的动画片段数量
        checkedAnimationClipCount++;

        try
        {
            // 获取所有动画曲线
            EditorCurveBinding[] bindings = AnimationUtility.GetCurveBindings(clip);

            foreach (var binding in bindings)
            {
                AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                if (curve != null)
                {
                    CheckAnimationCurve(prefab, animationObject, clip, component, curve, binding);
                }
            }

            // 检测Object引用曲线
            EditorCurveBinding[] objectBindings = AnimationUtility.GetObjectReferenceCurveBindings(clip);
            foreach (var binding in objectBindings)
            {
                CheckObjectReferenceBinding(prefab, animationObject, clip, component, binding);
            }
        }
        catch (System.Exception ex)
        {
            AddIssue("动画检测异常", "AnimationClip检测失败", prefab, animationObject, clip, component,
                $"检测动画片段 {clip.name} 时发生异常: {ex.Message}");
        }
    }

    private void CheckAnimationCurve(GameObject prefab, GameObject animationObject, AnimationClip clip, Component component,
        AnimationCurve curve, EditorCurveBinding binding)
    {
        if (curve == null || curve.keys == null) return;

        string propertyPath = $"{binding.path}.{binding.propertyName}";
        bool isTransformProperty = binding.propertyName.StartsWith("m_LocalPosition") ||
                                  binding.propertyName.StartsWith("m_LocalRotation") ||
                                  binding.propertyName.StartsWith("m_LocalScale");

        foreach (var key in curve.keys)
        {
            // 检测NaN和Infinity值（所有曲线都检测）
            if (checkAnimationCurveValues)
            {
                if (float.IsNaN(key.value) || float.IsInfinity(key.value))
                {
                    string issueType = isTransformProperty ? "Transform动画异常" : "动画曲线异常";
                    AddIssue(issueType, "动画曲线包含无效值", prefab, animationObject, clip, component,
                        $"属性 {propertyPath} 在时间 {key.time:F3} 处包含无效值 (NaN/Infinity): {key.value}");
                }

                string whatNaN = string.Empty;
                if (float.IsNaN(key.inTangent))
                    whatNaN = "inTangent:非数字值";
                if (float.IsNaN(key.outTangent))
                    whatNaN += " outTangent:非数字值";
                if (float.IsInfinity(key.inTangent))
                    whatNaN += " inTangent:无穷大值";
                if (float.IsInfinity(key.outTangent))
                    whatNaN += " outTangent:无穷大值";

                if (!string.IsNullOrEmpty(whatNaN))
                {
                    string issueType = isTransformProperty ? "Transform动画异常" : "动画曲线异常";
                    AddIssue(issueType, "动画曲线包含无效值", prefab, animationObject, clip, component,
                        $"动画:{clip.name} 的属性 {propertyPath} 在时间 {key.time:F3} 处包含无效值 {whatNaN}");
                }
            }

            // 检测极值 - 根据属性类型使用不同的阈值和开关
            if (isTransformProperty && checkTransformExtremes)
            {
                // Transform属性使用专门的阈值
                float threshold = maxCurveValue;
                if (binding.propertyName.Contains("Position"))
                    threshold = maxPositionValue;
                else if (binding.propertyName.Contains("Scale"))
                    threshold = maxScaleValue;

                if (Mathf.Abs(key.value) > threshold)
                {
                    AddIssue("Transform动画异常", "Transform动画包含极值", prefab, animationObject, clip, component,
                        $"属性 {propertyPath} 在时间 {key.time:F3} 处值过大: {key.value:F3} (阈值: {threshold})");
                }
            }
            else if (!isTransformProperty && checkAnimationCurveExtremes)
            {
                // 非Transform属性使用通用曲线阈值
                if (Mathf.Abs(key.value) > maxCurveValue)
                {
                    AddIssue("动画曲线异常", "动画曲线包含极值", prefab, animationObject, clip, component,
                        $"属性 {propertyPath} 在时间 {key.time:F3} 处值过大: {key.value:F3} (阈值: {maxCurveValue})");
                }
            }
        }
    }

    private void CheckObjectReferenceBinding(GameObject prefab, GameObject animationObject, AnimationClip clip,
        Component component, EditorCurveBinding binding)
    {
        ObjectReferenceKeyframe[] keyframes = AnimationUtility.GetObjectReferenceCurve(clip, binding);
        if (keyframes == null) return;

        foreach (var keyframe in keyframes)
        {
            if (keyframe.value == null && !string.IsNullOrEmpty(binding.path))
            {
                AddIssue("动画引用异常", "动画对象引用丢失", prefab, animationObject, clip, component,
                    $"属性 {binding.path}.{binding.propertyName} 在时间 {keyframe.time:F3} 处对象引用为空");
            }
        }
    }
    #endregion
    #endregion

    #region SkinnedMeshRenderer检测
    private void CheckSkinnedMeshRendererIssues(GameObject prefab, SkinnedMeshRenderer renderer)
    {
        if (renderer == null) return;

        // 检测边界问题
        if (checkSkinnedMeshBounds)
        {
            CheckSkinnedMeshBounds(prefab, renderer);
        }

        // 检测骨骼引用
        if (checkMissingBoneReferences)
        {
            CheckMissingBoneReferences(prefab, renderer);
        }

        // 检测骨骼Transform
        if (checkBoneTransformIssues)
        {
            CheckBoneTransformIssues(prefab, renderer);
        }
    }

    private void CheckSkinnedMeshBounds(GameObject prefab, SkinnedMeshRenderer renderer)
    {
        try
        {
            Bounds bounds = renderer.bounds;

            // 检测无效边界
            if (!IsValidBounds(bounds))
            {
                AddIssue("SkinnedMesh边界异常", "SkinnedMeshRenderer边界无效", prefab, renderer.gameObject, null, renderer,
                    $"SkinnedMeshRenderer {renderer.name} 的边界包含无效值: center={bounds.center}, size={bounds.size}");
            }

            // 检测过大边界
            if (bounds.size.magnitude > 10000f)
            {
                AddIssue("SkinnedMesh边界异常", "SkinnedMeshRenderer边界过大", prefab, renderer.gameObject, null, renderer,
                    $"SkinnedMeshRenderer {renderer.name} 的边界过大: size={bounds.size} (magnitude: {bounds.size.magnitude:F2})");
            }
        }
        catch (System.Exception ex)
        {
            AddIssue("SkinnedMesh边界异常", "边界计算异常", prefab, renderer.gameObject, null, renderer,
                $"计算 SkinnedMeshRenderer {renderer.name} 边界时发生异常: {ex.Message}");
        }
    }

    private void CheckMissingBoneReferences(GameObject prefab, SkinnedMeshRenderer renderer)
    {
        if (renderer.bones == null) return;

        int missingBoneCount = 0;
        List<int> missingIndices = new List<int>();

        for (int i = 0; i < renderer.bones.Length; i++)
        {
            if (renderer.bones[i] == null)
            {
                missingBoneCount++;
                missingIndices.Add(i);
            }
        }

        if (missingBoneCount > 0)
        {
            if (missingBoneCount == renderer.bones.Length)
            {
                AddIssue("骨骼引用异常", "所有骨骼引用丢失", prefab, renderer.gameObject, null, renderer,
                    $"SkinnedMeshRenderer {renderer.name} 的所有 {renderer.bones.Length} 个骨骼引用都为空");
            }
            else
            {
                string indices = string.Join(", ", missingIndices);
                AddIssue("骨骼引用异常", "部分骨骼引用丢失", prefab, renderer.gameObject, null, renderer,
                    $"SkinnedMeshRenderer {renderer.name} 的骨骼索引 [{indices}] 引用为空 ({missingBoneCount}/{renderer.bones.Length})");
            }
        }
    }

    private void CheckBoneTransformIssues(GameObject prefab, SkinnedMeshRenderer renderer)
    {
        if (renderer.bones == null) return;

        foreach (var bone in renderer.bones)
        {
            if (bone == null) continue;

            // 检测Transform值异常
            if (!IsValidPosition(bone.position))
            {
                AddIssue("骨骼Transform异常", "骨骼世界位置无效", prefab, renderer.gameObject, null, renderer,
                    $"骨骼 {bone.name} 的世界位置包含无效值: {bone.position}");
            }

            if (!IsValidPosition(bone.localPosition))
            {
                AddIssue("骨骼Transform异常", "骨骼本地位置无效", prefab, renderer.gameObject, null, renderer,
                    $"骨骼 {bone.name} 的本地位置包含无效值: {bone.localPosition}");
            }

            if (!IsValidRotation(bone.rotation))
            {
                AddIssue("骨骼Transform异常", "骨骼旋转无效", prefab, renderer.gameObject, null, renderer,
                    $"骨骼 {bone.name} 的旋转包含无效值: {bone.rotation}");
            }

            // 检测极值
            if (bone.localPosition.magnitude > maxPositionValue)
            {
                AddIssue("骨骼Transform异常", "骨骼位置值过大", prefab, renderer.gameObject, null, renderer,
                    $"骨骼 {bone.name} 的本地位置过大: {bone.localPosition} (magnitude: {bone.localPosition.magnitude:F2})");
            }
        }
    }
    #endregion

    #region Generic Avatar检测
    private void CheckGenericAvatarIssues(GameObject prefab, Animator animator)
    {
        if (animator.avatar == null || animator.avatar.isHuman) return;

        // 检测Generic Avatar的Transform映射
        Transform[] transforms = prefab.GetComponentsInChildren<Transform>(true);
        foreach (var transform in transforms)
        {
            if (transform == null) continue;

            // 检测Transform值异常
            if (!IsValidPosition(transform.position))
            {
                AddIssue("Avatar Transform异常", "Transform世界位置无效", prefab, transform.gameObject, null, animator,
                    $"Transform {transform.name} 的世界位置包含无效值: {transform.position}");
            }

            if (!IsValidPosition(transform.localPosition))
            {
                AddIssue("Avatar Transform异常", "Transform本地位置无效", prefab, transform.gameObject, null, animator,
                    $"Transform {transform.name} 的本地位置包含无效值: {transform.localPosition}");
            }

            if (!IsValidRotation(transform.rotation))
            {
                AddIssue("Avatar Transform异常", "Transform旋转无效", prefab, transform.gameObject, null, animator,
                    $"Transform {transform.name} 的旋转包含无效值: {transform.rotation}");
            }
        }
    }
    #endregion

    #region 修复方法
    /// <summary>
    /// 修复动画片段中的无效值
    /// </summary>
    private bool FixAnimationClipInvalidValues(AnimationClip clip)
    {
        if (clip == null) return false;

        bool hasFixed = false;

        try
        {
            // 获取所有动画曲线
            EditorCurveBinding[] bindings = AnimationUtility.GetCurveBindings(clip);

            foreach (var binding in bindings)
            {
                AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                if (curve != null)
                {
                    bool curveFixed = FixAnimationCurveInvalidValues(curve, binding);
                    if (curveFixed)
                    {
                        // 将修复后的曲线设置回动画片段
                        AnimationUtility.SetEditorCurve(clip, binding, curve);
                        hasFixed = true;
                        Debug.Log($"修复了属性 {binding.path}.{binding.propertyName} 的无效切线值");
                    }
                }
            }

            if (hasFixed)
            {
                EditorUtility.SetDirty(clip);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"修复动画片段 {clip.name} 时发生错误: {ex.Message}");
            return false;
        }

        return hasFixed;
    }

    /// <summary>
    /// 修复动画曲线中的无效值
    /// </summary>
    private bool FixAnimationCurveInvalidValues(AnimationCurve curve, EditorCurveBinding binding)
    {
        if (curve == null || curve.keys == null) return false;

        bool hasFixed = false;
        Keyframe[] keys = curve.keys;

        for (int i = 0; i < keys.Length; i++)
        {
            Keyframe key = keys[i];
            bool keyFixed = false;

            // 修复无效的值
            if (float.IsNaN(key.value) || float.IsInfinity(key.value))
            {
                key.value = 0f; // 将无效值重置为0
                keyFixed = true;
                Debug.LogWarning($"修复了关键帧值: {binding.path}.{binding.propertyName} 时间 {key.time:F3}");
            }

            // 修复无效的切线值
            if (float.IsNaN(key.inTangent) || float.IsInfinity(key.inTangent))
            {
                key.inTangent = 0f; // 将无效切线重置为0（平滑）
                keyFixed = true;
                Debug.LogWarning($"修复了inTangent: {binding.path}.{binding.propertyName} 时间 {key.time:F3}");
            }

            if (float.IsNaN(key.outTangent) || float.IsInfinity(key.outTangent))
            {
                key.outTangent = 0f; // 将无效切线重置为0（平滑）
                keyFixed = true;
                Debug.LogWarning($"修复了outTangent: {binding.path}.{binding.propertyName} 时间 {key.time:F3}");
            }

            if (keyFixed)
            {
                keys[i] = key;
                hasFixed = true;
            }
        }

        if (hasFixed)
        {
            curve.keys = keys;

            // 可选：重新计算切线以确保平滑过渡
            for (int i = 0; i < curve.keys.Length; i++)
            {
                AnimationUtility.SetKeyLeftTangentMode(curve, i, AnimationUtility.TangentMode.ClampedAuto);
                AnimationUtility.SetKeyRightTangentMode(curve, i, AnimationUtility.TangentMode.ClampedAuto);
            }
        }

        return hasFixed;
    }

    /// <summary>
    /// 检查动画片段是否包含无效值
    /// </summary>
    private bool CheckAnimationClipForInvalidValues(AnimationClip clip)
    {
        if (clip == null) return false;

        try
        {
            EditorCurveBinding[] bindings = AnimationUtility.GetCurveBindings(clip);

            foreach (var binding in bindings)
            {
                AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                if (curve != null && curve.keys != null)
                {
                    foreach (var key in curve.keys)
                    {
                        // 检查值和切线是否有无效值
                        if (float.IsNaN(key.value) || float.IsInfinity(key.value) ||
                            float.IsNaN(key.inTangent) || float.IsInfinity(key.inTangent) ||
                            float.IsNaN(key.outTangent) || float.IsInfinity(key.outTangent))
                        {
                            Debug.Log($"发现无效值: {binding.path}.{binding.propertyName} 时间 {key.time:F3} " +
                                     $"值:{key.value} inTangent:{key.inTangent} outTangent:{key.outTangent}");
                            return true;
                        }
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"检查动画片段 {clip.name} 时发生错误: {ex.Message}");
            return false;
        }

        return false;
    }
    #endregion

    #region 路径过滤方法
    /// <summary>
    /// 检查路径是否应该被过滤（跳过）
    /// </summary>
    private bool ShouldExcludePath(string assetPath)
    {
        if (string.IsNullOrEmpty(assetPath)) return false;

        // 检查文件夹过滤
        if (excludeFolders != null && excludeFolders.Length > 0)
        {
            foreach (string excludeFolder in excludeFolders)
            {
                if (!string.IsNullOrEmpty(excludeFolder))
                {
                    // 标准化路径分隔符
                    string normalizedPath = assetPath.Replace('\\', '/');
                    string normalizedExclude = excludeFolder.Replace('\\', '/');

                    // 确保文件夹路径以 / 结尾进行比较
                    if (!normalizedExclude.EndsWith("/"))
                        normalizedExclude += "/";

                    if (normalizedPath.StartsWith(normalizedExclude, System.StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }
        }

        // 检查文件名模式过滤
        if (excludeFileNamePatterns != null && excludeFileNamePatterns.Length > 0)
        {
            string fileName = System.IO.Path.GetFileNameWithoutExtension(assetPath);
            foreach (string pattern in excludeFileNamePatterns)
            {
                if (!string.IsNullOrEmpty(pattern))
                {
                    if (IsFileNameMatch(fileName, pattern))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 检查文件名是否匹配模式（支持通配符 * 和 ?）
    /// </summary>
    private bool IsFileNameMatch(string fileName, string pattern)
    {
        if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(pattern))
            return false;

        // 简单的通配符匹配实现
        return IsWildcardMatch(fileName.ToLower(), pattern.ToLower());
    }

    /// <summary>
    /// 通配符匹配算法
    /// </summary>
    private bool IsWildcardMatch(string text, string pattern)
    {
        int textIndex = 0;
        int patternIndex = 0;
        int starIndex = -1;
        int match = 0;

        while (textIndex < text.Length)
        {
            if (patternIndex < pattern.Length && (pattern[patternIndex] == '?' || pattern[patternIndex] == text[textIndex]))
            {
                textIndex++;
                patternIndex++;
            }
            else if (patternIndex < pattern.Length && pattern[patternIndex] == '*')
            {
                starIndex = patternIndex;
                match = textIndex;
                patternIndex++;
            }
            else if (starIndex != -1)
            {
                patternIndex = starIndex + 1;
                match++;
                textIndex = match;
            }
            else
            {
                return false;
            }
        }

        while (patternIndex < pattern.Length && pattern[patternIndex] == '*')
        {
            patternIndex++;
        }

        return patternIndex == pattern.Length;
    }
    #endregion

    #region 辅助方法
    private void AddIssue(string issueType, string issueName, GameObject prefab, GameObject animationObject,
        AnimationClip animationClip, Component component, string description)
    {
        detectedIssues.Add(new AnimationIssue
        {
            issueType = issueType,
            issueName = issueName,
            prefab = prefab,
            animationObject = animationObject,
            animationClip = animationClip,
            component = component,
            description = description
        });
    }

    /// <summary>
    /// 检查3D位置是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidPosition(Vector3 position)
    {
        return !float.IsNaN(position.x) && !float.IsNaN(position.y) && !float.IsNaN(position.z) &&
               !float.IsInfinity(position.x) && !float.IsInfinity(position.y) && !float.IsInfinity(position.z);
    }

    /// <summary>
    /// 检查旋转是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidRotation(Quaternion rotation)
    {
        return !float.IsNaN(rotation.x) && !float.IsNaN(rotation.y) && !float.IsNaN(rotation.z) && !float.IsNaN(rotation.w) &&
               !float.IsInfinity(rotation.x) && !float.IsInfinity(rotation.y) && !float.IsInfinity(rotation.z) && !float.IsInfinity(rotation.w);
    }

    /// <summary>
    /// 检查边界框是否有效（不包含NaN或Infinity）
    /// </summary>
    private bool IsValidBounds(Bounds bounds)
    {
        return IsValidPosition(bounds.center) && IsValidPosition(bounds.size) &&
               bounds.size.x >= 0 && bounds.size.y >= 0 && bounds.size.z >= 0;
    }

    /// <summary>
    /// 获取Transform在层级中的深度
    /// </summary>
    private int GetTransformDepth(Transform transform, Transform root)
    {
        if (transform == null || transform == root) return 0;

        int depth = 0;
        Transform current = transform;

        while (current != null && current != root && depth < 50) // 防止无限循环
        {
            current = current.parent;
            depth++;
        }

        return current == root ? depth : -1; // 如果没有找到根节点，返回-1
    }
    #endregion
}