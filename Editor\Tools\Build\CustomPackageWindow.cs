﻿using UnityEngine;
using UnityEditor;
using Build;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Generic;

public class CustomPackageWindow : BaseEditorWindow
{
    private static int packageSize;
    private static BuildPlatType platType;

    [MenuItem("自定义工具/发布版本/生成SDK入包列表（自定义包体大小）")]
    private static void ShowWindow()
    {
        EditorWindow.GetWindow<CustomPackageWindow>(false, "生成SDK入包列表");
    }

    private void OnGUI()
    {
        platType = (BuildPlatType)EditorGUILayout.EnumPopup("Plat", platType);
        packageSize = EditorGUILayout.IntSlider("Size(MB)", packageSize, 150, 2000);
        if (GUILayout.Button("Build"))
        {
            if (!CheckConfig())
            {
                ShowNotification(new GUIContent("config_predownload文件为空"));
                return;
            }

            if (!CheckAssetBundle(platType))
            {
                ShowNotification(new GUIContent("相关平台的AssetBundle目录为空"));
                return;
            }

            if (!CheckAssetBundleJson(platType))
            {
                ShowNotification(new GUIContent("相关平台的AssetBundle.json文件不存在"));
                return;
            }

            if (!CheckLuaAssetBundleJson(platType))
            {
                ShowNotification(new GUIContent("相关平台的LuaAssetBundle.json文件不存在"));
                return;
            }

            long size = BuildPackage(packageSize * 1024 * 1024, platType);
            AssetBundleInstaller.WriteInstallBundlesList(platType);

            ShowNotification(new GUIContent(string.Format("入包资源({0}MB)生成在install_bundles_l.txt", size / 1024 / 1024)));

            AssetDatabase.Refresh();
        }
    }

    private bool CheckConfig()
    {
        var config_predownload_path = Path.Combine(Application.dataPath, "Game/Lua/config/config_predownload.lua");
        return File.Exists(config_predownload_path);
    }

    private bool CheckAssetBundleJson(BuildPlatType platType)
    {
        string path = BuilderConfig.GetBuildLogPath(string.Format("AssetBundle{0}.json", (int)platType));
        return File.Exists(path);
    }

    private bool CheckLuaAssetBundleJson(BuildPlatType platType)
    {
        string path = BuilderConfig.GetBuildLogPath(string.Format("LuaAssetBundle{0}.json", (int)platType));
        return File.Exists(path);
    }

    private bool CheckAssetBundle(BuildPlatType platType)
    {
        string bundle_dir = BuilderConfig.GetAssetBundlePath(platType);
        return Directory.Exists(bundle_dir);
    }

    private long BuildPackage(long targetSize, BuildPlatType platType)
    {
        long totalSize = 0;
        HashSet<string> filters = new HashSet<string>();
        var manifest = BuilderConfig.GetManifestInfo(platType);
        var luaManifest = BuilderConfig.GetLuaManifestInfo(platType);

        StringBuilder builder = new StringBuilder();

        // 先统计小包资源
        var package_s = AssetBundleInstaller.GetInstallBundlesSizeList(platType, InstallBundleSize.sizeS);
        foreach (var kv in package_s)
        {
            string bundle = kv.Key;
            filters.Add(bundle);
            if (bundle.StartsWith("LuaAssetBundle/"))
            {
                totalSize += luaManifest.GetAssetBundleSize(bundle.Replace("LuaAssetBundle/", ""));
            }
            else
            {
                totalSize += manifest.GetAssetBundleSize(bundle);
            }
            builder.Append(bundle);
            builder.Append("\n");
        }

        // 根据等级排序，从预下载列表依次收集
        var config_predownload_path = Path.Combine(Application.dataPath, "Game/Lua/config/config_predownload.lua");
        string[] lines = File.ReadAllLines(config_predownload_path);
        for (int i = 1; i < lines.Length - 1; i++)
        {
            if (totalSize >= targetSize)
            {
                break;
            }

            string line = lines[i];
            if (string.IsNullOrEmpty(line))
            {
                continue;
            }

            var matcehs = Regex.Match(line, "{bundle=\"(.*?)\", level=([0-9]+)},");
            if (matcehs.Success)
            {
                string bundleName = matcehs.Groups[1].Value;
                if (filters.Contains(bundleName))
                {
                    continue;
                }

                filters.Add(bundleName);
                totalSize += manifest.GetAssetBundleSize(bundleName);

                var depencies = manifest.GetAllDependencies(bundleName);
                foreach (var depend in depencies)
                {
                    if (filters.Contains(depend))
                    {
                        continue;
                    }

                    filters.Add(depend);
                    totalSize += manifest.GetAssetBundleSize(depend);
                }

                builder.Append(bundleName);
                builder.Append("\n");
            }
        }

        builder.Insert(0, string.Format("//大包资源({0}M)是通过 自定义工具 -> 发版工具 -> 生成SDK入包列表 合成\n\n", totalSize / 1024 / 1024));
        string installFileName = BuilderConfig.GetAssetBundleIntallFilterTxtName(InstallBundleSize.sizeL);
        string installFilePath = Path.Combine(Application.dataPath, string.Format("Game/Deploy/Install/{0}.txt", installFileName));
        File.WriteAllText(installFilePath, builder.ToString());

        return totalSize;
    }
}
