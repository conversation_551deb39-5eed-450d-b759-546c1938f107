﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;
using System;
using System.Threading;
using System.Reflection;
using Nirvana;
using UnityEngine.Profiling;

public static class ProToo {
    public static bool ContainProperty(this object instance, string propertyName)
    {
        if (instance != null && !string.IsNullOrEmpty(propertyName))
        {
            PropertyInfo _findedPropertyInfo = instance.GetType().GetProperty(propertyName);
            return (_findedPropertyInfo != null);
        }
        return false;
    }
}

public class OperaAssetEditor : EditorWindow
{
    //public struct NodeInfo {
    //    public string path;
    //}

    public struct DependNode
    {
        public string path;
        public List<DependNode> nodeList;
    }

    public struct TextureInfo {
        public string path;
        public string mem;
        public string size;
        public bool isFormatError;
        public UnityEngine.Object obj;
    }

    static List<string> setPath = new List<string>();
    static int setIndex = 0;

    public UnityEngine.Object file;
    public Dictionary<string, DependNode> dependDic = new Dictionary<string, DependNode>();
    public List<string> checkList = new List<string>();
    public List<string> copyList = new List<string>();
    public int copyNum = 0;
    public int alrealyCopyNum = 0;
    public int checkNum = 0;

    Dictionary<string, string> guidOldToNewMap = new Dictionary<string, string>();
    Dictionary<string, List<string>> guidsInFileMap = new Dictionary<string, List<string>>();
    HashSet<string> ownGuids = new HashSet<string>();
    Dictionary<string, string> oldGuidAsset = new Dictionary<string, string>();
    Dictionary<string, string> onewGuidAsset = new Dictionary<string, string>();
    Dictionary<string, string> shaderList = new Dictionary<string, string>();
    List<string> gameObjAttachList = new List<string>();
    Dictionary<string, List<string>> addGameObjList = new Dictionary<string, List<string>>();
    Dictionary<string, List<string>> dependList = new Dictionary<string, List<string>>();
    List<string> colletcGuidList = new List<string>();
    Dictionary<string, List<string>> nirvanaList = new Dictionary<string, List<string>>();

    private static List<UnityEngine.Object> objectList = new List<UnityEngine.Object>();
    private static List<UnityEngine.Object> scriptList = new List<UnityEngine.Object>();
    private static List<UnityEngine.Object> prefabList = new List<UnityEngine.Object>();
    private static List<string> custonNameList = new List<string>();
    private static Vector2 scrollerPos = Vector2.zero;
    private static Vector2 scriptScrollerPos = Vector2.zero;
    private static Vector2 objScrollerPos = Vector2.zero;
    private static Vector2 texScrollerPos = Vector2.zero;
    private bool explaceFlag = true;
    private bool acceptFlag = false;
    Dictionary<string, string> effectDic = new Dictionary<string, string>();
    List<string> replaceSameList = new List<string>();
    private bool isSkinPrefabe = false;
    Dictionary<string, string> missList = new Dictionary<string, string>();

    List<TextureInfo> texCheckList = new List<TextureInfo>();
    private bool stopTexFlush = false;
    private bool texCheckFinish = false;

    public UnityEngine.Object removeType;

    private enum AutoStates {
        Normal = 0,
        BuildOldGuid,
        ChangeGuid,
        DeleteSame,
        MoveFile,
    }

    private int autoCopyCount = 0;
    private AutoStates autoStep = AutoStates.Normal;

    [MenuItem("自定义工具/策划工具/【旧】资源处理")]
    static void Init()
    {
        OperaAssetEditor window = GetWindowWithRect(typeof(OperaAssetEditor), new Rect(0, 0, 1000, 600), false, "AssetOpera") as OperaAssetEditor;
        window.Show();
    }

    void Start () {
		
	}

    void OnGUI()
    {
        EditorGUILayout.BeginHorizontal();
        file = EditorGUILayout.ObjectField("特殊操作目录: ", file, typeof(UnityEngine.Object), false);
        removeType = EditorGUILayout.ObjectField("移除类型: ", removeType, typeof(UnityEngine.Object), false);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("导出资源"))
        {
            this.Export();
        }

        if (GUILayout.Button("接受资源"))
        {
            this.Accept();
        }

        if (GUILayout.Button("重新绑定"))
        {
            this.ChangeDepend();
        }

        if (GUILayout.Button("打开缓存目录"))
        {
            if (Directory.Exists(this.GetCopyDic()))
            {
                System.Diagnostics.Process.Start(this.GetCopyDic());
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        explaceFlag = GUILayout.Toggle(explaceFlag, "ExplaceFlag");
        acceptFlag = GUILayout.Toggle(acceptFlag, "AccxeptFlag");
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Instance"))
        {
            objectList.Add(new UnityEngine.Object());
            custonNameList.Add(string.Empty);
        }

        if (GUILayout.Button("Remove"))
        {
            if (objectList.Count > 0)
            {
                objectList.RemoveAt(objectList.Count - 1);
                custonNameList.RemoveAt(custonNameList.Count - 1);
            }
        }
        EditorGUILayout.EndHorizontal();


        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("InstanceScript"))
        {
            scriptList.Add(new MonoScript());
        }

        if (GUILayout.Button("RemoveScript"))
        {
            if (scriptList.Count > 0)
            {
                scriptList.RemoveAt(scriptList.Count - 1);
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("InstancePrefab"))
        {
            prefabList.Add(new UnityEngine.Object());
        }

        if (GUILayout.Button("RemovePrefab"))
        {
            if (prefabList.Count > 0)
            {
                prefabList.RemoveAt(prefabList.Count - 1);
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("修复特效丢失"))
        {
            this.CheckGameObjAttach();
        }

        if (GUILayout.Button("修改特效缩放方式为根据父节点缩放"))
        {
            this.ChangeParticleScaleMode();
        }

        if (GUILayout.Button("生成目录GameAttack组件失效目录"))
        {
            this.CreateGameAttackLog();
        }

        if (GUILayout.Button("模型渐变材质处理"))
        {
            this.CreateFadeMat();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("脚本绑定"))
        {
            this.AddScript();
        }

        if (GUILayout.Button("清除无效脚本"))
        {
            this.ClearInVaildScript();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("生成品质材质球"))
        {
            this.CreateQualityMaterials();
        }

        if (GUILayout.Button("新增特殊挂点"))
        {
            this.AddSpecialPoint();
        }

        if (GUILayout.Button("怪物模型受击挂点处理"))
        {
            this.MonsterHitPoint();
        }

        this.isSkinPrefabe = GUILayout.Toggle(this.isSkinPrefabe, "是否是身体部位预制体");
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("一键导入"))
        {
            this.AutoReceive();
        }

        if (GUILayout.Button("查找场景引用"))
        {
            this.FindDependByGuid();
        }

        if (GUILayout.Button("检测图片所占大小"))
        {
            this.CheckTextureMem();
        }
        EditorGUILayout.EndHorizontal();

        scrollerPos = EditorGUILayout.BeginScrollView(scrollerPos);
        for (int i = 0; i < objectList.Count; ++i)
        {
            EditorGUILayout.BeginHorizontal();
            objectList[i] = EditorGUILayout.ObjectField(objectList[i], typeof(UnityEngine.Object), false);
            custonNameList[i] = EditorGUILayout.TextArea(custonNameList[i]);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();

        int num = 0;
        scriptScrollerPos = EditorGUILayout.BeginScrollView(scriptScrollerPos);
        for (int i = 0; i < scriptList.Count; ++i)
        {
            if (i >= num)
            {
                num = ((int)(i / 5) + 1) * 5;
                EditorGUILayout.BeginHorizontal();
            }
            scriptList[i] = EditorGUILayout.ObjectField(scriptList[i], typeof(MonoScript), false);
            if (i == num - 1 || i == scriptList.Count - 1)
            {
                EditorGUILayout.EndHorizontal();
            }
            //EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
        num = 0;
        objScrollerPos = EditorGUILayout.BeginScrollView(objScrollerPos);
        for (int i = 0; i < prefabList.Count; ++i)
        {
            if (i >= num)
            {
                num = ((int)(i / 5) + 1) * 5;
                EditorGUILayout.BeginHorizontal();
            }

            prefabList[i] = EditorGUILayout.ObjectField(prefabList[i], typeof(UnityEngine.Object), false);

            if (i == num - 1 || i == prefabList.Count - 1)
            {
                EditorGUILayout.EndHorizontal();
            }
        }
        EditorGUILayout.EndScrollView();

        texCheckFinish = false;
        stopTexFlush = false;

        texScrollerPos = EditorGUILayout.BeginScrollView(texScrollerPos, false, true, GUILayout.Width(1000), GUILayout.Height(250));
        for (int i = 0; i < this.texCheckList.Count; ++i)
        {
            TextureInfo info = this.texCheckList[i];
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.ObjectField(info.obj, typeof(UnityEngine.Object), false);
            EditorGUILayout.LabelField("内存占用：" + info.mem + " " + "硬盘占用：" + info.size + " " + "格式是否错误：" + info.isFormatError + " "+ "文件路径：" + info.path);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
    }

    void Export()
    {
        string dicPath = this.GetTempDicByDel();
        if (Directory.Exists(dicPath))
        {
            Directory.Delete(dicPath, true);
        }

        dependDic.Clear();
        checkList.Clear();
        copyList.Clear();
        guidOldToNewMap.Clear();
        guidsInFileMap.Clear();
        ownGuids.Clear();
        oldGuidAsset.Clear();
        shaderList.Clear();
        gameObjAttachList.Clear();
        addGameObjList.Clear();
        dependList.Clear();
        colletcGuidList.Clear();
        nirvanaList.Clear();

        bool isFile = true;
        this.checkNum = 0;

        for (int j = 0; j < objectList.Count; j ++)
        {
            string checkPath = AssetDatabase.GetAssetPath(objectList[j]);
            if (Path.GetExtension(checkPath).Equals(string.Empty))
            {
                string StrCheckFolderPath = AssetDatabase.GetAssetPath(objectList[j]);
                string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, "*.*", SearchOption.AllDirectories);
                for (int i = 0; i < assetsPath.Length; i++)
                {
                    var real_path = assetsPath[i].Replace('\\', '/');
                    if (dependDic.ContainsKey(real_path))
                    {
                        continue;
                    }

                    this.checkNum++;
                    DependNode node;
                    node.path = real_path;
                    node.nodeList = new List<DependNode>();
                    dependDic.Add(real_path, node);
                    this.FindDepend(real_path, node);
                }

                this.WriteLog("OldGuidAsst", this.oldGuidAsset);
            }
            else
            {
                isFile = false;
                this.checkNum++;
                string path = AssetDatabase.GetAssetPath(objectList[j]);
                DependNode node;
                node.path = path;
                node.nodeList = new List<DependNode>();
                this.FindDepend(path, node);
                dependDic.Add(path, node);
                this.WriteLog("OldGuidAsst", this.oldGuidAsset);
            }
        }


        this.WriteDependLog("OldDependLog", this.dependList);
        if (gameObjAttachList.Count > 0)
        {
            for (int i = 0; i < gameObjAttachList.Count; i ++)
            {
                string attachPath = AssetDatabase.GUIDToAssetPath(gameObjAttachList[i]);
                if (attachPath != null && !attachPath.Equals(string.Empty))
                {
                    string checkObjPath = attachPath.Replace('\\', '/');
                    if (dependDic.ContainsKey(checkObjPath))
                    {
                        continue;
                    }

                    this.checkNum++;
                    DependNode node;
                    node.path = checkObjPath;
                    node.nodeList = new List<DependNode>();
                    dependDic.Add(checkObjPath, node);
                    this.FindDepend(checkObjPath, node);
                }
            }
        }

        this.WriteGameObjAttachLog("GameObjAttachLog", this.addGameObjList);
        this.WriteGameObjAttachLog("NirvanaList", this.nirvanaList);

        this.copyNum = 0;
        this.alrealyCopyNum = 0;
        foreach (var kv in dependDic)
        {
            this.CopyDicAsset(kv.Value);
        }

        this.WriteLog("ShaderList", this.shaderList, true);
    }

    bool Accept()
    {
        bool isSuccess = false;
        string acceptPath = Application.dataPath + "//Export";
        if (Directory.Exists(acceptPath))
        {
            Directory.Delete(acceptPath,true);
        }

        this.BuildGameObjAttackByLog();
        isSuccess = this.BuildOldGuidByLog();
        if (!isSuccess)
        {
            Debug.LogError("导入失败，找不到旧引用日志");
            return isSuccess;
        }

        this.onewGuidAsset.Clear();
        isSuccess = this.CopyByTemp();

        return isSuccess;
    }

    void FindDepend(string path, DependNode parent)
    {
        if (this.checkList.Contains(path))
        {
            return;
        }


        this.checkNum++;
        checkList.Add(path);
  
        DependNode node;
        node.path = path;
        node.nodeList = new List<DependNode>();
        parent.nodeList.Add(node);
        string exName = Path.GetExtension(path);
        string metaPath = path + ".meta";

        if (path.EndsWith(".prefab"))
        {
            GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(path, typeof(GameObject));
            if (obj != null)
            {

#if (explaceFlag)
                var attach = obj.GetComponentsInChildren<Nirvana.GameObjectAttach>();
#else
                var attach = obj.GetComponentsInChildren<Game.GameObjectAttach>();
#endif
                foreach (var c in attach)
                {
                    if (c != null)
                    {
                        string objPath = this.GetPathByParent(obj.transform, c.transform);
                        string attackGuid = string.Empty;
                        bool isHas = ProToo.ContainProperty(c, "AssetGuid");
#if (isHas)
                        attackGuid = c.AssetGuid;
#else
                        attackGuid = c.Asset.AssetGUID;
#endif
                        List<string> childList = new List<string>();
                        string assetPath = AssetDatabase.GUIDToAssetPath(attackGuid);
                        string result = this.ChangeNameByList(path.Replace('\\', '/'));
                        assetPath = this.ChangeNameByList(assetPath.Replace('\\', '/'));
                        if (!result.Equals(path.Replace('\\', '/')))
                        {
                            string[] objPathList = objPath.Split('/');
                            objPath = "";
                            for (int i = 0; i < objPathList.Length; i ++)
                            {
                                string pathKey = objPathList[i];
                                if (i == 0)
                                {
                                    for (int j = 0; j < objectList.Count; j++)
                                    {

                                        if (!custonNameList[j].Equals(""))
                                        {
                                            pathKey = pathKey.Replace(objectList[j].name, custonNameList[j]);
                                            break;
                                        }
                                    }
                                }

                                objPath = objPath + pathKey;
                                if ( i < objPathList.Length - 1)
                                {
                                    objPath = objPath + "/";
                                }
                            }
                        }

                        objPath = objPath + "|" + assetPath;
                        if (this.addGameObjList.TryGetValue(result, out childList))
                        {
                            childList.Add(objPath);
                        }
                        else
                        {
                            if (childList == null)
                            {
                                childList = new List<string>();
                            }

                            childList.Add(objPath);
                            this.addGameObjList.Add(result, childList);
                        }
                        if (!isHas)
                        {
                            attackGuid = c.Asset.AssetGUID;
                        }
                        else
                        {
                            attackGuid = c.AssetGuid;
                        }

                        if (!gameObjAttachList.Contains(attackGuid))
                        {
                            gameObjAttachList.Add(attackGuid);
                            //DestroyImmediate(c);
                        }
                    }
                }
            }

            var renderList = obj.GetComponentsInChildren<NirvanaRenderer>();
            foreach (var c in renderList)
            {
                if (c != null)
                {
                    string objPath = this.GetPathByParent(obj.transform, c.transform);
                    string result = this.ChangeNameByList(path.Replace('\\', '/'));
                    List<string> childList = new List<string>();
                    if (!result.Equals(path.Replace('\\', '/')))
                    {
                        string[] objPathList = objPath.Split('/');
                        objPath = "";
                        for (int i = 0; i < objPathList.Length; i++)
                        {
                            string pathKey = objPathList[i];
                            if (i == 0)
                            {
                                for (int j = 0; j < objectList.Count; j++)
                                {

                                    if (!custonNameList[j].Equals(""))
                                    {
                                        pathKey = pathKey.Replace(objectList[j].name, custonNameList[j]);
                                        break;
                                    }
                                }
                            }

                            objPath = objPath + pathKey;
                            if (i < objPathList.Length - 1)
                            {
                                objPath = objPath + "/";
                            }
                        }

                        var render = c.GetComponent<Renderer>();
                        for (int k = 0; k < render.sharedMaterials.Length; k++)
                        {
                            objPath = objPath + "&" + AssetDatabase.GetAssetPath(render.sharedMaterials[k]);
                        }
                    }

                    if (this.nirvanaList.TryGetValue(result, out childList))
                    {
                        childList.Add(objPath);
                    }
                    else
                    {
                        if (childList == null)
                        {
                            childList = new List<string>();
                        }

                        childList.Add(objPath);
                        this.nirvanaList.Add(result, childList);
                    }
                }
            }
        }
        //BuildGuid(metaPath);
        //BuildGuid(path);
        this.CollectOldGuid(path);
        var dependList = AssetDatabase.GetDependencies(path);
        if (dependList.Length > 1)
        {
            if (!this.dependList.ContainsKey(path))
            {
                List<string> dList = new List<string>();
                for (int i = 0; i < dependList.Length; i ++)
                {
                    dList.Add(dependList[i]);
                }
                this.dependList.Add(path, dList);
            }

            for (int i = 0; i < dependList.Length; i++)
            {
                this.FindDepend(dependList[i], node);
            }
        }
        else
        {
            //if (!parent.Equals(null) && parent.nodeList != null)
            //{
            //    parent.nodeList.Add(info);
            //}
            return;
        }
    }

    string GetPathByParent(Transform parent, Transform m)
    {
        string path = string.Empty;
        if (parent != null && parent != m)
        {
            int childCount = parent.childCount;
            if (childCount > 0)
            {
                for (int i = 0; i < childCount; i++)
                {
                    string name = GetPathByParent(parent.GetChild(i), m);
                    if (name != null && !name.Equals(string.Empty))
                    {
                        path = parent.gameObject.name + "/" + name;
                    }
                }
            }
        }
        else
        {
            if (parent == m)
            {
                path = parent.gameObject.name;
            }
        }
        return path;


    }

    void GetTransformByPath(Transform parent, string path, out Transform childObj)
    {
        Transform m = null;
        string[] pathList = path.Split('/');
        if (pathList.Length > 1)
        {
            string findPath = "";
            for (int i = 1; i < pathList.Length; i++)
            {
                string flag = i == pathList.Length - 1 ? "" : "/";
                findPath = findPath + pathList[i] + flag;
            }
            if (parent.childCount > 0)
            {
                for (int i = 0; i < parent.childCount; i++)
                {
                    Transform child = parent.GetChild(i);
                    if (child.name == pathList[1])
                    {
                        //Transform c = GetTransformByPath(child, findPath);
                        Transform c;
                        GetTransformByPath(child, findPath, out c);
                        if (c != null)
                            m = c;
                    }
                }
            }
        }
        else
        {
            if (pathList[0] == parent.gameObject.name)
            {
                m = parent.transform;
            }
        }

        childObj = m;
    }

    string ChangeNameByList(string path)
    {
        string relust = path;
        for (int j = 0; j < objectList.Count; j ++)
        {
            string dicPath = AssetDatabase.GetAssetPath(objectList[j]);
            string[] allFiles = Directory.GetFiles(dicPath, "*.*", SearchOption.AllDirectories);
            for (int i = 0; i < allFiles.Length; i++)
            {
                if (relust.Equals(allFiles[i].Replace('\\', '/')) && !custonNameList[j].Equals(""))
                {
                    relust = relust.Replace(objectList[j].name, custonNameList[j]);
                    break;
                }
            }
        }

        return relust;
    }

    void CopyDicAsset(DependNode node)
    {
        this.copyNum++;
        EditorUtility.DisplayProgressBar("Copy Asset", node.path, (float)this.copyNum / (float)this.checkNum);
        if (node.nodeList.Count > 0)
        {
            for (int i = 0; i < node.nodeList.Count; i++)
            {
                if (node.nodeList[i].path == node.path)
                {
                    this.alrealyCopyNum++;
                }

                this.CopyDicAsset(node.nodeList[i]);
            }
        }
        //else
        //{
            if (node.path.EndsWith(".dll") || node.path.EndsWith(".cs") || node.path.EndsWith(".shader"))
            {
                return;
            }

            if (node.path.EndsWith(".mat"))
            {
                string shaderName = string.Empty;
                string resultMat = this.ChangeNameByList(node.path);
                if (!shaderList.ContainsKey(resultMat))
                {
                    Material mat = (Material) AssetDatabase.LoadAssetAtPath(node.path, typeof(Material));
                shaderList.Add(resultMat, mat.shader.name);
                }
            }

            this.alrealyCopyNum++;
        // string dicPath = Application.temporaryCachePath + "//Export//";
            string dicPath = this.GetTempDic();
            string result = ChangeNameByList(node.path);
            string savePath = Path.Combine(dicPath, Path.GetDirectoryName(result));
            string assetPath = Path.Combine(savePath, Path.GetFileName(result));
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            if (!File.Exists(assetPath))
            {
                File.Copy(node.path, assetPath);
            }

        //List<string> guidList = new List<string>();
        //if (guidsInFileMap.TryGetValue(node.path, out guidList))
        //{
        //    string contents = File.ReadAllText(assetPath);

        //    foreach (string oldGuid in guidList)
        //    {
        //        if (!ownGuids.Contains(oldGuid))
        //            continue;

        //        string newGuid = guidOldToNewMap[oldGuid];
        //        if (string.IsNullOrEmpty(newGuid))
        //            throw new NullReferenceException("newGuid == null");

        //        contents = contents.Replace("guid: " + oldGuid, "guid: " + newGuid);
        //        Debug.LogError("replace guid:    "+ assetPath+"|" + oldGuid + "|"+ newGuid);
        //    }
        //    File.WriteAllText(assetPath, contents);
        //}

        if (this.copyNum == this.checkNum)
        {
            EditorUtility.ClearProgressBar();
        }
        //}
    }

    bool CopyByTemp()
    {
        bool isSucc = false;
        string dicPath = this.GetTempDic();
        if (!Directory.Exists(dicPath))
        {
            Debug.LogError("并没有可接收资源");
            return isSucc;
        }

        string[] kDefaultFileExtensions = {
                "*.*"
            };

        // Get list of working files  
        List<string> filesPaths = new List<string>();
        foreach (string extension in kDefaultFileExtensions)
        {
            filesPaths.AddRange(
                Directory.GetFiles(dicPath, extension, SearchOption.AllDirectories)
                );
        }

        if (filesPaths.Count <= 0)
        {
            return isSucc;
        }
        foreach (string filePath in filesPaths)
        {
            if (filePath.EndsWith(".meta"))
            {
                continue;
            }
            string newPath = filePath.Replace(this.GetCopyDic(), Application.dataPath);
            string fileDicPath = Path.GetDirectoryName(newPath);
            if (!Directory.Exists(fileDicPath))
            {
                Directory.CreateDirectory(fileDicPath);
            }

            File.Copy(filePath, newPath);

            //this.addGameObjList
            //if (newPath.EndsWith(".prefab"))
            //{
            //    string savePath = newPath.Replace(Application.dataPath + "//Export/", "");
            //    savePath = savePath.Replace('\\', '/');
            //    List<string> attachList = new List<string>();
            //    if (this.addGameObjList.TryGetValue(savePath, out attachList))
            //    {
            //        GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(newPath.Replace('\\', '/'), typeof(GameObject));
            //        for (int i = 0; i < attachList.Count; i ++)
            //        {
            //            string[] kv = attachList[i].Split('|');
            //            Transform child = this.GetTransformByPath(obj.transform, kv[0]);
            //            if (child != null)
            //            {
            //                var attack = child.gameObject.GetComponent<Game.GameObjectAttach>();
            //                if (attack == null)
            //                {
            //                    attack = child.gameObject.AddComponent<Game.GameObjectAttach>();
            //                }

            //                string effPath = Application.dataPath + "//Export/" + kv[1];
            //                string guid = AssetDatabase.AssetPathToGUID(effPath);
            //                attack.AssetGuid = guid;
            //            }
            //        }
            //    }
            //}
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();

        isSucc = true;
        return isSucc;
    }

    bool ChangeDepend()
    {
        bool isSucc = false;
        this.onewGuidAsset.Clear();
        this.effectDic.Clear();
        this.BuildEffectDic();
        this.replaceSameList.Clear();

        string acceptPath = Application.dataPath + "//Export//";
        if (!Directory.Exists(acceptPath))
        {
            Debug.LogError("创建接受文件夹失败");
            return isSucc;
        }

        string[] assetsPath = Directory.GetFiles(acceptPath, "*.*", SearchOption.AllDirectories);

        for (int i = 0; i < assetsPath.Length; i++)
        {
            if (assetsPath[i].EndsWith(".meta"))
            {
                this.CollectNewGuid(assetsPath[i]);
            }
        }

        this.WriteLog("NewGuidAsset", this.onewGuidAsset);
        for (int i = 0; i < assetsPath.Length; i++)
        {
            if (!assetsPath[i].EndsWith(".meta") && !this.GetIsIgnoreReplace(assetsPath[i]))
            {
                this.ReplaceGuid(assetsPath[i]);
            }
        }

        this.WriteLogByList("ReplaceSameEffAsset", this.replaceSameList);
        for (int j = 0; j < assetsPath.Length; j++)
        {
            if (!assetsPath[j].EndsWith(".meta") && assetsPath[j].EndsWith(".prefab"))
            {
                string savePath = assetsPath[j].Replace(Application.dataPath + "//Export/", "");
                string findPath = assetsPath[j].Replace('\\', '/');
                findPath = findPath.Replace(Application.dataPath + "//Export/", "");
                findPath = "Assets/Export/" + findPath;
                savePath = savePath.Replace('\\', '/');
                List<string> attachList = new List<string>();
                if (this.addGameObjList.TryGetValue(savePath, out attachList))
                {
                    GameObject aobj = (GameObject)AssetDatabase.LoadAssetAtPath(findPath, typeof(GameObject));
                    for (int i = 0; i < attachList.Count; i++)
                    {

                        var allContents = string.Empty;
                        allContents = File.ReadAllText(findPath);

                        string[] kv = attachList[i].Split('|');
                        Transform child;
                        this.GetTransformByPath(aobj.transform, kv[0], out child);
                        if (child != null)
                        {
#if (acceptFlag)
                                    var attack = child.gameObject.GetComponent<Nirvana.GameObjectAttach>();
#else
                            var attack = child.gameObject.GetComponent<Game.GameObjectAttach>();
#endif

                            //var attack = child.gameObject.GetComponent<Game.GameObjectAttach>();
                            if (attack == null)
                            {
#if (acceptFlag)
                                        attack = child.gameObject.AddComponent<Nirvana.GameObjectAttach>();
#else
                                attack = child.gameObject.AddComponent<Game.GameObjectAttach>();
#endif
                                //attack = child.gameObject.AddComponent<Game.GameObjectAttach>();
                            }


                            string effPath = "Assets/Export/" + kv[1];
                            string guid = AssetDatabase.AssetPathToGUID(effPath.Replace('\\', '/'));
                            string metaPath = effPath.Replace('\\', '/') + ".meta";

                            if (File.Exists(metaPath))
                            {
                                var contents = string.Empty;
                                try
                                {
                                    contents = File.ReadAllText(metaPath);
                                }
                                catch (Exception e)
                                {
                                    Debug.LogError(metaPath);
                                    Debug.LogError(e.ToString());
                                }

                                IEnumerable<string> guids = GetGuids(contents);
                                foreach (string oldGuid in guids)
                                {
                                    guid = oldGuid;
                                    allContents = allContents.Replace("assetGUID: " + attack.AssetGuid, "assetGUID: " + oldGuid);
                                    break;
                                }
                            }


                            attack.AssetGuid = guid;
                            File.WriteAllText(findPath, allContents);
                            //AssetDatabase.SaveAssets(); 
                        }
                    }
                }

                if (this.nirvanaList.TryGetValue(savePath, out attachList))
                {
                    GameObject aobj = (GameObject)AssetDatabase.LoadAssetAtPath(findPath, typeof(GameObject));
                    var nrList = aobj.GetComponentsInChildren<NirvanaRenderer>();
                    foreach (var a in nrList)
                    {
                        DestroyImmediate(a, true);
                    }

                    AssetDatabase.SaveAssets();
                    for (int i = 0; i < attachList.Count; i++)
                    {

                        var allContents = string.Empty;
                        allContents = File.ReadAllText(findPath);

                        string[] kv = attachList[i].Split('&');
                        Transform child;
                        this.GetTransformByPath(aobj.transform, kv[0], out child);
                        if (child != null)
                        {
                            var rd = child.GetComponent<Renderer>();
                            for (int k = 1; k < kv.Length; k++)
                            {
                                string newGuid = string.Empty;
                                if (this.onewGuidAsset.TryGetValue(kv[k], out newGuid))
                                {
                                    string path = AssetDatabase.GUIDToAssetPath(newGuid);
                                    rd.sharedMaterials[k] = (Material)AssetDatabase.LoadAssetAtPath(path, typeof(Material));
                                }
                            }
                        }
                    }
                }
            }
        }


        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();

        isSucc = true;
        return isSucc;
    }

    void BuildEffectDic()
    {
        string checkRoot = Application.dataPath + "/Game/Effects/";
        string[] assetsPath = Directory.GetFiles(checkRoot, "*.*", SearchOption.AllDirectories);
        for (int i = 0; i < assetsPath.Length; i++)
        {
            if (!assetsPath[i].EndsWith(".meta"))
            {
                string path = assetsPath[i].Replace('\\', '/');
                string key = assetsPath[i].Replace(checkRoot, "");
                key = key.Replace('\\', '/');
                if (!this.effectDic.ContainsKey(key))
                {
                    string checkPath = path.Replace(Application.dataPath, "Assets");
                    string guid = AssetDatabase.AssetPathToGUID(checkPath);
                    this.effectDic.Add(key, guid);
                }
            }
        }
    }

    void CollectOldGuid(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return;
        }

        if (this.colletcGuidList.Contains(path))
        {
            return;
        }
        else
        {
            this.colletcGuidList.Add(path);
        }
        //oldGuidAsset
        var contents = string.Empty;
        try
        {
            contents = File.ReadAllText(path);
        }
        catch (Exception e)
        {
            Debug.LogError(path);
            Debug.LogError(e.ToString());
        }

        IEnumerable<string> guids = GetGuids(contents);
        foreach (string oldGuid in guids)
        {
            if (!this.oldGuidAsset.ContainsKey(oldGuid))
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(oldGuid);
                if (!assetPath.Equals(string.Empty))
                {
                    string result = this.ChangeNameByList(assetPath);
                    this.oldGuidAsset.Add(oldGuid, result);
                }
            }
        }
    }

    void WriteLog(string name, Dictionary<string, string> dic, bool isSpe = false)
    {

        string dicPath = this.GetTempDic();

        string logPath = dicPath + name + ".txt";
        if (!Directory.Exists(dicPath))
        {
            Directory.CreateDirectory(dicPath);
        }

        if (File.Exists(logPath))
        {
            File.Delete(logPath);
        }

        FileStream fs = new FileStream(logPath, FileMode.Create, FileAccess.ReadWrite);
        StreamWriter sw = new StreamWriter(fs);
        foreach (var k in dic)
        {
            sw.WriteLine(k.Key.Replace('\\', '/') + "|" + k.Value);
        }
        sw.Close();
        fs.Close();
    }

    void WriteLogByList(string name, List<string> list, bool isSpe = false)
    {

        string dicPath = this.GetTempDic();

        string logPath = dicPath + name + ".txt";
        if (!Directory.Exists(dicPath))
        {
            Directory.CreateDirectory(dicPath);
        }

        if (File.Exists(logPath))
        {
            File.Delete(logPath);
        }

        FileStream fs = new FileStream(logPath, FileMode.Create, FileAccess.ReadWrite);
        StreamWriter sw = new StreamWriter(fs);
        foreach (var k in list)
        {
            sw.WriteLine(k.Replace('\\', '/'));
        }
        sw.Close();
        fs.Close();
    }

    void WriteDependLog(string name, Dictionary<string, List<string>> dic)
    {

        string dicPath = this.GetTempDic();

        string logPath = dicPath + name + ".txt";
        if (!Directory.Exists(dicPath))
        {
            Directory.CreateDirectory(dicPath);
        }

        if (File.Exists(logPath))
        {
            File.Delete(logPath);
        }

        FileStream fs = new FileStream(logPath, FileMode.Create, FileAccess.ReadWrite);
        StreamWriter sw = new StreamWriter(fs);
        foreach (var k in dic)
        {
            sw.WriteLine(k.Key.Replace('\\', '/') + "   引用列表如下：");
            foreach (string s in k.Value)
            {
                sw.WriteLine(s);
            }
            sw.WriteLine("");
            sw.WriteLine("");
            sw.WriteLine("");
        }
        sw.Close();
        fs.Close();
    }

    void WriteGameObjAttachLog(string name, Dictionary<string, List<string>> dic)
    {

        string dicPath = this.GetTempDic();

        string logPath = dicPath + name + ".txt";
        if (!Directory.Exists(dicPath))
        {
            Directory.CreateDirectory(dicPath);
        }

        if (File.Exists(logPath))
        {
            File.Delete(logPath);
        }

        FileStream fs = new FileStream(logPath, FileMode.Create, FileAccess.ReadWrite);
        StreamWriter sw = new StreamWriter(fs);
        foreach (var k in dic)
        {
            foreach (string s in k.Value)
            {
                sw.WriteLine(k.Key.Replace('\\', '/') + "|" + s);
            }
        }
        sw.Close();
        fs.Close();
    }

    void CollectNewGuid(string path)
    {
        if (Path.GetExtension(path) != ".meta")
        {
            return ;
        }

        var contents = string.Empty;
        try
        {
            contents = File.ReadAllText(path);
        }
        catch (Exception e)
        {
            Debug.LogError(path);
            Debug.LogError(e.ToString());
        }

        IEnumerable<string> guids = GetGuids(contents);
        bool isFirstGuid = true;
        foreach (string newGuid in guids)
        {
            string savePath = path.Replace(Application.dataPath + "//Export/", "");
            //string assetGuid = AssetDatabase.AssetPathToGUID(path.Replace(".meta", ""));
            if (!this.onewGuidAsset.ContainsKey(newGuid))
            {
                savePath = savePath.Replace(".meta", "");
                savePath = savePath.Replace("Assets//Export/", "");
                savePath = savePath.Replace('\\', '/');
                this.onewGuidAsset.Add(savePath, newGuid);
            }
        }

        return ;
    }

    void ReplaceGuid(string path)
    {
        var contents = string.Empty;
        try
        {
            contents = File.ReadAllText(path);
        }
        catch (Exception e)
        {
            Debug.LogError(path);
            Debug.LogError(e.ToString());
        }

        IEnumerable<string> guids = GetGuids(contents);
        foreach (string oldGuid in guids)
        {
            string oldAssetPath = string.Empty;
            if (this.oldGuidAsset.TryGetValue(oldGuid, out oldAssetPath))
            {
                string newGuid = string.Empty;
                oldAssetPath = oldAssetPath.Replace('\\', '/');
                oldAssetPath = oldAssetPath.Replace(Application.dataPath + "Assets//Export/", "");
                string checkSamePath = oldAssetPath.Replace("Assets/Game/Effects/", "");
                checkSamePath = checkSamePath.Replace("Assets/Game/Effects/", "");
                string sameGuid = "";
                if (this.effectDic.TryGetValue(checkSamePath, out sameGuid))
                {
                    if (!this.replaceSameList.Contains(checkSamePath))
                    {
                        this.replaceSameList.Add(checkSamePath);
                    }
                }

                if (!oldAssetPath.Equals(string.Empty) && this.onewGuidAsset.TryGetValue(oldAssetPath, out newGuid))
                {
                    if (!string.IsNullOrEmpty(sameGuid))
                    {
                        newGuid = sameGuid;
                    }
                    contents = contents.Replace("guid: " + oldGuid, "guid: " + newGuid);
                }
            }
        }

        File.WriteAllText(path, contents);
    }

    void BuildGuid(string path)
    {
        var contents = string.Empty;
        try
        {
            contents = File.ReadAllText(path);
        }
        catch (Exception e)
        {
            Debug.LogError(path);
            Debug.LogError(e.ToString());
        }

        IEnumerable<string> guids = GetGuids(contents);
        bool isFirstGuid = true;
        foreach (string oldGuid in guids)
        {
            // First GUID in .meta file is always the GUID of the asset itself  
            if (isFirstGuid && Path.GetExtension(path) == ".meta")
            {
                ownGuids.Add(oldGuid);
                isFirstGuid = false;
            }
            // Generate and save new GUID if we haven't added it before  
            if (!guidOldToNewMap.ContainsKey(oldGuid))
            {
                string newGuid = Guid.NewGuid().ToString("N");
                guidOldToNewMap.Add(oldGuid, newGuid);
            }

            if (!guidsInFileMap.ContainsKey(path))
                guidsInFileMap[path] = new List<string>();

            if (!guidsInFileMap[path].Contains(oldGuid))
            {
                guidsInFileMap[path].Add(oldGuid);
            }
        }
    }

    string GetTempDic()
    {
        int index = Application.temporaryCachePath.IndexOf("YouYan");
        string saveDicPath = Application.temporaryCachePath.Substring(0, index);
        string dicPath = saveDicPath + "YouYan" + "//Export//";
        return dicPath;
    }

    string GetCopyDic()
    {
        int index = Application.temporaryCachePath.IndexOf("YouYan");
        string saveDicPath = Application.temporaryCachePath.Substring(0, index);
        string dicPath = saveDicPath + "YouYan";
        return dicPath;
    }

    string GetTempDicByDel()
    {
        int index = Application.temporaryCachePath.IndexOf("YouYan");
        string saveDicPath = Application.temporaryCachePath.Substring(0, index);
        string dicPath = saveDicPath + "YouYan" + "//Export";
        return dicPath;
    }

    public bool GetIsIgnoreReplace(string path)
    {
        bool isIgnore = false;
        string e = Path.GetExtension(path).ToLower();
        if (e.Equals(".tga") || e.Equals(".jpg") || e.Equals(".png") || e.Equals(".fbx") || e.Equals(".dll")|| e.Equals(".cs") || e.Equals(".psd"))
        {
            isIgnore = true;
        }

        return isIgnore;
    }

    public bool BuildOldGuidByLog()
    {
        bool isSuccess = false;
        string dicPath = this.GetTempDic();
        string logPath = dicPath + "OldGuidAsst" + ".txt";
        if (File.Exists(logPath))
        {
            this.oldGuidAsset.Clear();
            var allLine = File.ReadAllLines(logPath);
            foreach (string line in allLine)
            {
                string[] kv = line.Split('|');
                if (kv[0] != null && !kv[0].Equals(string.Empty) && kv[1] != null && !kv[1].Equals(string.Empty))
                {
                    this.oldGuidAsset.Add(kv[0], kv[1]);
                }
            }

            isSuccess = true;
        }

        return isSuccess;
    }

    public void BuildGameObjAttackByLog()
    {
        string dicPath = this.GetTempDic();
        string logPath = dicPath + "GameObjAttachLog" + ".txt";
        if (File.Exists(logPath))
        {
            this.addGameObjList.Clear();
            var allLine = File.ReadAllLines(logPath);
            foreach (string line in allLine)
            {
                string[] kv = line.Split('|');
                if (kv[0] != null && !kv[0].Equals(string.Empty) && kv[1] != null && !kv[1].Equals(string.Empty) && kv[2] != null && !kv[2].Equals(string.Empty))
                {
                    List<string> list = new List<string>();
                    if (this.addGameObjList.TryGetValue(kv[0], out list))
                    {
                        list.Add(kv[1] + "|" + kv[2]);
                    }
                    else
                    {
                        if (list == null)
                        {
                            list = new List<string>();
                        }

                        list.Add(kv[1] + "|" + kv[2]);
                        this.addGameObjList.Add(kv[0], list);
                    }
                }
            }
        }
    }

    public void BuildNirvanaByLog()
    {
        string dicPath = this.GetTempDic();
        string logPath = dicPath + "NirvanaList" + ".txt";
        if (File.Exists(logPath))
        {
            this.nirvanaList.Clear();
            var allLine = File.ReadAllLines(logPath);
            foreach (string line in allLine)
            {
                string[] kv = line.Split('|');
                if (kv[0] != null && !kv[0].Equals(string.Empty) && kv[1] != null && !kv[1].Equals(string.Empty))
                {
                    string[] rv = line.Split('&');
                    List<string> list = new List<string>();
                    if (this.nirvanaList.TryGetValue(kv[0], out list))
                    {
                        list.Add(kv[1]);
                    }
                    else
                    {
                        if (list == null)
                        {
                            list = new List<string>();
                        }

                        list.Add(kv[1]);
                        this.nirvanaList.Add(kv[0], list);
                    }
                }
            }
        }
    }

    public void CheckGameObjAttach()
    {
        if (file == null)
        {
            Debug.LogError("未指定修复目录");
            return;
        }

#if (!explaceFlag)
        string checkPath = AssetDatabase.GetAssetPath(file);
        AssetsCheck.GameObjectAttachChecker checker = new AssetsCheck.GameObjectAttachChecker();
        string[] checkDis = { checkPath, };
        AssetsCheck.GameObjectAttachChecker.checkDirs = checkDis;
        //AssetsCheck.GameObjectAttachChecker.isUseSetDic = true;
        //AssetsCheck.GameObjectAttachChecker.OnGameObjectAttachChecker();
#endif

    }

    private static IEnumerable<string> GetGuids(string text)
    {
        const string guidStart = "guid: ";
        const int guidLength = 32;
        int textLength = text.Length;
        int guidStartLength = guidStart.Length;
        List<string> guids = new List<string>();

        int index = 0;
        while (index + guidStartLength + guidLength < textLength)
        {
            index = text.IndexOf(guidStart, index, StringComparison.Ordinal);
            if (index == -1)
                break;

            index += guidStartLength;
            string guid = text.Substring(index, guidLength);
            index += guidLength;

            if (IsGuid(guid))
            {
                guids.Add(guid);
            }
        }
        return guids;
    }
    private static bool IsGuid(string text)
    {
        for (int i = 0; i < text.Length; i++)
        {
            char c = text[i];
            if (
                !((c >= '0' && c <= '9') ||
                  (c >= 'a' && c <= 'z'))
                )
                return false;
        }

        return true;
    }
    private static string MakeRelativePath(string fromPath, string toPath)
    {
        Uri fromUri = new Uri(fromPath);
        Uri toUri = new Uri(toPath);

        Uri relativeUri = fromUri.MakeRelativeUri(toUri);
        string relativePath = Uri.UnescapeDataString(relativeUri.ToString());

        return relativePath;
    }

    private void ChangeParticleScaleMode()
    {
        if (file == null)
        {
            Debug.LogError("未指定修改目录");
            return;
        }

        string[] assetsPath = this.GetAssetPatgArray(file, "*.prefab");
        if (assetsPath == null)
        {
            return;
        }

        for (int i = 0; i < assetsPath.Length; i++)
        {
            var real_path = assetsPath[i].Replace('\\', '/');
            GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
            var particleList = obj.GetComponentsInChildren<ParticleSystem>();
            for (int j = 0; j < particleList.Length; j ++)
            {
                var main = particleList[j].main;
                main.scalingMode = ParticleSystemScalingMode.Hierarchy;
            }

            EditorUtility.SetDirty(obj);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    private void CreateGameAttackLog()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录");
        }

        string[] assetsPath = this.GetAssetPatgArray(file);
        if (assetsPath == null)
        {
            return;
        }

        List<string> checkAttackLog = new List<string>();

        for (int k = 0; k < assetsPath.Length; k++)
        {
            if (assetsPath[k].EndsWith(".prefab"))
            {
                var real_path = assetsPath[k].Replace('\\', '/');

                GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
                if (obj != null)
                {
#if (explaceFlag)
                        var attach = obj.GetComponentsInChildren<Nirvana.GameObjectAttach>();
#else
                    var attach = obj.GetComponentsInChildren<Game.GameObjectAttach>();
#endif
                    bool isInit = false;
                    foreach (var c in attach)
                    {
                        if (c != null)
                        {
                            bool isCheck = false;
                            string bundle = "";
                            string assetS = "";
                            if (string.IsNullOrEmpty(c.BundleName) || string.IsNullOrEmpty(c.AssetName))
                            {
                                isCheck = true;
                            }

                            var asset = EditorResourceMgr.LoadGameObject(c.BundleName, c.AssetName);
                            if (asset == null)
                            {
                                isCheck = true;
                            }

                            if (isCheck)
                            {
                                string objPath = this.GetPathByParent(obj.transform, c.transform);
                                if (!string.IsNullOrEmpty(c.BundleName))
                                {
                                    if (!isInit)
                                    {
                                        checkAttackLog.Add("该预制体GameAttack组件异常：     " + obj.name);
                                        isInit = true;
                                    }

                                    checkAttackLog.Add("该节点GameAttack组件无法生成特效模型：     " + objPath);
                                }
                            }
                        }
                    }

                    if (isInit)
                    {
                        checkAttackLog.Add("");
                        checkAttackLog.Add("");
                        checkAttackLog.Add("");
                    }
                }
            }
        }

        if (checkAttackLog.Count > 0)
        {
            this.WriteLogByList("checkAttackLog", checkAttackLog);
        }
    }

    private void CreateFadeMat()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录");
        }

        string[] assetsPath = this.GetAssetPatgArray(file);
        if (assetsPath == null)
        {
            return;
        }

        List<string> logList = new List<string>();


        for (int k = 0; k < assetsPath.Length; k++)
        {
            if (assetsPath[k].EndsWith(".prefab"))
            {
                var real_path = assetsPath[k].Replace('\\', '/');

                GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
                var fade = obj.GetComponent<ActorFadeout>();
                if (fade != null)
                {
                    //DestroyImmediate(obj);
                    //continue;
                    fade.AutoFetch();
                    if (fade.GetListSize() == 0)
                    {
                        if (!logList.Contains(real_path))
                        {
                            logList.Add(real_path);
                        }
                    }
                }             
            }
        }

        if (logList.Count > 0)
        {
            foreach (var path in logList)
            {
                Debug.LogErrorFormat("生成渐变材质失败，检查该模型的材质球_MaterialStyle是否为defalut：   " + path);
            }
        }
    }

    private void AddScript()
    {
        if (scriptList.Count <= 0)
        {
            return;
        }

        if (prefabList.Count <= 0)
        {
            return;
        }

        for (int i = 0; i < prefabList.Count; i ++)
        {
            string checkPath = AssetDatabase.GetAssetPath(prefabList[i]);
            GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath<GameObject>(checkPath);
            for (int j = 0; j < scriptList.Count; j ++)
            {
                foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
                {
                    foreach (Type type in assembly.GetTypes())
                    {
                        if (type.Name == scriptList[j].name)
                        {
                            if (obj.GetComponent(type) == null)
                            {
                                obj.AddComponent(type);
                            }
                        }
                    }
                }
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    private bool RemoveMissScripts(Transform trans)
    {
        bool isMiss = false;
        var components = trans.GetComponents<Component>();
        var serializedObject = new SerializedObject(trans.gameObject);
        var prop = serializedObject.FindProperty("m_Component");
        int r = 0;
        for (int i = 0; i < components.Length; i++)
        {
            if (components[i] == null)
            {
                isMiss = true;
                prop.DeleteArrayElementAtIndex(i - r);
                r++;
            }
        }

        if (isMiss)
        {
            serializedObject.ApplyModifiedProperties();
        }

        return isMiss;
    }

    private void ClearInVaildScript()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录 ");
            return;
        }

        this.missList.Clear();

        string[] assetsPath = this.GetAssetPatgArray(file);
        if (assetsPath == null)
        {
            return;
        }

        bool isRefresh = false;
        for (int k = 0; k < assetsPath.Length; k++)
        {
            if (assetsPath[k].EndsWith(".prefab"))
            {
                var real_path = assetsPath[k].Replace('\\', '/');
                GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
                /*GameObject p = (GameObject) PrefabUtility.InstantiatePrefab(obj);
                bool isMiss = this.RemoveMissScripts(p.transform);
                if (isMiss)
                {
                    isRefresh = true;
                    PrefabUtility.ReplacePrefab(p, obj);
                    AssetDatabase.Refresh();
                }

                DestroyImmediate(p);*/
                Debug.LogError("Check Miss: " + real_path);
                MonoBehaviour[] ml = obj.GetComponentsInChildren<MonoBehaviour>();
                foreach (var m in ml)
                {
                    if (m == null)
                    {
                        if (!this.missList.ContainsKey(real_path))
                        {
                            this.missList.Add(obj.name, real_path);
                        }

                        break;
                    }
                }

            }
        }

       WriteLog("MissLog", this.missList);
    }

    private string[] GetAssetPatgArray(UnityEngine.Object file, string findType = "*.*")
    {
        string StrCheckFolderPath = AssetDatabase.GetAssetPath(file);
        if (!AssetDatabase.IsValidFolder(StrCheckFolderPath))
        {
            if (EditorUtility.DisplayDialog("提示", "请选择文件目录！！！", "确定"))
            {
            }
            return null;
        }

        string[] assetsPath = Directory.GetFiles(StrCheckFolderPath, findType, SearchOption.AllDirectories);
        return assetsPath;
    }

    private void CreateQualityMaterials()
    {
        List<string> log = new List<string>();
        if (this.file != null)
        {
            string[] assetsPath = this.GetAssetPatgArray(file, "*.prefab");
            if (assetsPath == null)
            {
                return;
            }

            for (int i = 0; i < assetsPath.Length; i++)
            {
                var real_path = assetsPath[i].Replace('\\', '/');
                this.AtuoFetchByPrefabe(real_path, log);
            }

            if (log.Count > 0)
            {
                this.WriteLogByList("AutoCreateQualityMaterialLog", log);
            }
            return;
        }


        if (prefabList.Count <= 0)
        {
            return;
        }

        for (int i = 0; i < prefabList.Count; i++)
        {
            string checkPath = AssetDatabase.GetAssetPath(prefabList[i]);
            this.AtuoFetchByPrefabe(checkPath, log);
        }

        if (log.Count > 0)
        {
            this.WriteLogByList("AutoCreateQualityMaterialLog", log);
        }
    }

    private void AtuoFetchByPrefabe(string path, List<string> log = null)
    {
        GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath<GameObject>(path);
        if (obj == null)
        {
            if (log != null)
            {
                log.Add(path);
            }
            return;
        }

        var particle = obj.GetComponentInChildren<ParticleSystem>();
        if (particle != null)
        {
            var actorRenderCheck = obj.GetComponent<ActorRender>();
            if (log != null && actorRenderCheck != null)
            {
                log.Add(path);
            }
            return;
        }

        GameObject p = (GameObject)PrefabUtility.InstantiatePrefab(obj);
        var actorRender = p.GetComponent<ActorRender>();
        bool isOprea = false;
        if (actorRender == null)
        {
            if (this.isSkinPrefabe)
            {
                isOprea = true;
                var skinRender = p.GetOrAddComponent<SkinActorRender>();
                skinRender.AutoFetch();
            }
        }
        else
        {
            isOprea = true;
            actorRender.AutoFetch();
        }

        if (isOprea)
        {
            PrefabUtility.ReplacePrefab(p, obj);
            AssetDatabase.Refresh();
        }

        DestroyImmediate(p);
    }

    public void AddSpecialPoint()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录 ");
            return;
        }

        string[] assetsPath = this.GetAssetPatgArray(file);
        if (assetsPath == null)
        {
            return;
        }

        Vector3 off = new Vector3(-2.16317f, 0.7f, -0.1f);

        bool isRefresh = false;
        for (int k = 0; k < assetsPath.Length; k++)
        {
            if (assetsPath[k].EndsWith(".prefab"))
            {
                string dName = Path.GetDirectoryName(assetsPath[k]);
                var s = dName.Split('\\');
                var checkName = "";
                if (s.Length <= 1)
                {
                    var t = dName.Split('/');
                    checkName = t[t.Length - 1];
                }
                else
                {
                    checkName = s[1];
                }

                if (checkName == Path.GetFileNameWithoutExtension(assetsPath[k]))
                {
                    var real_path = assetsPath[k].Replace('\\', '/');
                    GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
                    GameObject p = (GameObject) PrefabUtility.InstantiatePrefab(obj);
                    Vector3 v = Vector3.zero;
                    bool isNeedAdd = p.transform.FindByName("AdjustPoint") == null;
                    var l = p.GetComponentsInChildren<Transform>();
                    bool isFindFail = true;
                    foreach (Transform t in l)
                    {
                        if (t.name == "mount_point" || t.name == "wing_point")
                        {
                            isFindFail = false;
                            v = t.position;
                            break;
                        }
                    }

                    if (isFindFail)
                    {
                        Debug.LogError("该资源不合规范，没有对应的mount_point或wing_point节点，请整理资源："+ real_path);
                        DestroyImmediate(p);
                        continue;
                    }

                    if (isNeedAdd)
                    {
                        var g = new GameObject();
                        g.transform.SetParent(p.transform, true);
                        g.name = "AdjustPoint";
                        g.transform.position = v + off;
                    }
                    else
                    {
                        Transform ap = p.transform.FindByName("AdjustPoint");
                        ap.position = v + off;
                    }

                    isRefresh = true;
                    PrefabUtility.ReplacePrefab(p, obj);
                    //AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
          
                    DestroyImmediate(p);
                }
               // var real_path = assetsPath[k].Replace('\\', '/');
               // GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
            }
        }
        
        if(isRefresh)
            AssetDatabase.Refresh();
    }

    public bool DeleteSameFile()
    {
        bool isSucc = true;
        if (this.replaceSameList.Count > 0)
        {
            foreach (var s in this.replaceSameList)
            {
                if (File.Exists(s))
                {
                    File.Delete(s);
                }
            }
        }

        AssetDatabase.Refresh();

        return isSucc;
    }

    private void StepLog()
    {
        string info = "";
        if (this.autoStep == AutoStates.Normal)
        {
            info = "开始拷贝资源，建立旧资源引用目录";
        }
        else if (this.autoStep == AutoStates.BuildOldGuid)
        {
            info = "建立旧资源引用目录成功，开始替换资源里的旧GUID";

        }
        else if (this.autoStep == AutoStates.ChangeGuid)
        {
            info = "替换资源里的旧GUID成功，开始删除重复的特效资源";

        }
        else if (this.autoStep == AutoStates.DeleteSame)
        {
            info = "删除重复的特效资源成功，开始移动文件";

        }
        else if (this.autoStep == AutoStates.MoveFile)
        {
            info = "移动文件成功，开始修复GameAttach引用";

        }

        bool isCancel = EditorUtility.DisplayCancelableProgressBar(info, "当前进度：", (float)autoStep / (float)AutoStates.MoveFile);
    }

    public void CheckFileByDir(string root)
    {
        if (root.Equals(""))
        {
            return;
        }

        string[] fileList = Directory.GetFiles(root);
        foreach (string f in fileList)
        {
            string expPath = f.Replace('\\', '/');
            string mPath = expPath.Replace("//Export//Assets//Game//", "/Game/");
            mPath = mPath.Replace("//", "/");
            if (!File.Exists(mPath))
            {
                Debug.LogError("Move File    " + mPath);
                File.Move(f, mPath);
                AssetDatabase.Refresh();
            }
        }
    }

    public void CheckAllDir(string root)
    {
        if (root.Equals(""))
        {
            return;
        }

        string[] dirList = Directory.GetDirectories(root);
        if (dirList.Length <= 0)
        {
            this.CheckFileByDir(root);
        }

        foreach (string s in dirList)
        {
            string expPath = s.Replace('\\', '/');
            string mPath = expPath.Replace("//Export//Assets//Game//", "/Game/");
            mPath = mPath.Replace("//", "/");
            if (!Directory.Exists(mPath))
            {
                Debug.LogError("Move Dic    " + mPath);
                Directory.Move(s, mPath);
                File.Move(s + ".meta", mPath + ".meta");
                AssetDatabase.Refresh();
            }
            else
            {
                this.CheckFileByDir(s);
                this.CheckAllDir(s);
            }
        }
    }

    public bool MoveFile()
    {
        bool isSucc = false;
        string acceptPath = Application.dataPath + "//Export//";
        if (!Directory.Exists(acceptPath))
        {
            Debug.LogError("检测不到接收目录");
            return isSucc;
        }

        string actorPath = acceptPath + "Assets//Game//Actors";
        string effectPath = acceptPath + "Assets//Game//Effects";

        string[] dirList = Directory.GetDirectories(actorPath);
        foreach (var s in dirList)
        {
            if (s.Contains("Shared"))
            {
                continue;
            }

            string[] subList = Directory.GetDirectories(s);
            foreach (var ss in subList)
            {
                string mPath = "";
                if (Directory.Exists(ss))
                {
                    string expPath = ss.Replace('\\', '/');
                    mPath = expPath.Replace("//Export//Assets//Game//", "/Game/");
                    mPath = mPath.Replace("//", "/");
                    mPath = mPath + "(clone)";
                }

                if (!mPath.Equals(""))
                {
                    Debug.LogError(ss + "|" + mPath);
                    Directory.Move(ss, mPath);
                    File.Move(ss + ".meta", mPath + ".meta");
                }
            }
        }

        this.CheckAllDir(effectPath);

        isSucc = true;
        return isSucc;
    }

    public void AutoReceive()
    {
        this.autoCopyCount = 1;
        this.autoStep = AutoStates.Normal;

        if (!this.Accept())
        {
            Debug.LogError("接受资源失败 无法创建旧的引用目录");
            //EditorUtility.ClearProgressBar();
            //EditorApplication.update -= StepLog;
            return;
        }
        else
        {
            this.autoStep = AutoStates.BuildOldGuid;
        }

        if (!this.ChangeDepend())
        {
            Debug.LogError("更新引用关系失败");
            //EditorUtility.ClearProgressBar();
            //EditorApplication.update -= StepLog;
            return;
        }
        else
        {
            this.autoStep = AutoStates.ChangeGuid;
        }


        if (!this.DeleteSameFile())
        {
            Debug.LogError("删除重复特效资源失败");
            //EditorUtility.ClearProgressBar();
            //EditorApplication.update -= StepLog;
            return;
        }
        else
        {
            this.autoStep = AutoStates.DeleteSame;
        }

        if (!this.MoveFile())
        {
            Debug.LogError("移动文件失败");
            //EditorUtility.ClearProgressBar();
            //EditorApplication.update -= StepLog;
            return;
        }
        else
        {
            this.autoStep = AutoStates.MoveFile;
        }

        //EditorUtility.ClearProgressBar();
        //EditorApplication.update -= StepLog;
        this.CheckGameObjAttach();
    }

    public void MonsterHitPoint()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录 ");
            return;
        }

        if (removeType == null)
        {
            Debug.LogError("请先移除脚本 ");
            return;
        }

        Type checkType = null;
        foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
        {
            foreach (Type type in assembly.GetTypes())
            {
                if (type.Name == removeType.name)
                {
                    checkType = type;
                    break;
                }
            }
        }

        if (checkType == null)
        {
            Debug.LogError("初始化查找类型失败 ");
            return;
        }

        string[] assetsPath = this.GetAssetPatgArray(file);
        if (assetsPath == null)
        {
            return;
        }

        Vector3 off = new Vector3(-2.16317f, 0.7f, -0.1f);

        bool isRefresh = false;
        for (int k = 0; k < assetsPath.Length; k++)
        {
            if (assetsPath[k].EndsWith(".prefab"))
            {

                var real_path = assetsPath[k].Replace('\\', '/');
                GameObject obj = (GameObject)AssetDatabase.LoadAssetAtPath(real_path, typeof(GameObject));
                RemoveObjComponent(checkType, obj);
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    public void RemoveObjComponent(Type c, GameObject obj)
    {
        if (obj == null)
        {
            return;
        }

        var child = obj.GetComponentsInChildren<Transform>();
        if (child.Length <= 0)
        {
            return;
        }

        for(int i = 0; i < child.Length; i++)
        {
            var t = child[i].GetComponent<UnityEngine.Rendering.PostProcessing.PostProcessLayer>();
            if (t != null)
            {
                DestroyImmediate(t, true);
                Debug.LogErrorFormat("Delete Component:{0}, obj:{1}", c.Name, child[i].name);
            }
        }
    }

    public void FindDependByGuid()
    {
        if (file == null)
        {
            Debug.LogError("请先设置检查目录 ");
            return;
        }

        string path = AssetDatabase.GetAssetPath(file);
        var guid = AssetDatabase.AssetPathToGUID(path);
        var assetsPath = AssetDatabase.FindAssets("t:scene");
        Dictionary<string, string> dic = new Dictionary<string, string>();
        int count = 0;

        for (int k = 0; k < assetsPath.Length; k++)
        {
            var real_path = AssetDatabase.GUIDToAssetPath(assetsPath[k]);
            if (File.Exists(real_path))
            {
                var contents = string.Empty;
                try
                {
                    contents = File.ReadAllText(real_path);
                }
                catch (Exception e)
                {
                    Debug.LogError(real_path);
                    Debug.LogError(e.ToString());
                }

                IEnumerable<string> guids = GetGuids(contents);
                foreach (string oldGuid in guids)
                {
                    if (oldGuid == guid)
                    {
                        if (!dic.ContainsKey(real_path))
                        {
                            count = count + 1;
                            dic.Add(real_path, oldGuid);
                        }
                        
                        break;
                    }
                }
            }
        }

        Debug.LogErrorFormat("FindDependByGuid: guid:{0}  count:{1}  ", guid, count);
        this.WriteLog("CheckScene", dic);
    }

    public void CheckTextureMem() {
        this.texCheckList.Clear();
        setIndex = 0;
        setPath.Clear();
        texCheckFinish = false;
        stopTexFlush = false;

        if (file == null)
        {
            var assetsPath = AssetDatabase.FindAssets("t:Texture");
            for (int i = 0; i < assetsPath.Length; i++)
            {
                setPath.Add(assetsPath[i]);
            }
        }
        else
        {
            string[] assetsPath = this.GetAssetPatgArray(file);
            if (assetsPath == null)
            {
                return;
            }

            for (int i = 0; i < assetsPath.Length; i++)
            {
                if (assetsPath[i].EndsWith("png") || assetsPath[i].EndsWith("jpg") || assetsPath[i].EndsWith("tga"))
                {
                    setPath.Add(assetsPath[i]);
                }
            }
        }

        if (setPath.Count <= 0)
        {
            Debug.LogError("此次并没有搜索到贴图文件");
            return;
        }

        EditorApplication.update += AutoDoTexCheck;
    }

    public void AutoDoTexCheck()
    {
        var path = setPath[setIndex];
        bool isCancel = EditorUtility.DisplayCancelableProgressBar("正在检查贴图：", path, (float)setIndex / (float)setPath.Count);
        if (setPath != null && setPath.Count > 0 && setIndex < setPath.Count && setPath[setIndex] != null && !isCancel)
        {
            DoTextureCheck(path);
            setIndex = setIndex + 1;
        }

        if (isCancel || setIndex >= setPath.Count)
        {
            EditorApplication.update -= AutoDoTexCheck;
            EditorUtility.ClearProgressBar();
            setIndex = 0;
            setPath.Clear();
        }
    }

    public void DoTextureCheck(string checkPath)
    {
        long checkMen = 100 * 1024;
        long checkSize = 100 * 1024;

        var real_path = checkPath + ".meta";
        var tex = AssetDatabase.LoadAssetAtPath(checkPath, typeof(Texture)) as Texture;
        var bit = Profiler.GetRuntimeMemorySizeLong(tex);
        var mem = EditorUtility.FormatBytes(bit);
        var type = System.Reflection.Assembly.Load("UnityEditor.dll").GetType("UnityEditor.TextureUtil");
        MethodInfo info = type.GetMethod("GetStorageMemorySize", BindingFlags.Static | BindingFlags.Instance | BindingFlags.Public);
        //Debug.LogError("所占内存：" + mem);
        var bitSize = (int)info.Invoke(null, new object[] { tex });
        var memSize = EditorUtility.FormatBytes(bitSize);
        //Debug.LogError("所占硬盘：" + memSize);
        bool isWrite = false;
        bool formatError = false;
        if (bit >= checkMen || bitSize >= checkSize)
        {
            isWrite = true;
        }

        if (!isWrite)
        {
            try
            {
                var contents = File.ReadAllLines(real_path);
                int num = 0;
                for (int i = 0; i < contents.Length; i++)
                {
                    var index = contents[i].IndexOf("textureFormat: ", 0);
                    if (index != -1)
                    {
                        string fStr = contents[i].Replace("textureFormat: ", "");
                        var value = Decimal.Parse(fStr);
                        if (value != 56)
                        {
                            num++;
                        }
                    }

                    if (num > 2)
                    {
                        formatError = true;
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError(real_path);
                Debug.LogError(e.ToString());
            }
        }

        if (isWrite)
        {
            TextureInfo log = new TextureInfo();
            log.path = checkPath;
            log.mem = mem;
            log.size = memSize;
            log.isFormatError = formatError;
            log.obj = tex;
            this.texCheckList.Add(log);
            texCheckFinish = true;
        }
    }

    public string[] GetTextureMem(Texture t)
    {
        string[] s = new string[2];
        if (t == null)
        {
            return s;
        }

        var bit = Profiler.GetRuntimeMemorySizeLong(t);
        var mem = EditorUtility.FormatBytes(bit);
        var type = System.Reflection.Assembly.Load("UnityEditor.dll").GetType("UnityEditor.TextureUtil");
        MethodInfo info = type.GetMethod("GetStorageMemorySize", BindingFlags.Static | BindingFlags.Instance | BindingFlags.Public);
        //Debug.LogError("所占内存：" + mem);
        s[1] = mem;
        var bitSize = (int)info.Invoke(null, new object[] { t });
        var memSize = EditorUtility.FormatBytes(bitSize);
        //Debug.LogError("所占硬盘：" + memSize);
        s[2] = memSize;

        return s;
    }
}
