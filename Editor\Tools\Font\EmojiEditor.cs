﻿using System.Linq;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using TMPro;

namespace UniFramework.Editor
{

    public class EmojiEditor : EditorWindow
    {
        [MenuItem("Tools/字体/文字表情生成器")]
        protected static void Init()
        {
            var window = EditorWindow.GetWindow<EmojiEditor>();
            window.titleContent = new GUIContent("文字表情生成器");
        }

        protected string m_Path = @"Assets/Emoji";
        protected string m_OutPath = @"Assets/Game/UIs/Emoji";
        protected int m_EmojiSize = 64;
        protected int m_DataSize = 4;
        protected int m_Padding = 2;

        private const string m_TMPSettingsPath = @"Assets/TextMesh Pro/Resources/TMP Settings.asset";
        private TMP_Settings m_TMPSettings;
        private SerializedObject m_TMPSettingsSerializedObject;

        protected class EmojiFile
        {
            public string FileName;
            public string Name;
            public Dictionary<int, string> Frames = new Dictionary<int, string>();
        }

        protected int CalcPreferredRowCount(int file_count)
        {
            var v = Mathf.CeilToInt(Mathf.Sqrt(file_count));
            return Mathf.NextPowerOfTwo(v);
        }

        private Texture2D ScaleTexture(Texture2D source, int targetWidth, int targetHeight)
        {
            var result = new Texture2D(targetWidth, targetHeight, TextureFormat.RGBA32, false);
            var incX = (1.0f / (float)targetWidth);
            var incY = (1.0f / (float)targetHeight);
            for (int i = 0; i < result.height; ++i) {
                for (int j = 0; j < result.width; ++j) {
                    Color newColor = source.GetPixelBilinear((float)j / (float)result.width, (float)i / (float)result.height);
                    result.SetPixel(j, i, newColor);
                }
            }
            result.Apply();
            return result;
        }

        protected Rect DrawTextureTo(string path, Texture2D target_texture, int x, int y, int size, int padding = 0)
        {
            size = size - padding * 2;

            var tex = AssetDatabase.LoadAssetAtPath<Texture2D>(Path.Combine(m_Path, path));
            if (tex.width > size || tex.height > size) {
                tex = Instantiate<Texture2D>(tex);
                if (tex.width == tex.height) {
                    tex = ScaleTexture(tex, size, size);
                    //TextureScaler.Point(tex, size, size);
                }
                else {
                    var aspect_ratio = tex.width / (float)tex.height;
                    if (aspect_ratio > 1f) {
                        tex = ScaleTexture(tex, size, (int)(size / aspect_ratio));
                        //TextureScaler.Point(tex, size, (int)(size / aspect_ratio));
                    }
                    else {
                        tex = ScaleTexture(tex, (int)(size * aspect_ratio), size);
                        //TextureScaler.Point(tex, (int)(size * aspect_ratio), size);
                    }
                }
            }

            var pixels = tex.GetPixels32();
            var offset_x = Mathf.Ceil((size - tex.width) / 2f);
            var offset_y = Mathf.Ceil((size - tex.height) / 2f);
            var rect = new Rect(x + padding + offset_x, y + padding + offset_y, tex.width, tex.height);
            target_texture.SetPixels32((int)rect.x, (int)rect.y, (int)rect.width, (int)rect.height, pixels);

            return rect;
        }

        protected void DrawColorTo(Color c, Texture2D target_texture, int x, int y, int size)
        {
            for (var i = 0; i < size; ++i) {
                for (var j = 0; j < size; ++j) {
                    target_texture.SetPixel(x + i, y + j, c);
                }
            }
        }

        protected Color Idx2Color(int frame_count)
        {
            // 转为16进制保存，通过rgb3位保存，支持最大的idx为0xFFF即4095
            var r = frame_count / 16;
            var g = (frame_count - r * 16) / 4;
            var b = frame_count - r * 16 - g * 4;
            var half = 0.5f / 4f;

            return new Color(
                r / 4f + half,
                g / 4f + half,
                b / 4f + half,
                1f);
        }

        protected void FillTexture(Texture2D tex, Color c)
        {
            for (var x = 0; x < tex.width; ++x) {
                for (var y = 0; y < tex.width; ++y) {
                    tex.SetPixel(x, y, c);
                }
            }
        }

        protected void OnGUI()
        {
            if (GUILayout.Button("开始")) {
                var dir = new DirectoryInfo(m_Path);
                var files = dir.GetFiles("*.png",SearchOption.AllDirectories);
                var row_count = CalcPreferredRowCount(files.Length);
                var tex_size = row_count * m_EmojiSize;
                var target_texture = new Texture2D(tex_size, tex_size, TextureFormat.RGBA32, false, false);
                FillTexture(target_texture, new Color(0f, 0f, 0f, 0f));

                var data_texture = new Texture2D(row_count * m_DataSize, row_count * m_DataSize, TextureFormat.RGBA32, false, true);
                FillTexture(data_texture, new Color(0f, 0f, 0f, 0f));

                var emojis = new SortedDictionary<string, EmojiFile>();
                foreach (var file in files) {
                    var file_name = file.Name;
                    var name = Path.GetFileNameWithoutExtension(file.Name);
                    var frame_idx = 0;
                    var split_idx = name.LastIndexOf('_');
                    if (split_idx > 0) {
                        if (int.TryParse(name.Substring(split_idx + 1), out frame_idx)) {
                            name = name.Substring(0, split_idx);
                        }
                    }

                    EmojiFile emoji_file;
                    if (!emojis.TryGetValue(name, out emoji_file)) {
                        emoji_file = new EmojiFile() {
                            Name = name,
                            FileName = file_name,
                        };

                        emojis.Add(name, emoji_file);
                    }

                    if (frame_idx >= 0) {
                        emoji_file.Frames.Add(frame_idx, file_name);
                    }
                }

                var spr_metas = new List<SpriteMetaData>();
                var idx = 0;
                foreach (var kvp in emojis) {
                    var emoji_file = kvp.Value;

                    var x = idx % row_count;
                    var y = idx / row_count;
                    var rect = new Rect(
                        x * m_EmojiSize + m_Padding,
                        y * m_EmojiSize + m_Padding,
                        m_EmojiSize - m_Padding * 2,
                        m_EmojiSize - m_Padding * 2);

                    if (emoji_file.Frames.Count > 0) {
                        var frames = emoji_file.Frames.OrderBy(f => f.Key).Select(f => f.Value).ToList();
                        for (var i = 0; i < frames.Count; ++i) {
                            x = idx % row_count;
                            y = idx / row_count;
                            var ret = DrawTextureTo(frames[i], target_texture, x * m_EmojiSize, y * m_EmojiSize, m_EmojiSize, m_Padding);
                            if (i == 0) {
                                rect = ret;
                            }
                            DrawColorTo(Idx2Color(frames.Count), data_texture, x * m_DataSize, y * m_DataSize, m_DataSize);
                            idx++;
                        }
                    }
                    else {
                        x = idx % row_count;
                        y = idx / row_count;
                        rect = DrawTextureTo(emoji_file.FileName, target_texture, x * m_EmojiSize, y * m_EmojiSize, m_EmojiSize, m_Padding);
                        DrawColorTo(Idx2Color(1), data_texture, x * m_DataSize, y * m_DataSize, m_DataSize);
                        idx++;
                    }

                    var meta = new SpriteMetaData() {
                        name = emoji_file.Name,
                        rect = rect,
                    };
                    spr_metas.Add(meta);
                }

                target_texture.Apply();
                var bytes = target_texture.EncodeToPNG();
                var emoji_tex_path = Path.Combine(m_OutPath, "emoji_tex.png");
                var spr_asset_path = emoji_tex_path.Replace(".png", ".asset");


                Dictionary<string, TMP_SpriteGlyph> dic = new Dictionary<string, TMP_SpriteGlyph>();
                var p_asset = AssetDatabase.LoadAssetAtPath<TMP_SpriteAsset>(spr_asset_path);
                foreach (var s in p_asset.spriteGlyphTable) {
                    if (s.sprite != null) {
                        dic.Add(s.sprite.name, s);
                    }
                }

                if (File.Exists(emoji_tex_path)) {
                    AssetDatabase.DeleteAsset(emoji_tex_path);
                    AssetDatabase.Refresh();
                }

                File.WriteAllBytes(emoji_tex_path, bytes);

                data_texture.Apply();
                bytes = data_texture.EncodeToPNG();
                File.WriteAllBytes(Path.Combine(m_OutPath, "emoji_data.png"), bytes);

                AssetDatabase.Refresh();

                var importer = TextureImporter.GetAtPath(emoji_tex_path) as TextureImporter;

                TextureImporterSettings settings = new TextureImporterSettings();
                importer.ReadTextureSettings(settings);
                settings.textureType = TextureImporterType.Sprite;
                settings.spriteMode = (int)SpriteImportMode.Multiple;
                settings.spriteMeshType = SpriteMeshType.FullRect;
                settings.spriteGenerateFallbackPhysicsShape = false;
                settings.sRGBTexture = true;
                settings.alphaSource = TextureImporterAlphaSource.FromInput;
                settings.alphaIsTransparency = true;
                settings.wrapMode = TextureWrapMode.Clamp;
                settings.filterMode = FilterMode.Bilinear;
                settings.mipmapEnabled = false;
                importer.SetTextureSettings(settings);

                importer.SetPlatformTextureSettings(new TextureImporterPlatformSettings() {
                    name = "Default",
                    maxTextureSize = 2048,
                    format = TextureImporterFormat.Automatic,
                    textureCompression = TextureImporterCompression.CompressedHQ,
                    compressionQuality = 100,
                });

                importer.SetPlatformTextureSettings(new TextureImporterPlatformSettings() {
                    name = "Android",
                    overridden = true,
                    maxTextureSize = 2048,
                    format = TextureImporterFormat.ASTC_6x6,
                    compressionQuality = 100,
                });

                importer.spritesheet = spr_metas.ToArray();
                importer.SaveAndReimport();
                AssetDatabase.Refresh();

                Selection.activeObject = AssetDatabase.LoadAssetAtPath<Texture2D>(emoji_tex_path);
                TMPro.EditorUtilities.TMP_SpriteAssetMenu.CreateSpriteAsset();
                var spr_asset = AssetDatabase.LoadAssetAtPath<TMP_SpriteAsset>(spr_asset_path);
                foreach (var s in spr_asset.spriteGlyphTable) {
                    var m = s.metrics;
                    if (s.sprite != null) {
                        m.horizontalBearingX = 0;
                        m.horizontalBearingY = s.sprite.rect.height * 0.8f;
                        if (dic.TryGetValue(s.sprite.name, out var spriteGlyph)) {
                            m.horizontalBearingY = spriteGlyph.metrics.horizontalBearingY;
                            s.scale = spriteGlyph.scale;
                        }
                        s.metrics = m;
                    }
                }

                var spr_mat = AssetDatabase.LoadAssetAtPath<Material>(Path.Combine(m_OutPath, "emoji_mat.mat"));
                spr_asset.material = spr_mat;
                spr_asset.UpdateLookupTables();
                EditorUtility.SetDirty(spr_asset);
                dic.Clear();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                m_TMPSettings = AssetDatabase.LoadAssetAtPath<TMP_Settings>(m_TMPSettingsPath);
                m_TMPSettingsSerializedObject = new SerializedObject(m_TMPSettings);
                m_TMPSettingsSerializedObject.FindProperty("m_defaultSpriteAsset").objectReferenceValue = AssetDatabase.LoadAssetAtPath<TMP_SpriteAsset>(spr_asset_path);
                m_TMPSettingsSerializedObject.ApplyModifiedProperties();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();


            }
        }
    }
}
