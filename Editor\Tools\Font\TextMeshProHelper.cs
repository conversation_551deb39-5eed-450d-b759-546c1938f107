﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.TextCore;
using UnityEngine.TextCore.LowLevel;


public static class TextMeshProHelper
{
    //[MenuItem("Tools/字体/提取字体贴图")]
    //private static void ExtractTexture()
    //{
    //    var obj = Selection.activeObject;
    //    if (obj is TMP_FontAsset) {
    //        var path = AssetDatabase.GetAssetPath(obj);
    //        ExtractTexture(path);
    //    }
    //}

    private static void UpdateMaterialPrsets(string font_path, Texture2D atlas)
    {
        var font_name = Path.GetFileNameWithoutExtension(font_path);
        bool IsMeterialPresetOf(string mat_path)
        {
            return Path.GetFileNameWithoutExtension(mat_path).StartsWith(font_name);
        }

        var folder = Path.GetDirectoryName(font_path);
        var mats = AssetDatabase.FindAssets("t:material", new string[] { folder })
            .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
            .Where(IsMeterialPresetOf)
            .Select(path => AssetDatabase.LoadAssetAtPath<Material>(path));

        foreach (var mat in mats) {
            mat.mainTexture = atlas;
            EditorUtility.SetDirty(mat);
        }
    }

    public static void ExtractTexture(string font_path)
    {
        var texture_path = Path.ChangeExtension(font_path, ".png");
        var target_font_asset = AssetDatabase.LoadAssetAtPath<TMP_FontAsset>(font_path);

        var texture2d = new Texture2D(
            target_font_asset.atlasTexture.width, 
            target_font_asset.atlasTexture.height, 
            TextureFormat.Alpha8, 
            mipChain: false);
        Graphics.CopyTexture(target_font_asset.atlasTexture, texture2d);

        var bytes = texture2d.EncodeToPNG();
        using var fs = File.Open(texture_path, FileMode.OpenOrCreate);
        fs.Write(bytes, 0, bytes.Length);
        fs.Flush();
        fs.Close();

        AssetDatabase.Refresh();

        var atlas = AssetDatabase.LoadAssetAtPath<Texture2D>(texture_path);
        AssetDatabase.RemoveObjectFromAsset(target_font_asset.atlasTexture);

        target_font_asset.atlasTextures[0] = atlas;
        target_font_asset.material.mainTexture = atlas;

        // 这里还要把所有材质预设也修正一下
        UpdateMaterialPrsets(font_path, atlas);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    public static void CreateFontAsset(string font_name, int font_size, Texture2D atlas, List<Glyph> glyph_table, List<TMP_Character> character_table)
    {
        var font_asset = ScriptableObject.CreateInstance<TMP_FontAsset>();
        AssetDatabase.CreateAsset(font_asset, $@"Assets/Game/UIs/TMP/ArtFont/{font_name}.asset");

        // TMP_FontAsset的很多接口都是internal的,这里只能通过反射来调用
        // 创建方法参考自TMPro_FontAssetCreatorWindow.Save_Bitmap_FontAsset函数
        var FontAssetType = typeof(TMP_FontAsset);
        void set_property(string property, object val)
        {
            FontAssetType.GetProperty(property, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic)
                .SetValue(font_asset, val);
        }

        set_property(nameof(TMP_FontAsset.version), "1.1.0");
        set_property(nameof(TMP_FontAsset.atlasRenderMode), GlyphRenderMode.RASTER);
        set_property(nameof(TMP_FontAsset.faceInfo), new FaceInfo() {
            familyName = font_name,
            styleName = string.Empty,
            pointSize = font_size,
            scale = 1,
            lineHeight = font_size,
            ascentLine = font_size,
            baseline = font_size,
            tabWidth = 0,
        });
        set_property(nameof(TMP_FontAsset.glyphTable), glyph_table);
        set_property(nameof(TMP_FontAsset.characterTable), character_table);
        FontAssetType.GetMethod("SortAllTables", BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic).Invoke(font_asset, null);

        font_asset.atlasTextures = new Texture2D[] { atlas };
        set_property(nameof(TMP_FontAsset.atlasWidth), atlas.width);
        set_property(nameof(TMP_FontAsset.atlasHeight), atlas.height);
        set_property(nameof(TMP_FontAsset.atlasPadding), 0);

        AssetDatabase.AddObjectToAsset(atlas, font_asset);

        var mat = new Material(Shader.Find("TextMeshPro/Bitmap Custom Atlas"));
        mat.name = $"{font_name} Atlas Material";
        mat.SetTexture(ShaderUtilities.ID_MainTex, atlas);
        font_asset.material = mat;

        AssetDatabase.AddObjectToAsset(mat, font_asset);

        AssetDatabase.SaveAssets();

        AssetDatabase.ImportAsset(AssetDatabase.GetAssetPath(font_asset));
        font_asset.ReadFontAssetDefinition();

        AssetDatabase.Refresh();
    }
}

