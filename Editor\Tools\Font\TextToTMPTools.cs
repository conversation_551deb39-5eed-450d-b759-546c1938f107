using System;
using System.Collections.Generic;
using System.Linq;
using Nirvana;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Text转TextMeshPro工具
/// 用于批量将旧的Text组件转换为TextMeshPro组件，并处理相关的描边、阴影效果
/// </summary>
public class TextToTMPTools : OdinEditorWindow
{
    #region 常量定义
    private const string UI_VIEW_PATH = @"Assets/Game/UIs/View";
    private const string TITLE_PATH = @"Assets/Game/Effects/Prefab/Model/Title";
    private const string FONT_MAT_PATH = "Assets/Game/UIs/TMP/FontMat/";
    private const string FONT_NAME_MAIN = "FZSSKSJW_M";
    private const string FONT_NAME_LIGHT = "FZSSKSJW_L";
    #endregion

    #region 窗口初始化
    [MenuItem("Tools/字体/Text转TMP工具")]
    static void OpenWindow()
    {
        GetWindow<TextToTMPTools>("Text转TMP工具").Show();
    }
    #endregion

    #region 数据结构定义
    public enum OutLineSize
    {
        One = 1,
        Two = 2,
        Three = 3,
    }

    [System.Serializable]
    [HideReferenceObjectPicker]
    public class EffectData
    {
        public Color color;
        public GameObject prefab;
        public GameObject node;

        public EffectData(Color color, GameObject prefab, GameObject node)
        {
            this.color = color;
            this.prefab = prefab;
            this.node = node;
        }
    }
    #endregion

    #region 测试功能
    [FoldoutGroup("测试功能")]
    public GameObject prefab;

    [FoldoutGroup("测试功能")]
    [Button("测试")]
    public void Test()
    {
        if (prefab == null)
        {
            Debug.LogWarning("请先设置测试预制体");
            return;
        }

        var rectTransform = prefab.GetComponent<RectTransform>();
        Debug.Log($"测试预制体: {prefab.name}");
        UnityEngine.RectTransformUtility.WorldToScreenPoint(null, rectTransform.position);
    }
    #endregion

    #region 描边处理
    [LabelText("描边数据")]
    private Dictionary<OutLineSize, List<Color>> outlineDictionary = new Dictionary<OutLineSize, List<Color>>();

    [TabGroup("描边")]
    public List<EffectData> outlineList_1 = new List<EffectData>();
    [TabGroup("描边")]
    public List<EffectData> outlineList_2 = new List<EffectData>();
    [TabGroup("描边")]
    public List<EffectData> outlineList_3 = new List<EffectData>();

    [TabGroup("描边")]
    [Button("查找所有旧描边组件", ButtonSizes.Large)]
    public void FindAllOutline()
    {
        InitializeOutlineDictionary();
        ClearOutlineLists();

        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH, TITLE_PATH });
        int count = ProcessOutlineComponents(prefabs);

        SortOutlineLists();
        LogOutlineResults(count);
        EditorUtility.ClearProgressBar();
    }

    private void InitializeOutlineDictionary()
    {
        outlineDictionary.Clear();
        outlineDictionary.Add(OutLineSize.One, new List<Color>());
        outlineDictionary.Add(OutLineSize.Two, new List<Color>());
        outlineDictionary.Add(OutLineSize.Three, new List<Color>());
    }

    private void ClearOutlineLists()
    {
        outlineList_1.Clear();
        outlineList_2.Clear();
        outlineList_3.Clear();
    }

    private int ProcessOutlineComponents(List<GameObject> prefabs)
    {
        int count = 0;
        for (var i = 0; i < prefabs.Count; ++i)
        {
            var prefab = prefabs[i];
            EditorUtility.DisplayProgressBar("查找旧描边组件", prefab.name, i / (float)prefabs.Count);

            foreach (var outline in prefab.GetComponentsInChildren<Outline>(includeInactive: true))
            {
                var outLineSize = GetOutlineSize(outline.effectDistance.x);
                var color = outline.effectColor;
                color.a = 1;

                var list = outlineDictionary[outLineSize];
                if (!list.Contains(color))
                {
                    count++;
                    list.Add(color);
                    AddToOutlineList(new EffectData(color, prefab, outline.gameObject), outLineSize);
                }
            }
        }
        return count;
    }

    private OutLineSize GetOutlineSize(float distance)
    {
        if (distance > 1 && distance <= 2)
            return OutLineSize.Two;
        else if (distance > 2 && distance <= 3)
            return OutLineSize.Three;
        return OutLineSize.One;
    }

    private void AddToOutlineList(EffectData data, OutLineSize size)
    {
        switch (size)
        {
            case OutLineSize.Three:
                outlineList_3.Add(data);
                break;
            case OutLineSize.Two:
                outlineList_2.Add(data);
                break;
            default:
                outlineList_1.Add(data);
                break;
        }
    }

    private void SortOutlineLists()
    {
        outlineList_1 = SortByHSV(outlineList_1);
        outlineList_2 = SortByRGB(outlineList_2);
        outlineList_3 = SortByHSV(outlineList_3);
    }

    private List<EffectData> SortByHSV(List<EffectData> list)
    {
        return list.OrderBy(data =>
        {
            Color.RGBToHSV(data.color, out float h, out _, out _);
            return h;
        }).ThenBy(data =>
        {
            Color.RGBToHSV(data.color, out _, out _, out float v);
            return v;
        }).ThenBy(data =>
        {
            Color.RGBToHSV(data.color, out _, out float s, out _);
            return s;
        }).ToList();
    }

    private List<EffectData> SortByRGB(List<EffectData> list)
    {
        return list.OrderBy(data => data.color.r)
                   .ThenBy(data => data.color.g)
                   .ThenBy(data => data.color.b).ToList();
    }

    private void LogOutlineResults(int count)
    {
        foreach (var item in outlineDictionary)
        {
            Debug.Log($"旧描边组件数量:key:{item.Key} value:{item.Value.Count}");
        }
        Debug.Log($"旧描边组件总数量:{count}");
    }

    /// <summary>
    /// 查找当前组件描边数量
    /// </summary>
    public int FindOutlineCount(GameObject prefab)
    {
        if (prefab == null) return 0;
        return prefab.GetComponentsInChildren<Outline>(includeInactive: true).Length;
    }

    [PropertySpace(10)]
    [TabGroup("描边")]
    [Button("替换描边颜色", ButtonSizes.Large), GUIColor(0f, 0.74f, 1f)]
    public void ReplaceOutlineColor(List<Color> colorList, Color targetColor, OutLineSize targetOutLineSize = OutLineSize.One)
    {
        if (colorList == null || colorList.Count == 0)
        {
            Debug.LogWarning("颜色列表为空");
            return;
        }

        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int changedCount = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("替换描边颜色", prefab.name, i / (float)prefabs.Count);

                bool isChanged = ProcessOutlineColorReplacement(prefab, colorList, targetColor, targetOutLineSize);
                if (isChanged)
                {
                    changedCount++;
                    EditorUtility.SetDirty(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"替换描边颜色完成，共修改 {changedCount} 个预制体");
        }
    }

    private bool ProcessOutlineColorReplacement(GameObject prefab, List<Color> colorList, Color targetColor, OutLineSize targetOutLineSize)
    {
        bool isChanged = false;
        foreach (var outline in prefab.GetComponentsInChildren<Outline>(includeInactive: true))
        {
            var outLineSize = GetOutlineSize(outline.effectDistance.x);
            if (outLineSize == targetOutLineSize)
            {
                var color = outline.effectColor;
                color.a = 1;
                if (colorList.Contains(color))
                {
                    outline.effectColor = targetColor;
                    isChanged = true;
                    EditorUtility.SetDirty(outline);
                }
            }
        }
        return isChanged;
    }

    [PropertySpace(10)]
    [TabGroup("描边")]
    [Button("描边大小全改为1", ButtonSizes.Large), GUIColor(0f, 0.74f, 1f)]
    public void ReplaceOutlineSize()
    {
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH, TITLE_PATH });
        int count = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("修改描边大小", prefab.name, i / (float)prefabs.Count);

                bool isChanged = false;
                foreach (var outline in prefab.GetComponentsInChildren<Outline>(includeInactive: true))
                {
                    if (outline.effectDistance.x <= 2.5f && outline.effectDistance.x >= 1.001f)
                    {
                        outline.effectDistance = new Vector2(1f, -1f);
                        isChanged = true;
                        EditorUtility.SetDirty(outline);
                        count++;
                    }
                }

                if (isChanged)
                {
                    EditorUtility.SetDirty(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"修改 {count} 个描边组件大小");
        }
    }


    [PropertyOrder(100)]
    [PropertySpace(10)]
    [TabGroup("描边")]
    [LabelText("使用该描边的所有预制体"), GUIColor(0.5f, 0.74f, 1f)]
    public List<EffectData> outlineList_use = new List<EffectData>();

    [PropertyOrder(100)]
    [TabGroup("描边")]
    [Button("查找使用该描边的所有预制体", ButtonSizes.Large), GUIColor(0.5f, 0.74f, 1f)]
    public void FindOutlineColor(Color targetColor)
    {
        outlineList_use.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找描边颜色", prefab.name, i / (float)prefabs.Count);

                foreach (var outline in prefab.GetComponentsInChildren<Outline>(includeInactive: true))
                {
                    var color = outline.effectColor;
                    color.a = 1;
                    if (targetColor == color)
                    {
                        outlineList_use.Add(new EffectData(outline.effectColor, prefab, outline.gameObject));
                    }
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"找到 {outlineList_use.Count} 个使用该描边颜色的预制体");
        }
    }
    #endregion


    #region 阴影处理
    [TabGroup("阴影")]
    public List<EffectData> shadowList_1 = new List<EffectData>();
    [TabGroup("阴影")]
    public List<EffectData> shadowList_2 = new List<EffectData>();
    [TabGroup("阴影")]
    public List<EffectData> shadowList_3 = new List<EffectData>();

    [TabGroup("阴影")]
    [Button("查找所有旧阴影组件", ButtonSizes.Large)]
    public void FindAllShadow()
    {
        ClearShadowLists();
        var shadowDictionary = new Dictionary<OutLineSize, List<Color>>
        {
            { OutLineSize.One, new List<Color>() },
            { OutLineSize.Two, new List<Color>() },
            { OutLineSize.Three, new List<Color>() }
        };

        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int count = ProcessShadowComponents(prefabs, shadowDictionary);

        SortShadowLists();
        LogShadowResults(shadowDictionary, count);
    }

    private void ClearShadowLists()
    {
        shadowList_1.Clear();
        shadowList_2.Clear();
        shadowList_3.Clear();
    }

    private int ProcessShadowComponents(List<GameObject> prefabs, Dictionary<OutLineSize, List<Color>> shadowDictionary)
    {
        int count = 0;
        for (var i = 0; i < prefabs.Count; ++i)
        {
            var prefab = prefabs[i];
            EditorUtility.DisplayProgressBar("查找旧阴影组件", prefab.name, i / (float)prefabs.Count);

            foreach (var shadow in prefab.GetComponentsInChildren<Shadow>(includeInactive: true))
            {
                if (shadow is Outline) continue;

                var shadowSize = GetShadowSize(shadow.effectDistance.x);
                var color = shadow.effectColor;
                color.a = 1;

                var list = shadowDictionary[shadowSize];
                if (!list.Contains(color))
                {
                    count++;
                    list.Add(color);
                    AddToShadowList(new EffectData(color, prefab, shadow.gameObject), shadowSize);
                }
            }
        }
        EditorUtility.ClearProgressBar();
        return count;
    }

    private OutLineSize GetShadowSize(float distance)
    {
        if (distance > 2 && distance <= 3)
            return OutLineSize.Three;
        return OutLineSize.One;
    }

    private void AddToShadowList(EffectData data, OutLineSize size)
    {
        switch (size)
        {
            case OutLineSize.Three:
                shadowList_3.Add(data);
                break;
            default:
                shadowList_1.Add(data);
                break;
        }
    }

    private void SortShadowLists()
    {
        shadowList_1 = SortByHSV(shadowList_1);
        shadowList_3 = SortByHSV(shadowList_3);
    }

    private void LogShadowResults(Dictionary<OutLineSize, List<Color>> shadowDictionary, int count)
    {
        foreach (var item in shadowDictionary)
        {
            Debug.Log($"旧阴影组件数量:key:{item.Key} value:{item.Value.Count}");
        }
        Debug.Log($"旧阴影组件总数量:{count}");
    }

    [PropertySpace(10)]
    [TabGroup("阴影")]
    [GUIColor(0f, 0.74f, 1f)]
    [Button("替换阴影颜色", ButtonSizes.Large)]
    public void ReplaceShadowColor(List<Color> colorList, Color targetColor, OutLineSize targetOutLineSize = OutLineSize.One)
    {
        if (colorList == null || colorList.Count == 0)
        {
            Debug.LogWarning("颜色列表为空");
            return;
        }

        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int changedCount = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("替换阴影颜色", prefab.name, i / (float)prefabs.Count);

                bool isChanged = ProcessShadowColorReplacement(prefab, colorList, targetColor, targetOutLineSize);
                if (isChanged)
                {
                    changedCount++;
                    EditorUtility.SetDirty(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"替换阴影颜色完成，共修改 {changedCount} 个预制体");
        }
    }

    private bool ProcessShadowColorReplacement(GameObject prefab, List<Color> colorList, Color targetColor, OutLineSize targetOutLineSize)
    {
        bool isChanged = false;
        foreach (var shadow in prefab.GetComponentsInChildren<Shadow>(includeInactive: true))
        {
            if (shadow is Outline) continue;

            var shadowSize = GetShadowSize(shadow.effectDistance.x);
            if (shadowSize == targetOutLineSize)
            {
                var color = shadow.effectColor;
                color.a = 1;
                if (colorList.Contains(color))
                {
                    shadow.effectColor = targetColor;
                    isChanged = true;
                    EditorUtility.SetDirty(shadow);
                }
            }
        }
        return isChanged;
    }

    [PropertyOrder(100)]
    [PropertySpace(10)]
    [TabGroup("阴影")]
    [LabelText("使用该阴影的所有预制体"), GUIColor(0.5f, 0.74f, 1f)]
    public List<EffectData> shadowList_use = new List<EffectData>();

    [PropertyOrder(100)]
    [TabGroup("阴影")]
    [Button("查找使用该阴影的所有预制体", ButtonSizes.Large), GUIColor(0.5f, 0.74f, 1f)]
    public void FindShadowColor(Color targetColor)
    {
        shadowList_use.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找阴影颜色", prefab.name, i / (float)prefabs.Count);

                foreach (var shadow in prefab.GetComponentsInChildren<Shadow>(includeInactive: true))
                {
                    if (shadow is Outline) continue;

                    var color = shadow.effectColor;
                    color.a = 1;
                    if (targetColor == color)
                    {
                        shadowList_use.Add(new EffectData(shadow.effectColor, prefab, shadow.gameObject));
                    }
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"找到 {shadowList_use.Count} 个使用该阴影颜色的预制体");
        }
    }

    /// <summary>
    /// 查找当前组件阴影数量
    /// </summary>
    public int FindShadowCount(GameObject prefab)
    {
        if (prefab == null) return 0;

        int count = 0;
        foreach (var shadow in prefab.GetComponentsInChildren<Shadow>(includeInactive: true))
        {
            if (!(shadow is Outline))
            {
                count++;
            }
        }
        return count;
    }
    #endregion

    #region TMP组件检查和诊断
    /// <summary>
    /// TMP组件属性丢失信息
    /// </summary>
    [System.Serializable]
    public class TMPMissingInfo
    {
        [TableColumnWidth(120)]
        public GameObject prefab;

        [TableColumnWidth(120)]
        public GameObject node;

        [TableColumnWidth(100)]
        public string componentName;

        [TableColumnWidth(60)]
        public int missingCount;

        [TableColumnWidth(200)]
        [ShowInInspector, ReadOnly]
        public string missingPropertiesText => string.Join(", ", missingProperties);

        [HideInInspector]
        public List<string> missingProperties = new List<string>();

        [TableColumnWidth(80)]
        [Button("定位")]
        public void SelectInHierarchy()
        {
            if (node != null)
            {
                Selection.activeGameObject = node;
                EditorGUIUtility.PingObject(node);
            }
        }

        [TableColumnWidth(80)]
        [Button("预制体")]
        public void SelectPrefab()
        {
            if (prefab != null)
            {
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);
            }
        }

        public TMPMissingInfo(GameObject prefab, GameObject node, string componentName)
        {
            this.prefab = prefab;
            this.node = node;
            this.componentName = componentName;
        }
    }

    [FoldoutGroup("TMP诊断")]
    [PropertySpace(5)]
    [InfoBox("TMP组件诊断工具 - 可选择检查项进行诊断", InfoMessageType.Info)]

    [FoldoutGroup("TMP诊断")]
    [LabelText("属性丢失")]
    public bool checkMissingReferences = true;

    [FoldoutGroup("TMP诊断")]
    [LabelText("字体资源")]
    public bool checkFontAssets = true;

    [FoldoutGroup("TMP诊断")]
    [LabelText("字体大小")]
    public bool checkFontSize = true;

    [FoldoutGroup("TMP诊断")]
    [LabelText("尺寸检查")]
    public bool checkRectTransform = false;

    [FoldoutGroup("TMP诊断")]
    [LabelText("材质Shader")]
    public bool checkMaterialShader = true;

    [FoldoutGroup("TMP诊断")]
    [ShowInInspector, ReadOnly]
    [LabelText("诊断结果统计")]
    public string DiagnosticSummary => $"共发现 {diagnosticIssues.Count} 个问题";

    [FoldoutGroup("TMP诊断")]
    [LabelText("诊断问题列表")]
    [TableList(ShowIndexLabels = true, ShowPaging = true, NumberOfItemsPerPage = 10)]
    public List<TMPDiagnosticIssue> diagnosticIssues = new List<TMPDiagnosticIssue>();

    /// <summary>
    /// TMP诊断问题信息
    /// </summary>
    [System.Serializable]
    public class TMPDiagnosticIssue
    {
        [TableColumnWidth(120)]
        public GameObject prefab;

        [TableColumnWidth(120)]
        public GameObject node;

        [TableColumnWidth(100)]
        public string issueType;

        [TableColumnWidth(200)]
        public string description;

        [TableColumnWidth(80)]
        [Button("定位")]
        public void SelectInHierarchy()
        {
            if (node != null)
            {
                Selection.activeGameObject = node;
                EditorGUIUtility.PingObject(node);
            }
        }

        [TableColumnWidth(80)]
        [Button("预制体")]
        public void SelectPrefab()
        {
            if (prefab != null)
            {
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);
            }
        }

        public TMPDiagnosticIssue(GameObject prefab, GameObject node, string issueType, string description)
        {
            this.prefab = prefab;
            this.node = node;
            this.issueType = issueType;
            this.description = description;
        }
    }

    [System.Serializable]
    public class TMPConfigIssue
    {
        [TableColumnWidth(120)]
        public GameObject prefab;

        [TableColumnWidth(120)]
        public GameObject node;

        [TableColumnWidth(80)]
        public string issueType;

        [TableColumnWidth(180)]
        public string description;

        [TableColumnWidth(80)]
        [Button("定位")]
        public void SelectInHierarchy()
        {
            if (node != null)
            {
                Selection.activeGameObject = node;
                EditorGUIUtility.PingObject(node);
            }
        }

        [TableColumnWidth(80)]
        [Button("预制体")]
        public void SelectPrefab()
        {
            if (prefab != null)
            {
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);
            }
        }

        public TMPConfigIssue(GameObject prefab, GameObject node, string issueType, string description)
        {
            this.prefab = prefab;
            this.node = node;
            this.issueType = issueType;
            this.description = description;
        }
    }

    [FoldoutGroup("TMP诊断")]
    [HorizontalGroup("TMP诊断/操作按钮")]
    [Button("开始诊断", ButtonSizes.Large), GUIColor(0.7f, 0.9f, 1f)]
    public void StartTMPDiagnostic()
    {
        diagnosticIssues.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; i++)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("TMP组件诊断",
                    $"检查预制体: {prefab.name} ({i + 1}/{prefabs.Count})",
                    (float)i / prefabs.Count);

                CheckPrefabTMPIssues(prefab);
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            ShowDiagnosticResult();
        }
    }

    [FoldoutGroup("TMP诊断")]
    [HorizontalGroup("TMP诊断/操作按钮")]
    [Button("导出报告", ButtonSizes.Large), GUIColor(0.9f, 0.9f, 0.7f)]
    public void ExportDiagnosticReport()
    {
        if (diagnosticIssues.Count == 0)
        {
            EditorUtility.DisplayDialog("导出报告", "没有诊断数据可导出，请先执行诊断。", "确定");
            return;
        }

        var reportPath = EditorUtility.SaveFilePanel("保存TMP诊断报告", "Assets", "TMP_Diagnostic_Report", "txt");
        if (string.IsNullOrEmpty(reportPath)) return;

        var report = GenerateDiagnosticReport();
        System.IO.File.WriteAllText(reportPath, report);

        EditorUtility.DisplayDialog("导出完成", $"TMP诊断报告已保存到:\n{reportPath}", "确定");
        EditorUtility.RevealInFinder(reportPath);
    }

    [FoldoutGroup("TMP诊断")]
    [HorizontalGroup("TMP诊断/操作按钮")]
    [Button("清空结果", ButtonSizes.Large), GUIColor(1f, 0.7f, 0.7f)]
    public void ClearDiagnosticResults()
    {
        if (EditorUtility.DisplayDialog("清空诊断结果", "确定要清空所有诊断结果吗？", "确定", "取消"))
        {
            diagnosticIssues.Clear();
            Debug.Log("TMP诊断结果已清空");
        }
    }

    private void CheckPrefabTMPIssues(GameObject prefab)
    {
        // 检查TextMeshProUGUI组件
        foreach (var tmpText in prefab.GetComponentsInChildren<TextMeshProUGUI>(includeInactive: true))
        {
            CheckTMPComponent(tmpText, prefab);
        }

        // 检查TextMeshPro组件
        foreach (var tmpText in prefab.GetComponentsInChildren<TextMeshPro>(includeInactive: true))
        {
            CheckTMPComponent(tmpText, prefab);
        }
    }

    private void CheckTMPComponent(TMP_Text tmpText, GameObject prefab)
    {
        // 检查属性丢失
        if (checkMissingReferences)
        {
            CheckMissingReferences(tmpText, prefab);
        }

        // 检查字体资源
        if (checkFontAssets)
        {
            CheckFontAssets(tmpText, prefab);
        }

        // 检查字体大小
        if (checkFontSize && tmpText.fontSize <= 0)
        {
            diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "字体大小", $"无效的字体大小: {tmpText.fontSize}"));
        }



        // 检查RectTransform尺寸
        if (checkRectTransform)
        {
            var rectTransform = tmpText.GetComponent<RectTransform>();
            if (rectTransform != null && IsRectTransformSizeInvalid(rectTransform))
            {
                diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "尺寸", "RectTransform实际显示尺寸无效"));
            }
        }

        // 检查材质Shader
        if (checkMaterialShader && tmpText.fontSharedMaterial != null && tmpText.fontSharedMaterial.shader == null)
        {
            diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "材质", "字体材质的Shader丢失"));
        }
    }

    private void CheckMissingReferences(TMP_Text tmpText, GameObject prefab)
    {
        var serializedObject = new SerializedObject(tmpText);
        var iterator = serializedObject.GetIterator();
        var missingProperties = new List<string>();

        while (iterator.NextVisible(true))
        {
            if (iterator.propertyType == SerializedPropertyType.ObjectReference)
            {
                if (iterator.objectReferenceValue == null && iterator.objectReferenceInstanceIDValue != 0)
                {
                    missingProperties.Add(iterator.propertyPath);
                }
            }
        }

        if (missingProperties.Count > 0)
        {
            var description = $"丢失 {missingProperties.Count} 个属性: {string.Join(", ", missingProperties.Take(3))}" +
                             (missingProperties.Count > 3 ? "..." : "");
            diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "属性丢失", description));
        }
    }

    private void CheckFontAssets(TMP_Text tmpText, GameObject prefab)
    {
        if (tmpText.font == null)
        {
            diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "字体资源", "字体资源丢失"));
        }

        if (tmpText.fontSharedMaterial == null)
        {
            diagnosticIssues.Add(new TMPDiagnosticIssue(prefab, tmpText.gameObject, "字体材质", "字体材质丢失"));
        }
    }

    private void ShowDiagnosticResult()
    {
        string message;
        if (diagnosticIssues.Count == 0)
        {
            message = "✅ TMP诊断完成 - 未发现任何问题！";
            Debug.Log(message);
        }
        else
        {
            var groupedIssues = diagnosticIssues.GroupBy(x => x.issueType).ToDictionary(g => g.Key, g => g.Count());
            var summary = string.Join(", ", groupedIssues.Select(kv => $"{kv.Key}: {kv.Value}个"));
            message = $"⚠️ TMP诊断完成 - 发现 {diagnosticIssues.Count} 个问题\n{summary}";
            Debug.LogWarning(message);
        }

        EditorUtility.DisplayDialog("TMP诊断结果", message, "确定");
    }

    private string GenerateDiagnosticReport()
    {
        var report = new System.Text.StringBuilder();

        report.AppendLine("TextMeshPro 诊断报告");
        report.AppendLine("生成时间: " + System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        report.AppendLine("========================================");
        report.AppendLine();

        // 检查选项
        report.AppendLine("检查选项:");
        report.AppendLine($"- 属性丢失检查: {(checkMissingReferences ? "✓" : "✗")}");
        report.AppendLine($"- 字体资源检查: {(checkFontAssets ? "✓" : "✗")}");
        report.AppendLine($"- 字体大小检查: {(checkFontSize ? "✓" : "✗")}");
        report.AppendLine($"- 尺寸检查: {(checkRectTransform ? "✓" : "✗")}");
        report.AppendLine($"- 材质Shader检查: {(checkMaterialShader ? "✓" : "✗")}");
        report.AppendLine();

        report.AppendLine($"总计发现 {diagnosticIssues.Count} 个问题");
        report.AppendLine();

        if (diagnosticIssues.Count > 0)
        {
            // 按问题类型分组
            var groupedIssues = diagnosticIssues.GroupBy(x => x.issueType).OrderBy(g => g.Key);

            foreach (var group in groupedIssues)
            {
                report.AppendLine($"【{group.Key}】问题 ({group.Count()} 个):");
                report.AppendLine("----------------------------------------");

                int index = 1;
                foreach (var issue in group)
                {
                    report.AppendLine($"{index}. 预制体: {issue.prefab.name}");
                    report.AppendLine($"   节点: {issue.node.name}");
                    report.AppendLine($"   描述: {issue.description}");
                    report.AppendLine();
                    index++;
                }
            }
        }
        else
        {
            report.AppendLine("✅ 未发现任何问题！");
        }

        return report.ToString();
    }

    private void ShowDiagnosticSummary(string checkType, int totalIssues, int affectedComponents)
    {
        string message;
        if (totalIssues == 0)
        {
            message = $"✅ {checkType}完成 - 未发现任何问题！";
            Debug.Log(message);
        }
        else
        {
            message = $"⚠️ {checkType}完成 - 发现 {totalIssues} 个问题，涉及 {affectedComponents} 个组件";
            Debug.LogWarning(message);
        }

        EditorUtility.DisplayDialog("TMP诊断结果", message, "确定");
    }

    /// <summary>
    /// 检查RectTransform尺寸是否无效
    /// 考虑布局组件和锚点拉伸的影响
    /// </summary>
    private bool IsRectTransformSizeInvalid(RectTransform rectTransform)
    {
        var rect = rectTransform.rect;
        var actualSize = new Vector2(Mathf.Abs(rect.width), Mathf.Abs(rect.height));

        if (actualSize.x < 0.1f || actualSize.y < 0.1f)
        {
            // 进一步检查是否受布局组件影响, 受布局组件影响，sizeDelta为零是正常的
            if (IsAffectedByLayoutComponents(rectTransform))
            {
                return false;
            }

            // 检查是否使用了拉伸锚点, 使用拉伸锚点，sizeDelta为零可能是正常的
            if (IsUsingStretchAnchors(rectTransform))
            {
                return false;
            }

            return true;
        }

        return false;
    }

    /// <summary>
    /// 检查是否受布局组件影响
    /// </summary>
    private bool IsAffectedByLayoutComponents(RectTransform rectTransform)
    {
        var parent = rectTransform.parent;
        if (parent == null) return false;

        // 检查父物体是否有布局组件
        var layoutGroup = parent.GetComponent<UnityEngine.UI.LayoutGroup>();
        if (layoutGroup != null && layoutGroup.enabled)
        {
            return true;
        }

        // 检查自身是否有布局元素组件
        var layoutElement = rectTransform.GetComponent<UnityEngine.UI.LayoutElement>();
        if (layoutElement != null && layoutElement.enabled)
        {
            return true;
        }

        // 检查是否有内容尺寸适配器
        var contentSizeFitter = rectTransform.GetComponent<UnityEngine.UI.ContentSizeFitter>();
        if (contentSizeFitter != null && contentSizeFitter.enabled)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 检查是否使用了拉伸锚点
    /// </summary>
    private bool IsUsingStretchAnchors(RectTransform rectTransform)
    {
        var anchorMin = rectTransform.anchorMin;
        var anchorMax = rectTransform.anchorMax;

        // 检查水平拉伸
        bool horizontalStretch = !Mathf.Approximately(anchorMin.x, anchorMax.x);

        // 检查垂直拉伸
        bool verticalStretch = !Mathf.Approximately(anchorMin.y, anchorMax.y);

        return horizontalStretch || verticalStretch;
    }
    #endregion

    [FoldoutGroup("TMP诊断")]
    [LabelText("默认字体资源")]
    public TMP_FontAsset defaultFontAsset;

    [FoldoutGroup("TMP诊断")]
    [LabelText("默认字体大小")]
    public int defaultFontSize = 20;

    [FoldoutGroup("TMP诊断")]
    [Button("自动修复TMP字体资源丢失", ButtonSizes.Large), GUIColor(1f, 0.5f, 0f)]
    public void AutoFixTMPFontAssets()
    {
        if (defaultFontAsset == null)
        {
            Debug.LogError("请先设置默认字体资源");
            return;
        }

        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int fixedCount = 0;
        int totalChecked = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; i++)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("修复TMP字体资源",
                    $"修复预制体: {prefab.name} ({i + 1}/{prefabs.Count})",
                    (float)i / prefabs.Count);

                var _fixed = FixPrefabTMPFontAssets(prefab);
                if (_fixed > 0)
                {
                    fixedCount += _fixed;
                    EditorUtility.SetDirty(prefab);
                }
                totalChecked++;
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"TMP字体资源修复完成 - 检查了 {totalChecked} 个预制体，修复了 {fixedCount} 个组件");
        }
    }

    private int FixPrefabTMPFontAssets(GameObject prefab)
    {
        int fixedCount = 0;

        // 修复TextMeshProUGUI组件
        foreach (var tmpText in prefab.GetComponentsInChildren<TextMeshProUGUI>(includeInactive: true))
        {
            bool needsFix = false;

            if (tmpText.font == null)
            {
                tmpText.font = defaultFontAsset;
                needsFix = true;
            }

            if (tmpText.fontSize <= 0)
            {
                tmpText.fontSize = defaultFontSize;
                needsFix = true;
            }

            if (needsFix)
            {
                fixedCount++;
            }
        }

        // 修复TextMeshPro组件
        foreach (var tmpText in prefab.GetComponentsInChildren<TextMeshPro>(includeInactive: true))
        {
            bool needsFix = false;

            if (tmpText.font == null)
            {
                tmpText.font = defaultFontAsset;
                needsFix = true;
            }

            if (needsFix)
            {
                EditorUtility.SetDirty(tmpText);
                fixedCount++;
            }
        }

        return fixedCount;
    }



    #region 通用工具方法
    /// <summary>
    /// 加载指定路径下的所有预制体
    /// </summary>
    private List<GameObject> LoadPrefabs(string[] paths)
    {
        return AssetDatabase.FindAssets("t:Prefab", paths)
            .Select(guid => AssetDatabase.GUIDToAssetPath(guid))
            .Select(path => AssetDatabase.LoadAssetAtPath<GameObject>(path))
            .Where(prefab => prefab != null)
            .ToList();
    }
    #endregion


    #region 特殊查找功能
    [FoldoutGroup("特殊查找")]
    [LabelText("同时有描边和阴影的预制列表")]
    public List<GameObject> outlineAndShadowViewList = new List<GameObject>();

    [FoldoutGroup("特殊查找")]
    [Button("查找同时有描边和阴影的预制", ButtonSizes.Large)]
    public void FindOutlineAndShadowView()
    {
        outlineAndShadowViewList.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找描边和阴影", prefab.name, i / (float)prefabs.Count);

                if (CheckPrefabOutlineAndShadow(prefab) != null)
                {
                    outlineAndShadowViewList.Add(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"查找完成，找到 {outlineAndShadowViewList.Count} 个同时有描边和阴影的预制体");
        }
    }

    private GameObject CheckPrefabOutlineAndShadow(GameObject gameObject)
    {
        var shadows = gameObject.GetComponents<Shadow>();
        bool hasOutline = false;
        bool hasShadow = false;

        foreach (var shadow in shadows)
        {
            if (shadow is Outline)
                hasOutline = true;
            else
                hasShadow = true;
        }

        if (hasOutline && hasShadow)
            return gameObject;

        // 递归检查子物体
        for (int i = 0; i < gameObject.transform.childCount; i++)
        {
            var result = CheckPrefabOutlineAndShadow(gameObject.transform.GetChild(i).gameObject);
            if (result != null)
                return result;
        }

        return null;
    }

    [FoldoutGroup("特殊查找")]
    [LabelText("有TextMeshPro的预制列表")]
    public List<GameObject> textMeshProViewList = new List<GameObject>();

    [FoldoutGroup("特殊查找")]
    [Button("查找有TextMeshPro的预制", ButtonSizes.Large)]
    public void FindTextMeshProView()
    {
        textMeshProViewList.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找TextMeshPro", prefab.name, i / (float)prefabs.Count);

                if (CheckTextContent(prefab) != null)
                {
                    textMeshProViewList.Add(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"查找完成，找到 {textMeshProViewList.Count} 个有TextMeshPro的预制体");
        }
    }

    // 检查文本内容
    public GameObject CheckTextContent(GameObject gameObject)
    {
        var text = gameObject.GetComponent<TextMeshProUGUI>();
        if (text != null)
        {
            text.UpdateFontAsset();
            return gameObject;
        }

        var count = gameObject.transform.childCount;
        for (int i = 0; i < count; i++)
        {
            var result = CheckTextContent(gameObject.transform.GetChild(i).gameObject);
            if (result != null)
            {
                return result;
            }
        }
        return null;

    }

    [FoldoutGroup("特殊查找")]
    [LabelText("有InputField的预制列表")]
    public List<GameObject> inputFieldViewList = new List<GameObject>();

    [FoldoutGroup("特殊查找")]
    [Button("查找有InputField的预制", ButtonSizes.Large)]
    public void FindInputFieldView()
    {
        inputFieldViewList.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找InputField", prefab.name, i / (float)prefabs.Count);

                if (CheckPrefabInput(prefab) != null)
                {
                    inputFieldViewList.Add(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"查找完成，找到 {inputFieldViewList.Count} 个有InputField的预制体");
        }
    }

    /// <summary>
    /// 检查是否包含InputField
    /// </summary>
    private GameObject CheckPrefabInput(GameObject gameObject)
    {
        var inputField = gameObject.GetComponent<InputField>();
        if (inputField != null)
            return gameObject;

        // 递归检查子物体
        for (int i = 0; i < gameObject.transform.childCount; i++)
        {
            var result = CheckPrefabInput(gameObject.transform.GetChild(i).gameObject);
            if (result != null)
                return result;
        }
        return null;
    }
    #endregion

    #region 字体间距分析
    [FoldoutGroup("字体间距分析")]
    [Button("分析字体间距使用情况", ButtonSizes.Large)]
    public void AnalyzeCharacterSpacing()
    {
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        var spacingDictionary = new Dictionary<float, int>();

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("分析字体间距", prefab.name, i / (float)prefabs.Count);

                var result = CheckCharacterSpacing(prefab);
                foreach (var item in result)
                {
                    if (spacingDictionary.ContainsKey(item.Key))
                        spacingDictionary[item.Key] += item.Value;
                    else
                        spacingDictionary[item.Key] = item.Value;
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            LogCharacterSpacingResults(spacingDictionary);
        }
    }

    private Dictionary<float, int> CheckCharacterSpacing(GameObject gameObject)
    {
        var dictionary = new Dictionary<float, int>();
        var text = gameObject.GetComponent<TextMeshProUGUI>();

        if (text != null && text.characterSpacing != 0)
        {
            if (dictionary.ContainsKey(text.characterSpacing))
                dictionary[text.characterSpacing]++;
            else
                dictionary[text.characterSpacing] = 1;
        }

        // 递归检查子物体
        for (int i = 0; i < gameObject.transform.childCount; i++)
        {
            var childDictionary = CheckCharacterSpacing(gameObject.transform.GetChild(i).gameObject);
            foreach (var item in childDictionary)
            {
                if (dictionary.ContainsKey(item.Key))
                    dictionary[item.Key] += item.Value;
                else
                    dictionary[item.Key] = item.Value;
            }
        }
        return dictionary;
    }

    private void LogCharacterSpacingResults(Dictionary<float, int> spacingDictionary)
    {
        Debug.Log("=== 字体间距使用情况分析 ===");
        foreach (var item in spacingDictionary.OrderBy(x => x.Key))
        {
            Debug.Log($"间距值: {item.Key}, 使用次数: {item.Value}");
        }
        Debug.Log($"总共发现 {spacingDictionary.Count} 种不同的字体间距设置");
    }
    #endregion

    #region 删除功能
    /// <summary>
    /// 获取预制Image丢失引用数量
    /// </summary>
    private int GetMissingImageReferences(GameObject prefab)
    {
        if (prefab == null) return 0;

        int count = 0;
        var queue = new Queue<Transform>();
        queue.Enqueue(prefab.transform);

        while (queue.Count > 0)
        {
            var trans = queue.Dequeue();
            for (int i = 0; i < trans.childCount; ++i)
            {
                queue.Enqueue(trans.GetChild(i));
            }

            var images = trans.GetComponents<Image>();
            foreach (var image in images)
            {
                if (image == null) continue;

                var serializedObject = new SerializedObject(image);
                var iterator = serializedObject.GetIterator();
                while (iterator.NextVisible(true))
                {
                    if (iterator.propertyType == SerializedPropertyType.ObjectReference)
                    {
                        if (iterator.objectReferenceValue == null && iterator.objectReferenceInstanceIDValue != 0)
                        {
                            count++;
                        }
                    }
                }
            }
        }
        return count;
    }

    [TabGroup("删除")]
    [Button("删除所有未启用的Outline", ButtonSizes.Large)]
    public void DeleteDisabledOutlines()
    {
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int totalCount = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("删除未启用的Outline", prefab.name, i / (float)prefabs.Count);

                var outlinesToDelete = new List<Outline>();
                foreach (var outline in prefab.GetComponentsInChildren<Outline>(includeInactive: true))
                {
                    if (!outline.enabled)
                    {
                        outlinesToDelete.Add(outline);
                    }
                }

                if (outlinesToDelete.Count > 0)
                {
                    foreach (var outline in outlinesToDelete)
                    {
                        DestroyImmediate(outline, true);
                        totalCount++;
                    }
                    EditorUtility.SetDirty(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"删除未启用的Outline组件数量: {totalCount}");
        }
    }

    [TabGroup("删除")]
    [Button("删除所有未启用的Shadow", ButtonSizes.Large)]
    public void DeleteDisabledShadows()
    {
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int totalCount = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("删除未启用的Shadow", prefab.name, i / (float)prefabs.Count);

                var shadowsToDelete = new List<Shadow>();
                foreach (var shadow in prefab.GetComponentsInChildren<Shadow>(includeInactive: true))
                {
                    if (!shadow.enabled)
                    {
                        shadowsToDelete.Add(shadow);
                    }
                }

                if (shadowsToDelete.Count > 0)
                {
                    foreach (var shadow in shadowsToDelete)
                    {
                        DestroyImmediate(shadow, true);
                        totalCount++;
                    }
                    EditorUtility.SetDirty(prefab);
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"删除未启用的Shadow组件数量: {totalCount}");
        }
    }

    [TabGroup("删除")]
    [Button("删除预制下所有旧描边和旧阴影", ButtonSizes.Large)]
    public void DeletePrefabAllOutlineAndShadow(GameObject prefab)
    {
        if (prefab == null)
        {
            Debug.LogWarning("预制体为空");
            return;
        }

        int outlineCount = DeleteAllOutlines(prefab);
        int shadowCount = DeleteAllShadows(prefab);

        if (outlineCount > 0 || shadowCount > 0)
        {
            EditorUtility.SetDirty(prefab);
            AssetDatabase.Refresh();
        }

        Debug.Log($"删除完成 - 描边数量: {outlineCount}, 阴影数量: {shadowCount}");
    }

    private int DeleteAllOutlines(GameObject prefab)
    {
        var outlines = prefab.GetComponentsInChildren<Outline>(includeInactive: true).ToList();
        foreach (var outline in outlines)
        {
            DestroyImmediate(outline, true);
        }
        return outlines.Count;
    }

    private int DeleteAllShadows(GameObject prefab)
    {
        var shadows = prefab.GetComponentsInChildren<Shadow>(includeInactive: true).ToList();
        foreach (var shadow in shadows)
        {
            DestroyImmediate(shadow, true);
        }
        return shadows.Count;
    }
    #endregion


    #region 材质创建和替换
    [TabGroup("替换")]
    [LabelText("材质创建模板")]
    public Material material;

    [TabGroup("替换")]
    [Button("替换当前物体文本材质", ButtonSizes.Large)]
    public void ReplaceMaterial()
    {
        if (prefab == null)
        {
            Debug.LogWarning("请先设置预制体");
            return;
        }

        if (material == null)
        {
            Debug.LogWarning("请先设置材质模板");
            return;
        }

        var text = prefab.GetComponent<TMP_Text>();
        if (text != null)
        {
            text.fontSharedMaterial = material;
            Debug.Log("替换当前文本材质完成");
        }
        else
        {
            Debug.LogWarning("预制体上没有找到TMP_Text组件");
        }
    }





    [PropertySpace(10)]
    [TabGroup("替换")]
    [Button("根据描边颜色列表批量创建描边材质", ButtonSizes.Large)]
    public void CreateOutlineMaterialList()
    {
        if (material == null)
        {
            Debug.LogError("请先设置材质创建模板");
            return;
        }

        int createdCount = 0;

        foreach (var colorData in outlineList_1)
        {
            if (CreateOutlineMaterial(colorData.color, OutLineSize.One))
                createdCount++;
        }

        foreach (var colorData in outlineList_2)
        {
            if (CreateOutlineMaterial(colorData.color, OutLineSize.Two))
                createdCount++;
        }

        foreach (var colorData in outlineList_3)
        {
            if (CreateOutlineMaterial(colorData.color, OutLineSize.Three))
                createdCount++;
        }

        Debug.Log($"批量创建描边材质完成，共创建 {createdCount} 个材质");
    }

    public bool CreateOutlineMaterial(Color color, OutLineSize outlineSize = OutLineSize.One)
    {
        var lineSize = (int)outlineSize;
        var materialName = $"TMP_Font_SDF_O{lineSize}_{ColorUtility.ToHtmlStringRGB(color)}.mat";
        var materialPath = FONT_MAT_PATH + materialName;

        // 检查材质是否已存在
        var existingMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (existingMaterial != null)
        {
            return false; // 材质已存在，不需要创建
        }

        // 复制模板材质
        var templatePath = AssetDatabase.GetAssetPath(material);
        if (string.IsNullOrEmpty(templatePath))
        {
            Debug.LogError("材质模板路径无效");
            return false;
        }

        if (!AssetDatabase.CopyAsset(templatePath, materialPath))
        {
            Debug.LogError($"复制材质失败: {materialPath}");
            return false;
        }

        // 设置材质属性
        var newMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (newMaterial != null)
        {
            newMaterial.SetColor("_UnderlayColor", color);
            SetOutlineSize(newMaterial, lineSize);
            EditorUtility.SetDirty(newMaterial);
            return true;
        }

        return false;
    }

    private void SetOutlineSize(Material mat, int outlineSize = 1)
    {
        if (mat == null) return;

        mat.SetFloat("_UnderlayOffsetX", 0f);
        mat.SetFloat("_UnderlayOffsetY", 0f);

        switch (outlineSize)
        {
            case 1:
                mat.SetFloat("_FaceDilate", 0f);
                mat.SetFloat("_UnderlayDilate", 0.52f);
                break;
            case 2:
                mat.SetFloat("_FaceDilate", 0.03f);
                mat.SetFloat("_UnderlayDilate", 0.6f);
                break;
            case 3:
                mat.SetFloat("_FaceDilate", 0.06f);
                mat.SetFloat("_UnderlayDilate", 0.6f);
                break;
        }
    }

    [PropertySpace(10)]
    [TabGroup("替换")]
    [Button("根据阴影颜色列表批量创建阴影材质", ButtonSizes.Large)]
    public void CreateShadowMaterialList()
    {
        if (material == null)
        {
            Debug.LogError("请先设置材质创建模板");
            return;
        }

        int createdCount = 0;

        foreach (var colorData in shadowList_1)
        {
            if (CreateShadowMaterial(colorData.color, OutLineSize.One))
                createdCount++;
        }

        foreach (var colorData in shadowList_2)
        {
            if (CreateShadowMaterial(colorData.color, OutLineSize.Two))
                createdCount++;
        }

        foreach (var colorData in shadowList_3)
        {
            if (CreateShadowMaterial(colorData.color, OutLineSize.Three))
                createdCount++;
        }

        Debug.Log($"批量创建阴影材质完成，共创建 {createdCount} 个材质");
    }

    public bool CreateShadowMaterial(Color color, OutLineSize shadowSize = OutLineSize.One)
    {
        var size = (int)shadowSize;
        var materialName = $"TMP_Font_SDF_S{size}_{ColorUtility.ToHtmlStringRGB(color)}.mat";
        var materialPath = FONT_MAT_PATH + materialName;

        // 检查材质是否已存在
        var existingMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (existingMaterial != null)
        {
            return false; // 材质已存在，不需要创建
        }

        // 复制模板材质
        var templatePath = AssetDatabase.GetAssetPath(material);
        if (string.IsNullOrEmpty(templatePath))
        {
            Debug.LogError("材质模板路径无效");
            return false;
        }

        if (!AssetDatabase.CopyAsset(templatePath, materialPath))
        {
            Debug.LogError($"复制材质失败: {materialPath}");
            return false;
        }

        // 设置材质属性
        var newMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (newMaterial != null)
        {
            newMaterial.SetColor("_UnderlayColor", color);
            SetShadowSize(newMaterial, size);
            EditorUtility.SetDirty(newMaterial);
            return true;
        }

        return false;
    }

    private void SetShadowSize(Material mat, int shadowSize = 1)
    {
        if (mat == null) return;

        mat.SetFloat("_UnderlayDilate", 0f);
        mat.SetFloat("_FaceDilate", 0.03f);

        switch (shadowSize)
        {
            case 1:
                mat.SetFloat("_UnderlayOffsetX", 0.3f);
                mat.SetFloat("_UnderlayOffsetY", -0.3f);
                break;
            case 2:
                mat.SetFloat("_UnderlayOffsetX", 0.6f);
                mat.SetFloat("_UnderlayOffsetY", -0.6f);
                break;
            case 3:
                mat.SetFloat("_UnderlayOffsetX", 1f);
                mat.SetFloat("_UnderlayOffsetY", -1f);
                break;
        }
    }
    #endregion


    [TabGroup("替换")]
    [Button("替换指定对象文本材质", ButtonSizes.Large)]
    public void ReplaceTMPMaterial(GameObject gameObject)
    {
        if (gameObject == null)
        {
            Debug.LogWarning("游戏对象为空");
            return;
        }

        if (material == null)
        {
            Debug.LogWarning("材质为空");
            return;
        }

        var text = gameObject.GetComponent<TMP_Text>();
        if (text != null)
        {
            text.fontSharedMaterial = material;
            Debug.Log($"替换文本材质完成: {gameObject.name}");
        }
        else
        {
            Debug.LogWarning($"对象 {gameObject.name} 上没有找到TMP_Text组件");
        }
    }

    #region 批量文本转换
    [PropertyOrder(1000)]
    [PropertySpace(10)]
    [TabGroup("替换")]
    [Button("替换所有View预制的Text为TMP", ButtonSizes.Large), GUIColor(0f, 0.74f, 1f)]
    public void ReplaceAllViewTexts()
    {
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });
        int totalConverted = 0;
        int totalPrefabs = 0;

        try
        {
            for (var i = 0; i < prefabs.Count; i++)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("批量替换Text为TMP",
                    $"处理预制体: {prefab.name} ({i + 1}/{prefabs.Count})",
                    (float)i / prefabs.Count);

                var convertedCount = ProcessPrefabTextConversion(prefab);
                if (convertedCount > 0)
                {
                    totalConverted += convertedCount;
                    totalPrefabs++;
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"批量替换过程中发生错误: {ex.Message}\n{ex.StackTrace}");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.Refresh();
            Debug.Log($"批量替换完成 - 共处理 {totalPrefabs} 个预制体，转换 {totalConverted} 个文本组件");
        }
    }

    /// <summary>
    /// 处理单个预制体的文本转换
    /// </summary>
    private int ProcessPrefabTextConversion(GameObject prefab)
    {
        try
        {
            var prefabInstance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            if (prefabInstance == null)
            {
                Debug.LogWarning($"无法实例化预制体: {prefab.name}");
                return 0;
            }

            var convertedCount = ReplaceAllTextToTMP(prefabInstance);

            if (convertedCount > 0)
            {
                PrefabUtility.ApplyPrefabInstance(prefabInstance, InteractionMode.AutomatedAction);
            }

            // 清理实例
            if (prefabInstance != null)
            {
                DestroyImmediate(prefabInstance);
            }

            return convertedCount;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"处理预制体 {prefab.name} 时发生错误: {ex.Message}");
            return 0;
        }
    }

    [PropertyOrder(100)]
    [PropertySpace(10)]
    [TabGroup("替换")]
    [Button("把所有子物体Text替换为TMP", ButtonSizes.Large), GUIColor(0f, 0.74f, 1f)]
    public int ReplaceAllTextToTMP(GameObject view)
    {
        if (view == null)
        {
            Debug.LogWarning("视图对象为空");
            return 0;
        }

        int convertedCount = 0;
        var textComponents = view.GetComponentsInChildren<Text>(includeInactive: true);

        foreach (var text in textComponents)
        {
            if (ReplaceTextToTMP(text.gameObject))
            {
                convertedCount++;
            }
        }

        Debug.Log($"{view.name} 替换完成: {convertedCount} 个文本组件");
        return convertedCount;
    }

    #region TMP属性调整工具
    [TabGroup("替换")]
    [Button("TMP字符间距修改")]
    public bool ReplaceTMPCharacterSpacing(GameObject gameObject)
    {
        if (gameObject == null) return false;

        var tmp = gameObject.GetComponent<TextMeshProUGUI>();
        if (tmp != null && tmp.characterSpacing == 60f && tmp.text.Length == 4)
        {
            tmp.characterSpacing = 0f;
            EditorUtility.SetDirty(tmp);
            return true;
        }
        return false;
    }

    [TabGroup("替换")]
    [Button("启用渐变的字体把颜色都改为白色")]
    public bool ReplaceTMPColorByGradient(GameObject gameObject)
    {
        if (gameObject == null) return false;

        var tmp = gameObject.GetComponent<TextMeshProUGUI>();
        if (tmp != null && tmp.enableVertexGradient)
        {
            tmp.color = Color.white;
            EditorUtility.SetDirty(tmp);
            return true;
        }
        return false;
    }

    [TabGroup("替换")]
    [Button("移除TMP的加粗效果")]
    public bool RemoveTMPBoldStyle(GameObject gameObject)
    {
        if (gameObject == null) return false;

        var tmp = gameObject.GetComponent<TextMeshProUGUI>();
        if (tmp != null && tmp.fontStyle.HasFlag(FontStyles.Bold))
        {
            tmp.fontStyle = tmp.fontStyle & (~FontStyles.Bold);
            EditorUtility.SetDirty(tmp);
            return true;
        }
        return false;
    }
    #endregion

    #region 核心文本转换
    /// <summary>
    /// 文本转换配置数据
    /// </summary>
    private class TextConversionData
    {
        public string text = "";
        public int fontSize = 20;
        public TextAnchor textAnchor = TextAnchor.MiddleCenter;
        public bool isRaycastTarget = false;
        public FontStyle fontStyle = FontStyle.Normal;
        public HorizontalWrapMode horizontalOverflow = HorizontalWrapMode.Wrap;
        public VerticalWrapMode verticalOverflow = VerticalWrapMode.Overflow;
        public bool richText = true;
        public Color textColor = Color.white;
        public float textWidth = -1f;
        public float lineSpacing = 2f;
        public bool isExpand = false;
        public float characterSpacing = 0f;
    }

    [TabGroup("替换")]
    [Button("替换游戏对象的Text为TMP", ButtonSizes.Large)]
    public bool ReplaceTextToTMP(GameObject gameObject)
    {
        if (gameObject == null) return false;

        // 清理旧的UI组件
        CleanupOldUIComponents(gameObject);

        // 获取Text组件并验证
        var text = gameObject.GetComponent<Text>();
        if (!IsValidTextForConversion(text)) return false;

        // 提取文本配置数据
        var conversionData = ExtractTextData(text, gameObject);

        // 销毁原Text组件
        DestroyImmediate(text, true);

        // 创建并配置TMP组件
        var tmpText = CreateAndConfigureTMPText(gameObject, conversionData);

        // 应用特殊效果
        ApplySpecialEffects(gameObject, tmpText, conversionData);

        return true;
    }

    /// <summary>
    /// 清理旧的UI组件
    /// </summary>
    private void CleanupOldUIComponents(GameObject gameObject)
    {
        var componentsToRemove = new System.Type[]
        {
            typeof(UIGrayscale),
            typeof(UIMaterialEffect),
            typeof(UIVariableBindText),
            typeof(UILetterSpacing)
        };

        foreach (var componentType in componentsToRemove)
        {
            var component = gameObject.GetComponent(componentType);
            if (component != null)
            {
                DestroyImmediate(component, true);
            }
        }
    }

    /// <summary>
    /// 验证Text组件是否适合转换
    /// </summary>
    private bool IsValidTextForConversion(Text text)
    {
        if (text == null) return false;

        if (text.font != null &&
            text.font.name != FONT_NAME_MAIN &&
            text.font.name != FONT_NAME_LIGHT)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 提取Text组件的数据
    /// </summary>
    private TextConversionData ExtractTextData(Text text, GameObject gameObject)
    {
        var data = new TextConversionData();

        if (text != null)
        {
            // 基本文本属性
            data.text = text.text;
            data.textAnchor = text.alignment;
            data.fontSize = text.fontSize;
            data.isRaycastTarget = text.raycastTarget;
            data.horizontalOverflow = text.horizontalOverflow;
            data.verticalOverflow = text.verticalOverflow;
            data.richText = text.supportRichText;
            data.textColor = text.color;
            data.fontStyle = text.fontStyle;

            // 处理特殊的EmojiText
            var emojiText = text as EmojiText;
            if (emojiText != null)
            {
                data.textWidth = emojiText.workingRowWidth;
            }

            // 转换行间距
            data.lineSpacing = ConvertLineSpacing(text.lineSpacing);
        }

        // 处理UITextExpand组件
        ProcessUITextExpand(gameObject, data);

        return data;
    }

    /// <summary>
    /// 转换行间距值
    /// </summary>
    private float ConvertLineSpacing(float originalSpacing)
    {
        if (originalSpacing == 1f) return 2f;
        if (originalSpacing == 1.1f) return 16f;
        if (originalSpacing == 1.2f) return 24f;
        return (originalSpacing - 1) * 140f;
    }

    /// <summary>
    /// 处理UITextExpand组件
    /// </summary>
    private void ProcessUITextExpand(GameObject gameObject, TextConversionData data)
    {
        var uiTextExpand = gameObject.GetComponent<UITextExpand>();
        if (uiTextExpand != null && uiTextExpand.enabled)
        {
            if (uiTextExpand.fitWidth == -1)
            {
                data.isExpand = true;
            }
            else if (uiTextExpand.fitWidth == 60)
            {
                data.characterSpacing = 0f;
            }
            else
            {
                data.characterSpacing = uiTextExpand.fitWidth;
            }

            DestroyImmediate(uiTextExpand, true);
        }
    }

    /// <summary>
    /// 创建并配置TextMeshPro组件
    /// </summary>
    private TextMeshProUGUI CreateAndConfigureTMPText(GameObject gameObject, TextConversionData data)
    {
        var tmpText = gameObject.GetOrAddComponent<TextMeshProUGUI>();
        tmpText.UpdateFontAsset();

        // 设置基本属性
        tmpText.fontSize = data.fontSize;
        tmpText.text = data.text;
        tmpText.raycastTarget = data.isRaycastTarget;
        tmpText.richText = data.richText;
        tmpText.color = data.textColor;
        tmpText.lineSpacing = data.lineSpacing;
        tmpText.characterSpacing = data.characterSpacing;

        // 设置字体样式
        tmpText.fontStyle = ConvertFontStyle(data.fontStyle);

        // 设置对齐方式
        tmpText.alignment = ConvertTextAlignment(data.textAnchor, data.isExpand);

        // 设置溢出模式
        ConfigureTextOverflow(tmpText, data);

        // 调整RectTransform大小
        AdjustRectTransformSize(gameObject, tmpText, data);

        tmpText.ForceMeshUpdate();
        return tmpText;
    }

    /// <summary>
    /// 转换字体样式
    /// </summary>
    private FontStyles ConvertFontStyle(FontStyle fontStyle)
    {
        switch (fontStyle)
        {
            case FontStyle.Bold:
                return FontStyles.Bold;
            case FontStyle.Italic:
                return FontStyles.Italic;
            case FontStyle.BoldAndItalic:
                return FontStyles.Bold | FontStyles.Italic;
            default:
                return FontStyles.Normal;
        }
    }

    /// <summary>
    /// 转换文本对齐方式
    /// </summary>
    private TextAlignmentOptions ConvertTextAlignment(TextAnchor textAnchor, bool isExpand)
    {
        TextAlignmentOptions alignment;

        switch (textAnchor)
        {
            case TextAnchor.UpperLeft:
                alignment = TextAlignmentOptions.TopLeft;
                break;
            case TextAnchor.UpperCenter:
                alignment = TextAlignmentOptions.Top;
                break;
            case TextAnchor.UpperRight:
                alignment = TextAlignmentOptions.TopRight;
                break;
            case TextAnchor.MiddleLeft:
                alignment = TextAlignmentOptions.Left;
                break;
            case TextAnchor.MiddleCenter:
                alignment = TextAlignmentOptions.Center;
                break;
            case TextAnchor.MiddleRight:
                alignment = TextAlignmentOptions.Right;
                break;
            case TextAnchor.LowerLeft:
                alignment = TextAlignmentOptions.BottomLeft;
                break;
            case TextAnchor.LowerCenter:
                alignment = TextAlignmentOptions.Bottom;
                break;
            case TextAnchor.LowerRight:
                alignment = TextAlignmentOptions.BottomRight;
                break;
            default:
                alignment = TextAlignmentOptions.Center;
                break;
        }

        // 处理扩展对齐（flush对齐）
        if (isExpand)
        {
            var alignmentValue = (int)alignment >> 8;
            alignmentValue = alignmentValue << 8;
            alignmentValue = alignmentValue | 16; // 添加flush对齐标记
            alignment = (TextAlignmentOptions)alignmentValue;
        }

        return alignment;
    }

    /// <summary>
    /// 配置文本溢出模式
    /// </summary>
    private void ConfigureTextOverflow(TextMeshProUGUI tmpText, TextConversionData data)
    {
        // 设置自动换行
        tmpText.enableWordWrapping = data.horizontalOverflow == HorizontalWrapMode.Wrap;

        // 设置溢出模式
        if (data.horizontalOverflow == HorizontalWrapMode.Overflow)
        {
            tmpText.overflowMode = TextOverflowModes.Overflow;
        }
        else
        {
            tmpText.overflowMode = data.verticalOverflow == VerticalWrapMode.Truncate
                ? TextOverflowModes.Truncate
                : TextOverflowModes.Overflow;
        }
    }

    /// <summary>
    /// 调整RectTransform大小
    /// </summary>
    private void AdjustRectTransformSize(GameObject gameObject, TextMeshProUGUI tmpText, TextConversionData data)
    {
        var rectTransform = gameObject.GetComponent<RectTransform>();
        if (rectTransform == null) return;

        var bounds = tmpText.GetPreferredValues("1", 9999, 0);
        var currentSize = rectTransform.sizeDelta;

        if (data.textWidth > 0)
        {
            // 使用指定宽度
            var height = currentSize.y;
            if (bounds.y > height)
            {
                height += Mathf.Ceil(bounds.y);
            }
            rectTransform.sizeDelta = new Vector2(data.textWidth, height);
        }
        else
        {
            // 自动调整高度
            var height = currentSize.y;
            if (bounds.y > height)
            {
                height = Mathf.Ceil(bounds.y);
            }
            rectTransform.sizeDelta = new Vector2(currentSize.x, height);
        }
    }

    /// <summary>
    /// 应用特殊效果（渐变、描边、阴影）
    /// </summary>
    private void ApplySpecialEffects(GameObject gameObject, TextMeshProUGUI tmpText, TextConversionData data)
    {
        // 应用渐变效果
        ApplyGradientEffect(gameObject, tmpText, data);

        // 应用描边或阴影效果（描边优先）
        if (!ApplyOutlineEffect(gameObject, tmpText))
        {
            ApplyShadowEffect(gameObject, tmpText);
        }
    }

    /// <summary>
    /// 应用渐变效果
    /// </summary>
    private void ApplyGradientEffect(GameObject gameObject, TextMeshProUGUI tmpText, TextConversionData data)
    {
        var gradient = gameObject.GetComponent<Nirvana.UIGradient>();
        if (gradient != null)
        {
            var vertexGradient = new VertexGradient(gradient.Color1, gradient.Color1, gradient.Color2, gradient.Color2);
            tmpText.enableVertexGradient = true;
            tmpText.colorGradient = vertexGradient;
            tmpText.text = data.text;
            tmpText.color = Color.white;
            DestroyImmediate(gradient, true);
        }
    }

    /// <summary>
    /// 应用描边效果
    /// </summary>
    private bool ApplyOutlineEffect(GameObject gameObject, TextMeshProUGUI tmpText)
    {
        var outline = gameObject.GetComponent<Outline>();
        if (outline == null) return false;

        var outlineSize = GetOutlineSize(outline.effectDistance.x);
        var materialName = $"TMP_Font_SDF_O{(int)outlineSize}_{ColorUtility.ToHtmlStringRGB(outline.effectColor)}.mat";
        var materialPath = FONT_MAT_PATH + materialName;

        var outlineMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (outlineMaterial != null)
        {
            tmpText.fontSharedMaterial = outlineMaterial;
            DestroyImmediate(outline, true);
            return true;
        }
        else
        {
            Debug.LogError($"描边材质未找到：{materialName}");
            return false;
        }
    }

    /// <summary>
    /// 应用阴影效果
    /// </summary>
    private bool ApplyShadowEffect(GameObject gameObject, TextMeshProUGUI tmpText)
    {
        var shadow = gameObject.GetComponent<Shadow>();
        if (shadow == null) return false;

        var shadowSize = GetShadowSize(shadow.effectDistance.x);
        var materialName = $"TMP_Font_SDF_S{(int)shadowSize}_{ColorUtility.ToHtmlStringRGB(shadow.effectColor)}.mat";
        var materialPath = FONT_MAT_PATH + materialName;

        var shadowMaterial = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
        if (shadowMaterial != null)
        {
            tmpText.fontSharedMaterial = shadowMaterial;
            DestroyImmediate(shadow, true);
            return true;
        }
        else
        {
            Debug.LogError($"阴影材质未找到：{materialName}");
            return false;
        }
    }
    #endregion
    #endregion

    #region 美术字处理
    [TabGroup("美术字")]
    [LabelText("美术字View列表")]
    public List<GameObject> artFontViewList = new List<GameObject>();

    [TabGroup("美术字")]
    [Button("查找使用美术字的界面", ButtonSizes.Large)]
    public void FindAllOldView()
    {
        artFontViewList.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("查找美术字", prefab.name, i / (float)prefabs.Count);

                foreach (var text in prefab.GetComponentsInChildren<Text>(includeInactive: true))
                {
                    if (text.font != null && text.font.name != FONT_NAME_MAIN && text.font.name != FONT_NAME_LIGHT)
                    {
                        artFontViewList.Add(prefab);
                        break;
                    }
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"查找完成，找到 {artFontViewList.Count} 个使用美术字的界面");
        }
    }

    [TabGroup("美术字")]
    [Button("把美术字替换成通用字体", ButtonSizes.Large)]
    public void ReplaceFontToCommon(string fontName)
    {
        if (string.IsNullOrEmpty(fontName))
        {
            Debug.LogWarning("字体名称为空");
            return;
        }

        artFontViewList.Clear();
        var prefabs = LoadPrefabs(new[] { UI_VIEW_PATH });

        try
        {
            for (var i = 0; i < prefabs.Count; ++i)
            {
                var prefab = prefabs[i];
                EditorUtility.DisplayProgressBar("替换美术字", prefab.name, i / (float)prefabs.Count);

                foreach (var text in prefab.GetComponentsInChildren<Text>(includeInactive: true))
                {
                    if (text.font != null && text.font.name == fontName)
                    {
                        artFontViewList.Add(prefab);
                        break;
                    }
                }
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"替换完成，找到 {artFontViewList.Count} 个使用指定字体的界面");
        }
    }
    #endregion
}
