﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;

namespace AssetsCheck
{
    public enum CheckerType
    {
        // UI相关
        UITexture = 0,                                  // UI纹理
        UIAtlas,                                        // UI图集
        UITextureOpacity,                               // UI纹理不透明度
        UIBytes,                                        // UIBytes文件
        UIBundleDepend,                                 // UI的Bundle依赖
        UICommonImageRef,                               // UI的commonImage引用
        UIIconImageRef,                                 // UI图标类引用

        // 组件类
        InvalidComponent,                               // 无效组件
        UI3DDisplay,                                    // UI上显示模型的脚本
        LoadRawImage,                                   // 加载插画类

        // 特效
        UIEffectTexture,                                // UI特效纹理
        UIEffectAttach,                                 // UI特效有没使用GameobjectAttach
        UnActiveEffectChecker,                          // 没有激活的特效
        UIEffectErrorBundleNameChecker,                 // UI特效错误bundlename检查
        EffectDependUIRes,                              // 特效依赖于ui资源
        TrailRendererController,                        // 拖尾特效控制器
        ParticleSystemReference,                        // 特效 shape、render引用丢失
        EffectQualityControlActiveChecker,              // 粒子特效控制器检测
        EffectQualityControlActiveMissingCheck,         // 检查品质控制组件引用丢失
        EffectQualityControlActiveViolateQuoteCheck,    // 检测特效预制体上的QualityControlActive绑定资源异常（绑定丢失 或 直接绑定了其他预制体）
        EffectQualityControlActiveBindingOptimize,      // 优化QualityControlActive的绑定规则
        EffectQualityControlActiveMultiCheck,           // 特效预制体挂载了多个QualityActiveControl组件，或者是不能挂载该组件（UI特效、模型不挂）
        EffectUnuseMeshOrMaterialRefrenced,             // 特效上无用mesh、material引用资源

        // 材质球
        StandMaterial,                                  // 标准材质球
        UnuseKeywordValueCleanChecker,                  // 清理材质球无效资源引用

        // 角色相关
        ActorModel,                                     // 角色模型
        ActorReceivedShadow,                            // 角色接收阴影
        ActorGameObjectAttach,                          // 角色上使用特效时有没有用GameobjectAttach
        GameObjectAttachResourceMissing,                // 角色上使用的GameobjectAttach引用丢失
        ActorRenderer,                                  // 角色渲染
        ActorRendererMissingMaterials,                  // 角色渲染材质丢失
        ActorMissingConponentChecker,                   // 模型组件丢失
        AssetActorAttachment,                           // 模型ActorAttachment挂点绑定数据是否有问题

        // 场景相关
        SceneEdit,                                      // 场景编辑
        SceneModel,                                     // 场景相关模型
        SceneMeshCollider,                              // 网格碰撞器
        SceneCastShadow,                                // 投射阴影
        SceneMaterial,                                  // 场景材质球

        // 其他
        UnUsedAsset,                                    // 没用被使用的资源
        UnUsedMaterial,                                 // 没有用的材质
        LuaConfigMemory,                                // Lua配置占用内存
        AssetBundleVariant,                             // AssetBundle别名
        AssetBundleLoopDepend,                          // AssetBundle循环依赖
        AssetAcrossRefrenced,                           // 模型资源跨引用

        // CG
        CleanUpCGPlayableBind,                          // 清除CG PlayableDirector的Scenes Binding冗余

        ModelRoundedAnalysis,                           // 模型引用规范检测
    }

    class AssetsCheckConfig
    {
        // 排除列表文件夹
        public static string ExcludeDir = Path.Combine(Application.dataPath, "../AssetsCheck/Exclude");
        // 输出文件夹
        public static string OutputDir = Path.Combine(Application.dataPath, "../AssetsCheck");
    }
}
   