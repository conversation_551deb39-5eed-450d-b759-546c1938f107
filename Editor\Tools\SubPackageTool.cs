﻿using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using Build;

public class SubPackageTool : EditorWindow
{
    private static List<long> packageList = new List<long>();

    [MenuItem("自定义工具/分包工具/计算分包大小")]
    public static void OpenSubPackageWindow()
    {
        var window = EditorWindow.GetWindow<SubPackageTool>(false, "计算分包大小");
        window.minSize = new Vector2(600, 400);
    }

    private void OnGUI()
    {
        if (GUILayout.Button("刷新"))
        {
            FlushPackage();
        }

        if (packageList.Count > 0)
        {
            GUILayout.BeginVertical();
            for (int i = 0; i < packageList.Count; ++i)
            {
                GUILayout.Label(string.Format("分包{0}: {1}MB", i, Mathf.FloorToInt(packageList[i] / 1024 / 1024)));
            }
        }
    }

    private static void FlushPackage()
    {
        packageList.Clear();

        var manifest = BuilderConfig.GetManifestInfo(BuildPlatType.AndroidDev);
        string[] allbundles = manifest.GetAllAssetBundles();
        HashSet<string> allBundlesHashSet = new HashSet<string>();
        for (int i = 0; i < allbundles.Length; ++i)
        {
            allBundlesHashSet.Add(allbundles[i]);
        }

        // 整理出已经入包的资源
        HashSet<string> packageSet = AssetBundleInstaller.GetFilterBundleSet(BuildPlatType.AndroidDev, manifest, InstallBundleSize.sizeS);
        List<string> package = new List<string>(packageSet);
        GetDependAssetBundles(package, manifest);
        HashSet<string> filters = new HashSet<string>(package);

        string rootPath = Path.Combine(Application.dataPath, "Game/Deploy/Install");
        string bundleDir = BuilderConfig.GetAssetBundlePath(BuildPlatType.AndroidDev);

        List<string> bundleList = new List<string>();
        foreach (string d in Directory.GetFileSystemEntries(rootPath))
        {
            if (File.Exists(d))
            {
                string name = Path.GetFileNameWithoutExtension(d);
                if (name.StartsWith("SubPackage_") && Path.GetExtension(d) != ".meta")
                {
                    
                    string[] splits = name.Split('_');
                    int index = int.Parse(splits[1]);

                    long totalSize = 0;
                    string[] lines = File.ReadAllLines(d);
                    for (int i = 0; i < lines.Length; i++)
                    {
                        string line = lines[i];
                        if (string.IsNullOrEmpty(line))
                        {
                            continue;
                        }

                        if (line.IndexOf("//") >= 0)
                        {
                            continue;
                        }

                        bundleList.Clear();
                        if (line.EndsWith("/"))
                        {
                            SearchAllBundle(line, allbundles, bundleList);
                        }
                        else
                        {
                            bundleList.Add(line);
                        }

                        GetDependAssetBundles(bundleList, manifest);

                        foreach (var bundleName in bundleList)
                        {
                            //剔除掉已经入包的资源
                            if (filters.Contains(bundleName))
                            {
                                continue;
                            }

                            //剔除不存在的资源
                            if (!allBundlesHashSet.Contains(bundleName))
                            {
                                continue;
                            }

                            filters.Add(bundleName);

                            string path = Path.Combine(bundleDir, bundleName);
                            if (File.Exists(path))
                            {
                                FileInfo fileInfo = new FileInfo(path);
                                totalSize += fileInfo.Length;
                            }
                        }
                    }

                    packageList.Add(totalSize);
                }
            }
        }
    }

    private static void SearchAllBundle(string param, string[] allbundles, List<string> bundleList)
    {
        foreach (var bundle in allbundles)
        {
            if (bundle.StartsWith(param))
            {
                bundleList.Add(bundle);
            }
        }
    }

    private static void GetDependAssetBundles(List<string> bundleList, ManifestInfo manifest)
    {
        HashSet<string> dependSet = new HashSet<string>();
        foreach (var item in bundleList)
        {
            List<string> depends = manifest.GetAllDependencies(item);
            for (int i = 0; i < depends.Count; i++)
            {
                string depend = depends[i];
                dependSet.Add(depend);
            }
        }

        foreach (var depend in dependSet)
        {
            bundleList.Add(depend);
        }
    }
}
