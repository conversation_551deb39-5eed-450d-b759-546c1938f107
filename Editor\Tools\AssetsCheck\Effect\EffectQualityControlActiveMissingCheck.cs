﻿using UnityEngine;
using UnityEditor;
using System.Text;
using System.Collections.Generic;
using Nirvana;

namespace AssetsCheck
{
    class EffectQualityControlActiveMissingCheck : BaseChecker
    {
        private string[] checkRoots = {
            "Assets/Game/Actor",
            "Assets/Game/Model",
            "Assets/Game/Effects/Prefab",
        };

        private string[] filters = {
            "Assets/Game/Effects/Prefab/UI",
        };

        public override string GetErrorDesc()
        {
            return "检测特效预制体是否挂载QualityControlActive组件";
        }

        bool isFilter(string path)
        {
            foreach (string filter in filters)
            {
                if (path.StartsWith(filter))
                {
                    return true;
                }
            }
            return false;
        }

        protected override void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);

            try
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guids[i]).Replace("\\", "/");
                    if (isFilter(assetPath))
                    {
                        continue;
                    }
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    if (prefab != null)
                    {
                        if (prefab.GetComponent<QualityControlActive>() == null && prefab.GetComponent<ActorRender>() == null)
                        {
                            ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
                            TrailRenderer[] trailRenderers = prefab.GetComponentsInChildren<TrailRenderer>();
                            if (particleSystems != null && particleSystems.Length > 0 || trailRenderers != null && trailRenderers.Length > 0)
                            {
                                CheckItem checkItem = new CheckItem();
                                checkItem.asset = assetPath;
                                this.outputList.Add(checkItem);
                            }
                        }
                    }

                    EditorUtility.DisplayProgressBar("正在批量检查预制体", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
        }

        protected override void OnFix(string[] lines)
        {
            bool isComeFromCheck = false;
            string[] assets;
            if (lines == null || lines.Length == 0)
            {
                List<string> assetList = new List<string>();
                string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);
                foreach (string guid in guids)
                {
                    string asset = AssetDatabase.GUIDToAssetPath(guid);
                    assetList.Add(asset);
                }
                assets = assetList.ToArray();
                isComeFromCheck = true;
            }
            else
            {
                assets = lines;
            }

            int count = assets.Length;
            try
            {
                for (int i = 0; i < assets.Length; i++)
                {
                    string assetPath = assets[i];
                    if (isFilter(assetPath))
                    {
                        continue;
                    }
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    bool isPrefabDirty = false;
                    if (prefab != null)
                    {
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        ParticleSystem[] particleSystems = gameObject.GetComponentsInChildren<ParticleSystem>();
                        if (isComeFromCheck)
                        {
                            QualityControlActive qualityControlActive = gameObject.AddComponent<QualityControlActive>();
                            qualityControlActive.AutoFetch(false);
                            isPrefabDirty = true;
                        }
                        else if (gameObject.GetComponent<QualityControlActive>() == null && prefab.GetComponent<ActorRender>() == null)
                        {
                            TrailRenderer[] trailRenderers = prefab.GetComponentsInChildren<TrailRenderer>();
                            if (particleSystems != null && particleSystems.Length > 0 || trailRenderers != null && trailRenderers.Length > 0)
                            {
                                QualityControlActive qualityControlActive = gameObject.AddComponent<QualityControlActive>();
                                qualityControlActive.AutoFetch(false);
                                isPrefabDirty = true;
                            }
                        }

                        if (isPrefabDirty)
                        {
                            PrefabUtility.SaveAsPrefabAsset(gameObject, assetPath);
                        }

                        GameObject.DestroyImmediate(gameObject);
                    }

                    EditorUtility.DisplayProgressBar("正在批量修改预制体", string.Format("{0}/{1}", i, count), (float)i / (float)count);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(string.Format("{0}", asset));

                return builder;
            }
        }
    }
}
