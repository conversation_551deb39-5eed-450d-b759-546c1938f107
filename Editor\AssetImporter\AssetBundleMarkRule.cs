﻿using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text.RegularExpressions;
using Build;

public static class AssetBundleMarkRule
{
    public static readonly string BaseDir = "Assets/Game";
    public static readonly string UIDir = BaseDir + "/UIs";
    private static readonly string UIImagesDir = UIDir + "/Images";
    private static readonly string RawImageDir = UIDir + "/RawImages";
    private static readonly string FontDir = UIDir + "/Fonts";
    public static readonly string ArtFontDir = UIDir + "/TMP/ArtFont";
    public static readonly string FontMatDir = UIDir + "/TMP/FontMat";
    public static readonly string TMPStyleSheetDir = UIDir + "/TMP/StyleSheets";
    private static readonly string TTFDir = UIDir + "/TTF";
    private static readonly string WidgetDir = UIDir + "/Widgets";
    private static readonly string MainUIDir = UIDir + "/View/main_ui/Images/";
    private static readonly string EmojiDir = UIDir + "/Emoji";
    private static readonly string HUDProgrammeDir = UIDir + "/HUDProgramme";
    private static readonly string FunctionSkill = "/Icon/FunctionSkill";
    private static readonly string MainSkill = "/Icon/MainSkill";
    public static readonly string SpriteAtlasFileSuffix = ".spriteatlas";
    public static readonly string RenderTextureFileSuffix = ".renderTexture";

    public static readonly string AudioDir = BaseDir + "/Audios";
    public static readonly string EnvironmentsDir = BaseDir + "/Environments";
    public static readonly string ScenesDir = BaseDir + "/Scenes";

    public static readonly string ShaderDir = BaseDir + "/Shaders";
    public static readonly string ActorsDir = BaseDir + "/Actors";
    public static readonly string PupilTexturesDir = BaseDir + "/Actors/Shared/Textures/PupilTextures";
    public static readonly string FaceDecalTexturesDir = BaseDir + "/Actors/Shared/Textures/FaceDecalTextures";
    public static readonly string ModelDir = BaseDir + "/Model";
    public static readonly string CharacterDir = ModelDir + "/Character";
    private static readonly string CharacterBodyDir = "/Body";
    private static readonly string CharacterFaceDir = "/Face";
    private static readonly string CharacterHairDir = "/Hair";
    private static readonly string CharacterRealmDir = "/Realm";
    public static readonly string WeaponDir = ModelDir + "/Weapon";
    private static readonly string LuaBundleDir = BaseDir + "/LuaBundle";
    private static readonly string LuaBundleJitDir = BaseDir + "/LuaBundleJit";
    public static readonly string UrpEngineMonoAsset = BaseDir + "/Misc/UrpSettings";

    private static readonly string EffectPrefabDir = BaseDir + "/Effects/Prefab";
    private static readonly string ScreenEffectDir = BaseDir + "/Effects/Screen";
    private static readonly string PostEffectsDir = BaseDir + "/Scenes/PostEffects";
    private static readonly string EnvironmentsSharedTextureDir = EnvironmentsDir + "/Shared/Texture";

    private static readonly string IGNORE_MARK = "ignore_mark";
    private static readonly Regex LogicRegex = new Regex(@".+logic\d+\.unity");
    private static readonly Regex SceneNameRegex = new Regex(@".+_");

    private static char[] PathSeperator = new char[]
    {
        '/',
        '\\',
    };

    private static string GetRelativeDirPath(string path, string basePath)
    {
        var relativePath = path.Substring(basePath.Length + 1);
        return Path.GetDirectoryName(relativePath).ToLower().Replace("\\", "/");
    }

    private static string GetReleativeFilePath(string path, string basePath)
    {
        return path.Substring(basePath.Length + 1).Replace("\\", "/").ToLower();
    }

    public static string GetCharacterBundleName(string mainDir, string assetPath, bool is_dye_item = false)
    {
        string str = is_dye_item ? "model/{0}/{1}_dye" : "model/{0}/{1}_prefab";
        var paths = assetPath.Split(PathSeperator);
        var parentDirName = paths[paths.Length - 2];
        if (string.CompareOrdinal(parentDirName, mainDir) != 0)
        {
            return string.Format(str, mainDir, parentDirName);
        }
        else
        {
            return GetRelativeDirPath(assetPath, BaseDir);
        }
    }

    public static string GetAssetBundleName(string asset)
    {
        if (!IsNeedMark(asset))
        {
            return string.Empty;
        }

        string bundleName = string.Empty;

        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetFontName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetPrefabName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetAudioName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetSceneName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetSceneLODName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetUiName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetShaderName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetLuaBundleName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetActorDyeItemName(asset);
        if (string.IsNullOrEmpty(bundleName)) bundleName = TryGetSpecialFileName(asset);    // 其他特殊没有规则的文件

        bundleName = bundleName.Replace(" ", "");
        return IGNORE_MARK == bundleName ? string.Empty : bundleName.ToLower();
    }

    private static string TryGetFontName(string asset)
    {
        if (asset.StartsWith(TTFDir))
        {
            return "uis/ttf_bundle";
        }
        else if (asset.StartsWith(FontDir))
        {
            if (AssetDatabase.GetMainAssetTypeAtPath(asset) == typeof(Font)
                || asset.EndsWith("FontAtlas.png")
                || asset.EndsWith("FontAtlas.mat"))
            {
                return "uis/fonts_bundle";
            }
            else
            {
                return IGNORE_MARK;
            }
        }
        else if (asset.StartsWith(ArtFontDir))
        {
            if (AssetDatabase.GetMainAssetTypeAtPath(asset) == typeof(Font)
                || (asset.EndsWith(".asset")&& !asset.EndsWith("ArtFontTableData.asset")))
            {
                return "uis/fonts_bundle";
            }
            else
            {
                return IGNORE_MARK;
            }
        }
        else if (asset.StartsWith(FontMatDir))
        {
            return "uis/fonts_bundle";
        }
        else if (asset.StartsWith(TMPStyleSheetDir))
        {
            return "uis/fonts_bundle";
        }
        
        return string.Empty;
    }

    private static bool IsNeedMark(string asset)
    {
        if (!asset.StartsWith(BaseDir))
        {
            return false;
        }

        if (AssetDatabase.IsValidFolder(asset))
        {
            return false;
        }

        if (asset.StartsWith(WidgetDir))
        {
            return false;
        }

        return true;
    }

    private static string TryGetPrefabName(string asset)
    {
        if (asset.EndsWith(".prefab"))
        {
            if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterBodyDir))
            {
                string str = "character/body";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/body";
                }
                return GetCharacterBundleName(str, asset);
            }
            else if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterFaceDir))
            {
                string str = "character/face";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/face";
                }
                return GetCharacterBundleName(str, asset);
            }
            else if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterHairDir))
            {
                string str = "character/hair";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/hair";
                }
                return GetCharacterBundleName(str, asset);
            }
            else if (asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character"))
            {
                return GetCharacterBundleName("character", asset);
            }
            else if (asset.StartsWith(WeaponDir))
            {
                return GetCharacterBundleName("weapon", asset);
            }
            else if (asset.StartsWith(EffectPrefabDir))
            {
                return GetRelativeDirPath(asset, BaseDir) + "/" + Path.GetFileNameWithoutExtension(asset) + "_prefab";
            }
            else if (!asset.StartsWith(EnvironmentsDir))
            {
                return GetRelativeDirPath(asset, BaseDir) + "_prefab";
            }
        }

        return string.Empty;
    }

    private static string TryGetAudioName(string asset)
    {
        if (asset.StartsWith(AudioDir))
        {
            if (Path.GetDirectoryName(asset) != AudioDir)
            {
                return GetRelativeDirPath(asset, BaseDir);
            }
        }

        return string.Empty;
    }

    private static string TryGetSceneName(string asset)
    {
        if (asset.StartsWith("Assets/Game/Scenes/SkillEditor"))
        {
            return string.Empty;
        }

        if (asset.EndsWith(".unity") && !asset.EndsWith("_Main_Editor.unity") && !LogicRegex.IsMatch(asset.ToLower()))
        {
            string bundleName = GetRelativeDirPath(asset, BaseDir);
            var sceneMark = Regex.Replace(Path.GetFileNameWithoutExtension(asset), ".+_", "_");
            return bundleName + sceneMark;
        }

        return string.Empty;
    }

    private static string TryGetSceneLODName(string asset)
    {
        if (asset.StartsWith(EnvironmentsDir) && asset.Contains("LOD") && asset.EndsWith(".asset"))
        {
            string bundleName = GetRelativeDirPath(asset, BaseDir);
            return bundleName;
        }

        return string.Empty;
    }

    private static string TryGetUiName(string asset)
    {
        if (asset.StartsWith(UIDir) && !asset.StartsWith(EmojiDir) && !asset.StartsWith(HUDProgrammeDir))
        {
            var importer = AssetImporter.GetAtPath(asset);

            if (asset.Contains("/nopack/"))
            {
                //string asset_path = asset.Substring(0, asset.LastIndexOf("."));
                //asset_path = GetRelativeDirPath(asset_path, BaseDir) + "/" + Path.GetFileNameWithoutExtension(asset);
                return "uis/nopack/" + Path.GetFileNameWithoutExtension(asset);
            }
            else if (asset.Contains(FunctionSkill) || asset.Contains(MainSkill))
            {
                return "uis/skillicon/" + Path.GetFileNameWithoutExtension(asset);
            }
            else if (asset.StartsWith(MainUIDir))
            {
                return "uis/view/mainui/images_atlas";
            }
            else if (asset.StartsWith(RawImageDir))
            {
                return "uis/rawimages/" + Path.GetFileNameWithoutExtension(asset);
            }
            else if (asset.StartsWith(UIImagesDir))
            {
                int index = UIDir.Length + 1;
                string asset_path = asset.Substring(index, asset.Length - index);
                index = asset_path.LastIndexOf("/");
                asset_path = asset_path.Substring(0, index);
                index = asset_path.IndexOf("/") + 1;
                asset_path = asset_path.Substring(index, asset_path.Length - index);
                asset_path = asset_path.ToLower().Replace("\\", "/");
                return "uis/images/" + asset_path + "_atlas";
            }
            else if (importer as TextureImporter)
            {
                string asset_path = GetRelativeDirPath(asset, UIDir);
                if (asset_path == "icons/item")
                {
                    int img_num;
                    if (int.TryParse(Path.GetFileNameWithoutExtension(asset), out img_num))
                    {
                        int img_num2 = img_num / 1000;
                        int img_num3 = img_num % 1000 > 500 ? 2 : 1;
                        return "uis/" + asset_path + "_" + img_num2 + "_" + img_num3 + "_atlas";
                    }
                }

                return $"uis/{asset_path}_atlas";
            }
            else if (importer as VideoClipImporter)
            {
                string asset_path = GetRelativeDirPath(asset, UIDir);
                return $"uis/{asset_path}";
            }
            else if (asset.EndsWith(RenderTextureFileSuffix))
            {
                string asset_path = GetRelativeDirPath(asset, UIDir);
                return $"uis/{asset_path}";
            }
            else if (asset.EndsWith(SpriteAtlasFileSuffix))
            {
                string asset_path = GetRelativeDirPath(asset, UIDir);
                return $"uis/{asset_path}_atlas";
            }
        }

        return string.Empty;
    }

    private static string TryGetShaderName(string asset)
    {
        if (asset.StartsWith(ShaderDir))
        {
            if (ShaderLabOptimize.isOpenOptimize)
            {
                if (asset.Contains("Materials/VariantsShaders"))
                {
                    var fileName = Path.GetFileNameWithoutExtension(asset).Replace("Variants_", "");
                    return ("variant_shaders/" + fileName).ToLower();
                }

                if (asset.Contains("Materials") && asset.EndsWith(".mat"))
                {
                    var fileName = Path.GetFileNameWithoutExtension(asset);
                    return ("variant_shaders/" + fileName).ToLower();
                }

                if (asset.Contains("YifStandard/YifStandard.shader")
                    || asset.Contains("YYStandard/YYStandard")
                    || asset.Contains("YYStandard/YYStandardMap")
                     || asset.Contains("YYStandard/YYStandardGrass"))
                {
                    return ("standard_shaders").ToLower();
                }

                if (asset.Contains("Srp/Standard/EffectCf.shader")
                    || asset.Contains("Srp/Standard/GPUTerrainCf.shader")
                    || asset.Contains("Srp/Standard/SrpMap_PbrCf.shader")
                     || asset.Contains("Srp/Standard/SrpRole_PbrCf.shader")
                     || asset.Contains("Srp/Standard/SrpRoleHairAlphaTestPass_PbrCf.shader")
                     || asset.Contains("Srp/Standard/SrpTerrainCf.shader")
                     || asset.Contains("Srp/Distort.shader")
                     || asset.Contains("Srp/DistortMask.shader")
                     || asset.Contains("Srp/GrassCf.shader"))
                {
                    return ("srp_standard_shaders").ToLower();
                }
            }

            return "shaders";
        }

        return string.Empty;
    }

    private static string TryGetLuaBundleName(string asset)
    {
        if (asset.Contains("lua_bundle_lookup") || asset.Contains("luajit_bundle_lookup"))
        {
            return "lua_lookup";
        }

        if (asset.StartsWith(LuaBundleJitDir))
        {
            return "luajit/" + GetLuaBundleName(asset, LuaBundleJitDir);
        }

        if (asset.StartsWith(LuaBundleDir))
        {
            return "lua/" + GetLuaBundleName(asset, LuaBundleDir);
        }

        return string.Empty;
    }


    private static string TryGetActorDyeItemName(string asset)
    {
        if (asset.EndsWith(".asset"))
        {
            if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterBodyDir))
            {
                string str = "character/body";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/body";
                }
                return GetCharacterBundleName(str, asset, true);
            }
            else if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterFaceDir))
            {
                string str = "character/face";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/face";
                }
                return GetCharacterBundleName(str, asset, true);
            }
            else if ((asset.StartsWith(CharacterDir) || asset.StartsWith(ActorsDir + "/Character")) && asset.Contains(CharacterHairDir))
            {
                string str = "character/hair";
                if (asset.Contains(CharacterRealmDir))
                {
                    str = "character/realm/hair";
                }
                return GetCharacterBundleName(str, asset, true);
            }
        }

        return string.Empty;
    }

    private static string TryGetSpecialFileName(string asset)
    {
        if (asset.EndsWith("QualityConfig.asset"))
        {
            return "misc/quality";


        }
        else if (asset.Contains("Misc/CharacterData"))
        {
            return "misc/characterdata";
        }
        else if (asset.Contains("Misc/Material"))
        {
            return "misc/material";
        }
        else if (asset.EndsWith("toonylighting.psd"))
        {
            return "misc/psd";
        }
        else if (asset.Contains("Misc/QingGong"))
        {
            return "misc/qinggong";
        }
        else if (asset.Contains("Misc/Texture"))
        {
            return "misc/texture";
        }
        else if (asset.Contains("Actors/Character") && asset.EndsWith(".overrideController"))
        {
            return "misc/overridecontroller";
        }
        else if (asset.StartsWith(EmojiDir))
        {
            return "uis/emoji";
        }
        else if (asset.StartsWith(HUDProgrammeDir))
        {
            return "uis/hud_programme";
        }
        else if (asset.StartsWith(PostEffectsDir))
        {
            return "scenes/posteffects";
        }
        else if (asset.StartsWith(EnvironmentsSharedTextureDir))
        {
            return "environments/shared_texture";
        }
        else if (asset.StartsWith(UrpEngineMonoAsset))
        {
            return "urpsettings/urpengineassets";
        }
        else if (asset.StartsWith(PupilTexturesDir))
        {
            return "actors/pupil_textures";
        }
        else if (asset.StartsWith(FaceDecalTexturesDir))
        {
            return "actors/face_decal_textures";
        }
        else if (asset.StartsWith(ScreenEffectDir))
        {
            return "effects/screen";
        }
        
        return string.Empty;
    }

    private static string GetLuaBundleName(string asset, string luaBundleDir)
    {
        string bundle_name = string.Empty;
        string relative_dir_path = GetRelativeDirPath(asset, luaBundleDir);
        if (string.IsNullOrEmpty(relative_dir_path))
        {
            bundle_name = "main";
        }
        else
        {
            string relative_file_path = GetReleativeFilePath(asset, luaBundleDir);
            string[] names = relative_file_path.Split('/');
            if ("config" == names[0])
            {
                if (2 == names.Length)
                {
                    if (names[1].Contains("config_predownload") || names[1].Contains("config_strong_update"))
                        bundle_name = "config/" + names[1];
                    else
                        bundle_name = "config/main";
                }
                // 根据首字母进包
                else if ("auto_new" == names[1])
                {
                    bundle_name = "config/auto_new/" + names[names.Length - 1].Substring(0, 1) + "_chunk";
                }
                else
                {
                    bundle_name = "config/" + names[names.Length - 2];
                }
            }
            else if ("protocolcommon" == names[0])
            {
                bundle_name = "protocol";
            }
            else if ("game" == names[0])
            {
                // 功能模块 (每个配置一个ab包）
                bundle_name = "game/" + names[1];
            }
            else
            {
                bundle_name = "misc";
            }
        }

        return bundle_name.Replace(".lua.bytes", "");
    }

    public static void MarkAssetBundle(string asset)
    {
        if (!asset.StartsWith(BaseDir) || asset.EndsWith(".cs"))    //脚本不用添加abName
        {
            return;
        }

        var importer = AssetImporter.GetAtPath(asset);
        if (!importer)
        {
            return;
        }

        var bundleName = GetAssetBundleName(asset);
        bundleName = FixAssetBundleName(bundleName);

        if (!string.Equals(importer.assetBundleName, bundleName))
        {
            importer.assetBundleName = bundleName;
            importer.SaveAndReimport();
        }
    }

    private static string FixAssetBundleName(string bundleName)
    {
        bundleName = bundleName.Replace(" ", "");
        bundleName = bundleName.Replace("—", "-");
        bundleName = Regex.Replace(bundleName, "[\u4E00-\u9FA5]+", ""); //去除汉字

        return bundleName;
    }
}
