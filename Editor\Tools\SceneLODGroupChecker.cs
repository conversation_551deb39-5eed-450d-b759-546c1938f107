using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine.SceneManagement;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using Sirenix.Utilities;

namespace GameEditor.Tools
{
    /// <summary>
    /// 场景LOD Group检查工具
    /// 检查场景中所有LOD Group的mesh兼容性，防止移动端运行时错误
    /// </summary>
    public class SceneLODGroupChecker : OdinEditorWindow
    {
        [MenuItem("Tools/场景LOD Group检查器")]
        private static void OpenWindow()
        {
            var window = GetWindow<SceneLODGroupChecker>();
            window.titleContent = new GUIContent("场景LOD Group检查器");
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 600);
        }

        #region 配置设置
        [BoxGroup("扫描设置")]
        [LabelText("扫描当前场景")]
        [SerializeField] private bool scanCurrentScene = true;

        [BoxGroup("扫描设置")]
        [LabelText("指定场景路径")]
        [SerializeField, ShowIf("@!scanCurrentScene")]
        [FolderPath(AbsolutePath = false)]
        private string customScenePath = "Assets/Game/Scenes/Map";

        [BoxGroup("扫描设置")]
        [LabelText("包含非激活对象")]
        [SerializeField] private bool includeInactiveObjects = true;

        [BoxGroup("扫描设置")]
        [LabelText("检查深度")]
        [SerializeField] private CheckDepth checkDepth = CheckDepth.Standard;
        #endregion

        #region 检查结果
        [BoxGroup("检查结果")]
        [LabelText("检测到的问题")]
        [ListDrawerSettings(ShowIndexLabels = true, DraggableItems = false)]
        [SerializeField] private List<SceneLODIssue> detectedIssues = new List<SceneLODIssue>();

        [BoxGroup("统计信息")]
        [LabelText("扫描的LOD Group数量"), ReadOnly]
        [SerializeField] private int scannedLODGroups = 0;

        [BoxGroup("统计信息")]
        [LabelText("高风险问题"), ReadOnly]
        [SerializeField] private int highRiskIssues = 0;

        [BoxGroup("统计信息")]
        [LabelText("中风险问题"), ReadOnly]
        [SerializeField] private int mediumRiskIssues = 0;

        [BoxGroup("统计信息")]
        [LabelText("低风险问题"), ReadOnly]
        [SerializeField] private int lowRiskIssues = 0;
        #endregion

        public enum CheckDepth
        {
            [LabelText("快速检查")]
            Quick,    // 基本兼容性检查
            [LabelText("标准检查")]
            Standard, // 包含骨骼系统检查
            [LabelText("深度检查")]
            Deep      // 完整的移动端兼容性分析
        }

        public enum LODIssueType
        {
            [LabelText("缺失Mesh")]
            MissingMesh,
            [LabelText("顶点属性不兼容")]
            VertexAttributeIncompatible,
            [LabelText("骨骼系统不匹配")]
            BoneSystemMismatch,
            [LabelText("移动端不兼容")]
            MobileIncompatible,
            [LabelText("LOD配置错误")]
            LODConfigurationError,
            [LabelText("材质不一致")]
            MaterialInconsistency
        }

        public enum RiskLevel
        {
            [LabelText("低风险")]
            Low,
            [LabelText("中风险")]
            Medium,
            [LabelText("高风险")]
            High
        }

        [System.Serializable]
        public class SceneLODIssue
        {
            [BoxGroup("问题信息")]
            [LabelText("问题类型")]
            [SerializeField] public LODIssueType IssueType;

            [HorizontalGroup("问题信息/Header")]
            [LabelText("风险等级")]
            [SerializeField] public RiskLevel RiskLevel;

            [BoxGroup("问题信息")]
            [LabelText("场景路径")]
            [SerializeField] public string ScenePath;

            [BoxGroup("问题信息")]
            [LabelText("目标对象")]
            [SerializeField] public GameObject TargetGameObject;

            [BoxGroup("问题信息")]
            [LabelText("对象名称")]
            [SerializeField] public string TargetObjectName;

            [BoxGroup("问题信息")]
            [LabelText("对象路径")]
            [SerializeField] public string TargetObjectPath;

            [BoxGroup("问题信息")]
            [LabelText("LOD Group")]
            [SerializeField] public LODGroup TargetLODGroup;

            [BoxGroup("问题信息")]
            [LabelText("涉及LOD级别")]
            [SerializeField] public string LODLevels;

            [BoxGroup("问题信息")]
            [LabelText("问题描述")]
            [SerializeField, MultiLineProperty(3)]
            public string Description;

            [BoxGroup("问题信息")]
            [LabelText("修复建议")]
            [SerializeField, MultiLineProperty(2)]
            public string Solution;

            [HideInInspector]
            public Mesh Mesh1;

            [HideInInspector]
            public Mesh Mesh2;

            [HideInInspector]
            public SkinnedMeshRenderer Renderer1;

            [HideInInspector]
            public SkinnedMeshRenderer Renderer2;

            private Color GetRiskColor()
            {
                switch (RiskLevel)
                {
                    case RiskLevel.High: return Color.red;
                    case RiskLevel.Medium: return Color.yellow;
                    case RiskLevel.Low: return Color.green;
                    default: return Color.white;
                }
            }

            [BoxGroup("问题信息")]
            [HorizontalGroup("问题信息/Actions")]
            [Button("定位对象", ButtonSizes.Medium)]
            [GUIColor("@GetRiskColor()")]
            public void PingTarget()
            {
                if (TargetGameObject != null)
                {
                    EditorGUIUtility.PingObject(TargetGameObject);
                    Selection.activeGameObject = TargetGameObject;
                }
                else if (TargetLODGroup != null)
                {
                    EditorGUIUtility.PingObject(TargetLODGroup.gameObject);
                    Selection.activeGameObject = TargetLODGroup.gameObject;
                }
                else if (!string.IsNullOrEmpty(ScenePath) && !string.IsNullOrEmpty(TargetObjectPath))
                {
                    // 尝试打开场景并定位对象
                    var currentScene = SceneManager.GetActiveScene().path;
                    if (currentScene != ScenePath)
                    {
                        if (EditorUtility.DisplayDialog("切换场景", 
                            $"需要切换到场景 '{Path.GetFileName(ScenePath)}' 来定位对象，是否继续？", 
                            "是", "否"))
                        {
                            var scene = EditorSceneManager.OpenScene(ScenePath, OpenSceneMode.Single);
                            var targetObj = GameObject.Find(TargetObjectPath);
                            if (targetObj != null)
                            {
                                EditorGUIUtility.PingObject(targetObj);
                                Selection.activeGameObject = targetObj;
                            }
                            else
                            {
                                EditorUtility.DisplayDialog("未找到对象", $"在场景中未找到对象: {TargetObjectPath}", "确定");
                            }
                        }
                    }
                }
            }

            [HorizontalGroup("问题信息/Actions")]
            [Button("查看详情", ButtonSizes.Medium)]
            [GUIColor(0.8f, 1f, 0.8f)]
            public void ShowDetails()
            {
                string objectName = !string.IsNullOrEmpty(TargetObjectName) ? TargetObjectName : 
                                  (TargetGameObject ? TargetGameObject.name : "未知");
                string details = $"问题类型: {IssueType}\n风险等级: {RiskLevel}\n场景: {ScenePath}\n对象: {objectName}\n对象路径: {TargetObjectPath}\nLOD级别: {LODLevels}\n\n描述:\n{Description}\n\n解决方案:\n{Solution}";
                EditorUtility.DisplayDialog("问题详情", details, "确定");
            }
        }

        #region 扫描功能
        [Button("开始扫描", ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1f)]
        public void StartScan()
        {
            detectedIssues.Clear();
            scannedLODGroups = 0;
            
            try
            {
                EditorUtility.DisplayProgressBar("场景LOD检查", "准备扫描...", 0f);

                if (scanCurrentScene)
                {
                    ScanCurrentScene();
                }
                else
                {
                    ScanSpecifiedScenes();
                }

                CategorizeIssues();
                EditorUtility.DisplayDialog("扫描完成", 
                    $"扫描完成！\n检查了 {scannedLODGroups} 个LOD Group\n发现 {detectedIssues.Count} 个问题\n高风险: {highRiskIssues}, 中风险: {mediumRiskIssues}, 低风险: {lowRiskIssues}", 
                    "确定");
            }
            catch (Exception ex)
            {
                Debug.LogError($"扫描过程中发生错误: {ex.Message}");
                EditorUtility.DisplayDialog("扫描错误", $"扫描过程中发生错误: {ex.Message}", "确定");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void ScanCurrentScene()
        {
            var currentScene = SceneManager.GetActiveScene();
            if (!currentScene.IsValid())
            {
                throw new InvalidOperationException("当前场景无效");
            }
            
            ScanScene(currentScene);
        }

        private void ScanSpecifiedScenes()
        {
            if (string.IsNullOrEmpty(customScenePath))
            {
                throw new ArgumentException("请指定有效的场景路径");
            }

            var sceneGuids = AssetDatabase.FindAssets("t:Scene", new[] { customScenePath });
            var scenePaths = sceneGuids.Select(AssetDatabase.GUIDToAssetPath).ToList();

            // 过滤只包含以"_Main"结尾的场景（不区分大小写）
            var filteredScenePaths = scenePaths.Where(path => 
                Path.GetFileNameWithoutExtension(path).EndsWith("_Main", StringComparison.OrdinalIgnoreCase)
            ).ToList();

            if (filteredScenePaths.Count == 0)
            {
                EditorUtility.DisplayDialog("未找到场景", 
                    $"在路径 '{customScenePath}' 下未找到以'_Main'结尾的场景文件。\n总共找到 {scenePaths.Count} 个场景文件。", 
                    "确定");
                return;
            }

            Debug.Log($"[SceneLODGroupChecker] 在 '{customScenePath}' 下找到 {filteredScenePaths.Count} 个以'_Main'结尾的场景，总共 {scenePaths.Count} 个场景文件");

            for (int i = 0; i < filteredScenePaths.Count; i++)
            {
                var scenePath = filteredScenePaths[i];
                EditorUtility.DisplayProgressBar("场景LOD检查", 
                    $"扫描场景: {Path.GetFileName(scenePath)} ({i + 1}/{filteredScenePaths.Count})", 
                    (float)i / filteredScenePaths.Count);

                var scene = EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);
                ScanScene(scene);
            }
        }

        private void ScanScene(Scene scene)
        {
            var rootObjects = scene.GetRootGameObjects();
            var allLODGroups = new List<LODGroup>();

            foreach (var rootObj in rootObjects)
            {
                var lodGroups = rootObj.GetComponentsInChildren<LODGroup>(includeInactiveObjects);
                allLODGroups.AddRange(lodGroups);
            }

            for (int i = 0; i < allLODGroups.Count; i++)
            {
                var lodGroup = allLODGroups[i];
                EditorUtility.DisplayProgressBar("场景LOD检查", 
                    $"检查LOD Group: {lodGroup.name}", 
                    (float)i / allLODGroups.Count);

                ValidateLODGroup(lodGroup, scene.path);
                scannedLODGroups++;
            }
        }

        private void ValidateLODGroup(LODGroup lodGroup, string scenePath)
        {
            if (lodGroup == null) return;

            var lods = lodGroup.GetLODs();
            if (lods == null || lods.Length < 2) 
            {
                // AddIssue(new SceneLODIssue
                // {
                //     IssueType = LODIssueType.LODConfigurationError,
                //     RiskLevel = RiskLevel.Medium,
                //     ScenePath = scenePath,
                //     TargetGameObject = lodGroup.gameObject,
                //     TargetObjectName = lodGroup.gameObject.name,
                //     TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                //     TargetLODGroup = lodGroup,
                //     LODLevels = "配置错误",
                //     Description = $"LOD Group '{lodGroup.name}' 配置错误：LOD级别少于2个",
                //     Solution = "请为LOD Group配置至少2个LOD级别"
                // });
                return;
            }

            // 检查每个LOD级别之间的兼容性
            for (int i = 0; i < lods.Length; i++)
            {
                for (int j = i + 1; j < lods.Length; j++)
                {
                    ValidateLODLevels(lodGroup, lods[i], lods[j], i, j, scenePath);
                }
            }
        }

        private void ValidateLODLevels(LODGroup lodGroup, LOD lod1, LOD lod2, int level1, int level2, string scenePath)
        {
            if (lod1.renderers == null || lod2.renderers == null) return;

            // 检查renderer数量是否匹配
            if (lod1.renderers.Length != lod2.renderers.Length)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.LODConfigurationError,
                    RiskLevel = RiskLevel.High,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"LOD{level1}有{lod1.renderers.Length}个renderer，LOD{level2}有{lod2.renderers.Length}个renderer，数量不匹配",
                    Solution = "确保所有LOD级别具有相同数量的renderer"
                });
            }

            // 检查对应的renderer兼容性
            var minCount = Mathf.Min(lod1.renderers.Length, lod2.renderers.Length);
            for (int i = 0; i < minCount; i++)
            {
                var renderer1 = lod1.renderers[i];
                var renderer2 = lod2.renderers[i];

                if (renderer1 == null || renderer2 == null)
                {
                    AddIssue(new SceneLODIssue
                    {
                        IssueType = LODIssueType.LODConfigurationError,
                        RiskLevel = RiskLevel.High,
                        ScenePath = scenePath,
                        TargetGameObject = lodGroup.gameObject,
                        TargetObjectName = lodGroup.gameObject.name,
                        TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                        TargetLODGroup = lodGroup,
                        LODLevels = $"LOD{level1} vs LOD{level2}",
                        Description = $"LOD级别中存在空的renderer引用",
                        Solution = "请移除空的renderer引用或添加有效的renderer"
                    });
                    continue;
                }

                ValidateRendererCompatibility(renderer1, renderer2, level1, level2, lodGroup, scenePath);
            }
        }

        private void ValidateRendererCompatibility(Renderer renderer1, Renderer renderer2, int level1, int level2, LODGroup lodGroup, string scenePath)
        {
            // 检查SkinnedMeshRenderer的特殊兼容性
            if (renderer1 is SkinnedMeshRenderer smr1 && renderer2 is SkinnedMeshRenderer smr2)
            {
                ValidateSkinnedMeshRendererCompatibility(smr1, smr2, level1, level2, lodGroup, scenePath);
            }
            // 检查MeshRenderer的兼容性
            else if (renderer1 is MeshRenderer mr1 && renderer2 is MeshRenderer mr2)
            {
                ValidateMeshRendererCompatibility(mr1, mr2, level1, level2, lodGroup, scenePath);
            }
            // 检查类型不匹配
            else if (renderer1.GetType() != renderer2.GetType())
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.LODConfigurationError,
                    RiskLevel = RiskLevel.High,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"LOD{level1}使用{renderer1.GetType().Name}，LOD{level2}使用{renderer2.GetType().Name}，类型不匹配",
                    Solution = "确保相同位置的renderer使用相同类型"
                });
            }
        }

        private void ValidateSkinnedMeshRendererCompatibility(SkinnedMeshRenderer smr1, SkinnedMeshRenderer smr2, int level1, int level2, LODGroup lodGroup, string scenePath)
        {
            // 检查mesh引用
            if (smr1.sharedMesh == null || smr2.sharedMesh == null)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.MissingMesh,
                    RiskLevel = RiskLevel.High,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"SkinnedMeshRenderer缺少mesh引用",
                    Solution = "为SkinnedMeshRenderer分配有效的mesh",
                    Renderer1 = smr1,
                    Renderer2 = smr2
                });
                return;
            }

            // 检查mesh兼容性
            // if (!AreMeshesCompatible(smr1.sharedMesh, smr2.sharedMesh))
            // {
            //     AddIssue(new SceneLODIssue
            //     {
            //         IssueType = LODIssueType.VertexAttributeIncompatible,
            //         RiskLevel = RiskLevel.High,
            //         ScenePath = scenePath,
            //         TargetGameObject = lodGroup.gameObject,
            //         TargetObjectName = lodGroup.gameObject.name,
            //         TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
            //         TargetLODGroup = lodGroup,
            //         LODLevels = $"LOD{level1} vs LOD{level2}",
            //         Description = $"LOD{level1}的mesh '{smr1.sharedMesh.name}' 与 LOD{level2}的mesh '{smr2.sharedMesh.name}' 顶点属性不兼容，可能导致移动端错误",
            //         Solution = "确保LOD mesh具有相同的顶点属性布局",
            //         Mesh1 = smr1.sharedMesh,
            //         Mesh2 = smr2.sharedMesh,
            //         Renderer1 = smr1,
            //         Renderer2 = smr2
            //     });
            // }

            // 深度检查模式下检查骨骼系统
            if (checkDepth >= CheckDepth.Standard)
            {
                ValidateBoneSystemCompatibility(smr1, smr2, level1, level2, lodGroup, scenePath);
            }

            // 深度检查模式下检查移动端兼容性
            if (checkDepth >= CheckDepth.Deep)
            {
                ValidateMobileCompatibility(smr1, smr2, level1, level2, lodGroup, scenePath);
            }
        }

        private void ValidateMeshRendererCompatibility(MeshRenderer mr1, MeshRenderer mr2, int level1, int level2, LODGroup lodGroup, string scenePath)
        {
            var mf1 = mr1.GetComponent<MeshFilter>();
            var mf2 = mr2.GetComponent<MeshFilter>();

            if (mf1 == null || mf2 == null || mf1.sharedMesh == null || mf2.sharedMesh == null)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.MissingMesh,
                    RiskLevel = RiskLevel.High,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"MeshRenderer缺少MeshFilter或mesh引用",
                    Solution = "为MeshRenderer添加MeshFilter组件并分配有效的mesh"
                });
                return;
            }

            // if (!AreMeshesCompatible(mf1.sharedMesh, mf2.sharedMesh))
            // {
            //     AddIssue(new SceneLODIssue
            //     {
            //         IssueType = LODIssueType.VertexAttributeIncompatible,
            //         RiskLevel = RiskLevel.Medium,
            //         ScenePath = scenePath,
            //         TargetGameObject = lodGroup.gameObject,
            //         TargetObjectName = lodGroup.gameObject.name,
            //         TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
            //         TargetLODGroup = lodGroup,
            //         LODLevels = $"LOD{level1} vs LOD{level2}",
            //         Description = $"LOD{level1}的mesh '{mf1.sharedMesh.name}' 与 LOD{level2}的mesh '{mf2.sharedMesh.name}' 顶点属性不兼容",
            //         Solution = "确保LOD mesh具有相同的顶点属性布局",
            //         Mesh1 = mf1.sharedMesh,
            //         Mesh2 = mf2.sharedMesh
            //     });
            // }
        }

        private void ValidateBoneSystemCompatibility(SkinnedMeshRenderer smr1, SkinnedMeshRenderer smr2, int level1, int level2, LODGroup lodGroup, string scenePath)
        {
            // 检查根骨骼
            if (smr1.rootBone != smr2.rootBone)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.BoneSystemMismatch,
                    RiskLevel = RiskLevel.Medium,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"LOD{level1}和LOD{level2}使用不同的根骨骼",
                    Solution = "确保所有LOD级别使用相同的根骨骼",
                    Renderer1 = smr1,
                    Renderer2 = smr2
                });
            }

            // 检查骨骼数量
            var bones1 = smr1.bones?.Length ?? 0;
            var bones2 = smr2.bones?.Length ?? 0;
            if (bones1 != bones2)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.BoneSystemMismatch,
                    RiskLevel = RiskLevel.Medium,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"LOD{level1}有{bones1}个骨骼，LOD{level2}有{bones2}个骨骼，数量不匹配",
                    Solution = "确保所有LOD级别使用相同的骨骼结构",
                    Renderer1 = smr1,
                    Renderer2 = smr2
                });
            }
        }

        private void ValidateMobileCompatibility(SkinnedMeshRenderer smr1, SkinnedMeshRenderer smr2, int level1, int level2, LODGroup lodGroup, string scenePath)
        {
            // 检查移动端骨骼限制
            var bones1 = smr1.bones?.Length ?? 0;
            var bones2 = smr2.bones?.Length ?? 0;
            
            if (bones1 > 32 || bones2 > 32)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.MobileIncompatible,
                    RiskLevel = RiskLevel.Medium,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"骨骼数量超过移动端推荐值32个（LOD{level1}: {bones1}, LOD{level2}: {bones2}）",
                    Solution = "考虑减少骨骼数量或使用LOD Group来优化性能",
                    Renderer1 = smr1,
                    Renderer2 = smr2
                });
            }

            // 检查顶点数量
            var mesh1 = smr1.sharedMesh;
            var mesh2 = smr2.sharedMesh;
            
            if (mesh1.vertexCount > 10000 || mesh2.vertexCount > 10000)
            {
                AddIssue(new SceneLODIssue
                {
                    IssueType = LODIssueType.MobileIncompatible,
                    RiskLevel = RiskLevel.Low,
                    ScenePath = scenePath,
                    TargetGameObject = lodGroup.gameObject,
                    TargetObjectName = lodGroup.gameObject.name,
                    TargetObjectPath = GetGameObjectPath(lodGroup.gameObject),
                    TargetLODGroup = lodGroup,
                    LODLevels = $"LOD{level1} vs LOD{level2}",
                    Description = $"顶点数量可能对移动端过高（LOD{level1}: {mesh1.vertexCount}, LOD{level2}: {mesh2.vertexCount}）",
                    Solution = "考虑优化mesh或调整LOD距离设置",
                    Mesh1 = mesh1,
                    Mesh2 = mesh2,
                    Renderer1 = smr1,
                    Renderer2 = smr2
                });
            }
        }

        /// <summary>
        /// 检查两个mesh是否兼容（复用URPMeshCompatibilityChecker的逻辑）
        /// </summary>
        private bool AreMeshesCompatible(Mesh mesh1, Mesh mesh2)
        {
            if (mesh1 == null || mesh2 == null) return false;

            var attrs1 = mesh1.GetVertexAttributes();
            var attrs2 = mesh2.GetVertexAttributes();

            // 检查顶点属性数量
            if (attrs1.Length != attrs2.Length)
            {
                Debug.LogError($"顶点属性数量不匹配: {mesh1.name} vs {mesh2.name}  {attrs1.Length} != {attrs2.Length}");
                return false;
            }

            // 检查每个属性的兼容性
            for (int i = 0; i < attrs1.Length; i++)
            {
                if (attrs1[i].attribute != attrs2[i].attribute ||
                    attrs1[i].format != attrs2[i].format ||
                    attrs1[i].dimension != attrs2[i].dimension)
                {
                    Debug.LogError($"顶点属性不匹配: {mesh1.name} vs {mesh2.name}  {attrs1[i].attribute} != {attrs2[i].attribute}  {attrs1[i].format} != {attrs2[i].format}  {attrs1[i].dimension} != {attrs2[i].dimension}");
                    return false;
                }
            }

            return true;
        }

        private void AddIssue(SceneLODIssue issue)
        {
            detectedIssues.Add(issue);
        }

        /// <summary>
        /// 获取GameObject在场景中的完整路径
        /// </summary>
        private string GetGameObjectPath(GameObject obj)
        {
            if (obj == null) return "";
            
            var path = new List<string>();
            var current = obj.transform;
            
            while (current != null)
            {
                path.Insert(0, current.name);
                current = current.parent;
            }
            
            return string.Join("/", path);
        }

        private void CategorizeIssues()
        {
            highRiskIssues = detectedIssues.Count(i => i.RiskLevel == RiskLevel.High);
            mediumRiskIssues = detectedIssues.Count(i => i.RiskLevel == RiskLevel.Medium);
            lowRiskIssues = detectedIssues.Count(i => i.RiskLevel == RiskLevel.Low);
        }
        #endregion

        #region 报告生成和导出
        [Button("生成详细报告", ButtonSizes.Large), GUIColor(0.4f, 1f, 0.6f)]
        public void GenerateDetailedReport()
        {
            if (detectedIssues.Count == 0)
            {
                EditorUtility.DisplayDialog("无问题", "没有检测到问题，无需生成报告。", "确定");
                return;
            }

            var reportPath = EditorUtility.SaveFilePanel("保存LOD检查报告", Application.dataPath, "LOD_Check_Report", "txt");
            if (string.IsNullOrEmpty(reportPath)) return;

            try
            {
                GenerateTextReport(reportPath);
                EditorUtility.DisplayDialog("报告生成成功", $"报告已保存到: {reportPath}", "确定");
                System.Diagnostics.Process.Start("explorer.exe", "/select," + reportPath.Replace("/", "\\"));
            }
            catch (Exception ex)
            {
                Debug.LogError($"生成报告时发生错误: {ex.Message}");
                EditorUtility.DisplayDialog("报告生成失败", $"生成报告时发生错误: {ex.Message}", "确定");
            }
        }

        private void GenerateTextReport(string filePath)
        {
            using (var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8))
            {
                writer.WriteLine("========================================");
                writer.WriteLine("场景LOD Group检查报告");
                writer.WriteLine("========================================");
                writer.WriteLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine($"检查深度: {checkDepth}");
                writer.WriteLine($"扫描的LOD Group数量: {scannedLODGroups}");
                writer.WriteLine($"检测到的问题总数: {detectedIssues.Count}");
                writer.WriteLine($"  - 高风险: {highRiskIssues}");
                writer.WriteLine($"  - 中风险: {mediumRiskIssues}");
                writer.WriteLine($"  - 低风险: {lowRiskIssues}");
                writer.WriteLine();

                // 按风险等级分组
                var groupedIssues = detectedIssues.GroupBy(i => i.RiskLevel).OrderByDescending(g => g.Key);

                foreach (var group in groupedIssues)
                {
                    writer.WriteLine($"========== {group.Key}风险问题 ==========");
                    writer.WriteLine();

                    foreach (var issue in group)
                    {
                        string objectName = !string.IsNullOrEmpty(issue.TargetObjectName) ? issue.TargetObjectName : 
                                          (issue.TargetGameObject ? issue.TargetGameObject.name : "未知");
                        writer.WriteLine($"问题类型: {issue.IssueType}");
                        writer.WriteLine($"场景: {issue.ScenePath}");
                        writer.WriteLine($"对象: {objectName}");
                        writer.WriteLine($"对象路径: {issue.TargetObjectPath}");
                        writer.WriteLine($"LOD级别: {issue.LODLevels}");
                        writer.WriteLine($"描述: {issue.Description}");
                        writer.WriteLine($"解决方案: {issue.Solution}");
                        writer.WriteLine("----------------------------------------");
                    }
                    writer.WriteLine();
                }

                writer.WriteLine("========================================");
                writer.WriteLine("报告结束");
                writer.WriteLine("========================================");
            }
        }

        [Button("导出JSON报告", ButtonSizes.Medium), GUIColor(0.8f, 0.8f, 1f)]
        public void ExportJSONReport()
        {
            if (detectedIssues.Count == 0)
            {
                EditorUtility.DisplayDialog("无问题", "没有检测到问题，无需导出报告。", "确定");
                return;
            }

            var reportPath = EditorUtility.SaveFilePanel("导出JSON报告", Application.dataPath, "LOD_Check_Report", "json");
            if (string.IsNullOrEmpty(reportPath)) return;

            try
            {
                var reportData = new
                {
                    generatedTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    checkDepth = checkDepth.ToString(),
                    scannedLODGroups = scannedLODGroups,
                    totalIssues = detectedIssues.Count,
                    highRiskIssues = highRiskIssues,
                    mediumRiskIssues = mediumRiskIssues,
                    lowRiskIssues = lowRiskIssues,
                    issues = detectedIssues.Select(issue => new
                    {
                        issueType = issue.IssueType.ToString(),
                        riskLevel = issue.RiskLevel.ToString(),
                        scenePath = issue.ScenePath,
                        targetObjectName = !string.IsNullOrEmpty(issue.TargetObjectName) ? issue.TargetObjectName : 
                                         (issue.TargetGameObject ? issue.TargetGameObject.name : "未知"),
                        targetObjectPath = issue.TargetObjectPath,
                        lodLevels = issue.LODLevels,
                        description = issue.Description,
                        solution = issue.Solution
                    }).ToList()
                };

                var json = JsonUtility.ToJson(reportData, true);
                File.WriteAllText(reportPath, json, System.Text.Encoding.UTF8);

                EditorUtility.DisplayDialog("JSON报告导出成功", $"报告已保存到: {reportPath}", "确定");
                System.Diagnostics.Process.Start("explorer.exe", "/select," + reportPath.Replace("/", "\\"));
            }
            catch (Exception ex)
            {
                Debug.LogError($"导出JSON报告时发生错误: {ex.Message}");
                EditorUtility.DisplayDialog("导出失败", $"导出JSON报告时发生错误: {ex.Message}", "确定");
            }
        }

        [Button("清除结果", ButtonSizes.Medium), GUIColor(1f, 0.7f, 0.7f)]
        public void ClearResults()
        {
            detectedIssues.Clear();
            scannedLODGroups = 0;
            highRiskIssues = 0;
            mediumRiskIssues = 0;
            lowRiskIssues = 0;
        }
        #endregion
    }
} 