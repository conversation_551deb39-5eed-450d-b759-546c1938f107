using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;

/// <summary>
/// OverrideController绑定修复工具
/// 用于修复AnimatorOverrideController中丢失的动画绑定
/// </summary>
public class OverrideControllerBindingFixTool : OdinEditorWindow
{
    [MenuItem("自定义工具/动画工具/OverrideController绑定修复工具")]
    private static void OpenWindow()
    {
        GetWindow<OverrideControllerBindingFixTool>("OverrideController绑定修复工具").Show();
    }

    #region 配置设置

    [BoxGroup("配置设置"), LabelText("搜索路径")]
    [InfoBox("指定要搜索OverrideController文件的路径")]
    [FolderPath(AbsolutePath = false)]
    public string searchPath = "Assets/Game/Model/Boss";

    [Space(10)]
    [BoxGroup("配置设置"), LabelText("目标Original名称")]
    [InfoBox("指定要修复的Original动画名称")]
    public string targetOriginalName = "rest";

    [Space(10)]
    [BoxGroup("配置设置"), LabelText("动画名称列表")]
    [InfoBox("按优先级顺序查找动画文件，从第一个开始依次尝试")]
    [ListDrawerSettings(ShowIndexLabels = true)]
    public string[] animNames = new string[] { "rest", "idle" };

    [BoxGroup("配置设置"), LabelText("详细日志输出")]
    public bool enableDetailedLogging = true;

    #endregion

    #region 操作按钮

    [BoxGroup("操作"), Button("开始扫描和修复", ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1f)]
    private void StartScanAndFix()
    {
        if (string.IsNullOrEmpty(searchPath))
        {
            EditorUtility.DisplayDialog("错误", "请指定搜索路径", "确定");
            return;
        }

        if (!Directory.Exists(searchPath))
        {
            EditorUtility.DisplayDialog("错误", "指定的搜索路径不存在", "确定");
            return;
        }

        scanResults.Clear();
        fixResults.Clear();

        try
        {
            EditorUtility.DisplayProgressBar("OverrideController修复", "正在扫描文件...", 0f);
            ScanOverrideControllers();
            
            EditorUtility.DisplayProgressBar("OverrideController修复", "正在修复绑定...", 0.5f);
            FixMissingBindings();
            
            EditorUtility.DisplayProgressBar("OverrideController修复", "保存修改...", 0.9f);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        ShowResults();
    }

    [BoxGroup("操作"), Button("仅扫描（不修复）", ButtonSizes.Medium), GUIColor(0.8f, 0.8f, 0.4f)]
    private void ScanOnly()
    {
        if (string.IsNullOrEmpty(searchPath))
        {
            EditorUtility.DisplayDialog("错误", "请指定搜索路径", "确定");
            return;
        }

        if (!Directory.Exists(searchPath))
        {
            EditorUtility.DisplayDialog("错误", "指定的搜索路径不存在", "确定");
            return;
        }

        scanResults.Clear();
        fixResults.Clear();

        try
        {
            EditorUtility.DisplayProgressBar("OverrideController扫描", "正在扫描文件...", 0f);
            ScanOverrideControllers();
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        ShowResults();
    }

    #endregion

    #region 结果显示

    [BoxGroup("扫描结果"), ShowInInspector, ReadOnly]
    [InfoBox("扫描到的OverrideController文件及其状态")]
    private List<ScanResult> scanResults = new List<ScanResult>();

    [BoxGroup("修复结果"), ShowInInspector, ReadOnly]
    [InfoBox("修复操作的详细结果")]
    private List<FixResult> fixResults = new List<FixResult>();

    #endregion

    #region 数据结构

    [System.Serializable]
    public class ScanResult
    {
        [LabelText("文件路径")]
        public string filePath;
        
        [LabelText("是否需要修复")]
        public bool needsFix;
        
        [LabelText("问题描述")]
        public string issue;

        public ScanResult(string path, bool needs, string desc)
        {
            filePath = path;
            needsFix = needs;
            issue = desc;
        }
    }

    [System.Serializable]
    public class FixResult
    {
        [LabelText("文件路径")]
        public string filePath;
        
        [LabelText("修复状态")]
        public bool success;
        
        [LabelText("绑定的动画")]
        public string boundAnimation;
        
        [LabelText("详细信息")]
        public string details;

        public FixResult(string path, bool succ, string anim, string det)
        {
            filePath = path;
            success = succ;
            boundAnimation = anim;
            details = det;
        }
    }

    #endregion

    #region 核心功能方法

    /// <summary>
    /// 扫描指定路径下的所有OverrideController文件
    /// </summary>
    private void ScanOverrideControllers()
    {
        string[] overrideControllerGuids = AssetDatabase.FindAssets("t:AnimatorOverrideController", new string[] { searchPath });

        if (enableDetailedLogging)
        {
            Debug.Log($"在路径 {searchPath} 下找到 {overrideControllerGuids.Length} 个OverrideController文件");
        }

        for (int i = 0; i < overrideControllerGuids.Length; i++)
        {
            string guid = overrideControllerGuids[i];
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            EditorUtility.DisplayProgressBar("扫描OverrideController", $"正在扫描: {Path.GetFileName(assetPath)}", (float)i / overrideControllerGuids.Length);

            AnimatorOverrideController overrideController = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(assetPath);
            if (overrideController != null)
            {
                CheckOverrideController(overrideController, assetPath);
            }
        }
    }

    /// <summary>
    /// 检查单个OverrideController是否需要修复
    /// </summary>
    private void CheckOverrideController(AnimatorOverrideController overrideController, string assetPath)
    {
        var overrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
        overrideController.GetOverrides(overrides);

        bool needsFix = false;
        string issue = "";

        foreach (var kvp in overrides)
        {
            if (kvp.Key != null && kvp.Key.name.Equals(targetOriginalName, StringComparison.OrdinalIgnoreCase))
            {
                if (kvp.Value == null)
                {
                    needsFix = true;
                    issue = $"Original '{targetOriginalName}' 的Override绑定为空";
                    break;
                }
                else if (kvp.Value.name.Equals(targetOriginalName, StringComparison.OrdinalIgnoreCase))
                {
                    // 检查是否是默认绑定（即Override和Original是同一个）
                    needsFix = true;
                    issue = $"Original '{targetOriginalName}' 的Override绑定丢失，使用默认绑定";
                    break;
                }
            }
        }

        if (!needsFix)
        {
            // 检查是否存在目标Original
            bool hasTargetOriginal = overrides.Any(kvp => kvp.Key != null && kvp.Key.name.Equals(targetOriginalName, StringComparison.OrdinalIgnoreCase));
            if (!hasTargetOriginal)
            {
                issue = $"未找到名为 '{targetOriginalName}' 的Original";
            }
            else
            {
                issue = "绑定正常";
            }
        }

        scanResults.Add(new ScanResult(assetPath, needsFix, issue));

        if (enableDetailedLogging)
        {
            Debug.Log($"扫描 {assetPath}: {(needsFix ? "需要修复" : "正常")} - {issue}");
        }
    }

    /// <summary>
    /// 修复所有需要修复的绑定
    /// </summary>
    private void FixMissingBindings()
    {
        var needsFixList = scanResults.Where(r => r.needsFix).ToList();

        for (int i = 0; i < needsFixList.Count; i++)
        {
            var scanResult = needsFixList[i];
            EditorUtility.DisplayProgressBar("修复绑定", $"正在修复: {Path.GetFileName(scanResult.filePath)}", (float)i / needsFixList.Count);

            FixSingleOverrideController(scanResult.filePath);
        }
    }

    /// <summary>
    /// 修复单个OverrideController文件
    /// </summary>
    private void FixSingleOverrideController(string assetPath)
    {
        AnimatorOverrideController overrideController = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(assetPath);
        if (overrideController == null)
        {
            fixResults.Add(new FixResult(assetPath, false, "", "无法加载OverrideController"));
            return;
        }

        string directory = Path.GetDirectoryName(assetPath);
        AnimationClip foundClip = FindAnimationClipInDirectory(directory);

        if (foundClip == null)
        {
            fixResults.Add(new FixResult(assetPath, false, "", "未找到合适的动画文件"));
            return;
        }

        // 应用修复
        var overrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
        overrideController.GetOverrides(overrides);

        bool _fixed = false;
        for (int i = 0; i < overrides.Count; i++)
        {
            var kvp = overrides[i];
            if (kvp.Key != null && kvp.Key.name.Equals(targetOriginalName, StringComparison.OrdinalIgnoreCase))
            {
                if (kvp.Value == null || kvp.Value.name.Equals(targetOriginalName, StringComparison.OrdinalIgnoreCase))
                {
                    overrides[i] = new KeyValuePair<AnimationClip, AnimationClip>(kvp.Key, foundClip);
                    _fixed = true;
                    break;
                }
            }
        }

        if (_fixed)
        {
            overrideController.ApplyOverrides(overrides);
            EditorUtility.SetDirty(overrideController);

            fixResults.Add(new FixResult(assetPath, true, foundClip.name, $"成功绑定到 {foundClip.name}"));

            if (enableDetailedLogging)
            {
                Debug.Log($"修复成功: {assetPath} -> 绑定到 {foundClip.name}");
            }
        }
        else
        {
            fixResults.Add(new FixResult(assetPath, false, "", "未找到需要修复的Original"));
        }
    }

    #endregion

    #region 动画文件查找方法

    /// <summary>
    /// 在指定目录下查找合适的动画文件
    /// </summary>
    private AnimationClip FindAnimationClipInDirectory(string directory)
    {
        if (animNames == null || animNames.Length == 0)
        {
            if (enableDetailedLogging)
            {
                Debug.LogWarning("动画名称列表为空，无法查找动画文件");
            }
            return null;
        }

        // 按照数组中的顺序依次查找动画
        for (int i = 0; i < animNames.Length; i++)
        {
            string animName = animNames[i];
            if (string.IsNullOrEmpty(animName))
                continue;

            // 1. 先查找anim文件
            AnimationClip animClip = FindAnimationClipByName(directory, animName, "*.anim");
            if (animClip != null)
            {
                if (enableDetailedLogging)
                {
                    Debug.Log($"找到动画 (优先级 {i + 1}): {animClip.name} 在目录 {directory}");
                }
                return animClip;
            }

            // 2. 再查找FBX文件中的动画
            AnimationClip fbxClip = FindAnimationClipInFBX(directory, animName);
            if (fbxClip != null)
            {
                if (enableDetailedLogging)
                {
                    Debug.Log($"在FBX中找到动画 (优先级 {i + 1}): {fbxClip.name} 在目录 {directory}");
                }
                return fbxClip;
            }
        }

        if (enableDetailedLogging)
        {
            Debug.LogWarning($"在目录 {directory} 中未找到合适的动画文件，已尝试的动画名称: [{string.Join(", ", animNames)}]");
        }

        return null;
    }

    /// <summary>
    /// 根据名称查找动画文件
    /// </summary>
    private AnimationClip FindAnimationClipByName(string directory, string animName, string searchPattern)
    {
        if (string.IsNullOrEmpty(animName))
            return null;

        string[] files = Directory.GetFiles(directory, searchPattern, SearchOption.TopDirectoryOnly);

        foreach (string file in files)
        {
            string fileName = Path.GetFileNameWithoutExtension(file);
            if (fileName.IndexOf(animName, StringComparison.OrdinalIgnoreCase) >= 0)
            {
                string assetPath = file.Replace('\\', '/');
                if (assetPath.StartsWith(Application.dataPath))
                {
                    assetPath = "Assets" + assetPath.Substring(Application.dataPath.Length);
                }

                AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(assetPath);
                if (clip != null)
                {
                    return clip;
                }
            }
        }

        return null;
    }

    #endregion

    #region FBX动画查找和结果显示方法

    /// <summary>
    /// 在FBX文件中查找动画
    /// </summary>
    private AnimationClip FindAnimationClipInFBX(string directory, string animName)
    {
        if (string.IsNullOrEmpty(animName))
            return null;

        string[] fbxFiles = Directory.GetFiles(directory, "*.fbx", SearchOption.TopDirectoryOnly);

        foreach (string fbxFile in fbxFiles)
        {
            string assetPath = fbxFile.Replace('\\', '/');
            if (assetPath.StartsWith(Application.dataPath))
            {
                assetPath = "Assets" + assetPath.Substring(Application.dataPath.Length);
            }

            UnityEngine.Object[] assets = AssetDatabase.LoadAllAssetsAtPath(assetPath);
            foreach (var asset in assets)
            {
                if (asset is AnimationClip clip && !clip.name.StartsWith("__preview__"))
                {
                    if (clip.name.IndexOf(animName, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        return clip;
                    }
                }
            }
        }

        return null;
    }

    /// <summary>
    /// 显示扫描和修复结果
    /// </summary>
    private void ShowResults()
    {
        int totalScanned = scanResults.Count;
        int needsFix = scanResults.Count(r => r.needsFix);
        int _fixed = fixResults.Count(r => r.success);
        int failed = fixResults.Count(r => !r.success);

        string message = $"扫描完成!\n\n" +
                        $"总共扫描: {totalScanned} 个文件\n" +
                        $"需要修复: {needsFix} 个文件\n" +
                        $"修复成功: {_fixed} 个文件\n" +
                        $"修复失败: {failed} 个文件";

        EditorUtility.DisplayDialog("扫描结果", message, "确定");

        if (enableDetailedLogging)
        {
            Debug.Log("=== OverrideController绑定修复工具 - 执行结果 ===");
            Debug.Log(message);

            if (fixResults.Count > 0)
            {
                Debug.Log("\n=== 详细修复结果 ===");
                foreach (var result in fixResults)
                {
                    string status = result.success ? "成功" : "失败";
                    Debug.Log($"[{status}] {result.filePath} - {result.details}");
                }
            }
        }
    }

    #endregion
}
