﻿using System;
using System.Collections;
using System.IO;
using Nirvana;
using UnityEngine;
using UnityEngine.Networking;
using System.Collections.Generic;
using System.Text;

public static class UtilU3d
{
    private static Dictionary<string, System.Object> cacheDataDic = new Dictionary<string, System.Object>();

    public static void CacheData(string key, System.Object value)
    {
        cacheDataDic[key] = value;
    }

    public static void DelCacheData(string key, System.Object value)
    {
        cacheDataDic[key] = null;
    }

    public static System.Object GetCacheData(string key)
    {
        return cacheDataDic.ContainsKey(key) ? cacheDataDic[key] : null;
    }

    public static void SetFilterLogType(LogType logType)
    {
        Debug.unityLogger.filterLogType = logType;
    }

    public static void RequestGet(string url)
    {
        GameRoot.Instance.StartCoroutine(
            RequestGetHelper(url));
    }

    public static void RequestGet(
        string url, Action<bool, string> complete)
    {
        GameRoot.Instance.StartCoroutine(
            RequestGetHelper(url, complete));
    }

    public static void RequestPost(string url, string postData)
    {
        GameRoot.Instance.StartCoroutine(RequestPostHelper(url, postData));
    }

    public static void RequestPost(string url, string postData, Action<bool, string> complete)
    {
        GameRoot.Instance.StartCoroutine(RequestPostHelper(url, postData, complete));
    }

    public static void RequestWWWFormPost(string url, string postData)
    {
        GameRoot.Instance.StartCoroutine(RequestWWWFormPostHlper(url, postData));
    }

    public static void RequestWWWFormPost(string url, string postData, Action<bool, string> complete)
    {
        GameRoot.Instance.StartCoroutine(RequestWWWFormPostHlper(url, postData, complete));
    }

    public static void RequestJsonPost(string url, string postJsonData)
    {
        GameRoot.Instance.StartCoroutine(RequestJsonPostHlper(url, postJsonData));
    }

    public static void RequestJsonPost(string url, string postJsonData, Action<bool, string> complete)
    {
        GameRoot.Instance.StartCoroutine(RequestJsonPostHlper(url, postJsonData, complete));
    }


    public static void Download(
        string url, string path, Action<bool, string> complete)
    {
        GameRoot.Instance.StartCoroutine(DownloadHelper(url, path, complete));
    }

    public static bool Upload(
        string url, string path, Action<bool, string> complete)
    {
        try
        {
            using (var file = File.OpenRead(path))
            {
                var bodyData = new byte[file.Length];
                file.Read(bodyData, 0, bodyData.Length);
                GameRoot.Instance.StartCoroutine(
                    UploadHelper(url, bodyData, complete));
                return true;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("Upload Exception:" + ex.Message);
            return false;
        }
    }

    private static IEnumerator RequestGetHelper(string url)
    {
        using (var www = UnityWebRequest.Get(url))
        {
            yield return www.SendWebRequest();
        }
    }
    private static IEnumerator RequestPostHelper(string url, string postData)
    {
        using (var www = UnityWebRequest.Post(url, postData))
        {
            yield return www.SendWebRequest();
        }
    }

    private static IEnumerator RequestGetHelper(string url, Action<bool, string> complete)
    {
        using (var www = UnityWebRequest.Get(url))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                if (www.downloadHandler == null)
                {
                    complete(false, "DownloadHandler is null");
                }
                else
                {
                    complete(true, www.downloadHandler.text ?? string.Empty);
                }
            }
        }
    }
    private static IEnumerator RequestPostHelper(string url, string postData, Action<bool, string> complete)
    {
        using (var www = UnityWebRequest.Post(url, postData))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                if (www.downloadHandler == null)
                {
                    complete(false, "DownloadHandler is null");
                }
                else
                {
                    complete(true, www.downloadHandler.text ?? string.Empty);
                }
            }
        }
    }

    private static IEnumerator DownloadHelper(
        string url, string path, Action<bool, string> complete)
    {
        using (var www = UnityWebRequest.Get(url))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                try
                {
                    // 检查downloadHandler是否为null
                    if (www.downloadHandler == null)
                    {
                        complete(false, "DownloadHandler is null");
                        yield break;
                    }

                    // 检查data是否为null
                    var data = www.downloadHandler.data;
                    if (data == null)
                    {
                        complete(false, "Downloaded data is null");
                        yield break;
                    }

                    var dir = Path.GetDirectoryName(path);
                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(dir);
                    }

                    using (var file = File.Create(path))
                    {
                        file.Write(data, 0, data.Length);
                    }

                    complete(true, string.Empty);
                }
                catch (Exception ex)
                {
                    Debug.LogError("DownloadHelper Exception:" + ex.Message);
                    complete(false, "Exception: " + ex.Message);
                }
            }
        }
    }

    /// <summary>
    /// 封装表单数据，传入字符串格式："xx=xx&xx==xx"
    /// </summary>
    private static Dictionary<string, string> WWWFormPostDataSplit(string postString)
    {
        Dictionary<string, string> return_dic = new Dictionary<string, string>();
        string[] form_list = postString.Split('&');
        if (form_list != null && form_list.Length > 0)
        {
            for (int i = 0; i < form_list.Length; i++)
            {
                string[] key_velue_pair = form_list[i].Split('=');
                if (!string.IsNullOrEmpty(key_velue_pair[1]) && !string.IsNullOrEmpty(key_velue_pair[2]))
                {
                    return_dic.Add(key_velue_pair[1], key_velue_pair[2]);
                }
            }
        }
        return return_dic;
    }

    /// <summary>
    /// x-www-form-urlencoded POST请求方式
    /// </summary>
    private static IEnumerator RequestWWWFormPostHlper(string url, string postString)
    {
        Dictionary<string, string> postData = WWWFormPostDataSplit(postString);

        WWWForm form = new WWWForm();
        if (postData != null && postData.Count > 0)
        {
            foreach (var item in postData)
            {
                form.AddField(item.Key, item.Value);
            }
        }

        using (var www = UnityWebRequest.Post(url, form))
        {
            yield return www.SendWebRequest();
        }
    }

    /// <summary>
    /// x-www-form-urlencoded POST请求方式
    /// </summary>
    private static IEnumerator RequestWWWFormPostHlper(string url, string postString, Action<bool, string> complete)
    {
        Dictionary<string, string> postData = WWWFormPostDataSplit(postString);

        WWWForm form = new WWWForm();
        if (postData != null && postData.Count > 0)
        {
            foreach (var item in postData)
            {
                form.AddField(item.Key, item.Value);
            }
        }

        using (var www = UnityWebRequest.Post(url, form))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                if (www.downloadHandler == null)
                {
                    complete(false, "DownloadHandler is null");
                }
                else
                {
                    complete(true, www.downloadHandler.text ?? string.Empty);
                }
            }
        }
    }

    /// <summary>
    /// application/json POST请求方式
    /// </summary>
    private static IEnumerator RequestJsonPostHlper(string url, string postJsonData)
    {
        using (UnityWebRequest www = new UnityWebRequest(url, "POST"))
        {
            byte[] postBytes = Encoding.UTF8.GetBytes(postJsonData);
            www.uploadHandler = (UploadHandler)new UploadHandlerRaw(postBytes);
            www.downloadHandler = (DownloadHandler)new DownloadHandlerBuffer();
            www.SetRequestHeader("Content-Type", "application/json");

            yield return www.SendWebRequest();
        }
    }

    /// <summary>
    /// application/json POST请求方式
    /// </summary>
    private static IEnumerator RequestJsonPostHlper(string url, string postJsonData, Action<bool, string> complete)
    {
        using (UnityWebRequest www = new UnityWebRequest(url, "POST"))
        {
            byte[] postBytes = Encoding.UTF8.GetBytes(postJsonData);
            www.uploadHandler = (UploadHandler)new UploadHandlerRaw(postBytes);
            www.downloadHandler = (DownloadHandler)new DownloadHandlerBuffer();
            www.SetRequestHeader("Content-Type", "application/json");

            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                if (www.downloadHandler == null)
                {
                    complete(false, "DownloadHandler is null");
                }
                else
                {
                    complete(true, www.downloadHandler.text ?? string.Empty);
                }
            }
        }
    }

    private static IEnumerator DownloadHelper(
    string url, string postData, string path, Action<bool, string> complete)
    {
        using (var www = UnityWebRequest.Post(url, postData))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                try
                {
                    // 检查downloadHandler是否为null
                    if (www.downloadHandler == null)
                    {
                        complete(false, "DownloadHandler is null");
                        yield break;
                    }

                    // 检查data是否为null
                    var data = www.downloadHandler.data;
                    if (data == null)
                    {
                        complete(false, "Downloaded data is null");
                        yield break;
                    }

                    var dir = Path.GetDirectoryName(path);
                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(dir);
                    }

                    using (var file = File.Create(path))
                    {
                        file.Write(data, 0, data.Length);
                    }

                    complete(true, string.Empty);
                }
                catch (Exception ex)
                {
                    Debug.LogError("DownloadHelper Exception:" + ex.Message);
                    complete(false, "Exception: " + ex.Message);
                }
            }
        }
    }

    private static IEnumerator UploadHelper(
        string url, byte[] bodyData, Action<bool, string> complete)
    {
        using (var www = UnityWebRequest.Put(url, bodyData))
        {
            yield return www.SendWebRequest();

            if (www.result != UnityWebRequest.Result.Success)
            {
                complete(false, www.error ?? "Network request failed");
            }
            else
            {
                complete(true, string.Empty);
            }
        }
    }

    public static float WatchFunRunTime(Action action, float log_time = 0, string format = "")
    {
        System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
        sw.Start();
        action();
        sw.Stop();
        if (sw.ElapsedMilliseconds >= log_time)
        {
            Debug.Log(string.Format("{0} WatchFunRunTime :  {1}", format, sw.ElapsedMilliseconds));
        }

        return sw.ElapsedMilliseconds;
    }

    public static bool AudioPlayerIsPlaying(IAudioPlayer IAudioPlayer)
    {
        return IAudioPlayer.IsPlaying;
    }

    public static void StopAudioPlayer(IAudioPlayer IAudioPlayer)
    {
        IAudioPlayer.Stop();
    }

    public static void Screenshot(Action<Texture2D> callback, int size, bool convertJPG)
    {
        Scheduler.RunCoroutine(CaptureScreenshotAsTexture(callback, size, convertJPG));
    }

    public static void ScreenshotByJpg(Action<Texture2D> callback)
    {
        Scheduler.RunCoroutine(CaptureScreenshotAsTexture(callback, 1, true));
    }

    public static void ScreenshotByPng(Action<Texture2D> callback)
    {
        Scheduler.RunCoroutine(CaptureScreenshotAsTexture(callback, 1, false));
    }

    private static IEnumerator CaptureScreenshotAsTexture(Action<Texture2D> callback, int size, bool convertJPG)
    {
        yield return new WaitForEndOfFrame();
        var texture = ScreenCapture.CaptureScreenshotAsTexture(size);
        if (convertJPG)
        {
            var bytes = ImageConversion.EncodeToJPG(texture);
            Texture2D jpg = new Texture2D(texture.width, texture.height, TextureFormat.RGBA32, false, false);
            UnityEngine.GameObject.Destroy(texture);
            if (jpg.LoadImage(bytes))
                callback(jpg);
            else
                callback(null);
        }
        else
        {
            var bytes = ImageConversion.EncodeToPNG(texture);
            Texture2D png = new Texture2D(texture.width, texture.height, TextureFormat.RGBA32, false, false);
            UnityEngine.GameObject.Destroy(texture);
            if (png.LoadImage(bytes))
                callback(png);
            else
                callback(null);
        }
    }

    public static bool SaveScreenshot(Texture2D source, string path, Texture2D frame = null, bool saveByPNG = false)
    {
        var target = PhotoTool.PhotoAddFrame(source, frame);
        bool succ = true;
        try
        {
            RuntimeAssetHelper.InsureDirectory(path);
            byte[] bytes = saveByPNG ? target.EncodeToPNG() : target.EncodeToJPG();
            if (target != source)
            {
                UnityEngine.GameObject.Destroy(target);
            }
            File.WriteAllBytes(path, bytes);
        }
        catch (System.Exception)
        {
            succ = false;
        }

        return succ;
    }

    public static void Screenshot(string path, System.Action<bool, string> callback, int size = 1)
    {
        Scheduler.RunCoroutine(CaptureScreenshot(path, callback, size));
    }

    private static IEnumerator CaptureScreenshot(string path, System.Action<bool, string> callback, int size)
    {
        string newPath = path;
#if !UNITY_EDITOR
        newPath = path.Replace(Application.persistentDataPath + "/", "");
#endif
        UnityEngine.ScreenCapture.CaptureScreenshot(newPath, size);
        float time = Time.time;
        bool b = false;
        yield return new WaitUntil(() =>
        {
            b = System.IO.File.Exists(newPath);
            return b || ((Time.time - time) > 1f);
        });
        string str = newPath;
        if (b == false)
        {
            str = "截屏出错！";
        }
        if (callback != null)
        {
            callback(b, str);
        }
    }

    public static void DeleteFile(string path)
    {
        bool isExist = File.Exists(path);
        if (isExist)
        {
            File.Delete(path);
        }
    }

    //强制重设摄像机
    public static void ForceReSetCamera()
    {
        var mainCamera = Camera.main;
        if (mainCamera != null && mainCamera.isActiveAndEnabled)
        {
            var cameraFollow = mainCamera.GetComponentInParent<CameraFollow>();
            if (null != cameraFollow)
            {
                cameraFollow.FieldOfView = 0;
            }
        }
    }

    // 路径 和 限制最大字节 返回 1文件不存在 2文件过大
    public static int IsFileInfoLimit(string path, int length)
    {
        if (!File.Exists(path))
        {
            return 1;
        }
        FileInfo fileInfo = new FileInfo(path);
        if (fileInfo.Length <= length)
        {
            return 0;
        }
        return 2;
    }

    // 返回KB
    public static int GetFileInfoLength(string path)
    {
        if (!File.Exists(path))
        {
            return -1;
        }
        FileInfo fileInfo = new FileInfo(path);
        long legnth_kb = fileInfo.Length / 1024;
        int legnth = (int)legnth_kb;
        return legnth;
    }

    public static string GetGameObjectFullPath(GameObject gameObject)
    {
        if (null == gameObject) return string.Empty;
        Transform[] transforms = gameObject.GetComponentsInParent<Transform>(true);
        StringBuilder builder = new StringBuilder();
        for (int i = transforms.Length - 1; i >= 0;  -- i)
        {
            if (i  < transforms.Length - 1) builder.Append("/");
            builder.Append(transforms[i].name);
        }

        return builder.ToString();
    }

    // 把文字复制到剪贴板（预留接口）
    public static void CopyToClipboard(string content)
    {

    }
    public static void SetRendererLayer(GameObject obj, int layer)
    {
        if (null == obj) return;

        List<Renderer> list = ListPool<Renderer>.Get();
        obj.GetComponentsInChildren<Renderer>(list);
        for (int i = 0; i < list.Count; i++)
        {
            list[i].gameObject.layer = layer;
        }

        ListPool<Renderer>.Release(list);
    }

    public static void SetLayer(GameObject obj, int layer)
    {
        if (null == obj) return;

        List<Transform> list = ListPool<Transform>.Get();
        obj.GetComponentsInChildren<Transform>(list);
        for (int i = 0; i < list.Count; i++)
        {
            list[i].gameObject.layer = layer;
        }

        ListPool<Transform>.Release(list);
    }

    public static string ConvertColorToHex(Color now_color)
    {
        if (now_color != null)
        {
            string hex_color = ColorUtility.ToHtmlStringRGBA(now_color);
            return hex_color;
        }

        return "";
    }

    public static Vector3 ConvertColorToHSV(Color now_color)
    {
        float H, S, V;
        if (now_color != null)
        {
            Color.RGBToHSV(now_color, out H, out S, out V);
            return new Vector3(H, S, V);
        }

        return Vector3.zero;
    }

    public static Color ConvertHexToColor(string hex)
    {
        hex = hex.Trim().Replace("#", "");
        switch (hex.Length)
        {
            case 3: // RGB简写格式
                hex = $"{hex[0]}{hex[0]}{hex[1]}{hex[1]}{hex[2]}{hex[2]}FF";
                break;
            case 6: // RGB完整格式
                hex += "FF";
                break;
            case 8: // RGBA格式
                break;
            default:
                return Color.black;
        }

        try
        {
            uint argb = uint.Parse(hex, System.Globalization.NumberStyles.HexNumber);
            return new Color(
                ((argb >> 24) & 0xFF) / 255f,
                ((argb >> 16) & 0xFF) / 255f,
                ((argb >> 8) & 0xFF) / 255f,
                (argb & 0xFF) / 255f
            );
        }
        catch
        {
            return Color.black;
        }
    }

    public static Color ConvertHSVToColor(Vector3 HSV)
    {
        Color color = Color.HSVToRGB(HSV.x, HSV.y, HSV.z);
        color.a = 1;
        return color;
    }
}
