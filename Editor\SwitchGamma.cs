﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;

public class SwitchGamma
{
    private const string ShaderFilePath1 = "Assets/Game/Shaders/UIZTestOff.shader";
    private const string ShaderFilePath2 = "Assets/Game/Shaders/TMP/TMP_SDF_Mobile.shader";
    private const string ShaderFilePath3 = "Assets/Game/Shaders/TMP/TMP_SDF_MobileMasking.shader";
    private const string ShaderFilePath4 = "Assets/Game/Shaders/TMP/TMP_SDF_MobileOverlay.shader";

    private static string shaderContent = "";

    [MenuItem("切换Gamma/Scene窗口")]
    public static void ChangeToScene()
    {
        ReplaceFileStr(ShaderFilePath1, "color.rgb = LinearToGamma22(color.rgb)", "color.rgb = color.rgb");
        ReplaceFileStr(ShaderFilePath2, "= LinearToGamma22(input.color.rgb)", "= input.color.rgb");
        ReplaceFileStr(ShaderFilePath3, "= LinearToGamma22(input.color.rgb)", "= input.color.rgb");
        ReplaceFileStr(ShaderFilePath4, "= LinearToGamma22(input.color.rgb)", "= input.color.rgb");
        AssetDatabase.Refresh();
    }

    [MenuItem("切换Gamma/Game窗口")]
    public static void ChangeToGame()
    {
        ReplaceFileStr(ShaderFilePath1, "color.rgb = color.rgb", "color.rgb = LinearToGamma22(color.rgb)");
        ReplaceFileStr(ShaderFilePath2, "= input.color.rgb", "= LinearToGamma22(input.color.rgb)");
        ReplaceFileStr(ShaderFilePath3, "= input.color.rgb", "= LinearToGamma22(input.color.rgb)");
        ReplaceFileStr(ShaderFilePath4, "= input.color.rgb", "= LinearToGamma22(input.color.rgb)");
        AssetDatabase.Refresh();
    }

    private static void ReplaceFileStr(string path, string oldStr, string newStr)
    {
        string fullPath = Path.Combine(Application.dataPath, path.Replace("Assets/", ""));
        if (File.Exists(fullPath))
        {
            shaderContent = File.ReadAllText(fullPath);
            Debug.Log("shader读取成功");
            string newContent = shaderContent.Replace(oldStr, newStr);
            File.WriteAllText(fullPath, newContent, System.Text.Encoding.UTF8);
        }
    }
}
