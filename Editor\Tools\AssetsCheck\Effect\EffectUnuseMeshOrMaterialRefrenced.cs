﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using AssetsCheck;
using UnityEditor;
using UnityEngine;

public class EffectUnuseMeshOrMaterialRefrenced : BaseChecker
{
    string[] checkRoots = new string[] {
            "Assets/Game/Model",
            "Assets/Game/Effects/Prefab",
        };

    public override string GetErrorDesc()
    {
        return "检测ParticleSystem是否有无用mesh、material资源引用未清除";
    }

    protected override void OnCheck()
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);
        int index = 0;
        int count = guids.Length;
        try
        {
            foreach (string guid in guids)
            {
                string asset = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath(asset, typeof(GameObject)) as GameObject;

                ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
                if (null != particleSystems && particleSystems.Length > 0)
                {
                    CheckItem checkItem = new CheckItem();
                    checkItem.asset = asset;
                    bool isDirty = false;
                    foreach (ParticleSystem ps in particleSystems)
                    {
                        ParticleSystemRenderer psRender = ps.GetComponent<ParticleSystemRenderer>();
                        if (null != psRender)
                        {
                            if (psRender.enabled)
                            {
                                if (psRender.renderMode != ParticleSystemRenderMode.Mesh && psRender.mesh != null)
                                {
                                    isDirty = true;
                                    checkItem.asset += string.Format("| ({0})=>UnuseRenderMesh", psRender.name);
                                    if (psRender.renderMode == ParticleSystemRenderMode.None && psRender.sharedMaterial != null)
                                    {
                                        checkItem.asset += string.Format("| ({0})=>UnuseRenderMaterial", psRender.name);
                                    }
                                }
                                if (!ps.trails.enabled && psRender.trailMaterial != null)
                                {
                                    isDirty = true;
                                    checkItem.asset += string.Format("| ({0})=>UnuseTrailMaterial", psRender.name);
                                }
                            }
                            else if (null == ps.gameObject.GetComponent<UiParticles.UiParticles>())
                            {
                                isDirty = true;
                                checkItem.asset += string.Format("| ({0})=>ParticleSystemRenderNotEnable", psRender.name);
                            }
                        }

                        var shape = ps.shape;
                        if (shape.enabled
                            && shape.shapeType != ParticleSystemShapeType.Mesh
                             && shape.shapeType != ParticleSystemShapeType.MeshRenderer
                              && shape.shapeType != ParticleSystemShapeType.SkinnedMeshRenderer
                              && shape.mesh != null)
                        {
                            isDirty = true;
                            checkItem.asset += string.Format("| ({0})=>UnuseShapeMesh", psRender.name);
                        }
                        else if (!shape.enabled)
                        {
                            if (shape.mesh != null)
                            {
                                isDirty = true;
                                checkItem.asset += string.Format("| ({0})=>ShapeNotEnableButShapeMeshExisted", psRender.name);
                            }
                            if(shape.texture != null)
                            {
                                isDirty = true;
                                checkItem.asset += string.Format("| ({0})=>ShapeNotEnableButShapeTextureExisted", psRender.name);
                            }
                        }
                    }

                    if (isDirty)
                    {
                        this.outputList.Add(checkItem);
                        //EditorUtility.SetDirty(prefab);
                        //AssetDatabase.SaveAssets();
                        //AssetDatabase.Refresh();
                    }
                }

                index++;
                EditorUtility.DisplayProgressBar("删除ParticleSyatem无用mesh、trailMaterial引用", string.Format("进度: {0} / {1}", index, count), (float)index / (float)count);
            }
        }
        catch (System.Exception ex)
        {
            EditorUtility.ClearProgressBar();
            Debug.LogError(ex.ToString());
        }

        EditorUtility.ClearProgressBar();

        //StringBuilder builder = new StringBuilder();
        //foreach (var item in this.outputList)
        //{
        //    builder.Append(item.Output());
        //    builder.Append("\n");
        //}

        //File.WriteAllText(Path.Combine(AssetsCheckConfig.OutputDir, "EffectUnuseMeshOrMaterialRefrenced.txt"), builder.ToString());
    }

    protected override void OnFix(string[] lines)
    {
        string[] assets;
        if (lines == null || lines.Length == 0)
        {
            List<string> assetList = new List<string>();
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);
            foreach (string guid in guids)
            {
                string asset = AssetDatabase.GUIDToAssetPath(guid);
                assetList.Add(asset);
            }
            assets = assetList.ToArray();
        }
        else
        {
            assets = lines;
        }

        int index = 0;
        int count = assets.Length;
        try
        {
            foreach (string asset in assets)
            {
                string __asset = asset;
                if (asset.Contains("|"))
                {
                    __asset = asset.Split('|')[0];
                }
                GameObject prefab = AssetDatabase.LoadAssetAtPath(__asset, typeof(GameObject)) as GameObject;
                if (null != prefab)
                {
                    GameObject gameObject = GameObject.Instantiate(prefab);
                    ParticleSystem[] particleSystems = gameObject.GetComponentsInChildren<ParticleSystem>();
                    if (null != particleSystems && particleSystems.Length > 0)
                    {
                        bool isDirty = false;

                        for (int i = 0; i < particleSystems.Length; i++)
                        {
                            ParticleSystem ps = particleSystems[i];
                            ParticleSystemRenderer psRender = ps.GetComponent<ParticleSystemRenderer>();
                            if (null == psRender)
                            {
                                GameObject.DestroyImmediate(particleSystems[i]);
                                isDirty = true;
                            }
                            else
                            {

                                if (psRender.enabled)
                                {
                                    if (psRender.renderMode != ParticleSystemRenderMode.Mesh && psRender.mesh != null)
                                    {
                                        psRender.mesh = null;
                                        isDirty = true;
                                        if (psRender.renderMode == ParticleSystemRenderMode.None && psRender.sharedMaterial != null)
                                        {
                                            psRender.sharedMaterial = null;
                                        }
                                    }
                                    if (!ps.trails.enabled && psRender.trailMaterial != null)
                                    {
                                        psRender.trailMaterial = null;
                                        isDirty = true;
                                    }
                                }
                                else if (null == ps.gameObject.GetComponent<UiParticles.UiParticles>())
                                {
                                    //先删除ParticleSystemRenderer，否则会残留部分序列化数据
                                    if (null != ps.gameObject.GetComponent<ParticleSystemRenderer>())
                                    {
                                        GameObject.DestroyImmediate(ps.gameObject.GetComponent<ParticleSystemRenderer>());
                                    }
                                    GameObject.DestroyImmediate(particleSystems[i]);
                                    isDirty = true;
                                }

                                var shape = ps.shape;
                                if (shape.enabled
                                    && shape.shapeType != ParticleSystemShapeType.Mesh
                                     && shape.shapeType != ParticleSystemShapeType.MeshRenderer
                                      && shape.shapeType != ParticleSystemShapeType.SkinnedMeshRenderer
                                      && shape.mesh != null)
                                {
                                    shape.mesh = null;
                                    isDirty = true;
                                }
                                else if (!shape.enabled)
                                {
                                    if (shape.mesh != null)
                                    {
                                        shape.mesh = null;
                                        isDirty = true;
                                    }
                                    if (shape.texture != null)
                                    {
                                        shape.texture = null;
                                        isDirty = true;
                                    }
                                }
                            }
                        }

                        if (isDirty)
                        {
                            PrefabUtility.SaveAsPrefabAsset(gameObject, __asset);
                        }
                        GameObject.DestroyImmediate(gameObject);
                    }
                }

                index++;
                EditorUtility.DisplayProgressBar("删除ParticleSyatem无用mesh、trailMaterial引用", string.Format("进度: {0} / {1}", index, count), (float)index / (float)count);
            }
        }
        catch (System.Exception ex)
        {
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.ClearProgressBar();
            Debug.LogError(ex.ToString());
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        EditorUtility.ClearProgressBar();
    }

    struct CheckItem : ICheckItem
    {
        public string asset;

        public string MainKey
        {
            get { return this.asset; }
        }

        public StringBuilder Output()
        {
            StringBuilder builder = new StringBuilder();
            builder.Append(string.Format("{0}", asset));

            return builder;
        }
    }
}
