﻿using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using System.Collections.Generic;

public class AnimationModifyWindow : OdinEditorWindow
{
    [MenuItem("自定义工具/动画工具/已有动画参数整体修改")]
    static void OpenWindow()
    {
        GetWindow<AnimationModifyWindow>().Show();
    }
    [InfoBox("先分析动画文件,然后选择对应的属性,给对应属性的所有关键帧添加偏移值")]
    [InfoBox("只支持数值类型")]

    public AnimationClip clip;

    [LabelText("偏移值")]
    public float offset;

    [PropertyOrder(-1)]
    [Button("分析动画", ButtonSizes.Large), GUIColor(0f, 0.8f, 1f, 1f)]
    void Check()
    {
        SortList.Clear();
        if (clip!=null)
        {
            List<string> pathList = new List<string>();
            foreach (var binding in AnimationUtility.GetCurveBindings(clip))
            {
                AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                AnimationModifyItem animationModifyItem = new AnimationModifyItem(binding, curve);
                SortList.Add(new ValueDropdownItem<AnimationModifyItem>(binding.path+"/" +binding.propertyName, animationModifyItem));
                if(!pathList.Contains(binding.path))
                {
                    pathList.Add(binding.path);
                }
            }
            foreach (var path in pathList)
            {
                SortList.Add(new ValueDropdownItem<AnimationModifyItem>(path, null));
            }
        }
    }

    [PropertyOrder(-1)]
    [Button("修改", ButtonSizes.Large), GUIColor(0f, 0.8f, 1f, 1f)]
    void Modify()
    {
        if(Select!=null)
        {
            //Select.curve[1].value
            List<Keyframe> keyframeList = new List<Keyframe>();
            for (int i = 0; i < Select.curve.length; i++)
            {
                var frame = Select.curve[i];
                frame.value += offset;
                keyframeList.Add(frame);
            }
            AnimationCurve modifiedCurve = new AnimationCurve(keyframeList.ToArray());
            AnimationUtility.SetEditorCurve(clip, Select.binding, modifiedCurve);
            EditorUtility.SetDirty(clip);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }

    [ValueDropdown("SortList")]
    public AnimationModifyItem Select;

    private List<ValueDropdownItem<AnimationModifyItem>> SortList = new List<ValueDropdownItem<AnimationModifyItem>>();

    public class AnimationModifyItem
    {
        public AnimationModifyItem(EditorCurveBinding binding, AnimationCurve curve)
        {
            this.binding = binding;
            this.curve = curve;
        }
        public EditorCurveBinding binding;
        public AnimationCurve curve;
    }

}