﻿using System;
using UnityEngine;
using UnityEditor;
using System.IO;
using Nirvana;
using System.Collections.Generic;
using System.Text;
using System.Collections;

class ActorActionConfigBuilder : Editor
{
    enum ActorActionConfigType {
        RoleAction, TianShenAction, MonsterAction, WuhunAction, ZuoqiAction, YushouAction, GundamAction, ShuangShengTianShenAction
    }

    static Dictionary<ActorActionConfigType, List<string>> animNameDic = new Dictionary<ActorActionConfigType, List<string>>() 
    {
        { ActorActionConfigType.RoleAction, 
          new List<string>(){ "attack1", "attack2", "attack3", "attack4", "attack5", "attack6", "attack7",
                "attack8", "attack9", "attack10", "attack11", "attack12", "attack13", "attack14", "attack15",
				"combo1_1", "combo1_2", "combo1_3", "combo1_4",
                "esoterica_attack_1", "esoterica_attack_2", "esoterica_attack_3", "esoterica_attack_4", "esoterica_attack_5",
                "esoterica_attack_6", "esoterica_attack_7", "esoterica_attack_8", "esoterica_attack_9", "esoterica_attack_10",
                "custom_action_1", "custom_action_2", "custom_action_3", "custom_action_4", "custom_action_5",
                "custom_action_6", "custom_action_7", "custom_action_8", "custom_action_9", "custom_action_10",
                "custom_action_11", "custom_action_12","fall_rest", "prizedraw",
                "jump", "jump2"} },
        { ActorActionConfigType.TianShenAction,
           new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1", "combo1_2", "combo1_3", "combo1_4", "d_chuchang" } },
        { ActorActionConfigType.MonsterAction,
           new List<string>(){ "attack1", "attack2", "attack3", "attack4", "attack5", "magic1_1", "magic1_2",
                               "magic1_3", "magic2_1", "magic2_2", "magic2_3", "magic3_1", "magic3_2", "magic3_3", "magic4_1", "magic4_2", "magic4_3", "hurt", "rest", "dead", "die_fly" } },
		{ ActorActionConfigType.WuhunAction, 
			new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo2_1", "combo2_2", "combo2_3", "combo2_4", "combo1_1", "combo1_2", "combo1_3", "combo1_4" } },
        { ActorActionConfigType.ZuoqiAction,
           new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1", "rest", "dead" } },
        { ActorActionConfigType.YushouAction,
           new List<string>(){ "attack1", "attack2", "attack3", "magic1_1", "magic1_2", "magic1_3"} },
        { ActorActionConfigType.GundamAction,
           new List<string>(){ "attack1_1", "attack1_2", "attack1_3", "attack1_4", "attack1_5",
                               "attack2_1", "attack2_2", "attack2_3", "attack2_4", "attack2_5",
                               "attack3_1", "attack3_2", "attack3_3", "attack3_4", "attack3_5",
                               "combo1_1", "combo1_2", "combo1_3", "combo1_4", "combo1_5",
                               "combo2_1", "combo2_2", "combo2_3", "combo2_4", "combo2_5",
                               "combo3_1", "combo3_2", "combo3_3", "combo3_4", "combo3_5",
                               "rush_1", "rush_2", "rush_3",} },
        { ActorActionConfigType.ShuangShengTianShenAction,
            new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1", "combo1_2", "combo1_3", "combo1_4", "d_chuchang" } },
    };

    static Dictionary<ActorActionConfigType, List<string>> needHitTimeAnimDic = new Dictionary<ActorActionConfigType, List<string>>()
    {
        { ActorActionConfigType.RoleAction,
          new List<string>(){ "attack1", "attack2", "attack3", "attack4", "attack5", "attack6", "attack7",
                "attack8", "attack9", "attack10", "attack11", "attack12", "attack13", "attack14", "attack15",
                "combo1_1", "combo1_2", "combo1_3", "combo1_4",
                "esoterica_attack_1", "esoterica_attack_2", "esoterica_attack_3", "esoterica_attack_4", "esoterica_attack_5",
                "esoterica_attack_6", "esoterica_attack_7", "esoterica_attack_8", "esoterica_attack_9", "esoterica_attack_10",
                "custom_action_1", "custom_action_2", "custom_action_3", "custom_action_4", "custom_action_5",
                "custom_action_6", "custom_action_7", "custom_action_8", "custom_action_9", "custom_action_10",
                "custom_action_11", "custom_action_12",} },
        { ActorActionConfigType.TianShenAction,
          new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1", "combo1_2", "combo1_3", "combo1_4" } },
        { ActorActionConfigType.MonsterAction,
          new List<string>(){ "attack1", "attack2", "attack3", "attack4", "attack5", "magic1_1", "magic1_2", "magic1_3", "magic2_1", "magic2_2", "magic2_3", "magic3_1", "magic3_2", "magic3_3", "magic4_1", "magic4_2", "magic4_3", } },
		{ ActorActionConfigType.WuhunAction,
			new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo2_1", "combo2_2", "combo2_3", "combo2_4", "combo1_1", "combo1_2", "combo1_3", "combo1_4" } },
        { ActorActionConfigType.ZuoqiAction,
            new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1"} },
        { ActorActionConfigType.YushouAction,
            new List<string>(){ "attack1", "attack2", "attack3", "magic1_1", "magic1_2", "magic1_3" } },
        { ActorActionConfigType.GundamAction,
           new List<string>(){ "attack1_1", "attack1_2", "attack1_3", "attack1_4", "attack1_5",
                               "attack2_1", "attack2_2", "attack2_3", "attack2_4", "attack2_5",
                               "attack3_1", "attack3_2", "attack3_3", "attack3_4", "attack3_5",
                               "combo1_1", "combo1_2", "combo1_3", "combo1_4", "combo1_5",
                               "combo2_1", "combo2_2", "combo2_3", "combo2_4", "combo2_5",
                               "combo3_1", "combo3_2", "combo3_3", "combo3_4", "combo3_5",
                               "rush_1", "rush_2", "rush_3",} },
        { ActorActionConfigType.ShuangShengTianShenAction,
          new List<string>(){ "attack1", "attack2", "attack3", "attack4", "combo1_1", "combo1_2", "combo1_3", "combo1_4" } },
    };



    static string monsterConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/monster_action_config.lua";
    static string tianshenConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/tianshen_boss_action_config.lua";
    static string roleConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/role_action_config.lua";
	static string wuhunConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/wuhun_action_config.lua";
    static string zuoqiConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/zuoqi_action_config.lua";
    static string yushouConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/yushou_action_config.lua";
    static string gundamConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/gundam_action_config.lua";
    static string shuangshengtianshenConfigPath = "Assets/Game/Lua/game/scene/sceneobj/action_config/shuangsheng_tianshen_action_config.lua";


    static Dictionary<ActorActionConfigType, string[]> modelPathDic = new Dictionary<ActorActionConfigType, string[]>() {
        { ActorActionConfigType.RoleAction, new string[]{
                                                    "Assets/Game/Actors/Character/RoleMan/1101001",
                                                    //"Assets/Game/Actors/Character/RoleMan/1102001",
                                                    "Assets/Game/Actors/Character/RoleMan/1103001",
                                                    //"Assets/Game/Actors/Character/RoleMan/1104001",
                                                    "Assets/Game/Actors/Character/RoleMan/Realm/110100101",
                                                    "Assets/Game/Actors/Character/RoleMan/Realm/110100102",
                                                    "Assets/Game/Actors/Character/RoleMan/Realm/110100103",
                                                    "Assets/Game/Actors/Character/RoleWoman/3101001",
                                                    "Assets/Game/Actors/Character/RoleWoman/3102001",
                                                    //"Assets/Game/Actors/Character/RoleWoman/3102001",
                                                    //"Assets/Game/Actors/Character/RoleWoman/3102001",
                                                    //"Assets/Game/Actors/Character/RoleWoman/3102001",
                                                    //"Assets/Game/Actors/Character/RoleWoman/3102001",
                                                    "Assets/Game/Actors/Character/RoleWoman/Realm/310100101",
                                                    "Assets/Game/Actors/Character/RoleWoman/Realm/310100102",
                                                    "Assets/Game/Actors/Character/RoleWoman/Realm/310100103",
                                                    "Assets/Game/Actors/Character/RoleWoman/3103001",} },
        { ActorActionConfigType.TianShenAction, new string[]{ "Assets/Game/Model/Tianshen" } },
        { ActorActionConfigType.MonsterAction, new string[]{ "Assets/Game/Model/Boss" } },
		{ ActorActionConfigType.WuhunAction, new string[]{ "Assets/Game/Model/WuHun" } },
        { ActorActionConfigType.ZuoqiAction, new string[]{
                                                    "Assets/Game/Model/Zuoqi/2016",
                                                    "Assets/Game/Model/Zuoqi/2108",
                                                    "Assets/Game/Model/Zuoqi/2109",
                                                    "Assets/Game/Model/Zuoqi/2110",
                                                    "Assets/Game/Model/Zuoqi/2111",} },
        { ActorActionConfigType.YushouAction, new string[]{ "Assets/Game/Model/Yushou" } },
        { ActorActionConfigType.GundamAction, new string[]{ "Assets/Game/Model/jijia/body" } },
        { ActorActionConfigType.ShuangShengTianShenAction, new string[]{ "Assets/Game/Model/SSshenling" } },
    };

    static Dictionary<string, string> animNameConvert = new Dictionary<string, string>();

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/角色")]
    static void BuildRoleActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.RoleAction);
    }

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/天神")]
    static void BuildTianShenActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.TianShenAction);
    }

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/怪物")]
    static void BuildMonsterActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.MonsterAction);
    }

	[MenuItem("自定义工具/策划工具/一键生成动作时间配置/武魂")]
	static void BuildWuHunActionConfig()
	{
		BuildActionConfig(ActorActionConfigType.WuhunAction);
	}

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/坐骑")]
    static void BuildZuoqiActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.ZuoqiAction);
    }

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/驭兽")]
    static void BuildYuShouActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.YushouAction);
    }

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/高达")]
    static void BuildGundamActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.GundamAction);
    }

    [MenuItem("自定义工具/策划工具/一键生成动作时间配置/双生天神")]
    static void BuildShuangShengTianShenActionConfig()
    {
        BuildActionConfig(ActorActionConfigType.ShuangShengTianShenAction);
    }

    static void BuildActionConfig(ActorActionConfigType buildType)
    {
        animNameConvert.Clear();
        string configPath = "";
        string configCNName = "";
        string configLuaClassName = "";
        switch (buildType)
        { 
            case ActorActionConfigType.RoleAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("attack4", "attack4");
                animNameConvert.Add("attack5", "attack5");
                animNameConvert.Add("attack6", "attack6");
                animNameConvert.Add("attack7", "attack7");
                animNameConvert.Add("attack8", "attack8");
                animNameConvert.Add("attack9", "attack9");
                animNameConvert.Add("attack10", "attack10");
                animNameConvert.Add("attack11", "attack11");
                animNameConvert.Add("attack12", "attack12");
                animNameConvert.Add("attack13", "attack13");
                animNameConvert.Add("attack14", "attack14");
                animNameConvert.Add("attack15", "attack15");

                animNameConvert.Add("attack1_back", "attack1_back");
                animNameConvert.Add("attack2_back", "attack2_back");
                animNameConvert.Add("attack3_back", "attack3_back");
                animNameConvert.Add("attack4_back", "attack4_back");
                animNameConvert.Add("attack5_back", "attack5_back");
                animNameConvert.Add("attack6_back", "attack6_back");
                animNameConvert.Add("attack7_back", "attack7_back");
                animNameConvert.Add("attack8_back", "attack8_back");
                animNameConvert.Add("attack9_back", "attack9_back");
                animNameConvert.Add("attack10_back", "attack10_back");
                animNameConvert.Add("attack11_back", "attack11_back");
                animNameConvert.Add("attack12_back", "attack12_back");
                animNameConvert.Add("attack13_back", "attack13_back");
                animNameConvert.Add("attack14_back", "attack14_back");
                animNameConvert.Add("attack15_back", "attack15_back");

                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_2", "combo1_2");
                animNameConvert.Add("combo1_3", "combo1_3");
                animNameConvert.Add("combo1_4", "combo1_4");

                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("combo1_2_back", "combo1_2_back");
                animNameConvert.Add("combo1_3_back", "combo1_3_back");
                animNameConvert.Add("combo1_4_back", "combo1_4_back");

                animNameConvert.Add("esoterica_attack_1", "esoterica_attack_1");
                animNameConvert.Add("esoterica_attack_2", "esoterica_attack_2");
                animNameConvert.Add("esoterica_attack_3", "esoterica_attack_3");
                animNameConvert.Add("esoterica_attack_4", "esoterica_attack_4");
                animNameConvert.Add("esoterica_attack_5", "esoterica_attack_5");
                animNameConvert.Add("esoterica_attack_6", "esoterica_attack_6");
                animNameConvert.Add("esoterica_attack_7", "esoterica_attack_7");
                animNameConvert.Add("esoterica_attack_8", "esoterica_attack_8");
                animNameConvert.Add("esoterica_attack_9", "esoterica_attack_9");
                animNameConvert.Add("esoterica_attack_10", "esoterica_attack_10");

                animNameConvert.Add("esoterica_attack_1_back", "esoterica_attack_1_back");
                animNameConvert.Add("esoterica_attack_2_back", "esoterica_attack_2_back");
                animNameConvert.Add("esoterica_attack_3_back", "esoterica_attack_3_back");
                animNameConvert.Add("esoterica_attack_4_back", "esoterica_attack_4_back");
                animNameConvert.Add("esoterica_attack_5_back", "esoterica_attack_5_back");
                animNameConvert.Add("esoterica_attack_6_back", "esoterica_attack_6_back");
                animNameConvert.Add("esoterica_attack_7_back", "esoterica_attack_7_back");
                animNameConvert.Add("esoterica_attack_8_back", "esoterica_attack_8_back");
                animNameConvert.Add("esoterica_attack_9_back", "esoterica_attack_9_back");
                animNameConvert.Add("esoterica_attack_10_back", "esoterica_attack_10_back");

                animNameConvert.Add("custom_action_1", "custom_action_1");
                animNameConvert.Add("custom_action_2", "custom_action_2");
                animNameConvert.Add("custom_action_3", "custom_action_3");
                animNameConvert.Add("custom_action_4", "custom_action_4");
                animNameConvert.Add("custom_action_5", "custom_action_5");
                animNameConvert.Add("custom_action_6", "custom_action_6");
                animNameConvert.Add("custom_action_7", "custom_action_7");
                animNameConvert.Add("custom_action_8", "custom_action_8");
                animNameConvert.Add("custom_action_9", "custom_action_9");
                animNameConvert.Add("custom_action_10", "custom_action_10");
                animNameConvert.Add("custom_action_11", "custom_action_11");
                animNameConvert.Add("custom_action_12", "custom_action_12");
                
                animNameConvert.Add("fall_rest", "fall_rest");
                animNameConvert.Add("jump", "jump");
                animNameConvert.Add("jump2", "jump2");
                animNameConvert.Add("prizedraw", "prizedraw");

                configPath = roleConfigPath;
                configCNName = "角色模型";
                configLuaClassName = "RoleActionConfig";
                break;
            case ActorActionConfigType.TianShenAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("attack4", "attack4");
                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_2", "combo1_2");
                animNameConvert.Add("combo1_3", "combo1_3");
                animNameConvert.Add("combo1_4", "combo1_4");
                animNameConvert.Add("attack1_back", "attack1_back");
                animNameConvert.Add("attack2_back", "attack2_back");
                animNameConvert.Add("attack3_back", "attack3_back");
                animNameConvert.Add("attack4_back", "attack4_back");
                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("combo1_2_back", "combo1_2_back");
                animNameConvert.Add("combo1_3_back", "combo1_3_back");
                animNameConvert.Add("combo1_4_back", "combo1_4_back");
                animNameConvert.Add("d_chuchang", "chuchang");
                configPath = tianshenConfigPath;
                configCNName = "天神";
                configLuaClassName = "TianShenBossActionConfig";
                break;
            case ActorActionConfigType.MonsterAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("attack4", "attack4");
                animNameConvert.Add("attack5", "attack5");
                animNameConvert.Add("magic1_1", "magic1_1");
                animNameConvert.Add("magic1_2", "magic1_2");
                animNameConvert.Add("magic1_3", "magic1_3");
                animNameConvert.Add("magic2_1", "magic2_1");
                animNameConvert.Add("magic2_2", "magic2_2");
                animNameConvert.Add("magic2_3", "magic2_3");
                animNameConvert.Add("magic3_1", "magic3_1");
                animNameConvert.Add("magic3_2", "magic3_2");
                animNameConvert.Add("magic3_3", "magic3_3");
                animNameConvert.Add("magic4_1", "magic4_1");
                animNameConvert.Add("magic4_2", "magic4_2");
                animNameConvert.Add("magic4_3", "magic4_3");
                animNameConvert.Add("hurt", "hurt");
                animNameConvert.Add("rest", "rest");
                animNameConvert.Add("dead", "dead");
                animNameConvert.Add("die_fly", "die_fly");
                configPath = monsterConfigPath;
                configCNName = "怪物";
                configLuaClassName = "MonsterActionConfig";
                break;
			case ActorActionConfigType.WuhunAction:
				animNameConvert.Add("attack1", "attack1");
				animNameConvert.Add("attack2", "attack2");
				animNameConvert.Add("attack3", "attack3");
				animNameConvert.Add("attack4", "attack4");
				animNameConvert.Add("combo2_1", "combo2_1");
				animNameConvert.Add("combo2_2", "combo2_2");
				animNameConvert.Add("combo2_3", "combo2_3");
				animNameConvert.Add("combo2_4", "combo2_4");
                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_2", "combo1_2");
                animNameConvert.Add("combo1_3", "combo1_3");
                animNameConvert.Add("combo1_4", "combo1_4");
                animNameConvert.Add("attack1_back", "attack1_back");
				animNameConvert.Add("attack2_back", "attack2_back");
				animNameConvert.Add("attack3_back", "attack3_back");
				animNameConvert.Add("attack4_back", "attack4_back");
				animNameConvert.Add("combo2_1_back", "combo2_1_back");
				animNameConvert.Add("combo2_2_back", "combo2_2_back");
				animNameConvert.Add("combo2_3_back", "combo2_3_back");
				animNameConvert.Add("combo2_4_back", "combo2_4_back");
                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("combo1_2_back", "combo1_2_back");
                animNameConvert.Add("combo1_3_back", "combo1_3_back");
                animNameConvert.Add("combo1_4_back", "combo1_4_back");
                configPath = wuhunConfigPath;
				configCNName = "武魂";
				configLuaClassName = "WuhunActionConfig";
				break;
            case ActorActionConfigType.ZuoqiAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("attack4", "attack4");
                animNameConvert.Add("attack1_back", "attack1_back");
                animNameConvert.Add("attack2_back", "attack2_back");
                animNameConvert.Add("attack3_back", "attack3_back");
                animNameConvert.Add("attack4_back", "attack4_back");
                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("rest", "rest");
                animNameConvert.Add("dead", "dead");
                configPath = zuoqiConfigPath;
                configCNName = "坐骑";
                configLuaClassName = "ZuoqiActionConfig";
                break;
            case ActorActionConfigType.YushouAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("magic1_1", "magic1_1");
                animNameConvert.Add("magic1_2", "magic1_2");
                animNameConvert.Add("magic1_3", "magic1_3");
                configPath = yushouConfigPath;
                configCNName = "御兽";
                configLuaClassName = "YushouActionConfig";
                break;
            case ActorActionConfigType.GundamAction:
                animNameConvert.Add("attack1_1", "attack1_1");
                animNameConvert.Add("attack1_2", "attack1_2");
                animNameConvert.Add("attack1_3", "attack1_3");
                animNameConvert.Add("attack1_4", "attack1_4");
                animNameConvert.Add("attack1_5", "attack1_5");
                animNameConvert.Add("attack2_1", "attack2_1");
                animNameConvert.Add("attack2_2", "attack2_2");
                animNameConvert.Add("attack2_3", "attack2_3");
                animNameConvert.Add("attack2_4", "attack2_4");
                animNameConvert.Add("attack2_5", "attack2_5");
                animNameConvert.Add("attack3_1", "attack3_1");
                animNameConvert.Add("attack3_2", "attack3_2");
                animNameConvert.Add("attack3_3", "attack3_3");
                animNameConvert.Add("attack3_4", "attack3_4");
                animNameConvert.Add("attack3_5", "attack3_5");
                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_2", "combo1_2");
                animNameConvert.Add("combo1_3", "combo1_3");
                animNameConvert.Add("combo1_4", "combo1_4");
                animNameConvert.Add("combo1_5", "combo1_5");
                animNameConvert.Add("combo2_1", "combo2_1");
                animNameConvert.Add("combo2_2", "combo2_2");
                animNameConvert.Add("combo2_3", "combo2_3");
                animNameConvert.Add("combo2_4", "combo2_4");
                animNameConvert.Add("combo2_5", "combo2_5");
                animNameConvert.Add("combo3_1", "combo3_1");
                animNameConvert.Add("combo3_2", "combo3_2");
                animNameConvert.Add("combo3_3", "combo3_3");
                animNameConvert.Add("combo3_4", "combo3_4");
                animNameConvert.Add("combo3_5", "combo3_5");
                animNameConvert.Add("attack1_1_back", "attack1_1_back");
                animNameConvert.Add("attack1_2_back", "attack1_2_back");
                animNameConvert.Add("attack1_3_back", "attack1_3_back");
                animNameConvert.Add("attack1_4_back", "attack1_4_back");
                animNameConvert.Add("attack1_5_back", "attack1_5_back");
                animNameConvert.Add("attack2_1_back", "attack2_1_back");
                animNameConvert.Add("attack2_2_back", "attack2_2_back");
                animNameConvert.Add("attack2_3_back", "attack2_3_back");
                animNameConvert.Add("attack2_4_back", "attack2_4_back");
                animNameConvert.Add("attack2_5_back", "attack2_5_back");
                animNameConvert.Add("attack3_1_back", "attack3_1_back");
                animNameConvert.Add("attack3_2_back", "attack3_2_back");
                animNameConvert.Add("attack3_3_back", "attack3_3_back");
                animNameConvert.Add("attack3_4_back", "attack3_4_back");
                animNameConvert.Add("attack3_5_back", "attack3_5_back");
                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("combo1_2_back", "combo1_2_back");
                animNameConvert.Add("combo1_3_back", "combo1_3_back");
                animNameConvert.Add("combo1_4_back", "combo1_4_back");
                animNameConvert.Add("combo1_5_back", "combo1_5_back");
                animNameConvert.Add("combo2_1_back", "combo2_1_back");
                animNameConvert.Add("combo2_2_back", "combo2_2_back");
                animNameConvert.Add("combo2_3_back", "combo2_3_back");
                animNameConvert.Add("combo2_4_back", "combo2_4_back");
                animNameConvert.Add("combo2_5_back", "combo2_5_back");
                animNameConvert.Add("combo3_1_back", "combo3_1_back");
                animNameConvert.Add("dcombo3_2_back", "combo3_2_back");
                animNameConvert.Add("dcombo3_3_back", "combo3_3_back");
                animNameConvert.Add("combo3_4_back", "combo3_4_back");
                animNameConvert.Add("combo3_5_back", "combo3_5_back");
                animNameConvert.Add("rush_1", "rush_1");
                animNameConvert.Add("rush_1_back", "rush_1_back");
                animNameConvert.Add("rush_2", "rush_2");
                animNameConvert.Add("rush_2_back", "rush_2_back");
                animNameConvert.Add("rush_3", "rush_3");
                animNameConvert.Add("rush_3_back", "rush_3_back");
                configPath = gundamConfigPath;
                configCNName = "高达";
                configLuaClassName = "GundamActionConfig";
                break;
            case ActorActionConfigType.ShuangShengTianShenAction:
                animNameConvert.Add("attack1", "attack1");
                animNameConvert.Add("attack2", "attack2");
                animNameConvert.Add("attack3", "attack3");
                animNameConvert.Add("attack4", "attack4");
                animNameConvert.Add("combo1_1", "combo1_1");
                animNameConvert.Add("combo1_2", "combo1_2");
                animNameConvert.Add("combo1_3", "combo1_3");
                animNameConvert.Add("combo1_4", "combo1_4");
                animNameConvert.Add("attack1_back", "attack1_back");
                animNameConvert.Add("attack2_back", "attack2_back");
                animNameConvert.Add("attack3_back", "attack3_back");
                animNameConvert.Add("attack4_back", "attack4_back");
                animNameConvert.Add("combo1_1_back", "combo1_1_back");
                animNameConvert.Add("combo1_2_back", "combo1_2_back");
                animNameConvert.Add("combo1_3_back", "combo1_3_back");
                animNameConvert.Add("combo1_4_back", "combo1_4_back");
                animNameConvert.Add("d_chuchang", "chuchang");
                configPath = shuangshengtianshenConfigPath;
                configCNName = "双生天神";
                configLuaClassName = "ShuangShengTianShenActionConfig";
                break;
        }

        if (configPath == "")
        { 
            return;
        }
        if (!File.Exists(configPath))
        {
            File.Create(configPath);
        }

        List<string> needHitTimeAnim = needHitTimeAnimDic[buildType];
        List<string> animNameList = animNameDic[buildType];
        string[] modelPath = modelPathDic[buildType];

        int curIndex = 0;
        int totalCount = 0;
        string[] guids = AssetDatabase.FindAssets("t:prefab", modelPath);
        totalCount = guids.Length;
        Dictionary<string, Dictionary<string, float>> actionConfigDir = new Dictionary<string, Dictionary<string, float>>();
        foreach (string guid in guids)
        {
            string asset = AssetDatabase.GUIDToAssetPath(guid);
            GameObject obj = AssetDatabase.LoadAssetAtPath(asset, typeof(GameObject)) as GameObject;
            Animator animator = obj.GetComponentInChildren<Animator>();
            if (actionConfigDir.ContainsKey(obj.name))
            {
                Debug.LogError("发现同名资源，请检查！！！ 资源名称：" + obj.name + ", 资源路径：" + asset);
                continue;
            }

            if (!IsNumberString(obj.name))
            {
                continue;
            }

            if (animator)
            {
                Dictionary<string, float> animNameToTimeLength = new Dictionary<string, float>();
                foreach (string animName in animNameList)
                {
                    AnimationClip clip = AnimatorExtensions.GetAnimationClip(animator, animName);
                    if (clip && !IsAnimClipEmpty(animator, animName))
                    {
                        animNameToTimeLength.Add(animNameConvert[animName], clip.length);
                    }
					if (buildType == ActorActionConfigType.RoleAction || buildType == ActorActionConfigType.TianShenAction
                        || buildType == ActorActionConfigType.WuhunAction || buildType == ActorActionConfigType.ZuoqiAction
                        || buildType == ActorActionConfigType.GundamAction || buildType == ActorActionConfigType.ShuangShengTianShenAction)
                    {
                        string backAnimName = string.Format("{0}_back", animName);
                        AnimationClip back_clip = AnimatorExtensions.GetAnimationClip(animator, backAnimName);
                        if (back_clip && !IsAnimClipEmpty(animator, backAnimName))
                        {
                            animNameToTimeLength.Add(animNameConvert[backAnimName], back_clip.length);
                        }
                    }
                }
                actionConfigDir.Add(obj.name, animNameToTimeLength);
            }

            curIndex++;
            EditorUtility.DisplayProgressBar(string.Format("正在扫描{0}动作文件...", configCNName)
                , string.Format("当前进度：{0}/{1}", curIndex, totalCount)
                , (float)curIndex / (float)totalCount);
        }

        curIndex = 0;
        totalCount = actionConfigDir.Count;
        StringBuilder stringBuilder = new StringBuilder();
        try
        {
            stringBuilder.AppendLine(string.Format("--{0}动作时长配置(该配置由工具生成 自定义工具-策划工具-一键生成动作时间配置)", configCNName));
            stringBuilder.AppendLine(configLuaClassName + " = {");
            if (buildType == ActorActionConfigType.MonsterAction)
            {
                //默认配置
                stringBuilder.Append("	[0] = {\n");
                foreach (KeyValuePair<string, string> _animName in animNameConvert)
                {
                    string animName = _animName.Value;
                    stringBuilder.Append("		" + animName + " = { ");
                    stringBuilder.Append("time = 1,");
                    if (needHitTimeAnim.Contains(animName))
                    {
                        stringBuilder.Append("  hit_time = 1,");
                    }
                    stringBuilder.Append("  },\n");
                }
                stringBuilder.Append("  },\n");
            }

			if (buildType == ActorActionConfigType.MonsterAction || buildType == ActorActionConfigType.TianShenAction
                || buildType == ActorActionConfigType.WuhunAction || buildType == ActorActionConfigType.ZuoqiAction
                || buildType == ActorActionConfigType.YushouAction || buildType == ActorActionConfigType.GundamAction
                || buildType == ActorActionConfigType.ShuangShengTianShenAction)
            {
                foreach (KeyValuePair<string, Dictionary<string, float>> config in actionConfigDir)
                {
                    string res_id = config.Key.ToString();
                    stringBuilder.Append("	[" + res_id + "] = {\n");

                    foreach (KeyValuePair<string, float> animNameKeyPair in config.Value)
                    {
                        string animName = animNameKeyPair.Key;
                        float animTime = animNameKeyPair.Value;
                        if (!animName.EndsWith("_back"))
                        {
                            stringBuilder.Append("		" + animName + " = { ");
                            stringBuilder.Append("time = " + animTime.ToString("f3") + ",");
                            if (needHitTimeAnim.Contains(animName))
                            {
                                float hitTime = animTime * 0.85f;
                                stringBuilder.Append("  hit_time = " + hitTime.ToString("f3") + ",");
                            }
                            if (buildType == ActorActionConfigType.TianShenAction || buildType == ActorActionConfigType.ShuangShengTianShenAction)
                            {
                                if (animName != "d_chuchang" && animName != "chuchang")
                                {
                                    string backAnimName = string.Format("{0}_back", animName);
                                    if (config.Value.ContainsKey(backAnimName))
                                    {
                                        float backTime = config.Value[backAnimName];
                                        stringBuilder.Append("  has_back = true, back_time = " + backTime.ToString("f3") + ",");
                                    }
                                }
                            }

                            if (buildType == ActorActionConfigType.WuhunAction || buildType == ActorActionConfigType.ZuoqiAction || buildType == ActorActionConfigType.GundamAction)
                            {
                                string backAnimName = string.Format("{0}_back", animName);
                                if (config.Value.ContainsKey(backAnimName))
                                {
                                    float backTime = config.Value[backAnimName];
                                    stringBuilder.Append("  has_back = true, back_time = " + backTime.ToString("f3") + ",");
                                }
                            }

                            stringBuilder.Append("},\n");
                        }
                    }
                    stringBuilder.Append("  },\n");

                    curIndex++;
                    EditorUtility.DisplayProgressBar(string.Format("正在写入{0}动作配置...", configCNName)
                        , string.Format("当前进度：{0}/{1}", curIndex, totalCount)
                        , (float)curIndex / (float)totalCount);
                }
            }
            else if (buildType == ActorActionConfigType.RoleAction)
            {
                string[] sexId = { "0", "1" };
                Dictionary<string, string[]> sexProfId = new Dictionary<string, string[]>() {
                    { "0", new string[] { "1", "2", "3", "1001", "1002", "1003"} },//,
                    { "1", new string[] { "1",  "3", "1001", "1002", "1003" } },//"2",, "4", "1003"
                };
                Dictionary<string, Dictionary<string, string>> profToModelId = new Dictionary<string, Dictionary<string, string>>() {
                    { "0", new Dictionary<string, string>() {
                        { "1", "3101001" }, { "2", "3102001" }, { "3", "3103001" }, { "1001", "310100101" }, { "1002", "310100102" }, { "1003", "310100103"},
                    }},
                    { "1", new Dictionary<string, string>() {
                        { "1", "1101001" },  { "3", "1103001" },{ "1001", "110100101" },{ "1002", "110100102" },{ "1003", "110100103" }, 
                    }},
                };
                foreach (KeyValuePair<string, string[]> sexProf in sexProfId)
                {
                    string sex = sexProf.Key;
                    if (sex == "0") {
                        stringBuilder.Append("\n    --女角色\n");
                    }
                    else {
                        stringBuilder.Append("\n    --男角色\n");
                    }

                    stringBuilder.Append(String.Format("    [{0}]", sex) + " = \n   {\n");
                    foreach (string prof in sexProf.Value)
                    {
                        stringBuilder.Append(String.Format("\n        [{0}]", prof) + " = \n        {\n");
                        string modelId = profToModelId[sex][prof];
                        Dictionary<string, float> actionConfigList = null;
                        try
                        {
                            actionConfigList = actionConfigDir[modelId];
                        }
                        catch (Exception)
                        {
                            throw;
                        }
                        foreach (KeyValuePair<string, float> actions in actionConfigList)
                        {
                            string animName = actions.Key;
                            if (!animName.EndsWith("_back"))
                            {
                                float animTime = actions.Value;
                                stringBuilder.Append("		    " + animName + " = { ");
                                stringBuilder.Append("time = " + animTime.ToString("f3") + ",");
                                if (needHitTimeAnim.Contains(animName))
                                {
                                    float hitTime = animTime * 0.85f;
                                    stringBuilder.Append("  hit_time = " + hitTime.ToString("f3") + ",");
                                }

                                string backAnimName = string.Format("{0}_back", animName);
                                if (actionConfigList.ContainsKey(backAnimName))
                                {
                                    float backTime = actionConfigList[backAnimName];
                                    stringBuilder.Append("  has_back = true, back_time = " + backTime.ToString("f3") + ",");
                                }

                                stringBuilder.Append("},\n");
                            }
                        }
                        stringBuilder.Append("      },\n");
                    }
                    stringBuilder.Append("  },\n");
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogException(ex);
            EditorUtility.ClearProgressBar();
            return;
        }

        stringBuilder.AppendLine("}");

        EditorUtility.ClearProgressBar();
        File.WriteAllText(configPath, stringBuilder.ToString());
        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }

    static bool IsNumberString(string value)
    {
        if (string.IsNullOrEmpty(value))
            return false;

        ASCIIEncoding ascii = new ASCIIEncoding();
        byte[] byteStr = ascii.GetBytes(value);
        foreach (byte b in byteStr)
        {
            if (b < 48 || b > 57)
            {
                return false;
            }
        }

        return true;
    }

    static bool IsAnimClipEmpty(Animator animator, string animName)
    {
        foreach (AnimationClip clip in animator.runtimeAnimatorController.animationClips)
        {
            if (clip.name == animName)
            {
                return clip.empty;
            }
        }
        return false;
    }
}
