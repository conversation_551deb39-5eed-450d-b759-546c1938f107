﻿using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEditor.SceneManagement;

public class MapStatic : EditorWindow
{
    //[MenuItem("自定义工具/美术专用/地图工具")]
    private static void ShowWindow()
    {
        EditorWindow.GetWindow<MapStatic>(false, "地图工具");
    }

    GameObject controller = null;
    List<GameObject> gos = new List<GameObject>();

    private void OnGUI()
    {
        GUILayout.Space(10);
        GUILayout.Label("Models:");
        controller = (GameObject)EditorGUILayout.ObjectField(controller, typeof(GameObject), true, GUILayout.MinWidth(100f));

        GUILayout.Space(20);
        if (GUILayout.Button("修改static"))
        {
            ChangeButton();
            Repaint();
        }
    }

    #region 修改static
    private void ChangeButton()
    {
        if (null == controller)
        {
            Debug.LogError("No controller!!");
            return;
        }

        if (DoUnmarkStaticBatching(controller.transform))
        {
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
        }
    }

    private static bool DoUnmarkStaticBatching(Transform trans)
    {
        bool ret = false;

        var newFlag = ~StaticEditorFlags.BatchingStatic;

        GameObjectUtility.SetStaticEditorFlags(trans.gameObject, newFlag);
        trans.gameObject.tag = "StaticBatching";

        for (int i = 0; i < trans.childCount; ++i)
        {
            var childTransform = trans.GetChild(i);
            if (DoUnmarkStaticBatching(childTransform))
            {
                ret = true;
            }
        }

        return ret;
    }
    #endregion
}
