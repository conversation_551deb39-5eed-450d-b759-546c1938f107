﻿using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class OverrideCreateUI
{
    [MenuItem("GameObject/UI/TMP")]
    static void CreatTMP()
    {
        CreateUI(TMP);
    }

    [MenuItem("GameObject/UI/Text (a2 common)")]
    static void CreatText()
    {
        CreateUI(Text);
    }

    [MenuItem("GameObject/UI/Image (no RaycastTarget)")]
    static void CreatImage()
    {
        CreateUI(Image);
    }

    [MenuItem("GameObject/UI/RawImage (no RaycastTarget)")]
    static void CreatRawImage()
    {
        CreateUI(RawImage);
    }

	[MenuItem("GameObject/UI/InputField (Mask Emoji)")]
	static void CreatInputField()
	{
		CreateUI(InputField);
	}

    private static void CreateUI(System.Func<GameObject> callback)
    {
        if (Selection.activeTransform)
        {
            callback();
        }

        /*
        var canvasObj = SecurityCheck();
        if (!Selection.activeTransform)
        {
            callback().transform.SetParent(canvasObj.transform);
        }
        else // (Selection.activeTransform)
        {
            if (!Selection.activeTransform.GetComponentInParent<Canvas>())
            {
                callback().transform.SetParent(canvasObj.transform);
            }
            else
            {
                callback();
            }
        }
        */
    }

    private static GameObject Text()
    {
        System.Action<GameObject> callback = (go) =>
        {
            Text text = go.GetComponent<Text>();
            text.text = "显示文本";
            text.font = AssetDatabase.LoadAssetAtPath("Assets/Game/UIs/TTF/FZSSKSJW_M.ttf", typeof(Font)) as Font;
            text.fontSize = 20;
            text.supportRichText = true;
            text.alignment = TextAnchor.MiddleCenter;
            text.color = new Color(84 / 255f, 82 / 255f, 82 / 255f, 1f);
            text.raycastTarget = false;
            
            RectTransform rect = go.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(160, 30);
        };
        
        return CreateGO<Text>("text", callback);
    }

    private static GameObject TMP()
    {
        System.Action<GameObject> callback = (go) =>
        {
            TextMeshProUGUI text = go.GetComponent<TextMeshProUGUI>();
            text.text = "显示文本";
            text.fontSize = 20;
            text.richText = true;
            text.alignment = TextAlignmentOptions.Center;
            text.color = new Color(203 / 255f, 251 / 255f, 253 / 255f, 1f);
            text.raycastTarget = false;
            RectTransform rect = go.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(160, 30);
        };

        return CreateGO<TextMeshProUGUI>("text", callback);
    }

    private static GameObject InputField()
	{
		System.Action<GameObject> callback = (go) =>
		{
			InputField inputfield = go.GetComponent<InputField>();
			GameObject Placeholder = new GameObject("Placeholder");
			Placeholder.transform.SetParent(go.transform);
			RectTransform rect = Placeholder.AddComponent<RectTransform>();
			rect.offsetMin = new Vector2(10, 6);
			rect.offsetMax = new Vector2(-10, -7);
			rect.anchorMax = Vector2.one;
			rect.anchorMin = Vector2.zero;
			Text text = Placeholder.AddComponent<Text>();
			text.text = "Enter text...";
			text.fontStyle = FontStyle.Italic;
			text.color = new Color(50f / 255, 50f / 255, 50f / 255, 128f / 255);
			text.raycastTarget = false;
			text.font = AssetDatabase.LoadAssetAtPath("Assets/Game/UIs/TTF/FZSSKSJW_M.ttf", typeof(Font)) as Font;
			text.supportRichText = false;
			inputfield.placeholder = text;

			GameObject input_text_obj = new GameObject("Text");
			input_text_obj.transform.SetParent(go.transform);
			RectTransform input_rect = input_text_obj.AddComponent<RectTransform>();
			input_rect.offsetMin = new Vector2(10, 6);
			input_rect.offsetMax = new Vector2(-10, -7);
			input_rect.anchorMax = Vector2.one;
			input_rect.anchorMin = Vector2.zero;
			Text input_text = input_text_obj.AddComponent<Text>();
			input_text.font = AssetDatabase.LoadAssetAtPath("Assets/Game/UIs/TTF/FZSSKSJW_M.ttf", typeof(Font)) as Font;
			input_text.text = "";
			input_text.raycastTarget = false;
			input_text.supportRichText = false;
			inputfield.textComponent = input_text;

			inputfield.targetGraphic = go.AddComponent<Image>();
			RectTransform inputfield_rect = go.GetComponent<RectTransform>();
			inputfield_rect.sizeDelta = new Vector2(160, 30);
			go.AddComponent<UIMaskInputEmoji>();
		};

		return CreateGO<InputField>("InputMaskEmojiField", callback);
	}
		
    private static GameObject Image()
    {
        System.Action<GameObject> callback = (go) =>
        {
            Image image = go.GetComponent<Image>();
            image.raycastTarget = false;
        };

        return CreateGO<Image>("image", callback);
    }

    private static GameObject RawImage()
    {
        System.Action<GameObject> callback = (go) =>
        {
            RawImage rawImage = go.GetComponent<RawImage>();
            rawImage.raycastTarget = false;
        };

        return CreateGO<RawImage>("raw_image", callback);
    }


    private static GameObject CreateGO<T>(string defaultName, System.Action<GameObject> callback)
    {
        GameObject go = new GameObject(defaultName, typeof(T));
        callback(go);
        go.layer = 5;
        go.transform.SetParent(Selection.activeTransform);
        go.transform.localPosition = new Vector3(go.transform.localPosition.x, go.transform.localPosition.y, 0f);
        go.GetComponent<RectTransform>().anchoredPosition = Vector2.zero;
        go.transform.localScale = new Vector3(1, 1, 1);
        Selection.activeGameObject = go;
        return go;
    }

    private static GameObject SecurityCheck()
    {
        GameObject canvasGO;
        var cc = Object.FindObjectOfType<Canvas>();
        if (!cc)
        {
            canvasGO = new GameObject("Canvas", typeof(Canvas));
            var scaler = canvasGO.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            canvasGO.AddComponent<GraphicRaycaster>();
            Canvas canvas = canvasGO.GetComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        }
        else
        {
            canvasGO = cc.gameObject;
        }

        if (!Object.FindObjectOfType<EventSystem>())
        {
            GameObject eventSystemGo = new GameObject("EventSystem", typeof(EventSystem));
            eventSystemGo.AddComponent<UnityEngine.EventSystems.EventSystem>();
            eventSystemGo.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
        }

        return canvasGO;
    }
}