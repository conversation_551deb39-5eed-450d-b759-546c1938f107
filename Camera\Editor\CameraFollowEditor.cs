﻿using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using Sirenix.Utilities.Editor;
using Sirenix.Utilities;
using System.Reflection;

[CustomEditor(typeof(CameraFollow))]
public class CameraFollowEditor : OdinEditor
{
    #region 编辑器状态
    private int _modeSwitch = 1; // 默认自由模式
    private int _lastModeSwitch = -1;
    private Transform _newTarget;
    private int _selectedTab = 0; // 当前选中的标签页
    
    private readonly string[] _tabNames = new string[] 
    { 
        "目标设置", "移动控制", "寻路镜头", "战斗镜头", "遮挡检测", "强制过渡" 
    };
    
    [ShowInInspector, HideLabel]
    [PropertySpace(5)]
    [EnumToggleButtons]
    private CameraMode SelectedCameraMode
    {
        get => (CameraMode)_modeSwitch;
        set
        {
            _modeSwitch = (int)value;
            ApplyCameraMode();
        }
    }
    
    [ShowInInspector]
    [ShowIf("@SelectedCameraMode == CameraMode.Locked")]
    [PropertySpace(5)]
    [LabelText("默认距离")]
    public float DefaultDistance = 8;
    
    [ShowInInspector]
    [ShowIf("@SelectedCameraMode == CameraMode.Locked")]
    [LabelText("默认角度")]
    public Vector2 DefaultAngle = new Vector2(14, 0);
    
    [ShowInInspector]
    [PropertySpace(5)]
    [LabelText("旋转一周时间")]
    [ShowIf("@Application.isPlaying && !((CameraFollow)target)._useTargetGroup")]
    public float RotationDuration = 5f;
    #endregion

    #region 序列化属性
    private SerializedProperty _targetGroupCenterProp;
    private SerializedProperty _targetGroupRadiusProp;
    private SerializedProperty _isObstructedRestoringProp;
    #endregion

    #region 枚举定义
    public enum CameraMode
    {
        [LabelText("锁定模式")]
        Locked = 0,
        [LabelText("自由模式")]
        Free = 1,
    }
    #endregion

    protected override void OnEnable()
    {
        base.OnEnable();
        
        // 获取私有字段的序列化属性
        _targetGroupCenterProp = serializedObject.FindProperty("_targetGroupCenter");
        _targetGroupRadiusProp = serializedObject.FindProperty("_targetGroupRadius");
        _isObstructedRestoringProp = serializedObject.FindProperty("_isObstructedRestoring");
    }

    public override void OnInspectorGUI()
    {
        var cameraFollow = (CameraFollow)target;

        // 更新序列化对象
        serializedObject.Update();

        // 绘制相机模式选择
        SirenixEditorGUI.BeginBox("相机模式设置");
        {
            base.OnInspectorGUI();
            
            // 应用相机模式变化
            if (_lastModeSwitch != _modeSwitch)
            {
                ApplyCameraMode();
                _lastModeSwitch = _modeSwitch;
            }
        }
        SirenixEditorGUI.EndBox();

        // 基础信息显示
        DrawBasicInfo(cameraFollow);

        // 主要功能标签页
        DrawMainTabs(cameraFollow);

        // 运行时测试功能
        DrawRuntimeTests(cameraFollow);

        // 应用修改的属性
        serializedObject.ApplyModifiedProperties();

        // 处理变化
        HandleChanges(cameraFollow);
    }

    #region 绘制方法
    private void DrawBasicInfo(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginBox("基础信息");
        {
            EditorGUILayout.LabelField("当前距离", cameraFollow.Distance.ToString("F2"));
            
            using (new EditorGUI.DisabledScope(true))
            {
                EditorGUILayout.Toggle("允许X轴旋转（俯仰角）", cameraFollow.AllowXRotation);
                EditorGUILayout.Toggle("允许Y轴旋转（偏航角）", cameraFollow.AllowYRotation);
            }
            
            EditorGUILayout.Space(5);
            
            bool useGroup = EditorGUILayout.Toggle("使用目标组模式", cameraFollow._useTargetGroup);
            if (useGroup != cameraFollow._useTargetGroup)
            {
                if (useGroup)
                    cameraFollow.EnableTargetGroupMode();
                else
                    cameraFollow.DisableTargetGroupMode();
            }
            
            cameraFollow.smoothOffsetSpeed = EditorGUILayout.FloatField("偏移平滑速度", cameraFollow.smoothOffsetSpeed);
            cameraFollow.EBTransitionDuration = EditorGUILayout.FloatField("目标切换过渡持续时间", cameraFollow.EBTransitionDuration);
        }
        SirenixEditorGUI.EndBox();
    }

    private void DrawMainTabs(CameraFollow cameraFollow)
    {
        // 使用传统的工具栏实现标签页效果
        _selectedTab = GUILayout.Toolbar(_selectedTab, _tabNames);
        
        GUILayout.Space(5);
        
        // 根据选中的标签页绘制对应内容
        switch (_selectedTab)
        {
            case 0: // 目标设置
                    DrawTargetTab(cameraFollow);
                    DrawTargetGroupTab(cameraFollow);
                break;
            case 1: // 移动控制
                DrawMovementTab(cameraFollow);
                break;
            case 2: // 寻路镜头
                DrawPathFollowTab(cameraFollow);
                break;
            case 3: // 战斗镜头
                DrawEnterBattleTab(cameraFollow);
                break;
            case 4: // 遮挡检测
                DrawObstructionTab(cameraFollow);
                break;
            case 5: // 强制过渡
                DrawForcedTransitionTab(cameraFollow);
                break;
        }
    }

    private void DrawTargetTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            bool allowSceneObjects = !EditorUtility.IsPersistent(cameraFollow);
            cameraFollow.Target = (Transform)EditorGUILayout.ObjectField("目标", cameraFollow.Target, typeof(Transform), allowSceneObjects);
            cameraFollow.targetOffset = EditorGUILayout.Vector3Field("目标偏移量", cameraFollow.targetOffset);
            cameraFollow.AllowZoom = EditorGUILayout.Toggle("是否允许缩放", cameraFollow.AllowZoom);

            if (cameraFollow.AllowZoom)
            {
                EditorGUI.indentLevel++;
                cameraFollow.ZoomSmoothing = EditorGUILayout.FloatField("缩放平滑系数", cameraFollow.ZoomSmoothing);
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space(10);
            SirenixEditorGUI.BeginBox("距离设置");
            {
                EditorGUILayout.LabelField($"范围: {cameraFollow.MinDistance:F0} - {cameraFollow.MaxDistance:F0}");
                EditorGUILayout.MinMaxSlider(ref cameraFollow.MinDistance, ref cameraFollow.MaxDistance, 1, 200);
                cameraFollow.MinDistance = Mathf.Round(cameraFollow.MinDistance);
                cameraFollow.MaxDistance = Mathf.Round(cameraFollow.MaxDistance);
            }
            SirenixEditorGUI.EndBox();
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawTargetGroupTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            // 目标组信息
            SirenixEditorGUI.BeginBox("目标组信息");
            {
                EditorGUILayout.LabelField("数量", cameraFollow._targetGroup.Count.ToString());
                
                // 使用序列化属性访问私有字段
                if (_targetGroupCenterProp != null && _targetGroupRadiusProp != null)
                {
                    using (new EditorGUI.DisabledScope(true))
                    {
                        EditorGUILayout.PropertyField(_targetGroupCenterProp, new GUIContent("中心点"));
                        EditorGUILayout.PropertyField(_targetGroupRadiusProp, new GUIContent("半径"));
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("中心点", "属性未找到");
                    EditorGUILayout.LabelField("半径", "属性未找到");
                }
            }
            SirenixEditorGUI.EndBox();

            // 目标组设置
            SirenixEditorGUI.BeginBox("目标组设置");
            {
                cameraFollow._maxCameraFocalAngle = EditorGUILayout.FloatField("战斗时自动转向Boss角度阈值", cameraFollow._maxCameraFocalAngle);
                cameraFollow._maxCameraFocalCenterAngle = EditorGUILayout.FloatField("战斗时自动转向目标组中心角度阈值", cameraFollow._maxCameraFocalCenterAngle);
                cameraFollow._cameraAngleAdjustSpeed = EditorGUILayout.FloatField("战斗时自动转向角度速度(度/秒)", cameraFollow._cameraAngleAdjustSpeed);
            }
            SirenixEditorGUI.EndBox();

            // 添加目标
            SirenixEditorGUI.BeginBox("添加目标");
            {
                EditorGUILayout.BeginHorizontal();
                _newTarget = (Transform)EditorGUILayout.ObjectField("新目标", _newTarget, typeof(Transform), true);
                
                using (new EditorGUI.DisabledScope(_newTarget == null))
                {
                    if (GUILayout.Button("添加", GUILayout.Width(60)))
                    {
                        cameraFollow.AddTarget(_newTarget);
                        _newTarget = null;
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
            SirenixEditorGUI.EndBox();

            // 目标列表
            SirenixEditorGUI.BeginBox("目标列表");
            {
                for (int i = 0; i < cameraFollow._targetGroup.Count; i++)
                {
                    var targetInfo = cameraFollow._targetGroup[i];
                    
                    SirenixEditorGUI.BeginHorizontalToolbar();
                    {
                        targetInfo.target = (Transform)EditorGUILayout.ObjectField($"目标{i + 1}", targetInfo.target, typeof(Transform), true, GUILayout.ExpandWidth(true));
                        
                        GUILayout.Label("权重", GUILayout.Width(40));
                        targetInfo.weight = EditorGUILayout.FloatField(targetInfo.weight, GUILayout.Width(50));
                        
                        GUILayout.Label("半径", GUILayout.Width(40));
                        targetInfo.radius = EditorGUILayout.FloatField(targetInfo.radius, GUILayout.Width(50));
                        
                        if (SirenixEditorGUI.IconButton(EditorIcons.X))
                        {
                            cameraFollow.RemoveTarget(targetInfo.target);
                            break;
                        }
                    }
                    SirenixEditorGUI.EndHorizontalToolbar();
                }

                EditorGUILayout.Space(5);
                if (SirenixEditorGUI.Button("清空目标组", ButtonSizes.Medium))
                {
                    cameraFollow.ClearTargetGroup();
                }
            }
            SirenixEditorGUI.EndBox();
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawMovementTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            cameraFollow.OriginAngle = EditorGUILayout.Vector2Field("初始角度（X为俯仰角，Y为偏航角）", cameraFollow.OriginAngle);
            cameraFollow.AllowRotation = EditorGUILayout.Toggle("是否允许旋转", cameraFollow.AllowRotation);

            if (cameraFollow.AllowRotation && (cameraFollow.AllowXRotation || cameraFollow.AllowYRotation))
            {
                SirenixEditorGUI.BeginBox("旋转限制");
                {
                    EditorGUILayout.LabelField($"俯仰角限制: {cameraFollow.MinPitchAngle:F0}° - {cameraFollow.MaxPitchAngle:F0}°");
                    EditorGUILayout.MinMaxSlider(ref cameraFollow.MinPitchAngle, ref cameraFollow.MaxPitchAngle, -85, 85);
                    
                    cameraFollow.MinYawAngle = EditorGUILayout.Slider("最小偏航角", cameraFollow.MinYawAngle, -50, 0);
                    cameraFollow.MaxYawAngle = EditorGUILayout.Slider("最大偏航角", cameraFollow.MaxYawAngle, 0, 50);
                    
                    cameraFollow.MinPitchAngle = Mathf.Round(cameraFollow.MinPitchAngle);
                    cameraFollow.MaxPitchAngle = Mathf.Round(cameraFollow.MaxPitchAngle);
                    
                    cameraFollow.RotationSmoothing = EditorGUILayout.FloatField("旋转平滑系数", cameraFollow.RotationSmoothing);
                }
                SirenixEditorGUI.EndBox();
            }
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawPathFollowTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            cameraFollow.isAutoPathfinding = EditorGUILayout.Toggle("是否处于自动寻路状态", cameraFollow.isAutoPathfinding);
            cameraFollow.autoAdjustDelay = EditorGUILayout.FloatField("跟随调整延迟", cameraFollow.autoAdjustDelay);
            cameraFollow.autoAdjustSpeed = EditorGUILayout.FloatField("跟随调整速度(度/秒)", cameraFollow.autoAdjustSpeed);
            cameraFollow.angleReachThreshold = EditorGUILayout.FloatField("角度达到目标的阈值(度)", cameraFollow.angleReachThreshold);
            cameraFollow.behindPlayerAngleThreshold = EditorGUILayout.FloatField("寻路时判定为在玩家后方的角度阈值(度)", cameraFollow.behindPlayerAngleThreshold);
            cameraFollow.behindPlayerAngleByMannul = EditorGUILayout.FloatField("NPC对话时判定为在玩家后方的角度阈值(度)", cameraFollow.behindPlayerAngleByMannul);
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawEnterBattleTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            cameraFollow.isEnterBattle = EditorGUILayout.Toggle("是否处于入战状态", cameraFollow.isEnterBattle);
            cameraFollow.EnterBattleDistance = EditorGUILayout.FloatField("入战相机距离", cameraFollow.EnterBattleDistance);
            cameraFollow.EnterBattleAngleX = EditorGUILayout.FloatField("入战相机俯仰角", cameraFollow.EnterBattleAngleX);
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawObstructionTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            cameraFollow.enableBuildingObstructionAvoidance = EditorGUILayout.Toggle("是否启用建筑物遮挡避免", cameraFollow.enableBuildingObstructionAvoidance);

            SirenixEditorGUI.BeginBox("调试设置");
            {
                cameraFollow.DebugDrawRays = EditorGUILayout.Toggle("是否绘制调试射线", cameraFollow.DebugDrawRays);
                cameraFollow.DebugRayDrawDuration = EditorGUILayout.FloatField("调试射线绘制持续时间", cameraFollow.DebugRayDrawDuration);
                cameraFollow._rayNormalColor = EditorGUILayout.ColorField("普通射线颜色", cameraFollow._rayNormalColor);
                cameraFollow._rayObstructedColor = EditorGUILayout.ColorField("被遮挡射线颜色", cameraFollow._rayObstructedColor);
                cameraFollow._rayHitNormalColor = EditorGUILayout.ColorField("命中点法线颜色", cameraFollow._rayHitNormalColor);
            }
            SirenixEditorGUI.EndBox();

            SirenixEditorGUI.BeginBox("状态信息");
            {
                // 使用序列化属性访问私有字段
                if (_isObstructedRestoringProp != null)
                {
                    using (new EditorGUI.DisabledScope(true))
                    {
                        EditorGUILayout.PropertyField(_isObstructedRestoringProp, new GUIContent("是否恢复中"));
                    }
                }
                else
                {
                    using (new EditorGUI.DisabledScope(true))
                    {
                        EditorGUILayout.Toggle("是否恢复中", false);
                    }
                }
                cameraFollow.obstructionReturnSpeed = EditorGUILayout.FloatField("恢复速度", cameraFollow.obstructionReturnSpeed);
            }
            SirenixEditorGUI.EndBox();
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawForcedTransitionTab(CameraFollow cameraFollow)
    {
        SirenixEditorGUI.BeginVerticalList();
        {
            cameraFollow.forcedAngleTransitionSpeed = EditorGUILayout.FloatField("强制过渡速度(度/秒)", cameraFollow.forcedAngleTransitionSpeed);
            
            SirenixEditorGUI.BeginBox("状态信息");
            {
                using (new EditorGUI.DisabledScope(true))
                {
                    EditorGUILayout.Toggle("是否正在强制过渡", cameraFollow.IsForcedAngleTransitioning());
                }
            }
            SirenixEditorGUI.EndBox();
            
            if (Application.isPlaying)
            {
                SirenixEditorGUI.BeginBox("运行时测试");
                {
                    SirenixEditorGUI.BeginHorizontalToolbar();
                    {
                        if (SirenixEditorGUI.Button("过渡到 (30, 0)", ButtonSizes.Medium))
                        {
                            cameraFollow.ForceTransitionToAngle(new Vector2(30, 0), false);
                        }
                        if (SirenixEditorGUI.Button("立即设置到 (30, 0)", ButtonSizes.Medium))
                        {
                            cameraFollow.ForceTransitionToAngle(new Vector2(30, 0), true);
                        }
                    }
                    SirenixEditorGUI.EndHorizontalToolbar();
                    
                    SirenixEditorGUI.BeginHorizontalToolbar();
                    {
                        if (SirenixEditorGUI.Button("过渡到偏航角 90", ButtonSizes.Medium))
                        {
                            cameraFollow.ForceTransitionToYaw(90, false);
                        }
                        if (SirenixEditorGUI.Button("停止强制过渡", ButtonSizes.Medium))
                        {
                            cameraFollow.StopForcedAngleTransition();
                        }
                    }
                    SirenixEditorGUI.EndHorizontalToolbar();
                }
                SirenixEditorGUI.EndBox();
            }
        }
        SirenixEditorGUI.EndVerticalList();
    }

    private void DrawRuntimeTests(CameraFollow cameraFollow)
    {
        if (Application.isPlaying && !cameraFollow._useTargetGroup)
        {
            SirenixEditorGUI.BeginBox("运行时旋转测试");
            {
                SirenixEditorGUI.BeginHorizontalToolbar();
                {
                    if (SirenixEditorGUI.Button("顺时针旋转", ButtonSizes.Medium))
                    {
                        cameraFollow.RoationAround(RotationDuration, true);
                    }
                    if (SirenixEditorGUI.Button("逆时针旋转", ButtonSizes.Medium))
                    {
                        cameraFollow.RoationAround(RotationDuration, false);
                    }
                }
                SirenixEditorGUI.EndHorizontalToolbar();
            }
            SirenixEditorGUI.EndBox();
        }
    }
    #endregion

    #region 辅助方法
    private void ApplyCameraMode()
    {
        var cameraFollow = (CameraFollow)target;
        
        if (_modeSwitch == (int)CameraMode.Locked)
        {
            cameraFollow.AllowRotation = true;
            cameraFollow.AllowXRotation = true;
            cameraFollow.AllowYRotation = false;
            cameraFollow.Distance = DefaultDistance;
            cameraFollow.ChangeAngle(DefaultAngle);
        }
        else if (_modeSwitch == (int)CameraMode.Free)
        {
            cameraFollow.AllowRotation = true;
            cameraFollow.AllowXRotation = true;
            cameraFollow.AllowYRotation = true;
        }
    }

    private void HandleChanges(CameraFollow cameraFollow)
    {
        if (GUI.changed)
        {
            EditorUtility.SetDirty(cameraFollow);
            
            cameraFollow.ClampRotationAndDistance();
            cameraFollow.SyncFieldOfView();
            cameraFollow.SyncRotation();

            if (!Application.isPlaying)
            {
                EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            }
        }
    }
    #endregion
}
