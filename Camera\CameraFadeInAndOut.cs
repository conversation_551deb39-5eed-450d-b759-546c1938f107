﻿using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CameraFadeInAndOut : MonoBehaviour {

	public Shader shader;
	[Range(0, 1)]
	public float fadeProcess = 1;
	public float time = 1;
	private Material mat;
	private Tweener tweener = null;
	private Camera selfCamera = null;

	public static CameraFadeInAndOut Instance = null;

	void Awake()
	{
		if (Instance == null)
		{
			Instance = this;
		}
		else
		{
			Debug.LogError("请不要重复实例化单例");
		}
		this.selfCamera = GetComponent<Camera>();
		Reset();
	}

	void Start () {
		this.mat = new Material(this.shader);
	}
	
	void Update ()
	{
		this.mat.SetFloat("_Process", fadeProcess);
	}

	private void OnRenderImage(RenderTexture source, RenderTexture destination)
	{
		Graphics.Blit(source, destination, this.mat);
	}

	private void Reset()
	{
		fadeProcess = 1;
		enabled = false;
		this.selfCamera.enabled = false;
	}

	public void TriggerFade()
	{
		this.KillFadeTween();
		enabled = true;
		this.selfCamera.enabled = true;
		tweener = DOTween.To((x) => fadeProcess = x, 1, 0, time).SetLoops(2, LoopType.Yoyo).SetEase(Ease.Linear).OnComplete(() => {
			Reset();
		});
	}

    public void SetCameraDepth(float depth)
    {
        this.selfCamera.depth = depth;
    }

	public void KillFadeTween()
	{
		if(tweener != null)
		{
			tweener.Kill();
			tweener = null;
		}
		Reset();
	}
}
