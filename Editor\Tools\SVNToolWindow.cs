﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class SVNToolWindow : EditorWindow {

	[MenuItem("Tools/SVN/操作窗口", false, 60)]
	static void QuickSvnWindow()
	{
		EditorWindow myWindow = (SVNToolWindow)EditorWindow.GetWindow(typeof(SVNToolWindow), false, "SVN 操作");//创建窗口
		myWindow.Show();//展示
	}

	//都预留三处位置
	private string commit_path1 = "";
	private string commit_path2 = "";
	private string commit_path3 = "";
    private string commit_path4 = "";
    private string commit_path5 = "";

    private List<string> other_commit_list = new List<string>();

    private void Awake()
	{
		commit_path1 = EditorPrefs.GetString("svn_commit_path_1", "");	
		commit_path2 = EditorPrefs.GetString("svn_commit_path_2", "");	
		commit_path3 = EditorPrefs.GetString("svn_commit_path_3", "");
        commit_path4 = EditorPrefs.GetString("svn_commit_path_4", "");
        commit_path5 = EditorPrefs.GetString("svn_commit_path_5", "");


        if (string.IsNullOrEmpty(commit_path1))
        {
            commit_path1 = Application.dataPath + "/Game/UIs/View";
            EditorPrefs.SetString("svn_commit_path_1", commit_path1);
        }

        if (string.IsNullOrEmpty(commit_path2))
        {
            commit_path2 = Application.dataPath + "/Game/Lua";
            EditorPrefs.SetString("svn_commit_path_2", commit_path2);
        }

        if (string.IsNullOrEmpty(commit_path3))
        {
            commit_path3 = Application.dataPath + "/Game/UIs/RawImages";
            EditorPrefs.SetString("svn_commit_path_3", commit_path3);
        }

        if (string.IsNullOrEmpty(commit_path4))
        {
            commit_path4 = Application.dataPath + "/Game/UIs/Images";
            EditorPrefs.SetString("svn_commit_path_4", commit_path4);
        }

        if (string.IsNullOrEmpty(commit_path5))
        {
            commit_path5 = Application.dataPath + "/Game/UIs/Icons";
            EditorPrefs.SetString("svn_commit_path_5", commit_path5);
        }

        // 将数据插入进去
        for (int i = 0; i < 10; i++)
        {
            string path = EditorPrefs.GetString("svn_commit_other_path_" + i, "");
            if (string.IsNullOrEmpty(path))
                break;

            other_commit_list.Add(path);
        }
    }

	private void OnGUI()
	{
        string temp_input = string.Empty;

        EditorGUILayout.LabelField("选择文件夹提交", GUILayout.Width(500f));
        EditorGUILayout.LabelField("视图文件夹");
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        commit_path1 = EditorGUILayout.TextField(commit_path1, GUILayout.Width(400f));
        if (GUILayout.Button("浏览", GUILayout.Width(50)))
        {
            temp_input = string.Empty;
            temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取Views文件夹");
            if (!string.IsNullOrEmpty(temp_input))
            {
                commit_path1 = temp_input;
                EditorPrefs.SetString("svn_commit_path_1", commit_path1);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        EditorGUILayout.LabelField("Lua文件夹");
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        commit_path2 = EditorGUILayout.TextField(commit_path2, GUILayout.Width(400f));
        if (GUILayout.Button("浏览", GUILayout.Width(50)))
        {
            temp_input = string.Empty;
            temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取Lua文件夹");
            if (!string.IsNullOrEmpty(temp_input))
            {
                commit_path2 = temp_input;
                EditorPrefs.SetString("svn_commit_path_2", commit_path2);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        EditorGUILayout.LabelField("RawImage文件夹");
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        commit_path3 = EditorGUILayout.TextField(commit_path3, GUILayout.Width(400f));
        if (GUILayout.Button("浏览", GUILayout.Width(50)))
        {
            temp_input = string.Empty;
            temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取RawImage文件夹");
            if (!string.IsNullOrEmpty(temp_input))
            {
                commit_path3 = temp_input;
                EditorPrefs.SetString("svn_commit_path_3", commit_path3);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        EditorGUILayout.LabelField("Image文件夹");
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        commit_path4 = EditorGUILayout.TextField(commit_path4, GUILayout.Width(400f));
        if (GUILayout.Button("浏览", GUILayout.Width(50)))
        {
            temp_input = string.Empty;
            temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取Image文件夹");
            if (!string.IsNullOrEmpty(temp_input))
            {
                commit_path4 = temp_input;
                EditorPrefs.SetString("svn_commit_path_4", commit_path4);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        EditorGUILayout.LabelField("Icons文件夹");
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        commit_path5 = EditorGUILayout.TextField(commit_path5, GUILayout.Width(400f));
        if (GUILayout.Button("浏览", GUILayout.Width(50)))
        {
            temp_input = string.Empty;
            temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取Icons文件夹");
            if (!string.IsNullOrEmpty(temp_input))
            {
                commit_path5 = temp_input;
                EditorPrefs.SetString("svn_commit_path_5", commit_path5);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        EditorGUILayout.LabelField("拓展文件夹");
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("点击添加一个", GUILayout.Width(200f)))
        {
            if (other_commit_list.Count >= 10)
            {
                EditorUtility.DisplayDialog("警告", "额外拓展文件夹最多拓展10个！", "确定");
            }
            other_commit_list.Add(string.Empty);
        }

        if (GUILayout.Button("点击删除一个", GUILayout.Width(200f)))
        {
            if (other_commit_list.Count <= 0)
            {
                EditorUtility.DisplayDialog("警告", "当前没有拓展文件夹，请先添加！", "确定");
            }
            else
            {
                int index = other_commit_list.Count - 1;
                other_commit_list.RemoveAt(index);
                EditorPrefs.SetString(string.Format("svn_commit_other_path_{0}", index), "");
            }
        }
        GUILayout.EndHorizontal();

        if (other_commit_list.Count > 0)
        {
            for (int i = 0; i < other_commit_list.Count; i++)
            {
                GUILayout.BeginHorizontal();
                GUILayout.Label(string.Format("拓展第{0}个", i + 1), GUILayout.Width(80f));
                string commit_path = other_commit_list[i];

                other_commit_list[i] = EditorGUILayout.TextField(other_commit_list[i], GUILayout.Width(400f));
                if (GUILayout.Button("浏览", GUILayout.Width(50)))
                {
                    temp_input = string.Empty;
                    temp_input = EditorUtility.OpenFolderPanel("选取", Application.dataPath, "选取拓展文件夹");
                    if (!string.IsNullOrEmpty(temp_input))
                    {
                        other_commit_list[i] = temp_input;
                        EditorPrefs.SetString(string.Format("svn_commit_other_path_{0}", i), other_commit_list[i]);
                    }
                }

                GUILayout.EndHorizontal();
            }
        }

        GUILayout.Space(10);
        if (GUILayout.Button("一键提交", GUILayout.Width(200f)))
        {
            this.CommitFunction();
        }
    }


	private void CommitFunction()
	{
        List<string> pathList = new List<string>();
        pathList.Add(commit_path1);
        pathList.Add(commit_path2);
        pathList.Add(commit_path3);
        pathList.Add(commit_path4);
        pathList.Add(commit_path5);

        for (int i = 0; i < other_commit_list.Count; i++)
        {
            if (!string.IsNullOrEmpty(other_commit_list[i]))
            {
                pathList.Add(other_commit_list[i]);
            }
        }

        string commitPath = string.Join("*", pathList.ToArray());
        SVNTool.ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
	}

	private void UpdateFunction(string update_path)
	{
		SVNTool.ProcessCommand ("TortoiseProc.exe", "/command:update /path:" + update_path + " /closeonend:0");
	}
}
