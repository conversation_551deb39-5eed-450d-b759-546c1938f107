﻿using UnityEngine;
using UnityEditor;
using Nirvana;
using Unity.VisualScripting;

[InitializeOnLoad]
public class UINameTableExtendEditor
{
	private static bool m_IsActivity = true;
	private static UINameTable name_table = null;

	static UINameTableExtendEditor()
	{
		EditorApplication.hierarchyWindowItemOnGUI += OnUITableGUI;
	}

	private static void OnUITableGUI(int instanceID, Rect selectionRect)
	{
		if (Application.isPlaying)
		{
			return;
		}

		GameObject selectObj = EditorUtility.InstanceIDToObject(instanceID) as GameObject;

		if (selectObj != null)
		{
			//过滤.
			name_table = selectObj.GetComponentInParent<UINameTable>();
			if (name_table == null || name_table.gameObject == selectObj)
			{
				return;
			}

			if (!m_IsActivity)
			{
				return;
			}

			//过滤.
			bool flag = false;
			bool flag2 = false;
			string name_text = selectObj.name;
			foreach (var bind in name_table.binds)
			{
				if (bind.Widget == selectObj)
				{
					flag = true;
					break;
				}

				if (bind.Name == name_text)
				{
					flag2 = true;
					break;
				}
			}

			//在列表内.
			if (flag)
			{
				//移除按钮.
				Rect delBtn = new Rect(selectionRect.xMax - 30, selectionRect.y, 50, selectionRect.height);
				if (GUI.Button(delBtn, "移除") && name_table != null)
				{
					for (int i = 0; i < name_table.binds.Count; i++)
					{
						UINameTable.BindPair bind = name_table.binds[i];

						if (bind.Widget == selectObj)
						{
							name_table.binds.RemoveAt(i);

							if (name_table.Lookup.ContainsKey(bind.Name))
							{
								name_table.Lookup.Remove(bind.Name);
							}
						}
					}

					EditorUtility.SetDirty(name_table);
                }
				return;
			}

			//列表内名称相同.
			if (flag2)
			{
				return;
			}

			//添加按钮.
			Rect addBtn = new Rect(selectionRect.xMax - 80, selectionRect.y, 50, selectionRect.height);
			if (GUI.Button(addBtn, "添加") && name_table != null)
			{
				if (name_table.Lookup.ContainsKey(name_text))
				{
					name_table.Lookup.Remove(name_text);
				}

				name_table.Add(name_text, selectObj);
                EditorUtility.SetDirty(name_table);
            }
		}
	}

	[MenuItem("Tools/UI Tools/显示隐藏 UINameTable 快捷添加移除按钮")]
	[MenuItem("CONTEXT/UINameTable/显示隐藏 快捷添加移除按钮", false)]
	static void SetBtnActivity(MenuCommand cmd)
	{
		m_IsActivity = !m_IsActivity;
	}
}