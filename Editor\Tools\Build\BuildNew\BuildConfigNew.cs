﻿using UnityEditor;
using UnityEngine;
using Nirvana;
using Nirvana.Editor;

[CreateAssetMenu(fileName = "BuildConfigNew", menuName = "Nirvana/Build/BuildConfigNew")]
public sealed class BuildConfigNew : ScriptableObject
{
    [SerializeField]
    private OptionalStringNew companyName;

    [SerializeField]
    private OptionalStringNew productName;

    [SerializeField]
    private OptionalProductNameI18NNew productNameI18N;

    [SerializeField]
    private OptionalStringNew version;

    [SerializeField]
    private OptionalStringListNew defineSymbols;

    [SerializeField]
    private OptionalStringListNew scenes;

    [SerializeField]
    private OptionalSplashScreenConfigNew splashScreen;

    [SerializeField]
    [EnumLabel]
    private BuildDevice targetDevice;

    [SerializeField]
    private OptionalStringNew iOSApplicationIdentifier;

    [SerializeField]
    private OptionalStringNew iOSProvisioningProfile;

    [SerializeField]
    private OptionalStringNew iOSCodeSignIdentity;

    [SerializeField]
    private OptionalStringNew iOSBuildNumber;

    [SerializeField]
    private OptionalStringNew iOSApplicationDisplayName;

    [SerializeField]
    private OptionalBoolNew iOSEnableAutomaticSigning;

    [SerializeField]
    private OptionalStringNew iOSDeveloperTeamID;

    [SerializeField]
    private OptionalBoolNew iOSEnableBitCode;

    [SerializeField]
    private string[] iOSFrameworks;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon180;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon167;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon152;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon144;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon120;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon114;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon76;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon72;

    [SerializeField]
    private OptionalTexture2DNew iOSIcon57;

    [SerializeField]
    private OptionalTexture2DNew iOSSpotlightIcon120;

    [SerializeField]
    private OptionalTexture2DNew iOSSpotlightIcon80;

    [SerializeField]
    private OptionalTexture2DNew iOSSpotlightIcon40;

    [SerializeField]
    private OptionalTexture2DNew iOSSettingsIcon87;

    [SerializeField]
    private OptionalTexture2DNew iOSSettingsIcon58;

    [SerializeField]
    private OptionalTexture2DNew iOSSettingsIcon29;

    [SerializeField]
    private OptionalTexture2DNew iOSNotificationIcon60;

    [SerializeField]
    private OptionalTexture2DNew iOSNotificationIcon40;

    [SerializeField]
    private OptionalTexture2DNew iOSNotificationIcon20;

    [SerializeField]
    private OptionalTexture2DNew iOSAppstoreIcon1024;

    [SerializeField]
    private OptionalStringNew androidApplicationIdentifier;

    [SerializeField]
    private OptionalIntNew androidBundleVersionCode;

    [SerializeField]
    private OptionalStringNew androidKeystoreName;

    [SerializeField]
    private OptionalStringNew androidKeystorePass;

    [SerializeField]
    private OptionalStringNew androidKeyaliasName;

    [SerializeField]
    private OptionalStringNew androidKeyaliasPass;

    [SerializeField]
    private OptionalTexture2DNew androidIcon192;

    [SerializeField]
    private OptionalTexture2DNew androidIcon144;

    [SerializeField]
    private OptionalTexture2DNew androidIcon96;

    [SerializeField]
    private OptionalTexture2DNew androidIcon72;

    [SerializeField]
    private OptionalTexture2DNew androidIcon48;

    [SerializeField]
    private OptionalTexture2DNew androidIcon36;

    [SerializeField]
    private OptionalBoolNew desktopDefaultIsFullScreen;

    [SerializeField]
    private OptionalBoolNew desktopDefaultIsNativeResolution;

    [SerializeField]
    private OptionalIntNew desktopDefaultScreenWidth;

    [SerializeField]
    private OptionalIntNew desktopDefaultScreenHeight;

    [SerializeField]
    private OptionalBoolNew desktopResizableWindow;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon1024;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon512;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon256;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon128;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon48;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon32;

    [SerializeField]
    private OptionalTexture2DNew desktopIcon16;

    public void SetupConfig(ref BuildConfigDataNew data)
    {
        if (companyName.HasValue)
        {
            data.CompanyName = companyName.Value;
        }

        if (productName.HasValue)
        {
            data.ProductName = productName.Value;
        }

        if (productNameI18N.HasValue)
        {
            data.ProductNameI18N = productNameI18N.Value;
        }

        if (version.HasValue)
        {
            data.Version = version.Value;
        }

        if (defineSymbols.HasValue)
        {
            data.DefineSymbols = defineSymbols.Value.ToArray();
        }

        if (scenes.HasValue)
        {
            data.Scenes = scenes.Value.ToArray();
        }

        if (splashScreen.HasValue)
        {
            data.SplashScreen = splashScreen.Value;
        }

        switch (targetDevice)
        {
            case BuildDevice.iOS:
                SetupConfigIOS(ref data);
                break;
            case BuildDevice.Android:
                SetupConfigAndroid(ref data);
                break;
            case BuildDevice.Desktop:
            case BuildDevice.Desktop32:
                SetupConfigDesktop(ref data);
                break;
        }
    }

    private void SetupConfigIOS(ref BuildConfigDataNew data)
    {
        if (iOSApplicationIdentifier.HasValue)
        {
            data.IOSApplicationIdentifier = iOSApplicationIdentifier.Value;
        }

        if (iOSProvisioningProfile.HasValue)
        {
            data.IOSProvisioningProfile = iOSProvisioningProfile.Value;
        }

        if (iOSCodeSignIdentity.HasValue)
        {
            data.IOSCodeSignIdentity = iOSCodeSignIdentity.Value;
        }

        if (iOSBuildNumber.HasValue)
        {
            data.IOSBuildNumber = iOSBuildNumber.Value;
        }

        if (iOSApplicationDisplayName.HasValue)
        {
            data.IOSApplicationDisplayName = iOSApplicationDisplayName.Value;
        }

        if (iOSEnableAutomaticSigning.HasValue)
        {
            data.IOSEnableAutomaticSigning = iOSEnableAutomaticSigning.Value;
        }

        if (iOSDeveloperTeamID.HasValue)
        {
            data.IOSDeveloperTeamID = iOSDeveloperTeamID.Value;
        }

        if (iOSEnableBitCode.HasValue)
        {
            data.IOSEnableBitCode = iOSEnableBitCode.Value;
        }

        if (iOSFrameworks != null && iOSFrameworks.Length != 0)
        {
            if (data.IOSFrameworks == null)
            {
                data.IOSFrameworks = iOSFrameworks;
            }
            else
            {
                ArrayUtility.AddRange(ref data.IOSFrameworks, iOSFrameworks);
            }
        }

        if (iOSIcon180.HasValue)
        {
            data.IOSIcon180 = iOSIcon180.Value;
        }

        if (iOSIcon167.HasValue)
        {
            data.IOSIcon167 = iOSIcon167.Value;
        }

        if (iOSIcon152.HasValue)
        {
            data.IOSIcon152 = iOSIcon152.Value;
        }

        if (iOSIcon144.HasValue)
        {
            data.IOSIcon144 = iOSIcon144.Value;
        }

        if (iOSIcon120.HasValue)
        {
            data.IOSIcon120 = iOSIcon120.Value;
        }

        if (iOSIcon114.HasValue)
        {
            data.IOSIcon114 = iOSIcon114.Value;
        }

        if (iOSIcon76.HasValue)
        {
            data.IOSIcon76 = iOSIcon76.Value;
        }

        if (iOSIcon72.HasValue)
        {
            data.IOSIcon72 = iOSIcon72.Value;
        }

        if (iOSIcon57.HasValue)
        {
            data.IOSIcon57 = iOSIcon57.Value;
        }

        if (iOSSpotlightIcon120.HasValue)
        {
            data.IOSSpotlightIcon120 = iOSSpotlightIcon120.Value;
        }

        if (iOSSpotlightIcon80.HasValue)
        {
            data.IOSSpotlightIcon80 = iOSSpotlightIcon80.Value;
        }

        if (iOSSpotlightIcon40.HasValue)
        {
            data.IOSSpotlightIcon40 = iOSSpotlightIcon40.Value;
        }

        if (iOSSettingsIcon87.HasValue)
        {
            data.IOSSettingsIcon87 = iOSSettingsIcon87.Value;
        }

        if (iOSSettingsIcon58.HasValue)
        {
            data.IOSSettingsIcon58 = iOSSettingsIcon58.Value;
        }

        if (iOSSettingsIcon29.HasValue)
        {
            data.IOSSettingsIcon29 = iOSSettingsIcon29.Value;
        }

        if (iOSNotificationIcon60.HasValue)
        {
            data.IOSNotificationIcon60 = iOSNotificationIcon60.Value;
        }

        if (iOSNotificationIcon40.HasValue)
        {
            data.IOSNotificationIcon40 = iOSNotificationIcon40.Value;
        }

        if (iOSNotificationIcon20.HasValue)
        {
            data.IOSNotificationIcon20 = iOSNotificationIcon20.Value;
        }

        if (iOSAppstoreIcon1024.HasValue)
        {
            data.IOSAppstoreIcon1024 = iOSAppstoreIcon1024.Value;
        }
    }

    private void SetupConfigAndroid(ref BuildConfigDataNew data)
    {
        if (androidApplicationIdentifier.HasValue)
        {
            data.AndroidApplicationIdentifier = androidApplicationIdentifier.Value;
        }

        if (androidBundleVersionCode.HasValue)
        {
            data.AndroidBundleVersionCode = androidBundleVersionCode.Value;
        }

        if (androidKeystoreName.HasValue)
        {
            data.AndroidKeystoreName = androidKeystoreName.Value;
        }

        if (androidKeystorePass.HasValue)
        {
            data.AndroidKeystorePass = androidKeystorePass.Value;
        }

        if (androidKeyaliasName.HasValue)
        {
            data.AndroidKeyaliasName = androidKeyaliasName.Value;
        }

        if (androidKeyaliasPass.HasValue)
        {
            data.AndroidKeyaliasPass = androidKeyaliasPass.Value;
        }

        if (androidIcon192.HasValue)
        {
            data.AndroidIcon192 = androidIcon192.Value;
        }

        if (androidIcon144.HasValue)
        {
            data.AndroidIcon144 = androidIcon144.Value;
        }

        if (androidIcon96.HasValue)
        {
            data.AndroidIcon96 = androidIcon96.Value;
        }

        if (androidIcon72.HasValue)
        {
            data.AndroidIcon72 = androidIcon72.Value;
        }

        if (androidIcon48.HasValue)
        {
            data.AndroidIcon48 = androidIcon48.Value;
        }

        if (androidIcon36.HasValue)
        {
            data.AndroidIcon36 = androidIcon36.Value;
        }
    }

    private void SetupConfigDesktop(ref BuildConfigDataNew data)
    {
        if (desktopDefaultIsFullScreen.HasValue)
        {
            data.DesktopDefaultIsFullScreen = desktopDefaultIsFullScreen.Value;
        }

        if (desktopDefaultIsNativeResolution.HasValue)
        {
            data.DesktopDefaultIsNativeResolution = desktopDefaultIsNativeResolution.Value;
        }

        if (desktopDefaultScreenWidth.HasValue)
        {
            data.DesktopDefaultScreenWidth = desktopDefaultScreenWidth.Value;
        }

        if (desktopDefaultScreenHeight.HasValue)
        {
            data.DesktopDefaultScreenHeight = desktopDefaultScreenHeight.Value;
        }

        if (desktopResizableWindow.HasValue)
        {
            data.DesktopResizableWindow = desktopResizableWindow.Value;
        }

        if (desktopIcon1024.HasValue)
        {
            data.DesktopIcon1024 = desktopIcon1024.Value;
        }

        if (desktopIcon512.HasValue)
        {
            data.DesktopIcon512 = desktopIcon512.Value;
        }

        if (desktopIcon256.HasValue)
        {
            data.DesktopIcon256 = desktopIcon256.Value;
        }

        if (desktopIcon128.HasValue)
        {
            data.DesktopIcon128 = desktopIcon128.Value;
        }

        if (desktopIcon48.HasValue)
        {
            data.DesktopIcon48 = desktopIcon48.Value;
        }

        if (desktopIcon32.HasValue)
        {
            data.DesktopIcon32 = desktopIcon32.Value;
        }

        if (desktopIcon16.HasValue)
        {
            data.DesktopIcon16 = desktopIcon16.Value;
        }
    }
}
