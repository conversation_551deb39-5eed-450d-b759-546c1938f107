﻿using UnityEngine;
using System.Collections.Generic;
using UnityEditor;
using System.IO;

public class SVNTool
{
    [MenuItem("Tools/SVN/UpdateAll", false, 1)]
    static void UpdateAll()
    {
        string applicationPath = Path.Combine(Application.dataPath, "../");
        List<string> pathList = new List<string>();
        pathList.Add(applicationPath);
        string commitPath = string.Join("*", pathList.ToArray());
        ProcessCommand("TortoiseProc.exe", "/command:update /path:" + commitPath + " /closeonend:0");
    }

    [MenuItem("Tools/SVN/Update", false, 2)]
    static void Update()
    {
        List<string> pathList = new List<string>();
        pathList.Add(Path.Combine(Application.dataPath, "../ProjectSettings"));
        pathList.Add(Path.Combine(Application.dataPath, "../Packages"));
        pathList.Add(Path.Combine(Application.dataPath, ""));
        string commitPath = string.Join("*", pathList.ToArray());
        ProcessCommand("TortoiseProc.exe", "/command:update /path:" + commitPath + " /closeonend:0");
    }

    [MenuItem("Tools/SVN/Commit", false, 3)]
    static void SVNCommit()
    {
        string applicationPath = Application.dataPath.Replace("/Assets", "");
        List<string> pathList = new List<string>();
        pathList.Add(Application.dataPath);
        pathList.Add(applicationPath + "/ProjectSettings");

        string commitPath = string.Join("*",pathList.ToArray());
        ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
    }

    [MenuItem("Tools/SVN/", false, 4)]
    static void Breaker() { }

    [MenuItem("Tools/SVN/CleanUp", false, 5)]
    static void SVNCleanUp()
    {
        string applicationPath = Application.dataPath;
        ProcessCommand("TortoiseProc.exe", "/command:cleanup /path:" + Application.dataPath.Replace("/Assets", ""));
    }

    [MenuItem("Tools/SVN/Log", false, 6)]
    static void SVNLog()
    {
        string applicationPath = Application.dataPath;
        ProcessCommand("TortoiseProc.exe", "/command:log /path:" + Application.dataPath.Replace("/Assets", ""));
    }

    [MenuItem("Tools/SVN/Update Android Dev AssetBundles", false, 7)]
    static void UpdateAndroidDevAssetBundles()
    {
        string applicationPath = Application.dataPath + "/../../AssetBundleDev/Android";
        if (Directory.Exists(applicationPath))
        {
            ProcessCommand("TortoiseProc.exe", "/command:update /path:" + applicationPath);
        }
    }

    [MenuItem("Tools/SVN/Update Windows Dev AssetBundles", false, 8)]
    static void UpdateWindowsDevAssetBundles()
    {
        string applicationPath = Application.dataPath + "/../../AssetBundleDev/Windows";
        if (Directory.Exists(applicationPath))
        {
            ProcessCommand("TortoiseProc.exe", "/command:update /path:" + applicationPath);
        }
    }

    public static void ProcessCommand(string command,string argument)
    {
        System.Diagnostics.ProcessStartInfo info = new System.Diagnostics.ProcessStartInfo(command);
        info.Arguments = argument;
        info.CreateNoWindow = false;
        info.ErrorDialog = true;
        info.UseShellExecute = true;

        if (info.UseShellExecute)
        {
            info.RedirectStandardOutput = false;
            info.RedirectStandardError = false;
            info.RedirectStandardInput = false;
        }
        else
        {
            info.RedirectStandardOutput = true;
            info.RedirectStandardError = true;
            info.RedirectStandardInput = true;
            info.StandardOutputEncoding = System.Text.UTF8Encoding.UTF8;
            info.StandardErrorEncoding = System.Text.UTF8Encoding.UTF8;
        }

        System.Diagnostics.Process process = System.Diagnostics.Process.Start(info);
        if (!info.UseShellExecute)
        {
            Debug.Log(process.StandardOutput);
            Debug.Log(process.StandardError);
        }

        process.WaitForExit();
        process.Close();
    }
}
