﻿using UnityEngine;
using LuaInterface;
using DG.Tweening;
using System;
using System.Collections.Generic;

/// <summary>
/// 相机跟随系统
/// 实现相机对目标物体的平滑跟随、旋转和缩放功能
/// 支持单目标跟随和多目标组跟随模式
/// </summary>
public class CameraFollow : MonoBehaviour
{
    #region 枚举定义
    /// <summary>
    /// 相机类型枚举
    /// </summary>
    public enum CameraType
    {
        Free,
        Fixed
    }
    #endregion

    #region 核心组件和引用
    [SerializeField] private AudioListener audioListener;           // 音频监听器组件
    [HideInInspector] public CameraType CurrentCameraType = CameraType.Free;    // 当前相机类型
    
    private Camera _mainCamera;                                     // 主相机组件
    private Transform _cachedTransform;                             // 缓存的Transform组件
    private Transform _focalPoint;                                  // 相机焦点
    private Action _onDistanceChangeHandle = null;                  // 距离变化回调函数
    #endregion

    #region 目标跟随 - 基础配置
    [HideInInspector] private Transform target;
    public Transform Target
    {
        set
        {
            if (value && value == target)
                return;
            target = value;
            MoveToTarget();
        }
        get
        {
            return target;
        }
    }

    [HideInInspector] public Vector3 targetOffset = Vector3.zero;   // 目标偏移量
    [HideInInspector] public float smoothOffsetSpeed = 5;           // 目标偏移平滑速度
    
    private Vector3 _currentOffset;                                 // 当前偏移量
    private Vector3 _freeCameraLocalPosition = new Vector3(0, 0, -3);  // 自由相机本地位置
    #endregion

    #region 相机视野配置
    [SerializeField] private float fieldOfView = 45;                // 相机视场角
    
    /// <summary>
    /// 视场角属性
    /// </summary>
    public float FieldOfView
    {
        get => fieldOfView;
        set
        {
            if (fieldOfView != value)
            {
                fieldOfView = value;
                SyncFieldOfView();
            }
        }
    }
    #endregion

    #region 旋转控制配置
    [HideInInspector] public bool AllowRotation = true;             // 是否允许旋转
    [HideInInspector] public bool AllowXRotation = true;            // 是否允许X轴旋转（俯仰角）
    [HideInInspector] public bool AllowYRotation = true;            // 是否允许Y轴旋转（偏航角）
    [HideInInspector] public Vector2 OriginAngle = new Vector2(45, 10);         // 初始角度（X为俯仰角，Y为偏航角）
    [HideInInspector] public Vector2 RotationSensitivity = new Vector2(5, 5);   // 旋转灵敏度（X为俯仰角灵敏度，Y为偏航角灵敏度）
    [HideInInspector] public float MinPitchAngle = 15;              // 最小俯仰角
    [HideInInspector] public float MaxPitchAngle = 55;              // 最大俯仰角
    [HideInInspector] public float MinYawAngle = -10;               // 最小偏航角
    [HideInInspector] public float MaxYawAngle = 10;                // 最大偏航角
    [HideInInspector] public float RotationSmoothing = 20;          // 旋转平滑系数
    [HideInInspector] public bool AutoSmoothing = true;             // 是否启用自动平滑

    private Vector2 _angle;                                         // 当前角度值
    private Quaternion _oldRotation;                                // 上一次的旋转值
    #endregion

    #region 距离控制配置
    [HideInInspector] public bool AllowZoom = true;                 // 是否允许缩放
    [HideInInspector] public float MaxDistance = 13;                // 最大跟随距离
    [HideInInspector] public float MinDistance = 1;                 // 最小跟随距离
    [HideInInspector] public float ZoomSmoothing = 10;              // 缩放平滑系数

    private float _distance = 12;                                   // 当前跟随距离
    private float _oldDistance;                                     // 上一次的距离值
    
    /// <summary>
    /// 当前跟随距离属性
    /// </summary>
    public float Distance
    {
        get => _distance;
        set
        {
            _distance = Mathf.Max(value, MinDistance);
            _onDistanceChangeHandle?.Invoke();
        }
    }
    #endregion

    #region 自动旋转配置
    public float autoRotationCD = 2f;                              // 自动旋转冷却时间
    public float autoRotationSpeedX = 30f;                         // X轴自动旋转速度
    public float autoRotationSpeedY = 30f;                         // Y轴自动旋转速度
    public float bestRotationX = 105f;                             // 最佳X轴旋转角度
    
    private bool _isAutoRotating = false;                          // 是否正在自动旋转
    private float _autoRotationStartTime = 0f;                     // 自动旋转开始时间
    
    /// <summary>
    /// 自动旋转属性
    /// </summary>
    public bool AutoRotation
    {
        get => _isAutoRotating;
        set
        {
            if (_isAutoRotating != value)
            {
                _isAutoRotating = value;
                _autoRotationStartTime = Time.realtimeSinceStartup + autoRotationCD;
            }
        }
    }
    #endregion

    #region 常量定义
    private const float CAMERA_MIN_HEIGHT_ABOVE_GROUND = 1.5f;      // 相机最小地面高度
    private const float RAYCAST_MAX_DISTANCE = 2000f;               // 射线检测最大距离
    #endregion

    #region 辅助组件
    /// <summary>
    /// 音频监听器属性
    /// </summary>
    public AudioListener AudioListener
    {
        get => audioListener;
        set => audioListener = value;
    }
    #endregion

    #region 目标组跟随配置
    /// <summary>
    /// 目标信息类
    /// </summary>
    [Serializable]
    public class TargetInfo
    {
        public Transform target;        // 目标物体
        public float weight = 1f;       // 权重
        public float radius = 1f;       // 半径
    }

    [NoToLua] public List<TargetInfo> _targetGroup = new List<TargetInfo>();        // 目标组列表
    [NoToLua][HideInInspector] public bool _useTargetGroup = false;                 // 是否使用目标组模式
    [NoToLua][HideInInspector] public float _maxCameraFocalAngle = 10f;             // 最大相机-焦点-目标组第二个目标夹角(度)
    [NoToLua][HideInInspector] public float _maxCameraFocalCenterAngle = 3f;        // 最大相机-焦点-目标组中心夹角(度)
    [NoToLua][HideInInspector] public float _cameraAngleAdjustSpeed = 120f;         // 相机角度调整速度(度/秒)

    [SerializeField] private Vector3 _targetGroupCenter = Vector3.zero;             // 目标组中心点
    [SerializeField] private float _targetGroupRadius = 0f;                         // 目标组半径
    private bool _isManualSwipeActive = false;                     // 是否有手动滑动输入
    #endregion

    #region 寻路镜头配置
    [HideInInspector] public bool isAutoPathfinding = false;             // 是否处于自动寻路状态
    [HideInInspector] public float autoAdjustDelay = 0.2f;               // 自动调整延迟时间(秒)
    [HideInInspector] public float autoAdjustSpeed = 60f;                // 自动调整角度速度(度/秒)
    [HideInInspector] public float behindPlayerAngleThreshold = 20f;     // 判定为在玩家后方的角度阈值(度)
    [HideInInspector] public float behindPlayerAngleByMannul = 10f;      // 判定为在玩家后方的角度阈值(度)
    [HideInInspector] public float angleReachThreshold = 0.5f;           // 角度达到目标的阈值(度)

    private bool _needAdjustCamera = false;                              // 是否需要调整相机
    private float _adjustDelayTimer = 0f;                                // 调整延迟计时器
    private bool _isAdjustingCamera = false;                             // 正在调整相机
    private float _adjustTargetYaw = 0f;                                 // 调整目标偏航角
    private bool _isManualAdjusting = false;                             // 标记是否是手动触发的相机调整
    private float _originalAdjustSpeed = 0f;                             // 保存原始调整速度
    #endregion

    #region 战斗镜头配置
    [NoToLua][HideInInspector] public float EBTransitionDuration = 1.5f;    // 过渡持续时间
    [NoToLua][HideInInspector] public float EnterBattleDistance = 25f;      // 入战相机距离
    [NoToLua][HideInInspector] public float EnterBattleAngleX = 15f;        // 入战相机俯仰角
    [HideInInspector] public bool isEnterBattle = false;                    // 是否处于战斗状态

    private bool _EBTransitioning = false;                                  // 是否正在过渡（入战）
    private float _EBTransitionPro = 0f;                                    // 过渡进度（入战）
    private float _originalDistance = 0f;                                   // 原始距离
    private Vector2 _originalAngle = Vector2.zero;                          // 原始角度
    #endregion

    #region 强制角度过渡配置
    [HideInInspector] public float forcedAngleTransitionSpeed = 60f;        // 强制角度过渡速度(度/秒)
    
    private bool _isForcedAngleTransitioning = false;                       // 是否正在强制角度过渡
    private Vector2 _forcedTargetAngle = Vector2.zero;                      // 强制过渡的目标角度
    #endregion

    #region 被遮挡调整配置
    [NoToLua] public LayerMask groundLayers;                                // 地面图层
    [NoToLua] public LayerMask obstructionLayers;                           // 遮挡物图层
    [NoToLua][HideInInspector] public bool enableBuildingObstructionAvoidance = true;   // 是否启用建筑物遮挡避免
    [NoToLua][HideInInspector] public float safeDistanceMultiplier = 0.9f;              // 安全距离系数
    [NoToLua][HideInInspector] public float obstructionReturnSpeed = 10f;               // 恢复速度
    
    // 调试配置
    [NoToLua][HideInInspector] public bool DebugDrawRays = true;             // 是否绘制调试射线
    [NoToLua][HideInInspector] public float DebugRayDrawDuration = 0.01f;    // 调试射线绘制持续时间
    [NoToLua][HideInInspector] public Color _rayNormalColor = Color.green;   // 普通射线颜色
    [NoToLua][HideInInspector] public Color _rayObstructedColor = Color.red; // 被遮挡射线颜色
    [NoToLua][HideInInspector] public Color _rayHitNormalColor = Color.blue; // 命中点法线颜色

    [SerializeField] private bool _isObstructedRestoring = false;                            // 是否被障碍后恢复中
    private float _originalDistanceBeforeObstruction;                       // 遮挡前的原始距离
    private Vector3 _raycastOrigin = new Vector3();                         // 射线检测起点
    private RaycastHit[] _raycastHits = new RaycastHit[1];                  // 射线检测结果
    private float _fixedRestoreDistance;                                    // 修复后的恢复距离
    #endregion

    #region 镜头动画配置
    private bool _isDoDistanceTweening = false;                             // 是否正在进行距离动画
    private Sequence _activeCameraDistanceSequence = null;                  // 当前活跃的距离动画序列
    #endregion

    #region 缓存变量
    private Vector3 _cameraForward;                                         // 相机前向向量
    #endregion

    #region Unity 生命周期
    /// <summary>
    /// 初始化
    /// </summary>
    private void Awake()
    {
        _mainCamera = GetComponentInChildren<Camera>();
        CreateFocalPoint();
        
        // 初始化相机类型
        SetCameraType(CurrentCameraType);
    }

    /// <summary>
    /// 开始时的初始化
    /// </summary>
    private void Start()
    {
        _oldDistance = Distance;
        _cachedTransform = transform;

        _angle = OriginAngle;
        Quaternion cameraRotation = Quaternion.Euler(_angle.x, _angle.y, 0);
        _cachedTransform.position = _focalPoint.position - cameraRotation * Vector3.forward * Distance;
        _cachedTransform.LookAt(_focalPoint.position, Vector3.up);
        _oldRotation = cameraRotation;
    }

    /// <summary>
    /// 更新
    /// </summary>
    private void Update()
    {
        CameraCullObjMgr.Instance.UpdateCull();
    }

    /// <summary>
    /// 延迟更新
    /// </summary>
    private void LateUpdate()
    {
        if (_focalPoint == null) return;

        float deltaTime = Time.deltaTime;
        
        if (_EBTransitioning)
        {
            if (EBTransitionDuration <= Mathf.Epsilon)
            {
                EBTransitionDuration = 0.001f;
                _EBTransitionPro = 1f;
            }
            else
            {
                _EBTransitionPro += deltaTime / EBTransitionDuration;
                if (_EBTransitionPro >= 1f)
                {
                    _EBTransitionPro = 1f;
                }
            }
        }

        // 强制角度过渡具有绝对最高优先级，无论是否在入战过渡中
        if (_isForcedAngleTransitioning)
        {
            UpdateFocalPointPosition(deltaTime);
            UpdateForcedAngleTransition(deltaTime);
            
            // 如果同时在入战过渡中，只处理距离调整，不处理角度调整
            if (_EBTransitioning)
            {
                UpdateEnterBattleTransitioningDistanceOnly();
            }
        }
        else if (!_EBTransitioning)
        {
            if (isEnterBattle && _useTargetGroup && _targetGroup.Count >= 2)
            {
                UpdateFocalPointPosition(deltaTime);
                UpdateTargetGroup(deltaTime);
            }
            else if (target != null)
            {
                UpdateFocalPointPosition(deltaTime);
                if (isAutoPathfinding || _isManualAdjusting)
                    CheckAndAdjustCameraPosition(deltaTime);
            }
        }
        else
        {
            UpdateFocalPointPosition(deltaTime);
            UpdateEnterBattleTransitioning();
        }

        if (_EBTransitionPro >= 1f)
            _EBTransitioning = false;

        UpdateCacheTransform(deltaTime);

        if (this.audioListener != null && this.target != null)
            this.audioListener.transform.position = this.target.position;
    }

    /// <summary>
    /// 禁用时的处理
    /// </summary>
    private void OnDisable()
    {
        var camera = GetComponentInChildren<Camera>();
        if (camera)
        {
            camera.transform.localPosition = this._freeCameraLocalPosition;
        }
        this._isAutoRotating = false;
    }

    /// <summary>
    /// 销毁时清理
    /// </summary>
    private void OnDestroy()
    {
        if (_focalPoint != null)
        {
            GameObject.Destroy(_focalPoint.gameObject);
        }
        
        // 清理距离动画序列
        CleanCameraDistanceSequence();
    }
    #endregion

    #region 公共接口方法 - 基础控制
    /// <summary>
    /// 设置距离变化回调函数
    /// </summary>
    public void SetDistanceChangeCallback(Action callback)
    {
        _onDistanceChangeHandle = callback;
    }

    /// <summary>
    /// 设置相机类型
    /// </summary>
    public void SetCameraType(CameraType type)
    {
        CurrentCameraType = type;
        
        // 根据相机类型设置旋转属性
        switch (type)
        {
            case CameraType.Free:
                AllowXRotation = true;
                AllowYRotation = true;
                break;
            case CameraType.Fixed:
                AllowXRotation = true;
                AllowYRotation = false;
                break;
        }
    }
    
    /// <summary>
    /// 切换到自由相机
    /// </summary>
    public void SetFreeCamera()
    {
        SetCameraType(CameraType.Free);
    }
    
    /// <summary>
    /// 切换到固定相机
    /// </summary>
    public void SetFixedCamera()
    {
        SetCameraType(CameraType.Fixed);
    }

    /// <summary>
    /// 同步视场角到相机
    /// </summary>
    [NoToLua]
    public void SyncFieldOfView()
    {
        if (null != _mainCamera)
        {
            _mainCamera.fieldOfView = this.fieldOfView;
        }
    }

    /// <summary>
    /// 清除过渡和调整状态
    /// </summary>
    public void ClearAllTransitionState()
    {
        _EBTransitionPro = 1f;
        _EBTransitioning = false;
        _isObstructedRestoring = false;
        _isDoDistanceTweening = false;
        _isAdjustingCamera = false;
        _needAdjustCamera = false;
        _isManualAdjusting = false;
        isAutoPathfinding = false;
        _isManualSwipeActive = false;
        _isForcedAngleTransitioning = false;
        
        // 清理距离动画序列
        CleanCameraDistanceSequence();
    }
    #endregion

    #region 公共接口方法 - 输入处理

    /// <summary>
    /// 处理滑动输入
    /// </summary>
    public void Swipe(float x, float y)
    {
        if (!AllowRotation || _EBTransitioning || _isForcedAngleTransitioning)
        {
            return;
        }

        if (Mathf.Abs(x) > 5f || Mathf.Abs(y) > 5f)
        {
            _isManualSwipeActive = true;
        }

        if (AllowYRotation)
        {
            // 在接近角度限制时转换为缩放操作
            if (Mathf.Abs(_angle.x - MinPitchAngle) <= 0.1f &&
               !_useTargetGroup && !_isObstructedRestoring && (y > 0 && Distance > MinDistance || y < 0 && Distance < MaxDistance))
            {
                Pinch(2 * y); // 转换为缩放
            }
            else
            {
                _angle.x = Mathf.Clamp(_angle.x - y * RotationSensitivity.x * 0.1f,
                                      MinPitchAngle, MaxPitchAngle);
            }
        }

        if (AllowXRotation)
        {
            _angle.y += x * RotationSensitivity.y * 0.1f;
        }
    }

    /// <summary>
    /// 处理缩放输入
    /// </summary>
    public void Pinch(float delta)
    {
        if (!AllowZoom || _EBTransitioning || _isDoDistanceTweening) return;

        Distance = Mathf.Clamp(Distance + delta * -0.03f, MinDistance, MaxDistance);
        // 手动调整缩放，取消被障碍后恢复
        if (_isObstructedRestoring)
        {
            _originalDistanceBeforeObstruction = Distance;
            _isObstructedRestoring = false;
        }

        // 入战期间，缩放了镜头记录
        if(isEnterBattle)
        {
            _originalDistance = Distance;
        }
    }

    /// <summary>
    /// 立即同步到目标位置
    /// </summary>
    public void SyncImmediate()
    {
        if (target == null)
        {
            return;
        }

        MoveToTarget();
    }

    /// <summary>
    /// 同步旋转
    /// </summary>
    public void SyncRotation()
    {
        // 强制角度过渡期间允许同步，因为这只是同步显示状态，不修改目标角度
        var targetQuat = Quaternion.Euler(_angle.x, _angle.y, 0);
        _oldRotation = targetQuat;
    }

    /// <summary>
    /// 限制旋转和距离
    /// </summary>
    public void ClampRotationAndDistance()
    {
        Distance = Mathf.Clamp(Distance, MinDistance, MaxDistance);
        
        // 强制角度过渡期间不限制角度，避免干扰强制过渡
        if (!_isForcedAngleTransitioning)
        {
            _angle.x = Mathf.Clamp(_angle.x, MinPitchAngle, MaxPitchAngle);
            //_angle.y = Mathf.Clamp(_angle.y, OriginAngle.y + MinYawAngle, OriginAngle.y + MaxYawAngle);
        }
    }

    /// <summary>
    /// 改变角度
    /// </summary>
    public void ChangeAngle(Vector2 newAngle)
    {
        // 强制角度过渡期间禁止修改角度
        if (_isForcedAngleTransitioning)
        {
            return;
        }

        if (newAngle.x < MinPitchAngle || newAngle.x > MaxPitchAngle)
        {
            newAngle.x = Mathf.Clamp(newAngle.x, MinPitchAngle, MaxPitchAngle);
        }

        OriginAngle = newAngle;
        _angle = OriginAngle;
    }
    #endregion

    #region 公共接口方法 - 焦点和目标管理

    /// <summary>
    /// 创建焦点
    /// </summary>
    public void CreateFocalPoint(GameObject focal_point = null)
    {
        if (null == focal_point)
        {
            GameObject go = new GameObject();
            go.name = "CamerafocalPoint";
            _focalPoint = go.transform;
        }
        else
        {
            if (_focalPoint)
            {
                Destroy(_focalPoint.gameObject);
            }
            _focalPoint = focal_point.transform;
        }

        MoveToTarget();
    }

    /// <summary>
    /// 清除焦点
    /// </summary>
    public void ClearFocalPoint()
    {
        _focalPoint = null;
    }

    /// <summary>
    /// 移动到目标位置
    /// </summary>
    public void MoveToTarget()
    {
        if (target != null && _focalPoint != null)
        {
            _focalPoint.position = target.position + target.rotation * targetOffset;
        }
    }

    /// <summary>
    /// 绑定相机跟随组件到游戏对象
    /// </summary>
    public static CameraFollow Bind(GameObject go)
    {
        var _cameraFollow = go.GetComponent<CameraFollow>() ?? go.AddComponent<CameraFollow>();
        return _cameraFollow;
    }
    #endregion

    #region 核心更新方法
    /// <summary>
    /// 更新焦点位置
    /// </summary>
    private void UpdateFocalPointPosition(float deltaTime)
    {
        if (_focalPoint == null || target == null) return;

        _focalPoint.position = target.position + targetOffset;
    }

    /// <summary>
    /// 更新缓存的Transform
    /// </summary>
    private void UpdateCacheTransform(float deltaTime)
    {
        UpdateCameraRotation(deltaTime);
        UpdateCameraPosition(deltaTime);
        if (!_isObstructedRestoring)
        {
            HandleBuildingObstruction(deltaTime);
            EnsureCameraAboveGround();
        }
        else
        {
            HandleBuildingObstruction(deltaTime);
        }
            
        _cachedTransform.LookAt(_focalPoint.position);
    }

    /// <summary>
    /// 更新相机旋转
    /// </summary>
    private void UpdateCameraRotation(float deltaTime)
    {
        float lerpFactor = deltaTime * RotationSmoothing * 0.5f;
        
        // 分别对每个角度轴进行最短路径插值
        Vector3 oldEuler = _oldRotation.eulerAngles;
        Vector3 targetEuler = new Vector3(_angle.x, _angle.y, 0);
        
        // 使用LerpAngle确保最短路径
        float newX = Mathf.LerpAngle(oldEuler.x, targetEuler.x, lerpFactor);
        float newY = Mathf.LerpAngle(oldEuler.y, targetEuler.y, lerpFactor);
        
        _oldRotation = Quaternion.Euler(newX, newY, 0);
    }

    /// <summary>
    /// 更新相机位置
    /// </summary>
    private void UpdateCameraPosition(float deltaTime)
    {
        float currentDistance = Mathf.Lerp(_oldDistance, Distance, deltaTime * ZoomSmoothing);
        _cameraForward = _oldRotation * Vector3.forward;
        _cachedTransform.position = _focalPoint.position - _cameraForward * currentDistance;
        _oldDistance = currentDistance;
    }

    /// <summary>
    /// 缓动函数：平滑出入
    /// </summary>
    private float EaseInOutQuad(float t)
    {
        return t < 0.5 ? 2 * t * t : 1 - Mathf.Pow(-2 * t + 2, 2) / 2;
    }
    #endregion

    #region 目标组跟随 - 方法
    /// <summary>
    /// 添加一个目标到目标组
    /// </summary>
    public void AddTarget(Transform target, float weight = 1f, float radius = 1f)
    {
        if (target == null) return;

        // 检查是否已存在
        foreach (var info in _targetGroup)
        {
            if (info.target == target)
            {
                info.weight = weight;
                info.radius = radius;
                return;
            }
        }

        _targetGroup.Add(new TargetInfo { target = target, weight = weight, radius = radius });
    }

    /// <summary>
    /// 从目标组中移除目标
    /// </summary>
    public void RemoveTarget(Transform target)
    {
        if (target == null) return;

        for (int i = 0; i < _targetGroup.Count; i++)
        {
            if (_targetGroup[i].target == target)
            {
                _targetGroup.RemoveAt(i);
                break;
            }
        }
    }

    /// <summary>
    /// 清空目标组
    /// </summary>
    public void ClearTargetGroup()
    {
        _targetGroup.Clear();
    }

    /// <summary>
    /// 移除目标组中的无效目标
    /// </summary>
    private void CleanupTargetGroup()
    {
        for (int i = _targetGroup.Count - 1; i >= 0; i--)
        {
            if (_targetGroup[i].target == null)
            {
                _targetGroup.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// 启用目标组模式
    /// </summary>
    public void EnableTargetGroupMode()
    {
        CleanupTargetGroup();
        if (_targetGroup.Count < 2) return;
        _useTargetGroup = true;
        ResetManualSwipeState();
    }

    /// <summary>
    /// 重置手动滑动状态，允许目标组自动调整重新启用
    /// </summary>
    public void ResetManualSwipeState()
    {
        _isManualSwipeActive = false;
    }

    /// <summary>
    /// 禁用目标组模式，恢复到单目标跟随
    /// </summary>
    public void DisableTargetGroupMode()
    {
        _useTargetGroup = false;
        ResetManualSwipeState();
    }

    /// <summary>
    /// 计算目标组中心点和所需半径
    /// </summary>
    private void CalculateTargetGroupBounds()
    {
        Vector3 weightedCenter = Vector3.zero;
        float totalWeight = 0f;

        // 计算加权中心点
        foreach (var info in _targetGroup)
        {
            if (info.target == null) continue;
            weightedCenter += info.target.position * info.weight;
            totalWeight += info.weight;
        }

        if(totalWeight <= Mathf.Epsilon)
        {
            _targetGroupCenter = Vector3.zero;
            _targetGroupRadius = 0f;
            return;
        }

        _targetGroupCenter = weightedCenter / totalWeight;

        // 计算所需半径
        float maxDistance = 0f;
        foreach (var info in _targetGroup)
        {
            if (info.target == null) continue;
            float distance = Vector3.Distance(_targetGroupCenter, info.target.position) + info.radius;
            if (distance > maxDistance)
            {
                maxDistance = distance;
            }
        }

        _targetGroupRadius = maxDistance;
    }

    /// <summary>
    /// 根据目标组调整相机距离
    /// </summary>
    private void AdjustCameraForTargetGroup(float deltaTime, bool isTransition = false)
    {
        // 如果有手动滑动输入且不是过渡状态，则不执行自动调整
        if (_isManualSwipeActive && !isTransition)
        {
            return;
        }
        
        CalculateTargetGroupBounds();
        AdjustCameraToFocalPointAngle(deltaTime, isTransition);
    }

    /// <summary>
    /// 调整相机与目标组中心和焦点之间的夹角
    /// </summary>
    /// <param name="adjustFactor">调整因子：过渡时为t值(0-1)，非过渡时为deltaTime</param>
    /// <param name="isTransition">是否处于过渡状态</param>
    private void AdjustCameraToFocalPointAngle(float adjustFactor, bool isTransition = false)
    {
        if (_targetGroup.Count < 2 || _focalPoint == null) return;
        
        // 强制角度过渡具有最高优先级
        if (_isForcedAngleTransitioning) return;
        
        // 获取相机位置、目标组中心和焦点位置
        Vector3 cameraPos = transform.position;
        Vector3 groupCenterPos = _targetGroupCenter;
        float focalAngle = _maxCameraFocalCenterAngle;
        if (_targetGroup.Count == 2)
        {
            focalAngle = _maxCameraFocalAngle;
            groupCenterPos = _targetGroup[1].target.position;
        }
        Vector3 focalPos = _focalPoint.position;

        // 计算向量：相机到目标组中心的向量
        Vector3 cameraToGroupCenter = groupCenterPos - cameraPos;

        // 计算向量：相机到焦点的向量
        Vector3 cameraToFocal = focalPos - cameraPos;

        // 如果任一向量接近零向量，则不进行调整
        if (cameraToGroupCenter.magnitude < 0.001f || cameraToFocal.magnitude < 0.001f)
            return;

        // 归一化向量
        cameraToGroupCenter.Normalize();
        cameraToFocal.Normalize();

        // 计算两向量之间的夹角（度）
        float currentAngle = Vector3.Angle(cameraToGroupCenter, cameraToFocal);

        // 如果夹角小于等于最大允许夹角，则不需要调整
        if (currentAngle <= focalAngle)
            return;

        // 计算需要调整的夹角量
        float angleToAdjust = currentAngle - focalAngle;

        // 计算旋转轴（两向量的叉积）
        Vector3 rotationAxis = Vector3.Cross(cameraToGroupCenter, cameraToFocal);

        if (rotationAxis.magnitude < 0.001f)
            return; // 如果旋转轴接近零向量，无法确定旋转方向

        rotationAxis.Normalize();

        // 计算相机需要的Y轴旋转角度
        Vector3 newDirection = Quaternion.AngleAxis(angleToAdjust, rotationAxis) * cameraToGroupCenter;
        Vector3 flatForward = Vector3.ProjectOnPlane(newDirection, Vector3.up).normalized;
        float targetYaw = Mathf.Atan2(flatForward.x, flatForward.z) * Mathf.Rad2Deg;

        // 计算当前角度到目标角度的差异
        float yawDiff = Mathf.DeltaAngle(_angle.y, targetYaw);

        // 检查是否已达到目标角度
        if (Mathf.Abs(yawDiff) <= angleReachThreshold)
        {
            _angle.y = targetYaw;
            return;
        }

        // 平滑调整相机的偏航角
        if (isTransition)
        {
            // 过渡模式直接插值
            _angle.y = Mathf.LerpAngle(_angle.y, targetYaw, adjustFactor);
        }
        else
        {
            // 基于角度调整速度计算角度变化量
            float maxAngleStep = _cameraAngleAdjustSpeed * adjustFactor;
            float angleStep = Mathf.Sign(yawDiff) * Mathf.Min(Mathf.Abs(yawDiff), maxAngleStep);
            _angle.y += angleStep;
        }
    }



    /// <summary>
    /// 目标组模式更新函数 
    /// </summary>
    private void UpdateTargetGroup(float deltaTime)
    {
        if (!_useTargetGroup) return;

        CleanupTargetGroup();
        if (_targetGroup.Count < 2)
        {
            DisableTargetGroupMode();
            return;
        }

        AdjustCameraForTargetGroup(deltaTime, false);
    }
    #endregion

    #region 技能镜头 - 方法 - 镜头距离运动动画

    /// <summary>
    /// 清理当前活跃的距离动画序列
    /// </summary>
    private void CleanCameraDistanceSequence()
    {
        if (_activeCameraDistanceSequence != null)
        {
            _activeCameraDistanceSequence.Kill();
            _activeCameraDistanceSequence = null;
        }
    }

    /// <summary>
    /// 立即停止距离动画
    /// </summary>
    public void StopCameraDistanceAnimationImmediately()
    {
        if (_activeCameraDistanceSequence != null)
        {
            _activeCameraDistanceSequence.Complete(true);
        }
    }

    /// <summary>
    /// 暂停距离动画
    /// </summary>
    public void PauseCameraDistanceAnimation()
    {
        if (_activeCameraDistanceSequence != null)
        {
            _activeCameraDistanceSequence.Pause();
        }
    }

    /// <summary>
    /// 恢复距离动画
    /// </summary>
    public void ResumeCameraDistanceAnimation()
    {
        if (_activeCameraDistanceSequence != null)
        {
            _activeCameraDistanceSequence.Play();
        }
    }

    /// <summary>
    /// 视场动画
    /// </summary>
    public Tweener DOFieldOfView(float endValue, float duration)
    {
        return DOTween.To(
            () => this.fieldOfView,
            v =>
            {
                this.fieldOfView = v;
                this.SyncFieldOfView();
            },
            endValue,
            duration);
    }

    /// <summary>
    /// 调整相机镜头距离
    /// </summary>
    /// <param name="targetDistance">目标距离</param>
    /// <param name="duration">动画持续时间</param>
    /// <returns>调整距离的Sequence对象</returns>
    public Sequence DOCameraDistanceTweenTo(float targetDistance, float duration)
    {
        // 如果镜头被阻挡恢复中，且镜头动画是拉远的，不做镜头动画
        if (_isObstructedRestoring && targetDistance > Distance) return null;
        // 入战时，不让做动画
        if (_EBTransitioning) return null;

        // 清理现有序列
        CleanCameraDistanceSequence();

        targetDistance = Mathf.Clamp(targetDistance, MinDistance, MaxDistance);
        
        _activeCameraDistanceSequence = DOTween.Sequence();
        _activeCameraDistanceSequence.Append(DOTween.To(
            () => this.Distance,
            v => this.Distance = v,
            targetDistance,
            duration
        ))
        .OnStart(() => {
            _isDoDistanceTweening = true;
        })
        .OnComplete(() => {
            _isDoDistanceTweening = false;
            _activeCameraDistanceSequence = null;
        });

        return _activeCameraDistanceSequence;
    }

    /// <summary>
    /// 调整相机镜头距离，会恢复
    /// 使用场景：技能镜头运动
    /// </summary>
    /// <param name="distanceOffset">距离偏移量，正值表示拉远，负值表示拉近</param>
    /// <param name="duration">动画持续时间</param>
    /// <param name="restoreDuration">恢复原始距离的动画持续时间</param>
    /// <returns>调整距离的Sequence对象</returns>
    public Sequence DOCameraDistanceElasticTween(float distanceOffset, float duration, float restoreDuration)
    {
        // 如果镜头被阻挡恢复中，且镜头动画是拉远的，不做镜头动画
        if (_isObstructedRestoring && distanceOffset > 0) return null;
        // 入战时，不让做动画
        if (_EBTransitioning) return null;

        // 清理现有序列
        CleanCameraDistanceSequence();

        float originalDistance = this.Distance;
        float targetDistance = Mathf.Clamp(originalDistance + distanceOffset, MinDistance, MaxDistance);

        _activeCameraDistanceSequence = DOTween.Sequence();
        _activeCameraDistanceSequence
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                targetDistance,
                duration
            ))
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                originalDistance,
                restoreDuration
            ))
            .OnStart(() => {
                _isDoDistanceTweening = true;
            })
            .OnComplete(() => {
                _isDoDistanceTweening = false;
                _activeCameraDistanceSequence = null;
            });

        return _activeCameraDistanceSequence;
    }

    /// <summary>
    /// 调整相机镜头到指定距离，会恢复
    /// </summary>
    /// <param name="targetDistance">目标距离</param>
    /// <param name="duration">动画持续时间</param>
    /// <param name="restoreDuration">恢复原始距离的动画持续时间</param>
    /// <returns>调整距离的Sequence对象</returns>
    public Sequence DOCameraDistanceElasticTweenTo(float targetDistance, float duration, float restoreDuration)
    {
        // 如果镜头被阻挡恢复中，且镜头动画是拉远的，不做镜头动画
        if (_isObstructedRestoring && targetDistance > Distance) return null;
        // 入战时，不让做动画
        if (_EBTransitioning) return null;

        // 清理现有序列
        CleanCameraDistanceSequence();

        float originalDistance = this.Distance;
        targetDistance = Mathf.Clamp(targetDistance, MinDistance, MaxDistance);

        _activeCameraDistanceSequence = DOTween.Sequence();
        _activeCameraDistanceSequence
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                targetDistance,
                duration
            ))
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                originalDistance,
                restoreDuration
            ))
            .OnStart(() => {
                _isDoDistanceTweening = true;
            })
            .OnComplete(() => {
                _isDoDistanceTweening = false;
                _activeCameraDistanceSequence = null;
            });

        return _activeCameraDistanceSequence;
    }

    /// <summary>
    /// 两段式距离动画：先移动到DistanceA，再移动到DistanceB
    /// 使用场景：需要分阶段调整镜头距离的复杂动画
    /// </summary>
    /// <param name="distanceA">第一段目标距离</param>
    /// <param name="durationA">第一段动画持续时间</param>
    /// <param name="distanceB">第二段目标距离</param>
    /// <param name="durationB">第二段动画持续时间</param>
    /// <returns>距离动画的Sequence对象，可用于控制整个动画序列</returns>
    public Sequence DOCameraDistanceTwoStageTween(float distanceA, float durationA, float distanceB, float durationB)
    {
        // 如果镜头被阻挡恢复中，且第一段镜头动画是拉远的，不做镜头动画
        if (_isObstructedRestoring && distanceA > Distance) return null;
        // 入战时，不让做动画
        if (_EBTransitioning) return null;

        // 清理现有序列
        CleanCameraDistanceSequence();

        // 约束距离参数到有效范围
        distanceA = Mathf.Clamp(distanceA, MinDistance, MaxDistance);
        distanceB = Mathf.Clamp(distanceB, MinDistance, MaxDistance);

        _activeCameraDistanceSequence = DOTween.Sequence();
        _activeCameraDistanceSequence
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                distanceA,
                durationA
            ))
            .Append(DOTween.To(
                () => this.Distance,
                v => this.Distance = v,
                distanceB,
                durationB
            ))
            .OnStart(() => {
                _isDoDistanceTweening = true;
            })
            .OnComplete(() => {
                _isDoDistanceTweening = false;
                _activeCameraDistanceSequence = null;
            });

        return _activeCameraDistanceSequence;
    }
    #endregion

    #region 寻路镜头 - 方法 - 寻路过程中，自动调整摄像机偏航角
    /// <summary>
    /// 设置自动寻路状态
    /// </summary>
    /// <param name="isPathfinding">是否处于自动寻路状态</param>
    public void SetAutoPathfindingState(bool isPathfinding)
    {
        isAutoPathfinding = isPathfinding;
        if (!isPathfinding)
        {
            _needAdjustCamera = false;
            _isAdjustingCamera = false;
        }
    }

    /// <summary>
    /// 检查相机是否在玩家后方
    /// </summary>
    /// /// <param name="is_mannul">是否手动</param>
    private bool IsCameraBehindPlayer(bool is_mannul = false)
    {
        if (target == null) return true;

        // 获取玩家前进方向
        Vector3 playerForward = target.forward;

        // 将方向投影到水平面上
        playerForward.y = 0;
        playerForward.Normalize();

        // 获取玩家到相机的方向
        Vector3 playerToCamera = transform.position - target.position;
        playerToCamera.y = 0;
        playerToCamera.Normalize();

        // 计算夹角
        float angle = Vector3.Angle(playerForward, playerToCamera);

        // 如果夹角接近180度，则相机在玩家后方
        if (is_mannul)
            return angle >= (180f - behindPlayerAngleByMannul);
        else
            return angle >= (180f - behindPlayerAngleThreshold);
    }

    /// <summary>
    /// 检查并调整相机位置（在寻路状态下）
    /// </summary>
    private void CheckAndAdjustCameraPosition(float deltaTime)
    {
        if (!AllowRotation || !AllowYRotation)
            return;

        // 强制角度过渡具有最高优先级
        if (_isForcedAngleTransitioning) return;

        // 如果正在调整相机，继续调整过程
        if (_isAdjustingCamera)
        {
            ContinueAdjustCamera(deltaTime);
            return;
        }

        // 如果正在手动调整相机，不进行自动寻路调整的检查
        if (_isManualAdjusting)
            return;

        // 检查相机是否需要调整
        if (!_needAdjustCamera)
        {
            // 如果相机不在玩家后方，启动调整延迟计时
            if (!IsCameraBehindPlayer())
            {
                _needAdjustCamera = true;
                _adjustDelayTimer = 0f;
            }
        }
        else
        {
            // 如果需要调整，先检查是否已经回到后方
            if (IsCameraBehindPlayer())
            {
                _needAdjustCamera = false;
                return;
            }

            _adjustDelayTimer += deltaTime;
            // 达到延迟时间后开始调整
            if (_adjustDelayTimer >= autoAdjustDelay)
            {
                StartAdjustCamera();
            }
        }
    }

    /// <summary>
    /// 开始调整相机到玩家后方
    /// </summary>
    private void StartAdjustCamera()
    {
        if (target == null) return;

        _isAdjustingCamera = true;
        CalcCameraAdjust();
    }

    private void CalcCameraAdjust()
    {
        if (target == null) return;

        // 计算目标偏航角 - 让相机对准玩家的后方
        Vector3 targetForward = target.forward;
        targetForward.y = 0;
        targetForward.Normalize();

        float targetYaw = Mathf.Atan2(targetForward.x, targetForward.z) * Mathf.Rad2Deg;
        float currentYaw = _angle.y;

        // 计算最短路径角度差
        float angleDiff = Mathf.DeltaAngle(currentYaw, targetYaw);
        _adjustTargetYaw = currentYaw + angleDiff;
    }

    /// <summary>
    /// 继续相机调整过程
    /// </summary>
    private void ContinueAdjustCamera(float deltaTime)
    {
        if (target == null)
        {
            _isAdjustingCamera = false;
            _needAdjustCamera = false;
            _isManualAdjusting = false;
            
            // 如果使用了自定义速度，恢复原始速度
            if (_originalAdjustSpeed > 0f)
            {
                autoAdjustSpeed = _originalAdjustSpeed;
                _originalAdjustSpeed = 0f;
            }
            return;
        }

        CalcCameraAdjust();
        
        // 计算当前角度差异
        float currentYaw = _angle.y;
        float angleDiff = Mathf.DeltaAngle(currentYaw, _adjustTargetYaw);

        // 检查是否已达到目标角度
        if (Mathf.Abs(angleDiff) <= angleReachThreshold)
        {
            _angle.y = _adjustTargetYaw;
            _isAdjustingCamera = false;
            _needAdjustCamera = false;
            _isManualAdjusting = false;
            
            // 如果使用了自定义速度，恢复原始速度
            if (_originalAdjustSpeed > 0f)
            {
                autoAdjustSpeed = _originalAdjustSpeed;
                _originalAdjustSpeed = 0f;
            }
            return;
        }

        // 基于角度调整速度计算角度变化量
        float maxAngleStep = autoAdjustSpeed * deltaTime;
        float angleStep = Mathf.Sign(angleDiff) * Mathf.Min(Mathf.Abs(angleDiff), maxAngleStep);
        _angle.y += angleStep;
    }

    /// <summary>
    /// 立即调整相机到目标后方
    /// </summary>
    /// <param name="customSpeed">自定义调整速度，如果小于等于0则使用默认速度</param>
    public void AdjustCameraToBehindTarget(float customSpeed = 0f)
    {
        if (target == null || !AllowRotation || !AllowYRotation)
            return;

        if (_isAdjustingCamera || _EBTransitioning)
            return;

        if (IsCameraBehindPlayer(true))
        {
            return;
        }

        _isManualAdjusting = true;
        _isAdjustingCamera = true;

        if (customSpeed > 0f)
        {
            _originalAdjustSpeed = autoAdjustSpeed;
            autoAdjustSpeed = customSpeed;
        }
        else
        {
            _originalAdjustSpeed = 0f; // 标记没有使用自定义速度
        }

        CalcCameraAdjust();
    }
    #endregion

    #region 强制角度过渡 - 方法
    /// <summary>
    /// 强制将当前角度过渡到指定角度
    /// </summary>
    /// <param name="targetAngle">目标角度（x为俯仰角，y为偏航角）</param>
    /// <param name="setImmediately">是否立即设置到目标角度</param>
    /// <param name="transitionSpeed">过渡速度（度/秒），如果小于等于0则使用默认速度</param>
    public void ForceTransitionToAngle(Vector2 targetAngle, bool setImmediately = false, float transitionSpeed = 0f)
    {
        // 清理所有其他角度调整状态
        ClearOtherAngleAdjustmentStates();
        
        // 约束角度到有效范围
        targetAngle.x = Mathf.Clamp(targetAngle.x, MinPitchAngle, MaxPitchAngle);
        
        _forcedTargetAngle = targetAngle;
        
        // 设置过渡速度
        if (transitionSpeed > 0f)
        {
            forcedAngleTransitionSpeed = transitionSpeed;
        }
        
        if (setImmediately)
        {
            // 立即设置到目标角度
            _angle = _forcedTargetAngle;
            _isForcedAngleTransitioning = false;
        }
        else
        {
            // 启动平滑过渡
            _isForcedAngleTransitioning = true;
        }
    }

    /// <summary>
    /// 清理其他角度调整状态，确保强制过渡具有最高优先级
    /// </summary>
    private void ClearOtherAngleAdjustmentStates()
    {
        // 清理寻路镜头调整状态
        _isAdjustingCamera = false;
        _needAdjustCamera = false;
        _isManualAdjusting = false;
        
        // 恢复自定义调整速度
        if (_originalAdjustSpeed > 0f)
        {
            autoAdjustSpeed = _originalAdjustSpeed;
            _originalAdjustSpeed = 0f;
        }
        
        // 清理目标组手动滑动状态
        _isManualSwipeActive = false;
    }

    /// <summary>
    /// 强制将当前偏航角过渡到指定偏航角（只调整Y轴）
    /// </summary>
    /// <param name="targetYaw">目标偏航角</param>
    /// <param name="setImmediately">是否立即设置到目标角度</param>
    /// <param name="transitionSpeed">过渡速度（度/秒），如果小于等于0则使用默认速度</param>
    public void ForceTransitionToYaw(float targetYaw, bool setImmediately = false, float transitionSpeed = 0f)
    {
        Vector2 targetAngle = new Vector2(_angle.x, targetYaw);
        ForceTransitionToAngle(targetAngle, setImmediately, transitionSpeed);
    }

    /// <summary>
    /// 停止强制角度过渡
    /// </summary>
    public void StopForcedAngleTransition()
    {
        _isForcedAngleTransitioning = false;
    }

    /// <summary>
    /// 检查是否正在进行强制角度过渡
    /// </summary>
    /// <returns>是否正在强制角度过渡</returns>
    public bool IsForcedAngleTransitioning()
    {
        return _isForcedAngleTransitioning;
    }

    /// <summary>
    /// 更新强制角度过渡
    /// </summary>
    private void UpdateForcedAngleTransition(float deltaTime)
    {
        if (!_isForcedAngleTransitioning) return;

        // 计算当前角度与目标角度的差异
        float pitchDiff = Mathf.DeltaAngle(_angle.x, _forcedTargetAngle.x);
        float yawDiff = Mathf.DeltaAngle(_angle.y, _forcedTargetAngle.y);

        // 检查是否已达到目标角度
        if (Mathf.Abs(pitchDiff) <= angleReachThreshold && Mathf.Abs(yawDiff) <= angleReachThreshold)
        {
            _angle = _forcedTargetAngle;
            _isForcedAngleTransitioning = false;
            return;
        }

        // 基于角度调整速度计算角度变化量
        float maxAngleStep = forcedAngleTransitionSpeed * deltaTime;

        // 分别处理俯仰角和偏航角
        if (Mathf.Abs(pitchDiff) > angleReachThreshold)
        {
            float pitchStep = Mathf.Sign(pitchDiff) * Mathf.Min(Mathf.Abs(pitchDiff), maxAngleStep);
            _angle.x += pitchStep;
            _angle.x = Mathf.Clamp(_angle.x, MinPitchAngle, MaxPitchAngle);
        }

        if (Mathf.Abs(yawDiff) > angleReachThreshold)
        {
            float yawStep = Mathf.Sign(yawDiff) * Mathf.Min(Mathf.Abs(yawDiff), maxAngleStep);
            _angle.y += yawStep;
        }
    }
    #endregion

    #region 战斗镜头 - 方法 - 进入战斗状态的视野调整
    /// <summary>
    /// 设置进入战斗状态
    /// </summary>
    /// <param name="isEB">是否进入战斗状态</param>
    /// <param name="customDistance">自定义的战斗距离，如果不传或为负值则使用默认的EnterBattleDistance</param>
    public void SetEnterBattleState(bool isEB, float customDistance = -1f)
    {
        //if (isEnterBattle == isEB) return;

        isEnterBattle = isEB;
        if (isEB)
        {
            if (customDistance >= 0f)
            {
                EnterBattleDistance = customDistance;
            }

            _originalDistance = Distance;
            _originalAngle = _angle;

            if (_isObstructedRestoring)
            {
                _originalDistance = _originalDistanceBeforeObstruction;
                _originalAngle = _angle;
                _originalDistanceBeforeObstruction = EnterBattleDistance;
            }
            else
            {
                _originalDistance = Distance;
                _originalAngle = _angle;
            }
        }

        _EBTransitioning = true;
        _EBTransitionPro = 0f;
    }

    /// <summary>
    /// 入战镜头过渡
    /// </summary>
    private void UpdateEnterBattleTransitioning()
    {
        if (!_EBTransitioning) return;

        float t = EaseInOutQuad(_EBTransitionPro);
        if (isEnterBattle)  
        {
            EnterBattleDistance = Mathf.Clamp(EnterBattleDistance, MinDistance, MaxDistance);
            Distance = Mathf.Lerp(Distance, EnterBattleDistance, t);
            _angle.x = Mathf.LerpAngle(_angle.x, EnterBattleAngleX, t);
            if (_targetGroup.Count >= 2)
            {
                AdjustCameraForTargetGroup(t, true);
            }
        }
        else
        {
            Distance = Mathf.Lerp(Distance, _originalDistance, t);
        }
    }

    /// <summary>
    /// 入战镜头过渡（仅距离调整，不调整角度）
    /// 用于强制角度过渡期间的入战过渡处理
    /// </summary>
    private void UpdateEnterBattleTransitioningDistanceOnly()
    {
        if (!_EBTransitioning) return;

        float t = EaseInOutQuad(_EBTransitionPro);
        if (isEnterBattle)  
        {
            EnterBattleDistance = Mathf.Clamp(EnterBattleDistance, MinDistance, MaxDistance);
            Distance = Mathf.Lerp(Distance, EnterBattleDistance, t);
            // 不调整角度，因为强制角度过渡具有更高优先级
        }
        else
        {
            Distance = Mathf.Lerp(Distance, _originalDistance, t);
        }
    }

    /// <summary>
    /// 在战斗状态中更新战斗镜头距离
    /// </summary>
    /// <param name="newDistance">新的战斗镜头距离</param>
    /// <param name="smoothTransition">是否进行平滑过渡，默认为true</param>
    /// <param name="duration">过渡时间</param>
    public void UpdateEnterBattleDistance(float newDistance, bool smoothTransition = true, float duration = 0.5f)
    {
        if (!isEnterBattle)
        {
            Debug.LogWarning("UpdateEnterBattleDistance: 当前不在战斗状态，无法更新战斗镜头距离");
            return;
        }

        newDistance = Mathf.Clamp(newDistance, MinDistance, MaxDistance);
        
        if (smoothTransition)
        {
            EnterBattleDistance = newDistance;
            if (!_EBTransitioning)
            {
                DOTween.To(
                    () => Distance,
                    value => Distance = value,
                    newDistance,
                    duration
                ).SetEase(Ease.OutQuad);
            }
        }
        else
        {
            EnterBattleDistance = newDistance;
            if (!_EBTransitioning)
            {
                Distance = newDistance;
            }
        }
    }
    #endregion

    #region 被遮挡调整 - 方法
    /// <summary>
    /// 确保相机在地面以上
    /// </summary>
    private void EnsureCameraAboveGround()
    {
        _raycastOrigin.Set(_cachedTransform.position.x, 1000, _cachedTransform.position.z);

        // 从摄象机位置垂直向上计算与地板的相交点
        // 确保摄象机不会低于地板or水面（游泳池or温泉场景）
        // 使用Physics.RaycastNonAlloc减少GC
        int hitCount = Physics.RaycastNonAlloc(_raycastOrigin, Vector3.down, _raycastHits, RAYCAST_MAX_DISTANCE, groundLayers);

        if (hitCount > 0)
        {
            float minHeight = _raycastHits[0].point.y + CAMERA_MIN_HEIGHT_ABOVE_GROUND;
            if (_cachedTransform.position.y < minHeight)
            {
                Vector3 oldPos = _cachedTransform.position;
                Vector3 newPos = new Vector3(oldPos.x, minHeight, oldPos.z);
                float positionDelta = Vector3.Distance(oldPos, newPos);
                if (positionDelta > 0.01f)
                {
                    // 如果当前处于障碍恢复状态
                    //if (_isObstructedRestoring)
                    //{
                    //    float newDistance = Vector3.Distance(_focalPoint.position, newPos);
                    //    newDistance = Mathf.Clamp(newDistance, MinDistance, MaxDistance);
                    //    _originalDistanceBeforeObstruction = newDistance;
                    //    Distance = newDistance;
                    //}

                    _cachedTransform.position = newPos;
                }
            }
        }
    }

    /// <summary>
    /// 处理相机被建筑物遮挡的情况
    /// </summary>
    private void HandleBuildingObstruction(float deltaTime)
    {
        if (!enableBuildingObstructionAvoidance || _focalPoint == null || target == null)
            return;

        Vector3 targetPosition = _focalPoint.position;
        Vector3 cameraPosition = _cachedTransform.position;
        float currentDistance = Vector3.Distance(targetPosition, cameraPosition);

        // 地面高度预检测
        float minCameraHeight = GetGroundHeight(cameraPosition) + CAMERA_MIN_HEIGHT_ABOVE_GROUND;

        // 遮挡检测
        RaycastHit hit;
        bool obstructed = Physics.Raycast(targetPosition,
                                        (cameraPosition - targetPosition).normalized,
                                        out hit,
                                        currentDistance,
                                        obstructionLayers);

        // 绘制调试射线
        if (DebugDrawRays)
        {
            Color rayColor = obstructed ? _rayObstructedColor : _rayNormalColor;
            if (obstructed)
            {
                // 从目标到碰撞点绘制射线
                Debug.DrawLine(targetPosition, hit.point, rayColor, DebugRayDrawDuration);

                // 绘制碰撞点法线（用于可视化调整方向）
                Debug.DrawLine(hit.point, hit.point + hit.normal * 2f, _rayHitNormalColor, DebugRayDrawDuration);

                // 绘制碰撞点到相机的射线（以不同颜色表示被阻挡部分）
                Debug.DrawLine(hit.point, cameraPosition, Color.yellow, DebugRayDrawDuration);
            }
            else
            {
                // 没有遮挡，绘制整条射线
                Debug.DrawLine(targetPosition, cameraPosition, rayColor, DebugRayDrawDuration);
            }
        }

        if (obstructed)
        {
            HandleObstructionCase(hit, targetPosition, cameraPosition, minCameraHeight);
        }
        else if (_isObstructedRestoring)
        {
            // 恢复逻辑
            HandleRestorationCase(deltaTime, targetPosition, minCameraHeight);
        }
    }

    /// <summary>
    /// 处理遮挡
    /// </summary>
    private void HandleObstructionCase(RaycastHit hit, Vector3 targetPosition, Vector3 cameraPosition, float minCameraHeight)
    {
        // 计算基础安全距离
        float hitDistance = hit.distance *  safeDistanceMultiplier;
        Vector3 proposedPosition = targetPosition +
                                  (cameraPosition - targetPosition).normalized * hitDistance;

        // 确保不低于地面高度
        proposedPosition.y = Mathf.Max(proposedPosition.y, minCameraHeight);

        // 重新计算安全距离
        float newDistance = Vector3.Distance(targetPosition, proposedPosition);

        newDistance = Mathf.Clamp(newDistance, MinDistance, MaxDistance);
        if (!_isObstructedRestoring)
        {
            _isObstructedRestoring = true;
            _originalDistanceBeforeObstruction = Distance;
        }

        Distance = newDistance;
    }

    /// <summary>
    /// 尝试安全地恢复相机的原始设置
    /// </summary>
    private void HandleRestorationCase(float deltaTime, Vector3 targetPosition, float minCameraHeight)
    {
        // 在做距离动画时，不做恢复
        if (_isDoDistanceTweening) return;

        // 计算当前方向
        Vector3 currentDirection = _cachedTransform.forward * -1; // 相机后方向量

        // 检查从目标沿当前方向到原始距离是否安全
        Vector3 positionAtOriginalDistance = targetPosition + currentDirection * _originalDistanceBeforeObstruction;
        Vector3 directionToCheck = positionAtOriginalDistance - targetPosition;
        float distanceToCheck = directionToCheck.magnitude;
        RaycastHit hit;
        bool wouldBeObstructed = Physics.Raycast(targetPosition, directionToCheck.normalized, out hit,
                                               distanceToCheck, obstructionLayers);

        Vector3 currentDirNormalized = directionToCheck.normalized;
        if (wouldBeObstructed)
        {
            // 原始距离不安全，找到一个安全的最大距离
            float safeDistance = Vector3.Distance(targetPosition, hit.point) * safeDistanceMultiplier;
            // 考虑minCameraHeight，计算沿当前方向的最大允许距离
            if (currentDirNormalized.y != 0)
            {
                // 计算沿当前方向移动时，y坐标刚好等于minCameraHeight的距离
                float distanceForHeight = (minCameraHeight - targetPosition.y) / currentDirNormalized.y;
                safeDistance = currentDirNormalized.y < 0 ?
                    Mathf.Min(safeDistance, distanceForHeight) :
                    Mathf.Min(safeDistance, _originalDistanceBeforeObstruction);
            }

            safeDistance = Mathf.Clamp(safeDistance, MinDistance, MaxDistance);
            _fixedRestoreDistance = Mathf.Min(_originalDistanceBeforeObstruction, safeDistance);
        }
        else
        {
            float requiredDistance = _originalDistanceBeforeObstruction;
            // 计算沿当前方向的最低高度限制
            if (currentDirNormalized.y != 0)
            {
                // 计算满足最低高度所需的距离
                float distanceForHeight = (minCameraHeight - targetPosition.y) / currentDirNormalized.y;

                // 当方向向下时（Y分量为负），需要限制最大距离
                if (currentDirNormalized.y < 0)
                {
                    requiredDistance = Mathf.Min(requiredDistance, distanceForHeight);
                }
                // 当方向向上时（Y分量为正），不需要限制（距离越大高度越高）
            }

            _fixedRestoreDistance = Mathf.Clamp(requiredDistance, MinDistance, MaxDistance);
        }

        // 原始距离安全，可以直接恢复
        float lerpFactor = deltaTime * obstructionReturnSpeed;
        Distance = Mathf.Lerp(Distance, _fixedRestoreDistance, lerpFactor);
        // 如果距离已恢复到接近原始值，重置恢复中状态
        if (Mathf.Abs(Distance - _originalDistanceBeforeObstruction) < 0.05f)
        {
            _isObstructedRestoring = false;
        }
    }

    /// <summary>
    /// 获取地面高度
    /// </summary>
    private float GetGroundHeight(Vector3 position)
    {
        RaycastHit groundHit;
        if (Physics.Raycast(new Vector3(position.x, 1000, position.z),
                           Vector3.down, out groundHit,
                           RAYCAST_MAX_DISTANCE, groundLayers))
        {
            return groundHit.point.y;
        }
        return position.y - CAMERA_MIN_HEIGHT_ABOVE_GROUND; // 默认值
    }
    #endregion

    #region 编辑器工具方法
#if UNITY_EDITOR
    /// <summary>
    /// 编辑器模式下环绕旋转
    /// </summary>
    [NoToLua]
    public void RoationAround(float duration, bool clockWise)
    {
        float lastAmount = 0f;
        var t = DOTween.To(() => 0f,
            v =>
            {
                float amount = v - lastAmount;
                amount = clockWise ? -amount : amount;
                lastAmount = v;
                this.Swipe(amount, 0);
            },
            720f,
            duration);

        t.SetEase(Ease.Linear);
    }
#endif
    #endregion
}
