﻿using Nirvana;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace AssetsCheck
{
    class EffectQualityControlActiveBindingOptimize : BaseChecker
    {
        private string[] checkDirs = {
            "Assets/Game/Effects/Prefab",
            "Assets/Game/Model",
        };


        public override string GetErrorDesc()
        {
            return "优化QualityControlActive的绑定规则";
        }


        protected override void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);

        }

        protected override void OnFix(string[] lines)
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);

            string assetPath = "";
            try
            {
                string[] quelityName = new string[4] { "Perfect", "Good", "Normal", "Low" };
                for (int i = 0; i < guids.Length; i++)
                {
                    assetPath = AssetDatabase.GUIDToAssetPath(guids[i]);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    if (prefab != null)
                    {
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        QualityControlActive qualityControlActive = gameObject.GetComponentInChildren<QualityControlActive>(true);
                        if (null == qualityControlActive)
                        {
                            GameObject.DestroyImmediate(gameObject);
                            continue;
                        }

                        ControlItem[] controls = qualityControlActive.GetControls();
                        if (controls.Length == 4
                            && controls[0].Target.name == quelityName[0]
                            && controls[1].Target.name == quelityName[1]
                            && controls[2].Target.name == quelityName[2]
                            && controls[3].Target.name == quelityName[3])
                        {
                            GameObject.DestroyImmediate(gameObject);
                            continue;
                        }

                        Transform[] qualityRootParents = new Transform[4];
                        for (int j = 0; j < quelityName.Length; j++)
                        {
                            string quality = quelityName[j];
                            Transform transform = gameObject.transform.FindByName(quality);
                            if (null == transform)
                            {
                                GameObject qualityObj = new GameObject(quality);
                                qualityObj.transform.parent = gameObject.transform;
                                qualityRootParents[j] = qualityObj.transform;
                            }
                            else
                            {
                                qualityRootParents[j] = transform;
                            }
                        }

                        foreach (var control in controls)
                        {
                            if (control.Target != null)
                            {
                                if (control.EnabledLevels[3])
                                {
                                    (control.Target as GameObject).transform.parent = qualityRootParents[3].transform;
                                }
                                else if (control.EnabledLevels[2])
                                {
                                    (control.Target as GameObject).transform.parent = qualityRootParents[2].transform;
                                }
                                else if (control.EnabledLevels[1])
                                {
                                    (control.Target as GameObject).transform.parent = qualityRootParents[1].transform;
                                }
                                else if (control.EnabledLevels[0])
                                {
                                    (control.Target as GameObject).transform.parent = qualityRootParents[0].transform;
                                }
                            }
                        }

                        ControlItem[] controlsNew = new ControlItem[4];

                        List<ControlItem> list = new List<ControlItem>();
                        for (int k = 0; k < qualityRootParents.Length; k++)
                        {
                            GameObject childObj = qualityRootParents[k].gameObject;
                            ControlItem controlItem = new ControlItem();
                            controlItem.Target = childObj;
                            controlItem.EnabledLevels = new bool[4] { k >= 0, k >= 1, k >= 2, k >= 3 };
                            controlsNew[k] = controlItem;
                        }

                        qualityControlActive.SetControls(controlsNew);
                        PrefabUtility.SaveAsPrefabAsset(gameObject, assetPath);
                        GameObject.DestroyImmediate(gameObject);
                    }

                    EditorUtility.DisplayProgressBar("正在处理预制体...", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                EditorUtility.ClearProgressBar();
            }
            catch (Exception)
            {
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                EditorUtility.ClearProgressBar();
                Debug.LogErrorFormat("assetPath:{0}", assetPath);
                throw;
            }
        }

        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(string.Format("{0}", asset));

                return builder;
            }
        }
    }
}
