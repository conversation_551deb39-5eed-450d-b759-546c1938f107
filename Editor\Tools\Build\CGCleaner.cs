﻿using Game;
using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class CGCleaner
{
    private static string[] checkDirs = { "Assets/Game/CG" };

    public static void ClearCG()
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (null == gameobj)
            {
                continue;
            }

            if (null == gameobj.GetComponent<CGController>())
            {
                continue;
            }

            FixCameraCullingMask(gameobj);
            FixActorRender(gameobj);
            FixGameObjectAttach(gameobj);
            PrefabUtility.ResetToPrefabState(gameobj);
            PrefabUtility.SetPropertyModifications(gameobj, new PropertyModification[] { });
        }

        AssetDatabase.SaveAssets();
    }

    private static void FixCameraCullingMask(GameObject gameObject)
    {
        int count = 0;
        Camera[] cameras = gameObject.GetComponentsInChildren<Camera>();
        for (int i = 0; i < cameras.Length; i++)
        {
            Camera camera = cameras[i];
            if (0 != (camera.cullingMask))
            {
                camera.cullingMask |= 1 << GameLayers.ImportantCulling2;
                camera.cullingMask |= 1 << GameLayers.ImportantCulling3;
                camera.cullingMask |= 1 << GameLayers.CastShadow;
                camera.cullingMask |= 1 << GameLayers.ReceiveShadow;
                count++;
            }
        }

        if (count >= 2)
        {
            Debug.LogErrorFormat("错语的CullingMask, {0}", gameObject.name);
        }
    }

    private static void FixActorRender(GameObject gameobj)
    {
        ActorRender[] actorRenders = gameobj.GetComponentsInChildren<ActorRender>(true);
        if (actorRenders.Length > 1)
        {
            for (int i = 0; i < actorRenders.Length; i++)
            {
                GameObject.DestroyImmediate(actorRenders[i], true);
            }
        }
    }

    private static void FixGameObjectAttach(GameObject gameobj)
    {
        GameObjectAttach[] attachs = gameobj.GetComponentsInChildren<GameObjectAttach>(true);
        for (int i = 0; i < attachs.Length; i++)
        {
            if (attachs[i].IsGameobjectMissing())
            {
                GameObject.DestroyImmediate(attachs[i], true);
            }
        }
    }
}
