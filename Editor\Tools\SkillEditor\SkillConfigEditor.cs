using Newtonsoft.Json;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using Nirvana;

/// <summary>
/// 技能配置编辑器
/// </summary>
public class SkillConfigEditor : EditorWindow
{
    #region 模块管理器
    private SkillEditorModelManager _modelManager;
    private SkillEditorEffectManager _effectManager;
    private SkillEditorSoundManager _soundManager;
    private SkillEditorCameraEffectManager _cameraEffectManager;
    private SkillEditorProjectileManager _projectileManager;
    private SkillEditorHurtManager _hurtManager;
    private SkillEditorAnimationController _animationController;
    #endregion

    #region 状态管理
    private SkillEditorPrefabDataConfig _dataConfig;
    private int _prefabId;
    private bool _isGenerateActorController = true;
    private bool _isGenerateActorTriggers = true;
    private bool _isFarView = false;
    private GameObject _mainCamera;
    #endregion

    #region 界面状态
    private int _selectTriggerToolbar = 0;
    private int _selectActorToolbar = 0;
    private bool _tempIsPlaying = true;
    #endregion

    #region 事件系统
    private HashSet<string> _actionList = new HashSet<string>();
    private HashSet<string> _listenedList = new HashSet<string>();
    private AnimatorEventDispatcher _dispatcher;
    private bool _isInitEvent = false;
    #endregion

    #region Unity编辑器菜单与窗口
    /// <summary>
    /// Unity菜单项：显示技能编辑器窗口
    /// </summary>
    [MenuItem("自定义工具/技能编辑器")]
    public static void ShowWindow()
    {
        if (!SkillEditorGUIConfig.IsValidScene())
        {
            EditorUtility.DisplayDialog("警告", "请在SkillEditor场景下打开技能编辑器", "确定");
            return;
        }

        var window = EditorWindow.GetWindow<SkillConfigEditor>(false, "技能编辑器");
        window.minSize = new Vector2(800, 600);
    }

    /// <summary>
    /// 窗口启用时的初始化
    /// </summary>
    private void OnEnable()
    {
        if (!SkillEditorGUIConfig.IsValidScene())
            return;

        InitializeManagers();
        InitializeSceneReferences();
    }

    /// <summary>
    /// 初始化管理器
    /// </summary>
    private void InitializeManagers()
    {
        try
        {
            _modelManager = new SkillEditorModelManager();
            _effectManager = new SkillEditorEffectManager();
            _soundManager = new SkillEditorSoundManager();
            _cameraEffectManager = new SkillEditorCameraEffectManager();
            _projectileManager = new SkillEditorProjectileManager();
            _hurtManager = new SkillEditorHurtManager();
            _animationController = new SkillEditorAnimationController();

            _modelManager.Initialize();
            _modelManager.OnModelChanged += OnModelChanged;
            _modelManager.OnModelCleared += OnModelCleared;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"初始化管理器时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 初始化场景引用
    /// </summary>
    private void InitializeSceneReferences()
    {
        _mainCamera = GameObject.Find(SkillEditorGUIConfig.MAIN_CAMERA_NAME);
    }
    #endregion

    #region 主界面GUI
    /// <summary>
    /// 主GUI绘制方法
    /// </summary>
    private void OnGUI()
    {
        try
        {
            if (!SkillEditorGUIConfig.IsValidScene())
                return;

            // 在OnGUI中初始化样式
            SkillEditorGUI.InitializeStyles();

            HandlePlayModeChange();

            GUILayout.BeginVertical();
            DrawModelSelection();

            if (_modelManager == null || !_modelManager.HasValidModel)
            {
                GUILayout.EndVertical();
                return;
            }

            InitializeEventIfNeeded();
            DrawConfigurationControls();
            GUILayout.EndVertical();
        }
        catch (System.Exception ex)
        {
            GUILayout.EndVertical(); // 确保GUI布局正确结束
            EditorGUILayout.HelpBox($"GUI绘制错误: {ex.Message}", MessageType.Error);
            Debug.LogError($"SkillConfigEditor OnGUI 错误: {ex}");
        }
    }

    /// <summary>
    /// 处理播放模式变化
    /// </summary>
    private void HandlePlayModeChange()
    {
        if (Application.isPlaying != _tempIsPlaying)
        {
            _tempIsPlaying = Application.isPlaying;
            _modelManager.ClearModel();
            ClearEvent(true);
        }
    }

    /// <summary>
    /// 绘制模型选择界面
    /// </summary>
    private void DrawModelSelection()
    {
        _modelManager.DrawModelSelection();
    }

    /// <summary>
    /// 如果需要则初始化事件
    /// </summary>
    private void InitializeEventIfNeeded()
    {
        if (!_isInitEvent)
        {
            InitEvent();
            _isInitEvent = true;
        }
    }

    /// <summary>
    /// 绘制配置控制界面
    /// </summary>
    private void DrawConfigurationControls()
    {
        GUILayout.BeginVertical();
        SkillEditorGUI.DrawTitle("保存配置数据");

        GUILayout.BeginHorizontal();
        DrawSaveButton();
        DrawCameraToggleButton();
        GUILayout.EndHorizontal();

        DrawTriggerWindow();
        GUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制保存按钮
    /// </summary>
    private void DrawSaveButton()
    {
        if (GUILayout.Button("保存"))
        {
            SaveConfiguration();
        }
    }

    /// <summary>
    /// 绘制摄像机切换按钮
    /// </summary>
    private void DrawCameraToggleButton()
    {
        string buttonText = _isFarView ? "切换到前视觉角度" : "切换到后视觉角度";
        if (GUILayout.Button(buttonText))
        {
            ToggleCameraView();
        }
    }
    #endregion

    #region 配置管理
    /// <summary>
    /// 保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        if (!_isGenerateActorController)
        {
            _dataConfig.actorController = null;
        }

        if (!_isGenerateActorTriggers)
        {
            _dataConfig.actorTriggers = null;
        }

        string jsonStr = JsonConvert.SerializeObject(_dataConfig);
        string jsonPath = SkillEditorUtils.BuildConfigPath(_modelManager.ModelType, _modelManager.ModelResId, "json");
        File.WriteAllText(jsonPath, jsonStr);

        var luaConfig = JsonToLua.Convert(jsonStr).Replace("[none]", string.Empty);
        string luaPath = SkillEditorUtils.BuildConfigPath(_modelManager.ModelType, _modelManager.ModelResId, "lua");
        File.WriteAllText(luaPath, luaConfig);

        AssetDatabase.Refresh();
        ReadByJson();
        ReInitEvent();
    }

    /// <summary>
    /// 从JSON文件读取配置数据
    /// </summary>
    private void ReadByJson()
    {
        var jsonPath = SkillEditorUtils.BuildConfigPath(_modelManager.ModelType, _modelManager.ModelResId, "json");

        // 如果JSON文件不存在，尝试从Lua文件转换
        if (!File.Exists(jsonPath))
        {
            CreateJsonFromLua(jsonPath);
        }

        // 读取并反序列化JSON配置
        var jsonContents = File.ReadAllText(jsonPath);
        _dataConfig = JsonConvert.DeserializeObject<SkillEditorPrefabDataConfig>(jsonContents);

        // 初始化配置数据
        InitializeConfigData();

        // 初始化各个管理器
        InitializeManagersWithConfig();
    }

    /// <summary>
    /// 从Lua文件创建JSON文件
    /// </summary>
    private void CreateJsonFromLua(string jsonPath)
    {
        var luaPath = SkillEditorUtils.BuildConfigPath(_modelManager.ModelType, _modelManager.ModelResId, "lua");
        if (File.Exists(luaPath))
        {
            var jsonStr = LuaToJson.Convert(luaPath);
            File.WriteAllText(jsonPath, jsonStr);
        }
        else
        {
            File.Create(jsonPath).Dispose();
        }
        AssetDatabase.Refresh();
    }

    /// <summary>
    /// 初始化配置数据
    /// </summary>
    private void InitializeConfigData()
    {
        _isGenerateActorController = true;
        _isGenerateActorTriggers = true;

        if (_dataConfig == null)
        {
            _dataConfig = new SkillEditorPrefabDataConfig();
        }

        if (_dataConfig.actorController == null)
        {
            _dataConfig.actorController = new SkillEditorPrefabDataConfig.ActorControllerData();
            _isGenerateActorController = false;
        }

        if (_dataConfig.actorTriggers == null)
        {
            _dataConfig.actorTriggers = new SkillEditorPrefabDataConfig.ActorTriggersData();
            _isGenerateActorTriggers = false;
        }
    }

    /// <summary>
    /// 使用配置初始化管理器
    /// </summary>
    private void InitializeManagersWithConfig()
    {
        _effectManager.Initialize(_dataConfig, _modelManager);
        _soundManager.Initialize(_dataConfig, _modelManager);
        _cameraEffectManager.Initialize(_dataConfig, _modelManager);
        _projectileManager.Initialize(_dataConfig, _modelManager);
        _hurtManager.Initialize(_dataConfig, _modelManager, _soundManager);
        _animationController.Initialize(_modelManager, _projectileManager, _hurtManager);
    }
    #endregion

    #region 摄像机控制
    /// <summary>
    /// 切换摄像机视角
    /// </summary>
    private void ToggleCameraView()
    {
        if (_mainCamera == null) return;

        _isFarView = !_isFarView;

        Vector3 position = _isFarView
            ? SkillEditorGUIConfig.FarViewPosition
            : SkillEditorGUIConfig.NearViewPosition;

        Vector3 rotation = _isFarView
            ? SkillEditorGUIConfig.FarViewRotation
            : SkillEditorGUIConfig.NearViewRotation;

        _mainCamera.transform.localPosition = position;
        _mainCamera.transform.localEulerAngles = rotation;
    }
    #endregion

    #region 事件回调
    /// <summary>
    /// 模型变化回调
    /// </summary>
    private void OnModelChanged(int prefabId, string modelResId)
    {
        _prefabId = prefabId;
        ReadByJson();
        ReInitEvent();
    }

    /// <summary>
    /// 模型清理回调
    /// </summary>
    private void OnModelCleared()
    {
        ClearEvent(true);
    }
    #endregion

    #region 触发器窗口
    /// <summary>
    /// 绘制触发器窗口
    /// </summary>
    private void DrawTriggerWindow()
    {
        BeginWindows();

        DrawActorTriggersSection();
        DrawActorControllerSection();
        DrawButtonWindow();

        EndWindows();
    }

    /// <summary>
    /// 绘制ActorTriggers部分
    /// </summary>
    private void DrawActorTriggersSection()
    {
        _isGenerateActorTriggers = EditorGUILayout.Toggle("生成ActorTriggers表", _isGenerateActorTriggers, GUILayout.MaxWidth(200));

        if (!_isGenerateActorTriggers) return;

        _selectTriggerToolbar = SkillEditorGUI.DrawToolbar(_selectTriggerToolbar, SkillEditorGUIConfig.ToolBarNames);

        switch ((SkillEditorGUIConfig.TriggerToolbar)_selectTriggerToolbar)
        {
            case SkillEditorGUIConfig.TriggerToolbar.Effects:
                _effectManager.DrawEffectsWindow();
                GUILayout.Window(0, SkillEditorGUIConfig.UpWindowRect, DoEffectItem, "特效信息");
                break;
            case SkillEditorGUIConfig.TriggerToolbar.Sounds:
                _soundManager.DrawSoundsWindow();
                GUILayout.Window(2, SkillEditorGUIConfig.UpWindowRect, DoSoundItem, "sounds 信息");
                break;
            case SkillEditorGUIConfig.TriggerToolbar.CamerasShakes:
                _cameraEffectManager.DrawCameraShakesWindow();
                GUILayout.Window(12, SkillEditorGUIConfig.UpWindowRect, DoShakeItem, "CamerasShakes 信息");
                break;
            case SkillEditorGUIConfig.TriggerToolbar.RadialBlurs:
                _cameraEffectManager.DrawRadialBlursWindow();
                GUILayout.Window(22, SkillEditorGUIConfig.UpWindowRect, DoRadialBlursItem, "RadialBlurs 信息");
                break;
        }
    }

    /// <summary>
    /// 绘制ActorController部分
    /// </summary>
    private void DrawActorControllerSection()
    {
        _isGenerateActorController = EditorGUILayout.Toggle("生成ActorController表", _isGenerateActorController, GUILayout.MaxWidth(200));

        if (!_isGenerateActorController) return;

        _selectActorToolbar = SkillEditorGUI.DrawToolbar(_selectActorToolbar, SkillEditorGUIConfig.ActorToolbarNames);

        switch ((SkillEditorGUIConfig.ActorToolbar)_selectActorToolbar)
        {
            case SkillEditorGUIConfig.ActorToolbar.Hurts:
                _hurtManager.DrawHurtsWindow();
                GUILayout.Window(4, SkillEditorGUIConfig.MiddleRect, DoHurtItem, "hurt信息");
                GUILayout.Window(5, SkillEditorGUIConfig.MiddleRect2, DoHitItem, "hurt信息-多段击中");
                break;
            case SkillEditorGUIConfig.ActorToolbar.Projectiles:
                _projectileManager.DrawProjectilesWindow();
                GUILayout.Window(3, SkillEditorGUIConfig.MiddleRect, DoProjectileItem, "projectile信息");
                break;
            case SkillEditorGUIConfig.ActorToolbar.Others:
                // 预留给其他配置功能
                break;
        }
    }

    /// <summary>
    /// 绘制按钮窗口
    /// </summary>
    private void DrawButtonWindow()
    {
        GUILayout.Window(7, SkillEditorGUIConfig.ButtonRect, DoButtonWindow, "Button");
    }

    /// <summary>
    /// 特效项目窗口回调
    /// </summary>
    private void DoEffectItem(int windowId)
    {
        _effectManager.DrawEffectDetails();
    }

    /// <summary>
    /// 音效项目窗口回调
    /// </summary>
    private void DoSoundItem(int windowId)
    {
        _soundManager.DrawSoundDetails();
    }

    /// <summary>
    /// 震屏项目窗口回调
    /// </summary>
    private void DoShakeItem(int windowId)
    {
        _cameraEffectManager.DrawShakeDetails();
    }

    /// <summary>
    /// 径向模糊项目窗口回调
    /// </summary>
    private void DoRadialBlursItem(int windowId)
    {
        _cameraEffectManager.DrawRadialBlurDetails();
    }

    /// <summary>
    /// 弹道项目窗口回调
    /// </summary>
    private void DoProjectileItem(int windowId)
    {
        _projectileManager.DrawProjectileDetails();
    }

    /// <summary>
    /// 击中项目窗口回调
    /// </summary>
    private void DoHurtItem(int windowId)
    {
        _hurtManager.DrawHurtDetails();
    }

    /// <summary>
    /// 多段击中项目窗口回调
    /// </summary>
    private void DoHitItem(int windowId)
    {
        _hurtManager.DrawHitDetails();
    }

    /// <summary>
    /// 按钮窗口回调
    /// </summary>
    private void DoButtonWindow(int windowId)
    {
        _animationController.DrawAnimationButtonWindow(_effectManager.CurrentEffectArrayIndex);
    }
    #endregion

    #region 事件系统管理
    /// <summary>
    /// 清理事件
    /// </summary>
    private void ClearEvent(bool isClearAll = false)
    {
        _actionList?.Clear();

        if (isClearAll)
        {
            _listenedList?.Clear();
            _dispatcher = null;
            _isInitEvent = false;
        }
    }

    /// <summary>
    /// 重新初始化事件
    /// </summary>
    private void ReInitEvent(bool isClearAll = false)
    {
        ClearEvent(isClearAll);
        InitEvent();
    }

    /// <summary>
    /// 初始化事件系统
    /// </summary>
    private void InitEvent()
    {
        if (_modelManager?.MainAnimator == null || _dataConfig?.actorTriggers == null)
            return;

        _dispatcher = _modelManager.MainAnimator.GetOrAddComponent<AnimatorEventDispatcher>();
        if (_dispatcher == null) return;

        CollectEventNames();
        RegisterEventListeners();
    }

    /// <summary>
    /// 收集事件名称
    /// </summary>
    private void CollectEventNames()
    {
        CollectEffectEvents();
        CollectSoundEvents();
        CollectShakeEvents();
        CollectRadialBlurEvents();
    }

    /// <summary>
    /// 收集特效事件
    /// </summary>
    private void CollectEffectEvents()
    {
        if (_dataConfig.actorTriggers.effects == null) return;

        foreach (var effect in _dataConfig.actorTriggers.effects)
        {
            AddEventToList(effect.triggerEventName);
            AddEventToList(effect.triggerStopEvent);
        }
    }

    /// <summary>
    /// 收集音效事件
    /// </summary>
    private void CollectSoundEvents()
    {
        if (_dataConfig.actorTriggers.sounds == null) return;

        foreach (var sound in _dataConfig.actorTriggers.sounds)
        {
            AddEventToList(sound.soundEventName);
        }
    }

    /// <summary>
    /// 收集震屏事件
    /// </summary>
    private void CollectShakeEvents()
    {
        if (_dataConfig.actorTriggers.cameraShakes == null) return;

        foreach (var shake in _dataConfig.actorTriggers.cameraShakes)
        {
            AddEventToList(shake.eventName);
        }
    }

    /// <summary>
    /// 收集径向模糊事件
    /// </summary>
    private void CollectRadialBlurEvents()
    {
        if (_dataConfig.actorTriggers.radialBlurs == null) return;

        foreach (var radialBlur in _dataConfig.actorTriggers.radialBlurs)
        {
            AddEventToList(radialBlur.eventName);
        }
    }

    /// <summary>
    /// 添加事件到列表
    /// </summary>
    private void AddEventToList(string eventName)
    {
        if (!string.IsNullOrEmpty(eventName) && !_actionList.Contains(eventName))
        {
            _actionList.Add(eventName);
        }
    }

    /// <summary>
    /// 注册事件监听器
    /// </summary>
    private void RegisterEventListeners()
    {
        foreach (var eventName in _actionList)
        {
            if (!_listenedList.Contains(eventName))
            {
                _listenedList.Add(eventName);
                _dispatcher.ListenEvent(eventName, (str, state) => PlayEvent(eventName));
            }
        }
    }

    /// <summary>
    /// 播放事件
    /// </summary>
    private void PlayEvent(string eventName)
    {
        _effectManager.PlayEffectEvents(eventName);
        _soundManager.PlaySoundEvents(eventName);
        _cameraEffectManager.PlayCameraShakeEvents(eventName);
        _cameraEffectManager.PlayRadialBlurEvents(eventName);
    }
    #endregion
}
