﻿using UnityEngine;
using UnityEditor;
using Nirvana;
using System.Text;

namespace AssetsCheck
{
    class ActorGameObjectAttachChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/Actors", "Assets/Game/Model", };

        override public string GetErrorDesc()
        {
            return string.Format("Actor上使用了特效，请使用用GameObjectAttach的方式来做");
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                QualityControlActive qualityControlActive = gameobj.GetComponent<QualityControlActive>();
                if (qualityControlActive == null)   //不是特效相关预制
                {
                    ParticleSystem[] particle_systems = gameobj.GetComponentsInChildren<ParticleSystem>(true);
                    if (particle_systems.Length > 0)    // 存在粒子就记录下来
                    {
                        CheckItem item = new CheckItem();
                        item.asset = path;
                        this.outputList.Add(item);
                    }
                }
            }
        }

        protected override void OnFix(string[] lines)
        {
            int count = 0;
            int total = lines.Length;
            foreach (string line in lines)
            {
                string[] str = line.Split(',');
                GameObject prefab = AssetDatabase.LoadAssetAtPath(str[0].Replace("asset:", ""), typeof(GameObject)) as GameObject;
                if (prefab)
                {
                    DeleGameObjectAttachChild(prefab);
                }
                count++;
                EditorUtility.DisplayProgressBar("正在修复预制体GameObjectAttach多余特效...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        private void DeleGameObjectAttachChild(GameObject prefab)
        {
            Game.GameObjectAttach[] game_object_attachs = prefab.GetComponentsInChildren<Game.GameObjectAttach>(true);
            if (game_object_attachs.Length <= 0)
            {
                return;
            }

            for (int i = 0; i < game_object_attachs.Length; i++)
            {
                // 按QualityControlActive 匹配
                QualityControlActive[] qualityControlActives = game_object_attachs[i].gameObject.GetComponentsInChildren<QualityControlActive>(true);
                if (qualityControlActives.Length > 0)    // 存在粒子就记录下来
                {
                    for (int j = 0; j < qualityControlActives.Length; j++)
                    {
                        GameObject.DestroyImmediate(qualityControlActives[j].gameObject, true);
                    }
                }

                // 按名称 + (Clone) 匹配
                Transform trans_clone = game_object_attachs[i].transform.Find(game_object_attachs[i].name + "(Clone)");
                if (trans_clone != null)
                {
                    GameObject.DestroyImmediate(trans_clone.gameObject, true);
                }

                // 按名称 匹配
                trans_clone = game_object_attachs[i].transform.Find(game_object_attachs[i].name);
                if (trans_clone != null)
                {
                    GameObject.DestroyImmediate(trans_clone.gameObject, true);
                }
            }

        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public int width;
            public int height;

            public string MainKey
            {
                get { return string.Format("{0}", asset); }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(this.asset);
                return builder;
            }
        }
    }
}
