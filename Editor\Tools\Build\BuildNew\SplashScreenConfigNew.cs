﻿using System;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.Assertions;

[CreateAssetMenu(fileName = "SplashScreenConfigNew", menuName = "Nirvana/Build/SplashScreenConfigNew")]
public sealed class SplashScreenConfigNew : ScriptableObject
{
    [Serializable]
    internal class SplashScreenLogo
    {
        [SerializeField]
        [Tooltip("The Sprite that is shown during this logo. If this is null, then no logo will be displayed for the duration.")]
        private Sprite logo;

        [SerializeField]
        [Range(2f, 10f)]
        [Tooltip("The total time in seconds for which the logo is shown. The minimum duration is 2 seconds.")]
        private float duration = 2f;

        internal Sprite Logo => logo;

        internal float Duration => duration;
    }

    [SerializeField]
    [Tooltip("Set this to true to display the Splash Screen be shown when the application is launched. Set it to false to disable the Splash Screen.")]
    private bool show = true;

    [SerializeField]
    [Tooltip("The style to use for the Unity logo during the Splash Screen.")]
    private PlayerSettings.SplashScreen.UnityLogoStyle unityLogoStyle = PlayerSettings.SplashScreen.UnityLogoStyle.LightOnDark;

    [SerializeField]
    [Tooltip("The type of animation applied during the splash screen.")]
    private PlayerSettings.SplashScreen.AnimationMode animationMode = PlayerSettings.SplashScreen.AnimationMode.Dolly;

    [SerializeField]
    [Range(0f, 1f)]
    [Tooltip("The target zoom (from 0 to 1) for the background when it reaches the end of the SplashScreen animation's total duration. ")]
    private float animationBackgroundZoom = 1f;

    [SerializeField]
    [Range(0f, 1f)]
    [Tooltip("The target zoom (from 0 to 1) for the logo when it reaches the end of the logo animation's total duration.")]
    private float animationLogoZoom = 1f;

    [SerializeField]
    [Tooltip("Set this to true to show the Unity logo during the Splash Screen. Set it to false to disable the Unity logo.")]
    private bool showUnityLogo = true;

    [SerializeField]
    [Tooltip("Determines how the Unity logo should be drawn, if it is enabled.")]
    private PlayerSettings.SplashScreen.DrawMode drawMode = PlayerSettings.SplashScreen.DrawMode.UnityLogoBelow;

    [SerializeField]
    [Tooltip("The collection of logos that is shown during the splash screen. Logos are drawn in ascending order, starting from index 0, followed by 1, etc etc.")]
    private SplashScreenLogo[] logos;

    [SerializeField]
    [Range(0f, 1f)]
    [Tooltip("In order to increase contrast between the background and the logos, an overlay color modifier is applied. The overlay opacity is the strength of this effect.")]
    private float overlayOpacity = 1f;

    [SerializeField]
    [Tooltip("The background color shown if no background Sprite is assigned. Default is a dark blue RGB(34.44,54).")]
    private Color backgroundColor = new Color(142f / (339f * MathF.PI), 44f / 255f, 18f / 85f, 1f);

    [SerializeField]
    [Tooltip("The background Sprite that is shown in landscape mode. Also shown in portrait mode if backgroundPortrait is null.")]
    private Sprite background;

    [SerializeField]
    [Tooltip("The background Sprite that is shown in portrait mode.")]
    private Sprite backgroundPortrait;

    [SerializeField]
    [Tooltip("480x800, 800x400")]
    private Texture androidSplashScreen;

    [SerializeField]
    [Tooltip("320x480, 480x320")]
    private Texture iPhoneSplashScreen;

    [SerializeField]
    [Tooltip("640x960, 960x640")]
    private Texture iPhoneHighResSplashScreen;

    [SerializeField]
    [Tooltip("640x1136, 1136x640")]
    private Texture iPhoneTallHighResSplashScreen;

    [SerializeField]
    [Tooltip("750x1334, 1334x750")]
    private Texture iPhone47inSplashScreen;

    [SerializeField]
    [Tooltip("1242x2208")]
    private Texture iPhone55inPortraitSplashScreen;

    [SerializeField]
    [Tooltip("2208x1242")]
    private Texture iPhone55inLandscapeSplashScreen;

    [SerializeField]
    [Tooltip("768x1024")]
    private Texture iPadPortraitSplashScreen;

    [SerializeField]
    [Tooltip("1024x768")]
    private Texture iPadLandscapeSplashScreen;

    [SerializeField]
    [Tooltip("1536x2048")]
    private Texture iPadHighResPortraitSplashScreen;

    [SerializeField]
    [Tooltip("2048x1536")]
    private Texture iPadHighResLandscapeSplashScreen;

    internal static void ResetSettins()
    {
    }

    internal void ActiveSetting()
    {
        PlayerSettings.SplashScreen.show = show;
        PlayerSettings.SplashScreen.showUnityLogo = showUnityLogo;
        PlayerSettings.SplashScreen.unityLogoStyle = unityLogoStyle;
        PlayerSettings.SplashScreen.overlayOpacity = overlayOpacity;
        PlayerSettings.SplashScreen.drawMode = drawMode;
        PlayerSettings.SplashScreen.background = background;
        PlayerSettings.SplashScreen.backgroundColor = backgroundColor;
        PlayerSettings.SplashScreen.backgroundPortrait = backgroundPortrait;
        PlayerSettings.SplashScreen.animationMode = animationMode;
        PlayerSettings.SplashScreen.animationBackgroundZoom = animationBackgroundZoom;
        PlayerSettings.SplashScreen.animationLogoZoom = animationLogoZoom;
        if (logos != null && logos.Length != 0)
        {
            PlayerSettings.SplashScreenLogo[] array = new PlayerSettings.SplashScreenLogo[logos.Length];
            for (int i = 0; i < logos.Length; i++)
            {
                SplashScreenLogo splashScreenLogo = logos[i];
                Assert.IsNotNull(splashScreenLogo);
                array[i] = PlayerSettings.SplashScreenLogo.Create(splashScreenLogo.Duration, splashScreenLogo.Logo);
            }

            PlayerSettings.SplashScreen.logos = array;
        }
        else
        {
            PlayerSettings.SplashScreen.logos = new PlayerSettings.SplashScreenLogo[0];
        }

        MethodInfo method = typeof(PlayerSettings).GetMethod("FindProperty", BindingFlags.NonPublic | BindingFlags.Static);
        if (method != null)
        {
            SetSplashScreen(method, "androidSplashScreen", androidSplashScreen);
            SetSplashScreen(method, "iPhoneSplashScreen", iPhoneSplashScreen);
            SetSplashScreen(method, "iPhoneHighResSplashScreen", iPhoneHighResSplashScreen);
            SetSplashScreen(method, "iPhoneTallHighResSplashScreen", iPhoneTallHighResSplashScreen);
            SetSplashScreen(method, "iPhone47inSplashScreen", iPhone47inSplashScreen);
            SetSplashScreen(method, "iPhone55inPortraitSplashScreen", iPhone55inPortraitSplashScreen);
            SetSplashScreen(method, "iPhone55inLandscapeSplashScreen", iPhone55inLandscapeSplashScreen);
            SetSplashScreen(method, "iPadPortraitSplashScreen", iPadPortraitSplashScreen);
            SetSplashScreen(method, "iPadHighResPortraitSplashScreen", iPadHighResPortraitSplashScreen);
            SetSplashScreen(method, "iPadLandscapeSplashScreen", iPadLandscapeSplashScreen);
            SetSplashScreen(method, "iPadHighResLandscapeSplashScreen", iPadHighResLandscapeSplashScreen);
        }
    }

    private static void SetSplashScreen(MethodInfo method, string name, Texture texture)
    {
        object obj = method.Invoke(null, new object[1] { name });
        SerializedProperty serializedProperty = obj as SerializedProperty;
        serializedProperty.serializedObject.Update();
        serializedProperty.objectReferenceValue = texture;
        serializedProperty.serializedObject.ApplyModifiedProperties();
    }
}
