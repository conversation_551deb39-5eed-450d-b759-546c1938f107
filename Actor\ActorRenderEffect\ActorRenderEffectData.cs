
using UnityEngine;

public class ActorRenderEffectData : ScriptableObject
{
    private static ActorRenderEffectData s_Instance;
    public static ActorRenderEffectData Instance 
    {
        get
        {
            if (s_Instance == null)
            {
                var container = Object.FindObjectOfType<ArtDataContainer>();
#if UNITY_EDITOR
                if (container == null)
                {
                    s_Instance = UnityEditor.AssetDatabase.LoadAssetAtPath<ActorRenderEffectData>(
                        "Assets/Game/Misc/ActorRenderEffectData.asset");
                }
                else
                {
                    s_Instance = container.actorRenderEffectData;
                }
#else
                s_Instance = container.actorRenderEffectData;
#endif
                s_Instance.OnValidate();
            }

            return s_Instance;
        }
    }
    
    public Color dissolveColor;
    public float dissolveWidth;
    public AnimationCurve dissolveCurve;
    public Texture dissolveTexture;
    public float dissolveLife;
    
    public float blinkLife;
    public AnimationCurve blinkCurve;
    public float blinkFresnelPower;
    public Color blinkColor;
    

    private void OnValidate()
    {
        Shader.SetGlobalColor("_DissolveColor", dissolveColor);
        Shader.SetGlobalFloat("_DissolveWidth", dissolveWidth);
        Shader.SetGlobalTexture("_DissolveTex", dissolveTexture);
        Shader.SetGlobalColor("_BlinkColor", blinkColor);
        Shader.SetGlobalFloat("_BlinkFresnelPower", blinkFresnelPower);
    }

}