﻿using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class ReplaceTextClass
{
    public string copy_string = string.Empty;
    public bool is_change = false;
}

public class FindTextChange
{
    [MenuItem("GameObject/UI/查找Text文本并并复制到剪切板", false, 0)]
    private static void FindHireaFunc()
    {
        if (Selection.gameObjects.Length > 0)
        {
            string text_totle = string.Empty;
            GameObject select = Selection.gameObjects[0];
			text_totle = text_totle + FindTextAndChange(select, "", "", Color.clear, Color.clear, false);
            CopyString(text_totle);
        }
        else if (Selection.gameObjects.Length > 1)
        {
            UnityEditor.EditorUtility.DisplayDialog("错误提示", "当前多选会触发多次，请暂时单选", "确认");
        }
        else
        {
            Debug.LogError("至少选中一个物体");
        }
    }
    [MenuItem("Assets/查找Text文本并并复制到剪切板", false, 100)]
    private static void FindProjFunc()
    {
        if (Selection.gameObjects.Length > 0)
        {
            string text_totle = string.Empty;
            GameObject select = Selection.gameObjects[0];
			text_totle = text_totle + FindTextAndChange(select, "", "", Color.clear, Color.clear, false);
            CopyString(text_totle);
        }
        else if (Selection.gameObjects.Length > 1)
        {
            UnityEditor.EditorUtility.DisplayDialog("错误提示", "当前多选会触发多次，请暂时单选", "确认");
        }
        else
        {
            Debug.LogError("至少选中一个物体");
        }
    }
    /// <summary>
    /// 查找文本并是否替换
    /// </summary>
	public static string FindTextAndChange(GameObject obj, string change_text, string new_string, Color changeTextColor, Color newTextColor, 
                                            bool isCheckLable = false, bool is_record_change = false, bool is_record_all = true)
    {
        if (obj == null)
        {
            return string.Empty;
        }
        string text_totle = string.Empty;
        GameObject select = obj;
        Text[] TextComps = select.GetComponentsInChildren<Text>(true);
        bool is_chanage = false;

        for (int i = 0; i < TextComps.Length; i++)
        {
            ReplaceTextClass temp_replace_text = CheckAndCopyText(TextComps[i], change_text, new_string, changeTextColor, newTextColor, 
                isCheckLable, is_record_change, is_record_all, obj.name);

 
            if (text_totle == string.Empty && temp_replace_text.copy_string != string.Empty)
            {
                text_totle = obj.name + "\r\n";
            }
            is_chanage = temp_replace_text.is_change || is_chanage;
            text_totle += temp_replace_text.copy_string;
        }

        //Text owner = select.GetComponent<Text>();
        //if (owner != null)
        //{
        //    ReplaceTextClass temp_replace_text = CheckAndCopyText(owner, change_text, new_string, changeTextColor, newTextColor,
        //        isCheckLable, is_record_change, is_record_all, obj.name);

        //    is_chanage = temp_replace_text.is_change || is_chanage;
        //    text_totle += temp_replace_text.copy_string;
        //}

        if (is_chanage)
        {
            Debug.LogErrorFormat("有替换变更{0}", select.name);
            EditorUtility.SetDirty(select);
        }
  
        return text_totle;
    }

    private static ReplaceTextClass CheckAndCopyText(Text text_inst, string change_text, string new_string, Color changeTextColor, Color newTextColor,
                                            bool isCheckLable, bool is_record_change , bool is_record_all, string obj_name)
    {
        ReplaceTextClass temp_replace_text = new ReplaceTextClass();
        string old_text = text_inst.text;
        if (change_text != "")
        {
            if (old_text == change_text)
            {
                text_inst.text = new_string;
                temp_replace_text.is_change = true;
            }

            if (old_text.Contains(change_text))
            {
                text_inst.text = old_text.Replace(change_text, new_string);
                temp_replace_text.is_change = true;
            }
        }

        string old_color = "";
        string new_color = "";

        //是否检测符合文本的
        if (isCheckLable)
        {
            if (change_text != "" && change_text == old_text)
            {
                if (changeTextColor != Color.clear && newTextColor != Color.clear && ColorToHex(text_inst.color) == ColorToHex(changeTextColor))
                {
                    old_color = ColorToHex(changeTextColor);
                    new_color = ColorToHex(newTextColor);
                    text_inst.color = newTextColor;
                    temp_replace_text.is_change = true;

                }
            }
        }
        else
        {
            if (changeTextColor != Color.clear && newTextColor != Color.clear && ColorToHex(text_inst.color) == ColorToHex(changeTextColor))
            {
                old_color = ColorToHex(changeTextColor);
                new_color = ColorToHex(newTextColor);
                text_inst.color = newTextColor;
                temp_replace_text.is_change = true;
            }
        }

        if (!is_record_all && is_record_change && !temp_replace_text.is_change)   //只需要记录被替换
        {
            temp_replace_text.copy_string = string.Empty;
            return temp_replace_text;
        }

        //if (temp_replace_text.copy_string == string.Empty)
        //{
        //    temp_replace_text.copy_string = obj_name + "\r\n";
        //}

        string path = text_inst.gameObject.name;
        Transform parent = text_inst.transform.parent;

        if (parent == null)
        {
            if (change_text == "")
                path = string.Format("位置：{0}------>当前文本：{1}", path, old_text);
            else
                path = string.Format("位置：{0}------>当前文本：{1} 原来文本：{2}", path, text_inst.text, old_text);

            if (old_color != "" && new_color != "")
            {
                path = path + string.Format(" 当前颜色：{0} 原来颜色：{1}", new_color, old_color);
            }
        }

        while (parent != null)
        {
            bool is_break = false;
            if (parent.name == obj_name)
            {
                is_break = true;
            }
            path = string.Format("{0}/{1}", parent.name, path);
            if (is_break)
            {
                if (change_text == "")
                    path = string.Format("位置：{0}------>当前文本：{1}", path, old_text);
                else
                    path = string.Format("位置：{0}------>当前文本：{1} 原来文本：{2}", path, text_inst.text, old_text);

                if (old_color != "" && new_color != "")
                {
                    path = path + string.Format(" 当前颜色：{0} 原来颜色：{1}", new_color, old_color);
                }
                break;
            }
            parent = parent.parent;
        }

        if (string.IsNullOrEmpty(temp_replace_text.copy_string))
            temp_replace_text.copy_string = string.Format("{0}\r\n", path);
        else
            temp_replace_text.copy_string = string.Format("{0}{1}\r\n", temp_replace_text.copy_string, path);

        return temp_replace_text;
    }

    private static void CopyString(string str)
    {
        Debug.Log(str);
        TextEditor te = new TextEditor();
        te.text = str;
        te.SelectAll();
        te.Copy();
        UnityEditor.EditorUtility.DisplayDialog("提示", "已复制路径至剪切板", "确认");
    }

	private static string ColorToHex(Color32 color)
	{
		return color.r.ToString ("X2") + color.g.ToString ("X2") + color.b.ToString ("X2") + color.a.ToString ("X2");
	}

	private static Color HexToColor(string hex)
	{
		hex = hex.Replace ("0x", string.Empty);
		hex = hex.Replace ("#", string.Empty);
		byte a = byte.MaxValue;
		byte r = byte.Parse (hex.Substring (0, 2), System.Globalization.NumberStyles.HexNumber);
		byte g = byte.Parse (hex.Substring (2, 2), System.Globalization.NumberStyles.HexNumber);
		byte b = byte.Parse (hex.Substring (4, 2), System.Globalization.NumberStyles.HexNumber);

		if (hex.Length == 8) {
			a = byte.Parse (hex.Substring (6, 2), System.Globalization.NumberStyles.HexNumber);
		}
		return new Color32 (r, g, b, a);
	}
}



public class prefabTextChange : EditorWindow
{
    [MenuItem("Tools/换UI工具/预制文本替换")]
    static void TextChangeWindow()
    {
        EditorWindow window = GetWindow(typeof(prefabTextChange), true, "预制文本替换");

        window.Show();
        window.Focus();
    }


    private GameObject Choose;
    private string changeText;
    private string newText;
	private bool isCheckLable;
    private bool isCopyAllBlock;
    private bool isCopyChangeBlock;
    private Color changeTextColor;
	private Color newTextColor;

    /// <summary>
    /// 文件夹路径
    /// </summary>
    private string floder_path = string.Empty;

    private void Awake()
    {
        changeText = EditorPrefs.GetString("Change_text_input", "");
        floder_path = Application.dataPath + "/Game/UIs/View";
    }
    /// <summary>
    /// 文件夹下的所有预制
    /// </summary>
    List<GameObject> all_prefabs = new List<GameObject>();

    private void OnGUI()
    {
        Choose = (GameObject)EditorGUILayout.ObjectField("需要替换的物体", Choose, typeof(GameObject), true);
        changeText = EditorGUILayout.TextField("请输入需要被替换的文本", changeText);
		newText = EditorGUILayout.TextField("请输入新的文本", newText);
		isCheckLable = EditorGUILayout.Toggle("替换颜色是否检测文本", isCheckLable);
        isCopyAllBlock = EditorGUILayout.Toggle("是否拷贝全部详情", isCopyAllBlock);
        isCopyChangeBlock = EditorGUILayout.Toggle("是否拷贝替换详情", isCopyChangeBlock);
        changeTextColor = EditorGUILayout.ColorField("请输入需要被替换的颜色", changeTextColor);
		newTextColor = EditorGUILayout.ColorField("请输入新的颜色", newTextColor);

        if (GUILayout.Button("转换文本", GUILayout.Width(200)))
        {
            this.ChangeClick();
        }


        GUILayout.Space(20);
        GUILayout.Label("下面这个浏览替换会将整个文件夹下的预制的文本的符合上面的字符串都会被替换", GUILayout.Width(500f));
        GUILayout.BeginHorizontal();
        GUILayout.Label("路径", GUILayout.Width(50f));
        floder_path = EditorGUILayout.TextField(floder_path);
        if (GUILayout.Button("浏览", GUILayout.Width(100)))
        {
            floder_path = EditorUtility.OpenFolderPanel("选取拥有预制的文件夹", Application.dataPath + "/Game/UIs", "选取预制");
        }
        GUILayout.EndHorizontal();
        if (GUILayout.Button("转换文件夹预制文本", GUILayout.Width(200)))
        {
            this.FloderChange();
        }
    }

    private void ChangeClick()
    {
        EditorPrefs.SetString("Change_text_input", changeText);
		string result = FindTextChange.FindTextAndChange(Choose, changeText, newText, changeTextColor, newTextColor, isCheckLable, isCopyChangeBlock, isCopyAllBlock);
        EditorUtility.ClearProgressBar();
        if (isCopyAllBlock || isCopyChangeBlock)
        {
            putlog(result);
        }
        else
        {
            UnityEditor.EditorUtility.DisplayDialog("提示", "已转换完成", "确认");
        }
    }

    private void FloderChange()
    {
        if (string.IsNullOrEmpty(floder_path))
        {
            return;
        }
        string[] sub_folders = new string[] { floder_path };
        all_prefabs.Clear();
        this.GetAllPrefabs(sub_folders);
        string out_put = "";
        if (all_prefabs.Count > 0)
        {
            for (int i = 0; i < all_prefabs.Count; i++)
            {
				out_put += FindTextChange.FindTextAndChange(all_prefabs[i], changeText, newText, changeTextColor, newTextColor, isCheckLable, isCopyChangeBlock, isCopyAllBlock);
                EditorUtility.DisplayProgressBar("资源文本转换中", string.Format("正在转换（{0}/{1}）", i, all_prefabs.Count), (float)i / (float)all_prefabs.Count);
            }
        }
        EditorUtility.ClearProgressBar();

        if (isCopyAllBlock || isCopyChangeBlock)
        {
            putlog(out_put);
        }
        else
        {
            UnityEditor.EditorUtility.DisplayDialog("提示", "已转换完成", "确认");
        }

        AssetDatabase.Refresh();
        AssetDatabase.SaveAssets();
    }
    //获取所有的预制体
    private void GetAllPrefabs(string[] sub_folders)
    {
        if (sub_folders.Length <= 0)
        {
            return;
        }
        string[] guids = null;
        for (int i = 0; i < sub_folders.Length; i++)
        {
            guids = Directory.GetFiles(sub_folders[i], "*.prefab", SearchOption.AllDirectories);
            if (guids.Length > 0)
            {
                for (int j = 0; j < guids.Length; j++)
                {
                    string asset_path = guids[j].Replace(Application.dataPath, "");
                    asset_path = "Assets" + asset_path;
                    GameObject prefabs_res = AssetDatabase.LoadAssetAtPath(asset_path, typeof(GameObject)) as GameObject;
                    all_prefabs.Add(prefabs_res);
                    EditorUtility.DisplayProgressBar("资源添加中", string.Format("（正在添加资源{0}/{1}）", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
        }
    }


    private static void putlog(string str)
    {
        Debug.Log(str);
        TextEditor te = new TextEditor();
        te.text = str;
        te.SelectAll();
        te.Copy();
        UnityEditor.EditorUtility.DisplayDialog("提示", "已转换完成，并把转换结果复制至剪切板", "确认");
    }
}
