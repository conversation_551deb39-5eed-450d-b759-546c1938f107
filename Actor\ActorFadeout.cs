﻿using System;
using System.Collections.Generic;
using System.IO;
using Nirvana;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.Rendering;
using LuaInterface;

public sealed class ActorFadeout : MonoBehaviour
{
    [SerializeField]
    private RenderItem[] renderList;

    private float fadeout = -1.0f;
    private float fadeoutTotal = -1.0f;

    private float fadein = -1.0f;
    private float fadeinTotal = -1.0f;

    private Action fadeoutCallback;
    private Action fadeinCallback;

    [NoToLua]
    public void Fadeout(float time, Action callback)
    {
        this.fadeout = time;
        this.fadeoutTotal = time;
        this.fadeoutCallback = callback;

        if (this.fadein > 0)
        {
            this.ClearCache();
            this.fadein = -1.0f;
            this.fadeinTotal = -1.0f;
            if (this.fadeinCallback != null)
            {
                this.fadeinCallback();
                this.fadeinCallback = null;
            }
        }

        foreach (var item in this.renderList)
        {
            item.renderer.sharedMaterial = item.materal;
        }
    }

    [NoToLua]
    public void Fadein(float time, Action callback)
    {
        this.fadein = time;
        this.fadeinTotal = time;
        this.fadeinCallback = callback;

        if (this.fadeout > 0)
        {
            this.ClearCache();
            this.fadeout = -1.0f;
            this.fadeoutTotal = -1.0f;
            if (this.fadeoutCallback != null)
            {
                this.fadeoutCallback();
                this.fadeoutCallback = null;
            }
        }

        foreach (var item in this.renderList)
        {
            item.SetColor(new Color(1, 1, 1, 0));
            item.renderer.sharedMaterial = item.materal;
        }
    }

    public int GetListSize()
    {
        return this.renderList.Length;
    }

    private void ClearCache()
    {
        foreach (var item in this.renderList)
        {
            item.SetColor(Color.white);
        }
    }
    private void Update()
    {
        if (this.fadeout > 0.0f)
        {
            float value = this.fadeout / this.fadeoutTotal;
            foreach (var item in this.renderList)
            {
                item.SetColor(new Color(1, 1, 1, value));
            }

            this.fadeout -= Time.deltaTime;
            if (this.fadeout < 0)
            {
                if (this.fadeoutCallback != null)
                {
                    this.fadeoutCallback();
                    this.fadeoutCallback = null;
                }
                this.ClearCache();
                this.fadeout = -1.0f;
                this.fadeoutTotal = -1.0f;
            }
        }
        else if (this.fadein > 0.0f)
        {
            float value = 1 - this.fadein / this.fadeinTotal;
            foreach (var item in this.renderList)
            {
                item.SetColor(new Color(1, 1, 1, value));
            }

            this.fadein -= Time.deltaTime;
            if (this.fadein < 0)
            {
                if (this.fadeinCallback != null)
                {
                    this.fadeinCallback();
                    this.fadeinCallback = null;
                }
                this.ClearCache();
                this.fadein = -1.0f;
                this.fadeinTotal = -1.0f;
            }
        }
    }

#if UNITY_EDITOR
    [NoToLua]
    public void AutoFetch()
    {
        SkinnedMeshRenderer[] renderers = this.GetComponentsInChildren<SkinnedMeshRenderer>();
        List<RenderItem> list = new List<RenderItem>();
        foreach (var render in renderers)
        {
            foreach (var material in render.sharedMaterials)
            {
                if (null == material)
                {
                    Debug.LogErrorFormat("material is null , {0}", AssetDatabase.GetAssetPath(this.gameObject.GetInstanceID()));
                    continue;
                }

                if (!material.HasProperty("_MaterialStyle"))
                {
                    Debug.LogErrorFormat("not foud fade material, {0}", AssetDatabase.GetAssetPath(material));
                    continue;
                }

                if (material.GetFloat("_MaterialStyle") != 1)
                {
                    continue;
                }

                RenderItem renderItem = new RenderItem();
                renderItem.renderer = render;

                string path = AssetDatabase.GetAssetPath(material.GetInstanceID());
                string matFileName = Path.GetFileNameWithoutExtension(path);
                path = path.Replace(matFileName + ".mat", matFileName + "_fade.mat");

                Material fadeMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
                if (null == fadeMaterial)
                {
                    AssetDatabase.CopyAsset(AssetDatabase.GetAssetPath(material.GetInstanceID()), path);
                    fadeMaterial = AssetDatabase.LoadAssetAtPath<Material>(path);
                    AssetDatabase.ImportAsset(path, ImportAssetOptions.Default);

                    Debug.LogFormat("not foud fade material, so create new {0}", path);
                }

                if (null == fadeMaterial)
                {
                    Debug.LogErrorFormat("not foud fade material, {0}", path);
                }

                fadeMaterial.SetFloat("_SrcBlend", (float)BlendMode.SrcAlpha);
                fadeMaterial.SetFloat("_DstBlend", (float)BlendMode.OneMinusSrcAlpha);
                fadeMaterial.SetFloat("_RenderingMode", 3);
                fadeMaterial.SetFloat("_ZWrite", 1);
                fadeMaterial.SetFloat("_CullMode", (float)CullMode.Back);
                fadeMaterial.DisableKeyword("ENABLE_DIFFUSE");
                fadeMaterial.DisableKeyword("ENABLE_SPECULAR");
                fadeMaterial.DisableKeyword("ENABLE_FLOW_ADD");
                fadeMaterial.DisableKeyword("ENABLE_FLOW_MUL");
                fadeMaterial.DisableKeyword("ENABLE_FLOW_MASK");
                fadeMaterial.DisableKeyword("ENABLE_RIM_LIGHT");
                fadeMaterial.DisableKeyword("ENABLE_RIM");
                fadeMaterial.EnableKeyword("ENABLE_MAIN_TEX_USE_CHANNEL_RGB");
                fadeMaterial.EnableKeyword("ENABLE_ALPHA_BLEND");
                //Selection.activeObject = fadeMaterial;

                renderItem.materal = fadeMaterial;
                list.Add(renderItem);
                break;
            }
        }

        this.renderList = list.ToArray();
    }
#endif

    [Serializable]
    private class RenderItem
    {
        public Renderer renderer;
        public Material materal;

        [NonSerialized]
        private MaterialPropertyBlock propertyBlock;

        public void SetColor(Color color)
        {
            if (null == propertyBlock)
            {
                propertyBlock = new MaterialPropertyBlock();
            }

            propertyBlock.SetColor(ShaderProperty.MainColor, color);
            renderer.SetPropertyBlock(propertyBlock);
        }

    }
}
