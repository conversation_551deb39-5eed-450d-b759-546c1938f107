using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Game.Scripts.Editor.Tools
{
    class ChangeViewColor : BaseEditorWindow
    {
        private Color oldColor = Color.black;
        private Color newColor = Color.black;

        [MenuItem("Tools/换UI工具/View替换颜色")]
        private static void ShowWindow()
        {
            EditorWindow.GetWindow<ChangeViewColor>(false, "替换颜色");
        }

        private void OnGUI()
        {
            oldColor = EditorGUILayout.ColorField("oldColor: ", oldColor);
            newColor = EditorGUILayout.ColorField("newColor: ", newColor);
            //GUILayout.Space(100);
            //if (GUILayout.Button("替换选中预制体"))
            //{
            //    if (Selection.activeGameObject != null)
            //    {
            //        Check(Selection.activeGameObject);
            //        AssetDatabase.SaveAssets();
            //    }
            //}

            GUILayout.Space(50);
            if (GUILayout.Button("Change All"))
            {
                ChangeAll();
                AssetDatabase.SaveAssets();
            }
        }

        private void ChangeAll()
        {
            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { "Assets/Game/UIs/View" });
            int endIndex = guids.Length;
            if (endIndex < 1)
            {
                return;
            }

            float nextTime = 0;
            for (int i = 0; i < endIndex; i++)
            {
                var guid = guids[i];
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                this.Check(obj);
                if (nextTime <= Time.realtimeSinceStartup)
                {
                    bool cancel = EditorUtility.DisplayCancelableProgressBar("替换中", path, (float)i / endIndex);
                    nextTime = Time.realtimeSinceStartup + 0.1f;
                    if (cancel)
                    {
                        break;
                    }
                }
            }

            EditorUtility.ClearProgressBar();
        }


        private void Check(GameObject obj)
        {
            bool had_change = false;
            var texts = obj.GetComponentsInChildren<Text>(true);
            if (texts.Length < 1)
            {
                return;
            }

            foreach (Text text in texts)
            {
                if (text.color == oldColor)
                {
                    text.color = newColor;
                    had_change = true;
                }
            }

            if (had_change)
            {
                Debug.LogError("找到：" + obj.transform.name);
                //PrefabUtility.ResetToPrefabState(obj);
                //PrefabUtility.SetPropertyModifications(obj, new PropertyModification[] { });
                EditorUtility.SetDirty(obj);
            }

        }
    }
}