﻿//-----------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//-----------------------------------------------------------------------------

using System.Collections.Generic;
using Nirvana;
using UnityEngine;

/// <summary>
/// The actor blinker used to play blink on an actor.
/// </summary>
public sealed class ActorBlinker : MonoBehaviour
{
    private static int rimIntensityID = -1;
    private static int rimFresnelID = -1;
    private float rimlimitfloat = 0.4f;

    private static int RimIntensityID
    {
        get
        {
            if (rimIntensityID == -1)
            {
                rimIntensityID = Shader.PropertyToID("_RimIntensity");
            }

            return rimIntensityID;
        }
    }

    private static int RimFresnelID
    {
        get
        {
            if (rimFresnelID == -1)
            {
                rimFresnelID = Shader.PropertyToID("_RimFresnel");
            }

            return rimFresnelID;
        }
    }

    [SerializeField]
    private float fadeIn = 0.01f;

    [SerializeField]
    private float fadeHold = 0.05f;

    [SerializeField]
    private float fadeOut = 0.25f;

    private float blinkFadeIn = -1.0f;
    private float blinkFadeInTotal = -1.0f;
    private float blinkFadeHold = -1.0f;
    private float blinkFadeOut = -1.0f;
    private float blinkFadeOutTotal = -1.0f;
    private Renderer[] blinkRenderers;
    private List<NirvanaRenderer> renderers =
        new List<NirvanaRenderer>();

    public float FadeIn { get { return fadeIn; } }

    public float FadeHold { get { return fadeHold; } }

    public float FadeOut { get { return fadeOut; } }
    /// <summary>
    /// Blink this character.
    /// </summary>
    public void Blink(float fadeIn, float fadeHold, float fadeOut)
    {
        this.blinkFadeIn = fadeIn;
        this.blinkFadeInTotal = fadeIn;
        this.blinkFadeHold = fadeHold;
        this.blinkFadeOut = fadeOut;
        this.blinkFadeOutTotal = fadeOut;

        foreach (var renderer in this.renderers)
        {
            renderer.UnsetKeyword((int)ShaderKeyword.ENABLE_RIM);
        }

        this.GetComponentsInChildren(this.renderers);
        foreach (var renderer in this.renderers)
        {
            renderer.SetKeyword((int)ShaderKeyword.ENABLE_RIM);
        }

        if (this.blinkRenderers != null)
        {
            foreach (var renderer in this.blinkRenderers)
            {
                if (renderer != null)
                {
                    renderer.material.DisableKeyword("ENABLE_RIM");
                }
            }
        }
        var meshRenderers = this.GetComponentsInChildren<MeshRenderer>();
        var skinnedRenderers = this.GetComponentsInChildren<SkinnedMeshRenderer>();
        this.blinkRenderers = new Renderer[meshRenderers.Length + skinnedRenderers.Length];

        int index = 0;
        foreach (var renderer in meshRenderers)
        {
            this.blinkRenderers[index] = renderer;
            renderer.material.EnableKeyword("ENABLE_RIM");
            renderer.material.SetFloat(RimIntensityID, rimlimitfloat);
            renderer.material.SetFloat(RimFresnelID, 2.0f);
            ++index;
        }

        foreach (var renderer in skinnedRenderers)
        {
            this.blinkRenderers[index] = renderer;
            renderer.material.EnableKeyword("ENABLE_RIM");
            renderer.material.SetFloat(RimIntensityID, rimlimitfloat);
            renderer.material.SetFloat(RimFresnelID, 2.0f);
            ++index;
        }
    }

    private void Update()
    {
        if (this.blinkFadeIn > 0.0f)
        {
            float value = 1 - (this.blinkFadeIn / this.blinkFadeInTotal);
            foreach (var renderer in this.blinkRenderers)
            {
                if (renderer != null)
                {
                    renderer.material.SetFloat(
                        RimIntensityID, 3.5f * value + rimlimitfloat);
                }
            }
            foreach (var renderer in this.renderers)
            {
                renderer.PropertyBlock.SetFloat(
                    ShaderProperty.RimIntensity, 3.5f * value);
            }

            this.blinkFadeIn -= Time.deltaTime;
        }
        else if (this.blinkFadeHold > 0.0f)
        {
            this.blinkFadeHold -= Time.deltaTime;
        }
        else if (this.blinkFadeOut > 0.0f)
        {
            float value = this.blinkFadeOut / this.blinkFadeOutTotal;
            foreach (var renderer in this.blinkRenderers)
            {
                if (renderer != null)
                {
                    renderer.material.SetFloat(
                        RimIntensityID, 3.5f * value + rimlimitfloat);

                }
            }
            foreach (var renderer in this.renderers)
            {
                renderer.PropertyBlock.SetFloat(
                    ShaderProperty.RimIntensity, 3.5f * value);
            }

            this.blinkFadeOut -= Time.deltaTime;
            if (this.blinkFadeOut <= 0.0f)
            {
                foreach (var renderer in this.blinkRenderers)
                {
                    if (renderer != null)
                    {
                        renderer.material.DisableKeyword("ENABLE_RIM");
                    }
                }

                this.blinkRenderers = null;
                foreach (var renderer in this.renderers)
                {
                    renderer.UnsetKeyword((int)ShaderKeyword.ENABLE_RIM);
                }

                this.renderers.Clear();
            }
        }
    }
}
