﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[ExecuteInEditMode]
public class CGCameraFog : MonoBehaviour {

    [SerializeField]
    private Color color;
    [SerializeField]
    private FogMode mode = FogMode.Linear;
    [SerializeField]
    private float start;
    [SerializeField]
    private float end;

    private Color oldColor;
    private FogMode oldFogMode;
    private float oldStart;
    private float oldEnd;

    void OnEnable()
    {
        this.oldColor = RenderSettings.fogColor;
        this.oldFogMode = RenderSettings.fogMode;
        this.oldStart = RenderSettings.fogStartDistance;
        this.oldEnd = RenderSettings.fogEndDistance;

        RenderSettings.fogColor = this.color;
        RenderSettings.fogMode = this.mode;
        RenderSettings.fogStartDistance = this.start;
        RenderSettings.fogEndDistance = this.end;
    }

    void OnDisable()
    {
        RenderSettings.fogColor = this.oldColor;
        RenderSettings.fogMode = this.oldFogMode;
        RenderSettings.fogStartDistance = this.oldStart;
        RenderSettings.fogEndDistance = this.oldEnd;
    }
}
