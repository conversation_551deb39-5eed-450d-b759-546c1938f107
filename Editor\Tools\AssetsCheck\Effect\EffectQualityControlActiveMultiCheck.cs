﻿using UnityEngine;
using UnityEditor;
using System.Text;
using System.Collections.Generic;
using Nirvana;

namespace AssetsCheck
{
    class EffectQualityControlActiveMultiCheck : BaseChecker
    {
        private string[] checkRoots = {
            "Assets/Game/Actor",
            "Assets/Game/Model",
            "Assets/Game/Effects2/Prefab",
        };
        private string[] filters = {
            "Assets/Game/Effects2/Prefab/ui",
            "Assets/Game/Effects2/Prefab/UI_X",
            "Assets/Game/Effects2/Prefab/cg",
            "Assets/Game/Effects2/Prefab/Title",
        };

        public override string GetErrorDesc()
        {
            return "特效预制体挂载了多个QualityActiveControl组件，或者是不能挂载该组件（UI特效、模型不挂）";
        }

        bool isFilter(string path)
        {
            foreach (string filter in filters)
            {
                if (path.StartsWith(filter))
                {
                    return true;
                }
            }
            return false;
        }

        protected override void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);

            try
            {
                for (int i = 0; i < guids.Length; i++)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guids[i]).Replace("\\", "/");
                    string errorMsg = string.Format("Path: {0}", assetPath);
                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    if (prefab != null)
                    {
                        QualityControlActive[] qualityControlActives = prefab.GetComponentsInChildren<QualityControlActive>();
                        if (qualityControlActives.Length >= 1)
                        {
                            bool isDirty = false;
                            if (isFilter(assetPath) || null != prefab.GetComponent<ActorRender>() && assetPath.StartsWith("Assets/Game/Model"))
                            {
                                errorMsg += "|不应该挂载QualityControlActive组件";
                                isDirty = true;
                            }
                            else if (qualityControlActives.Length > 1)
                            {
                                errorMsg += "|挂载了多个QualityControlActive组件";
                                isDirty = true;
                            }

                            if (isDirty)
                            {
                                CheckItem checkItem = new CheckItem();
                                checkItem.asset = errorMsg;
                                this.outputList.Add(checkItem);
                            }
                        }
                    }

                    EditorUtility.DisplayProgressBar("正在批量检查预制体", string.Format("{0}/{1}", i, guids.Length), (float)i / (float)guids.Length);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
        }

        protected override void OnFix(string[] lines)
        {
            bool isComeFromCheck = false;
            string[] assets;
            if (lines == null || lines.Length == 0)
            {
                List<string> assetList = new List<string>();
                string[] guids = AssetDatabase.FindAssets("t:prefab", checkRoots);
                foreach (string guid in guids)
                {
                    string asset = AssetDatabase.GUIDToAssetPath(guid);
                    assetList.Add(asset);
                }
                assets = assetList.ToArray();
            }
            else
            {
                assets = lines;
                isComeFromCheck = true;
            }

            int count = assets.Length;
            try
            {
                for (int i = 0; i < assets.Length; i++)
                {
                    bool isDeleteAllComponent = false;
                    string assetPath = assets[i];
                    if (isComeFromCheck)
                    {
                        string[] splitStr = assets[i].Split('|');
                        assetPath = splitStr[0].Replace("Path: ", "");
                        for (int j = 1; j < splitStr.Length; j++)
                        {
                            if (splitStr[j] == "不应该挂载QualityControlActive组件")
                            {
                                isDeleteAllComponent = true;
                                break;
                            }
                        }
                    }

                    GameObject prefab = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject)) as GameObject;
                    if (prefab != null)
                    {
                        GameObject gameObject = GameObject.Instantiate(prefab);
                        if (gameObject.GetComponent<ActorRender>() != null)
                        {
                            QualityControlActive[] qcas = gameObject.GetComponentsInChildren<QualityControlActive>();
                            for (int k = 0; k < qcas.Length; k++)
                            {
                                QualityControlActive qca = qcas[k];
                                ParticleSystem[] particleSystems = qca.GetComponentsInChildren<ParticleSystem>();
                                if (particleSystems != null && particleSystems.Length > 0 && qca.gameObject != gameObject)
                                {
                                    GameObject.DestroyImmediate(qca.gameObject);
                                    continue;
                                }
                            }
                        }

                        QualityControlActive[] qualityControlActives = gameObject.GetComponentsInChildren<QualityControlActive>();
                        if (isDeleteAllComponent)
                        {
                            for (int j = 0; j < qualityControlActives.Length; j++)
                            {
                                GameObject.DestroyImmediate(qualityControlActives[j]);
                            }
                        }
                        else
                        {
                            List<GameObject> existObj = new List<GameObject>();
                            for (int j = 0; j < qualityControlActives.Length; j++)
                            {
                                if (existObj.Contains(qualityControlActives[j].gameObject))
                                {
                                    GameObject.DestroyImmediate(qualityControlActives[j]);
                                }
                                else
                                {
                                    existObj.Add(qualityControlActives[j].gameObject);
                                    if (qualityControlActives[j].gameObject != gameObject)
                                    {
                                        GameObject.DestroyImmediate(qualityControlActives[j]);
                                    }
                                }
                            }
                        }

                        PrefabUtility.SaveAsPrefabAsset(gameObject, assetPath);
                        GameObject.DestroyImmediate(gameObject);
                    }

                    EditorUtility.DisplayProgressBar("正在批量修改预制体", string.Format("{0}/{1}", (i + 1), count), (float)(i + 1) / (float)count);
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Debug.LogError(ex.ToString());
            }

            EditorUtility.ClearProgressBar();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        struct CheckItem : ICheckItem
        {
            public string asset;

            public string MainKey
            {
                get { return this.asset; }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(string.Format("{0}", asset));

                return builder;
            }
        }
    }
}
