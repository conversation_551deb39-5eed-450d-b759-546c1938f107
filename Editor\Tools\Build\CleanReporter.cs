﻿using Game;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public class CleanReporter
{
    public static void OutputCleanReport()
    {
        HashSet<string> reportSet = new HashSet<string>();

        string filePath = string.Format("{0}/../AssetsCheck/EffectsCheck.txt", Application.dataPath);
        GetNormalEffectReport(reportSet);
   //     GetSceneReport(reportSet);

        File.WriteAllLines(filePath, new List<string>(reportSet).ToArray());
        Debug.LogErrorFormat("输出完成，输出路径{0}, 需要优化个数{1}，", filePath, reportSet.Count);
    }

    private static void GetNormalEffectReport(HashSet<string> reportSet)
    {
        string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] {
            "Assets/Game/Effects/Prefab/Model/Boss",
            "Assets/Game/Effects/Prefab/Model/Buff",
            "Assets/Game/Effects/Prefab/Model/Drop",
             "Assets/Game/Effects/Prefab/Model/Title",
            "Assets/Game/Effects/Prefab/Model/Halo",
            "Assets/Game/Effects/Prefab/Model/Halo_01",
            "Assets/Game/Effects/Prefab/Model/HuobanHalo",
            "Assets/Game/Effects/Prefab/Model/Mingjiang",
           "Assets/Game/Effects/Prefab/Model/Fazhen",
           "Assets/Game/Effects/Prefab/Model/Footprint",
            "Assets/Game/Effects/Prefab/Model/Misc",
            "Assets/Game/Effects/Prefab/Model/Weapon",
            "Assets/Game/Effects/Prefab/Model/Role",

            //"Assets/Game/Actors/pet",
            //"Assets/Game/Actors/Wing",
            //"Assets/Game/Actors/Mount",
            //"Assets/Game/Actors/Weapon",
            //"Assets/Game/Actors/Gather",
           //"Assets/Game/Actors/Mingjiang",
           //"Assets/Game/Actors/MingJiangWeapon",
           //"Assets/Game/Actors/MingJiangWeapon1",
           //"Assets/Game/Actors/MingJiangWeaponfacade",
           // "Assets/Game/Actors/NPC",
           // "Assets/Game/Actors/Character",
           // "Assets/Game/Actors/Monster",
        });
        foreach (var guid in guids)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            ParticleSystem[] particles = gameObject.GetComponentsInChildren<ParticleSystem>();
            if (EffectDynamicChecker.CheckEffectIsBad(gameObject, particles, null))
            {
                string log = string.Format("{0} 特效不合格!!!!!", assetPath);
                reportSet.Add(log);
            }
        }
    }

    // 检查场景中的特效合格报告
    private static void GetSceneReport(HashSet<string> reportSet)
    {
        string[] checkDirs = { "Assets/Game/Scenes/Map" };
        string[] guids = AssetDatabase.FindAssets("t:scene", checkDirs);

        foreach (var guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.EndsWith("_Main.unity") || path.EndsWith("_Detail.unity"))
            {
                Scene scene = EditorSceneManager.OpenScene(path);
                GetSceneEffectReport(scene, reportSet);
                GetSceneComponentReport(scene, reportSet);
                EditorSceneManager.CloseScene(scene, true);
            }
        }     
    }

    // 场景特效
    private static void GetSceneEffectReport(Scene scene, HashSet<string> reportSet)
    {
        GameObject rootObj = GameObject.Find("Main");
        GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
        for (int i = 0; i < attchs.Length; i++)
        {
            string effectPath = AssetDatabase.GUIDToAssetPath(attchs[i].AssetGuid);
            var gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(effectPath);
            if (null == gameObject)
            {
                Debug.LogErrorFormat("找不到特效路径, {0} {1}", scene.name, attchs[i].name, effectPath);
                continue;
            }
            ParticleSystem[] particles = gameObject.GetComponentsInChildren<ParticleSystem>();
            if (EffectDynamicChecker.CheckEffectIsBad(gameObject, particles, null))
            {
                string log = string.Format("{0} 场景{1}节点下的{2} 特效不合格!!!!!", scene.name, attchs[i].name, effectPath);
                reportSet.Add(log);
            }
        }
    }

    // 场景组件
    private static void GetSceneComponentReport(Scene scene, HashSet<string> reportSet)
    {
        GameObject rootObj = GameObject.Find("Main");

        var particles = rootObj.GetComponentsInChildren<ParticleSystem>();
        for (int i = 0; i < particles.Length; i++)
        {
            GameObjectAttach attach = particles[i].GetComponentInParent<GameObjectAttach>();
            if (null == attach)
            {
                string log = string.Format("场景中的粒子特效请严格使用GameObjectAttach来加载，通知特效处理, {0},   {1}", particles[i].name, scene.name);
                reportSet.Add(log);
            }
        }

        if  (rootObj.GetComponent<SceneOptimize>())
        {
            GameObjectAttach[] attchs = rootObj.GetComponentsInChildren<GameObjectAttach>();
            for (int i = 0; i < attchs.Length; i++)
            {
                var cullObj = attchs[i].GetComponentInParent<CameraCullObj>();
                if (null == cullObj)
                {
                    string log = string.Format("场景中的粒子特效没用使用CullObj，通知特效处理, {0},   {1}", attchs[i].gameObject.name, scene.name);
                    reportSet.Add(log);
                }
            }
        }
    }
}
