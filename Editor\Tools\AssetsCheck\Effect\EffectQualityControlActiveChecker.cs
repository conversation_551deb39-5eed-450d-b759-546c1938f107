﻿using UnityEngine;
using UnityEditor;
using Nirvana;
using System.Text;

namespace AssetsCheck
{
    class EffectQualityControlActiveChecker : BaseChecker
    {
        // 指定要检查的文件夹
        private string[] checkDirs = { "Assets/Game/Actors", "Assets/Game/Model", "Assets/Game/Effects" };

        private string[] UIFloder = { "Assets/Game/Effects/Prefab/UI" };

        // 获得错误描述
        override public string GetErrorDesc()
        {
            return "特效文件必须挂上QualityControlActive组件进行管理";
        }

        override protected void OnCheck()
        {
            string[] guids = AssetDatabase.FindAssets("t:prefab", checkDirs);
            int count = 0;
            int total = guids.Length;

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject gameobj = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                QualityControlActive qualityControlActive = gameobj.GetComponent<QualityControlActive>();
                UIEffect UIEffect = gameobj.GetComponent<UIEffect>();
                SrpUIEffect srpUIEffect = gameobj.GetComponent<SrpUIEffect>();
                bool isUIFolder = CheckIsUIFolder(path);

                CheckItem item = new CheckItem();
                item.asset = path;
                item.desc = "";

                bool is_add_list = false;

                if (qualityControlActive != null)   //是特效相关预制 检测特效文件是否全是默认的只勾选了Perfect
                {
                    bool is_only_perfect = this.CheckThisControlActiveIsOnlyPerfect(qualityControlActive);
                    if (is_only_perfect)
                    {
                        item.desc = "只勾选了Perfect,和可能一个品质都未勾选";
                        is_add_list = true;
                    }
                }
                else// 不存在组件的检测时候子集包含粒子有粒子但未添加QualityControlActive
                {
                    ParticleSystem[] particle_systems = gameobj.GetComponentsInChildren<ParticleSystem>(true);
                    if (particle_systems.Length > 0)
                    {
                        item.desc = "字节点存在粒子";
                        if (!isUIFolder)
                            item.desc += ",但是没有添加 QualityControlActive 组件";
                        is_add_list = true;
                    }
                }

                if (is_add_list)
                {
                    if (isUIFolder)
                    {
                        if (qualityControlActive != null)
                            item.desc += ",在UI特效文件不应该添加 QualityControlActive组件";
                
                        if (UIEffect == null && srpUIEffect == null)
                            item.desc += ",在UI特效文件夹但是未添加 UIEffect 或者 srpUIEffect";
                    }
                    else
                    {
                        if (UIEffect != null || srpUIEffect != null)
                            item.desc += ",不在UI特效文件夹但是添加了 UIEffect 或者 srpUIEffect,";
                    }

                    this.outputList.Add(item);
                }

                count++;
                EditorUtility.DisplayProgressBar("正在排查预制体特效qualityControlActive组件...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }

            AssetDatabase.Refresh();
            EditorUtility.ClearProgressBar();
        }

        // 检测是否只存在一个勾选且只勾选了第一个
        private bool CheckThisControlActiveIsOnlyPerfect(QualityControlActive qualityControlActive)
        {
            ControlItem[] controls = qualityControlActive.GetControls();
            bool is_only = true;

            for (int i = 0; i < controls.Length; i++)
            {
                is_only = is_only && this.CheckControlItemOnlyPerfect(controls[i]);
            }

            return is_only;
        }

        //检测特效文件列表是否只勾选了第一个或者特效对象为空对象
        private bool CheckControlItemOnlyPerfect(ControlItem controlItem)
        {
            if (controlItem.Target == null)
                return false;

            bool statue = true;

            for (int i = 0; i < controlItem.EnabledLevels.Length; i++)
            {
                bool is_enable = controlItem.EnabledLevels[i];

                if (i != 0 && is_enable)        //不是perfect 只要勾选了就不用记录
                    statue = false;

            }

            return statue;
        }


        /// <summary>
        /// 一键修复
        /// </summary>
        /// <param name="lines"></param>
        protected override void OnFix(string[] lines)
        {
            int count = 0;
            int total = lines.Length;
            foreach (string line in lines)
            {
                string[] str = line.Split(',');
                string path = str[0].Replace("asset:", "");
                GameObject prefab = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                if (prefab)
                {
                    QualityControlActive qualityControlActive = prefab.GetComponent<QualityControlActive>();
                    SrpUIEffect srpUIEffect = prefab.GetComponent<SrpUIEffect>();
                    bool isUIFolder = CheckIsUIFolder(path);


                    // 在UI文件夹下的不能包含QualityControlActive, 没有挂载SrpUIEffect 和 UIEffect选哟美术自己手动操作
                    // 不在UI文件夹下的不能挂载SrpUIEffect 和 UIEffect， 需要挂载QualityControlActive
                    if (isUIFolder)
                    {
                        if (qualityControlActive != null)
                            Object.DestroyImmediate(qualityControlActive, true);
                    }
                    else
                    {
                        if (qualityControlActive == null)   //是特效相关预制 检测特效文件是否全是默认的只勾选了Perfect
                        {
                            qualityControlActive = prefab.GetOrAddComponent<QualityControlActive>();
                            qualityControlActive.AutoFetch(false);
                        }
                        else
                        {
                            ParticleSystem[] particle_systems = prefab.GetComponentsInChildren<ParticleSystem>(true);
                            if (particle_systems.Length > 0)
                                qualityControlActive.AutoFetch(false);
                            else
                                Object.DestroyImmediate(qualityControlActive, true);

                        }

                        if (srpUIEffect != null)
                            Object.DestroyImmediate(srpUIEffect, true);
                    }
                }
                count++;
                EditorUtility.DisplayProgressBar("一键修复预制体特效qualityControlActive组件...", string.Format("{0}/{1}", count, total), (float)count / (float)total);
            }

            AssetDatabase.Refresh();
            AssetDatabase.SaveAssets();
            EditorUtility.ClearProgressBar();
        }

        //校验是否是UI文件夹
        private bool CheckIsUIFolder(string path)
        {
            for (int i = 0; i < UIFloder.Length; i++)
            {
                if (path.Contains(UIFloder[i])) 
                    return true;
            }

            return false;
        }

        struct CheckItem : ICheckItem
        {
            public string asset;
            public string desc;

            public string MainKey
            {
                get { return string.Format("{0}", asset); }
            }

            public StringBuilder Output()
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(string.Format("{0},{1}", asset, desc));
                return builder;
            }
        }
    }
}
