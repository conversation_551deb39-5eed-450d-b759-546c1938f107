﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using LuaInterface;


public class LuaToJson 
{
	private static LuaState m_lua_state;
	private static string saveFolder = "/Game/Lua/";

	private static LuaFunction JsonToTable;
	private static LuaFunction TableToJson;
		
	public static string Convert(string luaStr)
	{
		var configPath = Application.dataPath + saveFolder;
		luaStr = luaStr.Replace (configPath, string.Empty);
		luaStr = luaStr.Replace (".lua", string.Empty);

		if (LuaToJson.m_lua_state == null)
		{
			LuaToJson.m_lua_state = new LuaState ();
			LuaToJson.m_lua_state.Start ();
		}
		// 执行启动文件.
		LuaToJson.m_lua_state.DoFile("utils/lua_json.lua");
		LuaTable table_data = LuaToJson.m_lua_state.Require<LuaTable>(luaStr);
		// 获取Update函数
		LuaToJson.JsonToTable = LuaToJson.m_lua_state.GetFunction("JsonToTable");
		LuaToJson.TableToJson = LuaToJson.m_lua_state.GetFunction("TableToJson");
		string json_str = LuaToJson.TableToJson.Invoke<LuaTable, string>(table_data);

		LuaToJson.m_lua_state.Dispose ();
		LuaToJson.m_lua_state = null;
		return json_str;
	}
}
