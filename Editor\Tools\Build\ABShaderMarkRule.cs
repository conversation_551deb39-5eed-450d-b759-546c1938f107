﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;

using System.IO;

public class ABShaderMarkRule
{
   
    public static string fix = "ZZKWS_";

    private static string shaderABFolderName = "shaderkws/";

    public static void Build()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });

        int length = guids.Length;
        int i = 0;
        foreach(var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (ABShaderMarkRule.IsNotBuildFolder(assetPath))
                continue;


            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            if (!BuildOne(mat)) { }
                //break;
            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }
        EditorUtility.ClearProgressBar();
    }

    public static bool IsNotBuildFolder(string assetPath)
    {
        if (assetPath.Contains("Assets/Game/Shaders/Materials"))
        {
            return true;
        }
        if (assetPath.Contains("MatFolder"))
        {
            return true;
        }
        return false;
    }

    public static bool BuildOne(Material mat, bool onlyCreateShader = false)
    {
        Shader newShader;
        string newShaderName;
        bool bb = BuildOneShader(mat, out newShader,out newShaderName, onlyCreateShader);
        if (bb)
        {
            var oldRenderQueue = mat.renderQueue;
            if (mat.shader.name != newShaderName)
            {
                mat.shader = newShader;
                mat.renderQueue = oldRenderQueue;
            }
  
            CheckMat(mat, newShaderName, newShader);
        }
        else
        {
            return false;
        }

        return true;
    }

    public static bool BuildOneShader(Material mat,out Shader newShader,out string newShaderName,
        bool onlyCreateShader=false,bool onlySetABName = false,bool onlyChargeMatShader = false)
    {
        newShader = null;
        newShaderName = "";
        
        // 已经绑定过
        if (mat.shader.name.Contains(fix))
        {
            return false;
        }
        // 不是要绑定的shader
        if (!IsYYShader(mat.shader.name))
        {
            return false;
        }

        newShaderName = fix+ABMaterialMarkRule.TryGetMaterialName(mat);
        string oldShaderName = mat.shader.name;
        if(newShaderName == oldShaderName)
        {
            return false;
        }
        //Shader newShader = null;
        string newShaderAssetPath = "";
        string outputDir = "";
        //if (!dicShader.TryGetValue(newShaderName, out newShader))


        newShader = Shader.Find(newShaderName);
        if(onlyChargeMatShader)
        {
            if(newShader==null)
            {
                Debug.LogError("onlyChargeMatShader is " + newShaderName);
            }
            return true;
        }
        if (newShader == null)
        {
            outputDir = GetOrginalShaderPathDir(mat.shader);
            newShaderAssetPath = string.Format("{0}/{1}.shader", outputDir, newShaderName);
            newShader = AssetDatabase.LoadAssetAtPath<Shader>(newShaderAssetPath);
        }

        if (newShader == null)
        {
           
            string orginalShaderAbsolutePath = GetOrginalShaderAbsolutePath(mat.shader);
            if (!File.Exists(orginalShaderAbsolutePath))
            {
                Debug.LogError("file not exists "+orginalShaderAbsolutePath);
                return false;
            }

            string dirAbsolutePath = string.Format("{0}/../{1}", Application.dataPath, outputDir);
            string newShaderAbsolutePath = string.Format("{0}/{1}.shader", dirAbsolutePath, newShaderName);
            if (File.Exists(newShaderAbsolutePath))
            {
                Debug.Log("file exist " + newShaderAbsolutePath);
            }
            else
            {

                string shaderContent = File.ReadAllText(orginalShaderAbsolutePath);
                //修改shader内容
                shaderContent = shaderContent.Replace(oldShaderName + "\"", newShaderName + "\"");
                File.WriteAllText(newShaderAbsolutePath, shaderContent);
            }

            if (!onlyCreateShader)
            {

                AssetDatabase.ImportAsset(newShaderAssetPath);
                newShader = Shader.Find(newShaderName);

                AssetImporter importer = AssetImporter.GetAtPath(newShaderAssetPath);
                if (importer == null)
                {
                    Debug.Log("importer is null " + newShaderAssetPath);
                }
                else
                {
                    importer.assetBundleName = shaderABFolderName + newShaderName;
                }
            }

        }

        if (onlySetABName&&!onlyCreateShader)
        {
            if(string.IsNullOrEmpty(newShaderAssetPath))
            {
                outputDir = GetOrginalShaderPathDir(mat.shader);
                newShaderAssetPath = string.Format("{0}/{1}.shader", outputDir, newShaderName);
            }
            AssetImporter importer = AssetImporter.GetAtPath(newShaderAssetPath);
            if (importer == null)
            {
                Debug.Log("importer is null " + newShaderAssetPath);
            }
            else
            {
                importer.assetBundleName = shaderABFolderName + newShaderName;
            }
        }
        return true;
    }

    private static string matFolder = "MatFolder";
    public static void CheckMat(Material mat, string newShaderName,Shader newShader,
        bool onlyCreateMat=false, bool onlySetABName = false)
    {
        string outputDir = GetOrginalShaderPathDir(mat.shader);
        string dirRelPath = Path.Combine(outputDir, matFolder);
        if (!AssetDatabase.IsValidFolder(dirRelPath))
        {
            AssetDatabase.CreateFolder(outputDir, matFolder);
        }

        string materilAssetPath = string.Format("{0}/{1}.mat", dirRelPath, newShaderName);
        Material material = AssetDatabase.LoadAssetAtPath<Material>(materilAssetPath);
        if (material == null)
        {
          

            material = new Material(newShader);
            material.shaderKeywords = mat.shaderKeywords;

            AssetDatabase.CreateAsset(material, materilAssetPath);

            //
            if (!onlyCreateMat)
            {
                AssetImporter importer = AssetImporter.GetAtPath(materilAssetPath);
                if (importer == null)
                {
                    Debug.Log("importer is null " + materilAssetPath);
                }
                else
                {
                    importer.assetBundleName = shaderABFolderName + newShaderName;
                }
            }

        }

        if (onlySetABName && !onlyCreateMat)
        {
           
            AssetImporter importer = AssetImporter.GetAtPath(materilAssetPath);
            if (importer == null)
            {
                Debug.Log("importer is null " + materilAssetPath);
            }
            else
            {
                importer.assetBundleName = shaderABFolderName + newShaderName;
            }
        }


    }


    private static string GetOrginalShaderAbsolutePath(Shader shader)
    {
        string assetPath = AssetDatabase.GetAssetPath(shader.GetInstanceID());
        string absolutePath = string.Format("{0}/../{1}", Application.dataPath, assetPath);
        return absolutePath;
    }
    private static string GetOrginalShaderPathDir(Shader shader)
    {
        string assetPath = AssetDatabase.GetAssetPath(shader.GetInstanceID());
        int ls = assetPath.LastIndexOf("/");
        assetPath = assetPath.Substring(0, ls);
        return assetPath;
    }

    private static bool IsYYShader(string shaderName)
    {
        //Debug.Log("IsYYShader is " + shaderName);
        if (shaderName == "YY/YYStandard"
           || shaderName == "YY/YYStandardMap"
           || shaderName == "YY/YYStandardT4M"
           || shaderName.StartsWith("YifStandard"))
        {
            return true;
        }

        return false;
    }

    private static string GetYYShaderName(string shaderName)
    {
       if(shaderName.Contains("_YYStandardMap_"))
        {
            return "YYStandardMap";
        }
        if (shaderName.Contains("_YYStandardT4M_"))
        {
            return "YYStandardT4M";
        }
        if (shaderName.Contains("_YYStandard_"))
        {
            return "YYStandard";
        }

        if (shaderName.Contains("_YifStandard_YifStandardActor_"))
        {
            return "YifStandard/YifStandardActor";
        }

        if (shaderName.Contains("_YifStandard_YifStandardEnvironments_"))
        {
            return "YifStandard/YifStandardEnvironments";
        }

        if (shaderName.Contains("_YifStandard_YifStandardParticle_"))
        {
            return "YifStandard/YifStandardParticle";
        }

        if (shaderName.Contains("_YifStandard_"))
        {
            return "YifStandard";
        }
        return "";
    }

    public static void BuildBack()
    {
        string[] guids = AssetDatabase.FindAssets("t:material", new string[] { "Assets/Game" });
        int length = guids.Length;
        int i = 0;
        foreach (var guid in guids)
        {
            i++;
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            if (IsNotBuildFolder(assetPath))
                continue;

            var mat = AssetDatabase.LoadAssetAtPath<Material>(assetPath);

            BuildOneBack(mat);

            EditorUtility.DisplayProgressBar("build", i + "/" + length + "" + assetPath, (float)i / (float)length);
        }

        EditorUtility.ClearProgressBar();
    }


    public static void BuildOneBack(Material mat)
    {
        // 没有已经绑定过
        if (!mat.shader.name.Contains(fix))
        {
            return;
        }
        // 不是要绑定的shader
        string newShaderName = GetYYShaderName(mat.shader.name);

        Shader newShader = null;
        newShader = Shader.Find(newShaderName);
        if (newShader == null)
        {
            string outputDir = GetOrginalShaderPathDir(mat.shader);
            string dirRelPath = outputDir;
            string newShaderAssetPath = string.Format("{0}/{1}.shader", dirRelPath, newShaderName);
            newShader = AssetDatabase.LoadAssetAtPath<Shader>(newShaderAssetPath);
        }
        if (mat.shader.name != newShaderName)
        {
            var oldRenderQueue = mat.renderQueue;
            mat.shader = newShader;
            mat.renderQueue = oldRenderQueue;
        }
    }



}
